# Database
DATABASE_URL="file:./dev.db"

# Authentication
NEXTAUTH_SECRET="2214FD8BDFAC9"
NEXTAUTH_URL="http://localhost:3000"
JWT_SECRET="1A4C8922E931D23848F7553323F39"

# OpenAI
OPENAI_API_KEY="********************************************************************************************************************************************************************"

# Encryption
ENCRYPTION_KEY="HEgTP1WCH4QlwfUlNOkhdMwLd9uvrpOc"

# Redis
REDIS_URL="redis://localhost:6379"

# Pinecone Vector Database
PINECONE_API_KEY="pcsk_5KF6MZ_CkPboJUuBeCVcKVP7tTno8Fm8qRVmPy6QVsfdhEXMMu5p9AEQ9JpU12jCcGzbaD"
PINECONE_ENVIRONMENT="us-east-1"
PINECONE_INDEX_NAME="query-scraft"

# Application Settings
NODE_ENV="development"
LOG_LEVEL="debug"

# Security
ENCRYPTION_KEY="K7t2cEsMk0xC3h77hfhJVfau94Q07Ik9"

# External Database Connections
MAX_CONNECTIONS_PER_USER=5
CONNECTION_TIMEOUT=30000
QUERY_TIMEOUT=60000
MAX_QUERY_RESULTS=1000

# Database Connection Pool Settings
DB_POOL_MIN=1
DB_POOL_MAX=10
DB_POOL_IDLE_TIMEOUT=30000

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_REQUESTS_PER_HOUR=1000
RATE_LIMIT_QUERIES_PER_MINUTE=50
