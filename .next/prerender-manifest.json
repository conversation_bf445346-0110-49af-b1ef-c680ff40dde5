{"version": 4, "routes": {"/dashboard": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/dashboard", "dataRoute": "/dashboard.rsc"}, "/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}, "/auth/signup": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/auth/signup", "dataRoute": "/auth/signup.rsc"}, "/auth/signin": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/auth/signin", "dataRoute": "/auth/signin.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "3a072dafbb58fb986b8acee8eb6e201b", "previewModeSigningKey": "9db1b744aebc23b4f9c906c632736d62434c4a5f38fc444422166d3e1b9a2b3f", "previewModeEncryptionKey": "804c650c7af2b6801069c7c42a5a4392265d2d16f1eee22161c0e69dd0a742b8"}}