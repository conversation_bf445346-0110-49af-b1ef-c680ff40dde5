"use strict";(()=>{var t={};t.id=348,t.ids=[348],t.modules={30517:t=>{t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},35900:t=>{t.exports=require("pg")},14300:t=>{t.exports=require("buffer")},6113:t=>{t.exports=require("crypto")},82361:t=>{t.exports=require("events")},41808:t=>{t.exports=require("net")},77282:t=>{t.exports=require("process")},12781:t=>{t.exports=require("stream")},71576:t=>{t.exports=require("string_decoder")},39512:t=>{t.exports=require("timers")},24404:t=>{t.exports=require("tls")},57310:t=>{t.exports=require("url")},73837:t=>{t.exports=require("util")},59796:t=>{t.exports=require("zlib")},2410:(t,e,r)=>{r.r(e),r.d(e,{headerHooks:()=>b,originalPathname:()=>q,patchFetch:()=>L,requestAsyncStorage:()=>y,routeModule:()=>w,serverHooks:()=>C,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>S});var n={};r.r(n),r.d(n,{DELETE:()=>m,GET:()=>p,POST:()=>h,PUT:()=>g});var o=r(95419),a=r(69108),i=r(99678),s=r(78070),c=r(55949),u=r(88302);let d=u.z.object({userId:u.z.string().cuid(),teamId:u.z.string().cuid().optional(),name:u.z.string().min(1).max(255),databaseType:u.z.enum(["MYSQL","POSTGRESQL"]),connectionString:u.z.string().min(1),host:u.z.string().optional(),port:u.z.number().int().min(1).max(65535).optional(),database:u.z.string().optional(),username:u.z.string().optional()}),l=u.z.object({name:u.z.string().min(1).max(255).optional(),connectionString:u.z.string().min(1).optional(),host:u.z.string().optional(),port:u.z.number().int().min(1).max(65535).optional(),database:u.z.string().optional(),username:u.z.string().optional(),schemaMetadata:u.z.any().optional(),isActive:u.z.boolean().optional()});async function p(t){try{let{searchParams:e}=new URL(t.url),r=e.get("userId"),n=e.get("teamId"),o=e.get("id");if(o){let t=await c.M.getConnectionById(o);if(!t)return s.Z.json({error:"Connection not found"},{status:404});return s.Z.json(t)}if(r){let t=await c.M.getUserConnections(r);return s.Z.json(t)}if(n){let t=await c.M.getTeamConnections(n);return s.Z.json(t)}return s.Z.json({error:"userId, teamId, or id parameter required"},{status:400})}catch(t){return console.error("GET /api/connections error:",t),s.Z.json({error:"Internal server error"},{status:500})}}async function h(t){try{let e=await t.json(),r=d.parse(e),n=await c.M.createConnection(r);return s.Z.json(n,{status:201})}catch(t){if(t instanceof u.z.ZodError)return s.Z.json({error:"Validation failed",details:t.errors},{status:400});return console.error("POST /api/connections error:",t),s.Z.json({error:"Internal server error"},{status:500})}}async function g(t){try{let{searchParams:e}=new URL(t.url),r=e.get("id");if(!r)return s.Z.json({error:"Connection ID required"},{status:400});let n=await t.json(),o=l.parse(n),a=await c.M.updateConnection(r,o);return s.Z.json(a)}catch(t){if(t instanceof u.z.ZodError)return s.Z.json({error:"Validation failed",details:t.errors},{status:400});return console.error("PUT /api/connections error:",t),s.Z.json({error:"Internal server error"},{status:500})}}async function m(t){try{let{searchParams:e}=new URL(t.url),r=e.get("id");if(!r)return s.Z.json({error:"Connection ID required"},{status:400});return await c.M.deleteConnection(r),s.Z.json({success:!0})}catch(t){return console.error("DELETE /api/connections error:",t),s.Z.json({error:"Internal server error"},{status:500})}}let w=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/connections/route",pathname:"/api/connections",filename:"route",bundlePath:"app/api/connections/route"},resolvedPagePath:"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/api/connections/route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:y,staticGenerationAsyncStorage:f,serverHooks:C,headerHooks:b,staticGenerationBailout:S}=w,q="/api/connections/route";function L(){return(0,i.patchFetch)({serverHooks:C,staticGenerationAsyncStorage:f})}},68505:(t,e,r)=>{r.d(e,{Lb:()=>a,_B:()=>o});let n=require("@prisma/client"),o=globalThis.prisma??new n.PrismaClient({log:["query","error","warn"],errorFormat:"pretty"});function a(t){if(console.error("Database error:",t),"P2002"===t.code)throw Error("A record with this information already exists");if("P2025"===t.code)throw Error("Record not found");if("P2003"===t.code)throw Error("Foreign key constraint failed");throw Error("Database operation failed")}},55949:(t,e,r)=>{r.d(e,{M:()=>a});var n=r(68505);function o(t){try{let e=Buffer.from(t).toString("base64");return"enc:"+e}catch(t){throw console.error("Encryption failed:",t),Error("Failed to encrypt data")}}r(6113);class a{static async createConnection(t){try{let e=o(t.connectionString),{connectionString:r,...a}=t;return await n._B.databaseConnection.create({data:{...a,connectionStringHash:e}})}catch(t){(0,n.Lb)(t)}}static async getConnectionById(t){try{return await n._B.databaseConnection.findUnique({where:{id:t},include:{user:{select:{id:!0,name:!0,email:!0}},team:{select:{id:!0,name:!0}}}})}catch(t){(0,n.Lb)(t)}}static async getUserConnections(t){try{return await n._B.databaseConnection.findMany({where:{userId:t,isActive:!0},orderBy:{lastConnectedAt:"desc"}})}catch(t){(0,n.Lb)(t)}}static async getTeamConnections(t){try{return await n._B.databaseConnection.findMany({where:{teamId:t,isActive:!0},include:{user:{select:{id:!0,name:!0,email:!0}}},orderBy:{lastConnectedAt:"desc"}})}catch(t){(0,n.Lb)(t)}}static async updateConnection(t,e){try{let r={...e};return e.connectionString&&(r.connectionStringHash=o(e.connectionString),delete r.connectionString),await n._B.databaseConnection.update({where:{id:t},data:r})}catch(t){(0,n.Lb)(t)}}static async updateLastConnected(t){try{await n._B.databaseConnection.update({where:{id:t},data:{lastConnectedAt:new Date}})}catch(t){(0,n.Lb)(t)}}static async deleteConnection(t){try{await n._B.databaseConnection.delete({where:{id:t}})}catch(t){(0,n.Lb)(t)}}static async getConnectionString(t){try{let e=await n._B.databaseConnection.findUnique({where:{id:t},select:{connectionStringHash:!0}});if(!e)return null;return function(t){try{if(!t.startsWith("enc:"))throw Error("Invalid encrypted data format");let e=t.substring(4);return Buffer.from(e,"base64").toString("utf8")}catch(t){throw console.error("Decryption failed:",t),Error("Failed to decrypt data")}}(e.connectionStringHash)}catch(t){return console.error("Failed to decrypt connection string:",t),null}}static async testConnection(t){try{let e=await this.getConnectionById(t);if(!e)return!1;let n=await this.getConnectionString(t);if(!n)return!1;let o=this.parseConnectionString(n),{ConnectorFactory:a}=await r.e(480).then(r.bind(r,98480)),i=a.createConnector(e.databaseType,o),s=await i.testConnection();return await i.disconnect(),s&&await this.updateLastConnected(t),s}catch(t){return console.error("Connection test failed:",t),!1}}static async getConnectionStats(t){try{let[e,r,o]=await Promise.all([n._B.generatedQuery.count({where:{session:{databaseConnectionId:t}}}),n._B.querySession.count({where:{databaseConnectionId:t}}),n._B.querySession.count({where:{databaseConnectionId:t,createdAt:{gte:new Date(Date.now()-6048e5)}}})]);return{totalQueries:e,totalSessions:r,recentActivity:o}}catch(t){(0,n.Lb)(t)}}static parseConnectionString(t){try{if(t.startsWith("postgresql://")||t.startsWith("postgres://"))return this.parsePostgreSQLConnectionString(t);if(t.startsWith("mysql://"))return this.parseMySQLConnectionString(t);return this.parseGenericConnectionString(t)}catch(t){throw Error(`Invalid connection string format: ${t}`)}}static parsePostgreSQLConnectionString(t){let e=new URL(t);return{host:e.hostname,port:parseInt(e.port)||5432,database:e.pathname.substring(1),username:e.username,password:e.password,ssl:"require"===e.searchParams.get("sslmode")}}static parseMySQLConnectionString(t){let e=new URL(t);return{host:e.hostname,port:parseInt(e.port)||3306,database:e.pathname.substring(1),username:e.username,password:e.password,ssl:"true"===e.searchParams.get("ssl")}}static parseGenericConnectionString(t){let e=new Map;return t.split(";").forEach(t=>{let[r,n]=t.split("=");r&&n&&e.set(r.trim().toLowerCase(),n.trim())}),{host:e.get("host")||"localhost",port:parseInt(e.get("port")||"5432"),database:e.get("database")||"",username:e.get("username")||e.get("user")||"",password:e.get("password")||"",ssl:"true"===e.get("ssl")}}static async getConnectionSchema(t){try{let e=await this.getConnectionById(t);if(!e)throw Error("Connection not found");let n=await this.getConnectionString(t);if(!n)throw Error("Connection string not found");let o=this.parseConnectionString(n),{ConnectorFactory:a}=await r.e(480).then(r.bind(r,98480)),i=a.createConnector(e.databaseType,o,t),s=await i.getSchema();return await this.updateConnection(t,{schemaMetadata:s}),s}catch(t){(0,n.Lb)(t)}}}}};var e=require("../../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),n=e.X(0,[638,543],()=>r(2410));module.exports=n})();