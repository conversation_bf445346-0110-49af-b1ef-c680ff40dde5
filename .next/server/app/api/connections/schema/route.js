"use strict";(()=>{var e={};e.id=525,e.ids=[525],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},35900:e=>{e.exports=require("pg")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},41808:e=>{e.exports=require("net")},77282:e=>{e.exports=require("process")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},39512:e=>{e.exports=require("timers")},24404:e=>{e.exports=require("tls")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},922:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>w,originalPathname:()=>C,patchFetch:()=>b,requestAsyncStorage:()=>g,routeModule:()=>p,serverHooks:()=>f,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>y});var n={};r.r(n),r.d(n,{GET:()=>l,POST:()=>h});var a=r(95419),o=r(69108),s=r(99678),i=r(78070),c=r(55949),d=r(88302);let u=d.z.object({connectionId:d.z.string().cuid(),refresh:d.z.boolean().optional().default(!1)});async function l(e){try{let{searchParams:t}=new URL(e.url),r=t.get("connectionId"),n="true"===t.get("refresh");if(!r)return i.Z.json({error:"connectionId parameter required"},{status:400});let a=u.parse({connectionId:r,refresh:n}),o=await c.M.getConnectionById(a.connectionId);if(!o)return i.Z.json({error:"Connection not found"},{status:404});if(!a.refresh&&o.schemaMetadata)try{let e="string"==typeof o.schemaMetadata?JSON.parse(o.schemaMetadata):o.schemaMetadata;return i.Z.json({schema:e,cached:!0,lastUpdated:o.updatedAt})}catch(e){console.warn("Failed to parse cached schema metadata:",e)}try{let e=await c.M.getConnectionSchema(a.connectionId);return i.Z.json({schema:e,cached:!1,lastUpdated:new Date().toISOString()})}catch(e){return console.error("Failed to fetch schema:",e),i.Z.json({error:"Failed to fetch database schema",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}catch(e){if(e instanceof d.z.ZodError)return i.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("GET /api/connections/schema error:",e),i.Z.json({error:"Internal server error"},{status:500})}}async function h(e){try{let t=await e.json(),r=u.parse({...t,refresh:!0});if(!await c.M.getConnectionById(r.connectionId))return i.Z.json({error:"Connection not found"},{status:404});try{let e=await c.M.getConnectionSchema(r.connectionId);return i.Z.json({schema:e,refreshed:!0,lastUpdated:new Date().toISOString()})}catch(e){return console.error("Failed to refresh schema:",e),i.Z.json({error:"Failed to refresh database schema",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}catch(e){if(e instanceof d.z.ZodError)return i.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("POST /api/connections/schema error:",e),i.Z.json({error:"Internal server error"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/connections/schema/route",pathname:"/api/connections/schema",filename:"route",bundlePath:"app/api/connections/schema/route"},resolvedPagePath:"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/api/connections/schema/route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:g,staticGenerationAsyncStorage:m,serverHooks:f,headerHooks:w,staticGenerationBailout:y}=p,C="/api/connections/schema/route";function b(){return(0,s.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:m})}},68505:(e,t,r)=>{r.d(t,{Lb:()=>o,_B:()=>a});let n=require("@prisma/client"),a=globalThis.prisma??new n.PrismaClient({log:["query","error","warn"],errorFormat:"pretty"});function o(e){if(console.error("Database error:",e),"P2002"===e.code)throw Error("A record with this information already exists");if("P2025"===e.code)throw Error("Record not found");if("P2003"===e.code)throw Error("Foreign key constraint failed");throw Error("Database operation failed")}},55949:(e,t,r)=>{r.d(t,{M:()=>o});var n=r(68505);function a(e){try{let t=Buffer.from(e).toString("base64");return"enc:"+t}catch(e){throw console.error("Encryption failed:",e),Error("Failed to encrypt data")}}r(6113);class o{static async createConnection(e){try{let t=a(e.connectionString),{connectionString:r,...o}=e;return await n._B.databaseConnection.create({data:{...o,connectionStringHash:t}})}catch(e){(0,n.Lb)(e)}}static async getConnectionById(e){try{return await n._B.databaseConnection.findUnique({where:{id:e},include:{user:{select:{id:!0,name:!0,email:!0}},team:{select:{id:!0,name:!0}}}})}catch(e){(0,n.Lb)(e)}}static async getUserConnections(e){try{return await n._B.databaseConnection.findMany({where:{userId:e,isActive:!0},orderBy:{lastConnectedAt:"desc"}})}catch(e){(0,n.Lb)(e)}}static async getTeamConnections(e){try{return await n._B.databaseConnection.findMany({where:{teamId:e,isActive:!0},include:{user:{select:{id:!0,name:!0,email:!0}}},orderBy:{lastConnectedAt:"desc"}})}catch(e){(0,n.Lb)(e)}}static async updateConnection(e,t){try{let r={...t};return t.connectionString&&(r.connectionStringHash=a(t.connectionString),delete r.connectionString),await n._B.databaseConnection.update({where:{id:e},data:r})}catch(e){(0,n.Lb)(e)}}static async updateLastConnected(e){try{await n._B.databaseConnection.update({where:{id:e},data:{lastConnectedAt:new Date}})}catch(e){(0,n.Lb)(e)}}static async deleteConnection(e){try{await n._B.databaseConnection.delete({where:{id:e}})}catch(e){(0,n.Lb)(e)}}static async getConnectionString(e){try{let t=await n._B.databaseConnection.findUnique({where:{id:e},select:{connectionStringHash:!0}});if(!t)return null;return function(e){try{if(!e.startsWith("enc:"))throw Error("Invalid encrypted data format");let t=e.substring(4);return Buffer.from(t,"base64").toString("utf8")}catch(e){throw console.error("Decryption failed:",e),Error("Failed to decrypt data")}}(t.connectionStringHash)}catch(e){return console.error("Failed to decrypt connection string:",e),null}}static async testConnection(e){try{let t=await this.getConnectionById(e);if(!t)return!1;let n=await this.getConnectionString(e);if(!n)return!1;let a=this.parseConnectionString(n),{ConnectorFactory:o}=await r.e(480).then(r.bind(r,98480)),s=o.createConnector(t.databaseType,a),i=await s.testConnection();return await s.disconnect(),i&&await this.updateLastConnected(e),i}catch(e){return console.error("Connection test failed:",e),!1}}static async getConnectionStats(e){try{let[t,r,a]=await Promise.all([n._B.generatedQuery.count({where:{session:{databaseConnectionId:e}}}),n._B.querySession.count({where:{databaseConnectionId:e}}),n._B.querySession.count({where:{databaseConnectionId:e,createdAt:{gte:new Date(Date.now()-6048e5)}}})]);return{totalQueries:t,totalSessions:r,recentActivity:a}}catch(e){(0,n.Lb)(e)}}static parseConnectionString(e){try{if(e.startsWith("postgresql://")||e.startsWith("postgres://"))return this.parsePostgreSQLConnectionString(e);if(e.startsWith("mysql://"))return this.parseMySQLConnectionString(e);return this.parseGenericConnectionString(e)}catch(e){throw Error(`Invalid connection string format: ${e}`)}}static parsePostgreSQLConnectionString(e){let t=new URL(e);return{host:t.hostname,port:parseInt(t.port)||5432,database:t.pathname.substring(1),username:t.username,password:t.password,ssl:"require"===t.searchParams.get("sslmode")}}static parseMySQLConnectionString(e){let t=new URL(e);return{host:t.hostname,port:parseInt(t.port)||3306,database:t.pathname.substring(1),username:t.username,password:t.password,ssl:"true"===t.searchParams.get("ssl")}}static parseGenericConnectionString(e){let t=new Map;return e.split(";").forEach(e=>{let[r,n]=e.split("=");r&&n&&t.set(r.trim().toLowerCase(),n.trim())}),{host:t.get("host")||"localhost",port:parseInt(t.get("port")||"5432"),database:t.get("database")||"",username:t.get("username")||t.get("user")||"",password:t.get("password")||"",ssl:"true"===t.get("ssl")}}static async getConnectionSchema(e){try{let t=await this.getConnectionById(e);if(!t)throw Error("Connection not found");let n=await this.getConnectionString(e);if(!n)throw Error("Connection string not found");let a=this.parseConnectionString(n),{ConnectorFactory:o}=await r.e(480).then(r.bind(r,98480)),s=o.createConnector(t.databaseType,a,e),i=await s.getSchema();return await this.updateConnection(e,{schemaMetadata:i}),i}catch(e){(0,n.Lb)(e)}}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[638,543],()=>r(922));module.exports=n})();