"use strict";(()=>{var e={};e.id=267,e.ids=[267],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},65021:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>q,originalPathname:()=>z,patchFetch:()=>B,requestAsyncStorage:()=>b,routeModule:()=>h,serverHooks:()=>T,staticGenerationAsyncStorage:()=>E,staticGenerationBailout:()=>j});var a={};t.r(a),t.d(a,{DELETE:()=>f,GET:()=>g,POST:()=>m,PUT:()=>w});var s=t(95419),n=t(69108),i=t(99678),o=t(78070),u=t(68505);class c{static async createSession(e){try{return await u._B.querySession.create({data:{...e,sessionData:e.sessionData||{}}})}catch(e){(0,u.Lb)(e)}}static async getSessionById(e){try{return await u._B.querySession.findUnique({where:{id:e},include:{user:{select:{id:!0,name:!0,email:!0}},databaseConnection:{select:{id:!0,name:!0,databaseType:!0}},generatedQueries:{orderBy:{createdAt:"desc"},take:10},chatMessages:{orderBy:{createdAt:"asc"}}}})}catch(e){(0,u.Lb)(e)}}static async getUserSessions(e,r=20){try{return await u._B.querySession.findMany({where:{userId:e,isActive:!0},include:{databaseConnection:{select:{id:!0,name:!0,databaseType:!0}},_count:{select:{generatedQueries:!0,chatMessages:!0}}},orderBy:{updatedAt:"desc"},take:r})}catch(e){(0,u.Lb)(e)}}static async createGeneratedQuery(e){try{return await u._B.generatedQuery.create({data:e})}catch(e){(0,u.Lb)(e)}}static async updateQueryExecution(e,r){try{return await u._B.generatedQuery.update({where:{id:e},data:r})}catch(e){(0,u.Lb)(e)}}static async addQueryFeedback(e,r){try{return await u._B.generatedQuery.update({where:{id:e},data:{userFeedback:r}})}catch(e){(0,u.Lb)(e)}}static async getUserQueryHistory(e,r=50,t){try{return await u._B.generatedQuery.findMany({where:{userId:e,...t&&{databaseType:t}},include:{session:{select:{id:!0,name:!0,databaseConnection:{select:{id:!0,name:!0}}}}},orderBy:{createdAt:"desc"},take:r})}catch(e){(0,u.Lb)(e)}}static async addChatMessage(e){try{return await u._B.chatMessage.create({data:e})}catch(e){(0,u.Lb)(e)}}static async getSessionMessages(e){try{return await u._B.chatMessage.findMany({where:{sessionId:e},orderBy:{createdAt:"asc"}})}catch(e){(0,u.Lb)(e)}}static async updateSession(e,r){try{return await u._B.querySession.update({where:{id:e},data:r})}catch(e){(0,u.Lb)(e)}}static async deleteSession(e){try{await u._B.querySession.delete({where:{id:e}})}catch(e){(0,u.Lb)(e)}}static async getQueryAnalytics(e,r=30){try{let t=new Date(Date.now()-864e5*r),[a,s,n,i,o]=await Promise.all([u._B.generatedQuery.count({where:{userId:e,createdAt:{gte:t}}}),u._B.generatedQuery.count({where:{userId:e,status:"EXECUTED",createdAt:{gte:t}}}),u._B.generatedQuery.aggregate({where:{userId:e,executionTime:{not:null},createdAt:{gte:t}},_avg:{executionTime:!0}}),u._B.generatedQuery.groupBy({by:["databaseType"],where:{userId:e,createdAt:{gte:t}},_count:{id:!0},orderBy:{_count:{id:"desc"}}}),u._B.$queryRaw`
          SELECT 
            DATE(created_at) as date,
            COUNT(*) as count
          FROM generated_queries 
          WHERE user_id = ${e} 
            AND created_at >= ${t}
          GROUP BY DATE(created_at)
          ORDER BY date DESC
        `]);return{totalQueries:a,successfulQueries:s,successRate:a>0?s/a*100:0,averageExecutionTime:n._avg.executionTime||0,topDatabases:i,dailyActivity:o}}catch(e){(0,u.Lb)(e)}}}var d=t(88302);let y=d.z.object({sessionId:d.z.string().cuid(),userId:d.z.string().cuid(),userInput:d.z.string().min(1),generatedSQL:d.z.string().min(1),explanation:d.z.string().optional(),databaseType:d.z.enum(["MYSQL","POSTGRESQL"]),executionTime:d.z.number().int().min(0).optional(),rowsAffected:d.z.number().int().min(0).optional(),performanceData:d.z.any().optional(),optimizationTips:d.z.any().optional()}),l=d.z.object({status:d.z.enum(["GENERATED","EXECUTED","FAILED","OPTIMIZED"]).optional(),executionTime:d.z.number().int().min(0).optional(),rowsAffected:d.z.number().int().min(0).optional(),errorMessage:d.z.string().optional(),performanceData:d.z.any().optional(),userFeedback:d.z.number().int().min(1).max(5).optional()}),p=d.z.object({userId:d.z.string().cuid(),databaseConnectionId:d.z.string().cuid(),name:d.z.string().optional(),sessionData:d.z.any().optional()});async function g(e){try{let{searchParams:r}=new URL(e.url),t=r.get("userId"),a=r.get("sessionId"),s=r.get("type"),n=parseInt(r.get("limit")||"20"),i=r.get("databaseType");if("sessions"===s&&t){let e=await c.getUserSessions(t,n);return o.Z.json(e)}if("history"===s&&t){let e=await c.getUserQueryHistory(t,n,i||void 0);return o.Z.json(e)}if(a){let e=await c.getSessionById(a);if(!e)return o.Z.json({error:"Session not found"},{status:404});return o.Z.json(e)}return o.Z.json({error:"Invalid parameters. Use type=sessions&userId=X or type=history&userId=X or sessionId=X"},{status:400})}catch(e){return console.error("GET /api/queries error:",e),o.Z.json({error:"Internal server error"},{status:500})}}async function m(e){try{let{searchParams:r}=new URL(e.url),t=r.get("type"),a=await e.json();if("session"===t){let e=p.parse(a),r=await c.createSession(e);return o.Z.json(r,{status:201})}if("query"===t){let e=y.parse(a),r=await c.createGeneratedQuery(e);return o.Z.json(r,{status:201})}return o.Z.json({error:"Type parameter required (query or session)"},{status:400})}catch(e){if(e instanceof d.z.ZodError)return o.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("POST /api/queries error:",e),o.Z.json({error:"Internal server error"},{status:500})}}async function w(e){try{let{searchParams:r}=new URL(e.url),t=r.get("id"),a=r.get("type");if(!t)return o.Z.json({error:"ID required"},{status:400});let s=await e.json();if("query"===a){let e=l.parse(s);if(e.userFeedback){let r=await c.addQueryFeedback(t,e.userFeedback);return o.Z.json(r)}if(!e.status)return o.Z.json({error:"Either userFeedback or status is required for query updates"},{status:400});{let r={status:e.status,executionTime:e.executionTime,rowsAffected:e.rowsAffected,errorMessage:e.errorMessage,performanceData:e.performanceData},a=await c.updateQueryExecution(t,r);return o.Z.json(a)}}if("session"===a){let e=d.z.object({name:d.z.string().optional(),sessionData:d.z.any().optional(),isActive:d.z.boolean().optional()}).parse(s),r=await c.updateSession(t,e);return o.Z.json(r)}return o.Z.json({error:"Type parameter required (query or session)"},{status:400})}catch(e){if(e instanceof d.z.ZodError)return o.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("PUT /api/queries error:",e),o.Z.json({error:"Internal server error"},{status:500})}}async function f(e){try{let{searchParams:r}=new URL(e.url),t=r.get("id");if(!t)return o.Z.json({error:"Session ID required"},{status:400});return await c.deleteSession(t),o.Z.json({success:!0})}catch(e){return console.error("DELETE /api/queries error:",e),o.Z.json({error:"Internal server error"},{status:500})}}let h=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/queries/route",pathname:"/api/queries",filename:"route",bundlePath:"app/api/queries/route"},resolvedPagePath:"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/api/queries/route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:b,staticGenerationAsyncStorage:E,serverHooks:T,headerHooks:q,staticGenerationBailout:j}=h,z="/api/queries/route";function B(){return(0,i.patchFetch)({serverHooks:T,staticGenerationAsyncStorage:E})}},68505:(e,r,t)=>{t.d(r,{Lb:()=>n,_B:()=>s});let a=require("@prisma/client"),s=globalThis.prisma??new a.PrismaClient({log:["query","error","warn"],errorFormat:"pretty"});function n(e){if(console.error("Database error:",e),"P2002"===e.code)throw Error("A record with this information already exists");if("P2025"===e.code)throw Error("Record not found");if("P2003"===e.code)throw Error("Foreign key constraint failed");throw Error("Database operation failed")}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[638,543],()=>t(65021));module.exports=a})();