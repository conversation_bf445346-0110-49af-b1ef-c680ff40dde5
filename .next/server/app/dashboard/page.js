(()=>{var e={};e.id=702,e.ids=[702],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9334:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>f,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var n=r(50482),a=r(69108),o=r(62563),i=r.n(o),s=r(68300),l={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);r.d(t,l);let c=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,69194)),"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/dashboard/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,98890)),"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/dashboard/page.tsx"],u="/dashboard/page",f={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},36950:(e,t,r)=>{Promise.resolve().then(r.bind(r,26994)),Promise.resolve().then(r.bind(r,35658))},97909:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("BrainCircuit",[["path",{d:"M12 4.5a2.5 2.5 0 0 0-4.96-.46 2.5 2.5 0 0 0-1.98 3 2.5 2.5 0 0 0-1.32 4.24 3 3 0 0 0 .34 5.58 2.5 2.5 0 0 0 2.96 3.08 2.5 2.5 0 0 0 4.91.05L12 20V4.5Z",key:"ixwj2a"}],["path",{d:"M16 8V5c0-1.1.9-2 2-2",key:"13dx7u"}],["path",{d:"M12 13h4",key:"1ku699"}],["path",{d:"M12 18h6a2 2 0 0 1 2 2v1",key:"105ag5"}],["path",{d:"M12 8h8",key:"1lhi5i"}],["path",{d:"M20.5 8a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"1s25gz"}],["path",{d:"M16.5 13a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"127460"}],["path",{d:"M20.5 21a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"fys062"}],["path",{d:"M18.5 3a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"1vib61"}]])},62312:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},97751:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},99046:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},96213:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},18822:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},20783:(e,t,r)=>{e.exports=r(61476)},26994:(e,t,r)=>{"use strict";r.r(t),r.d(t,{DashboardLayout:()=>nX});var n,a=r(95344),o=r(3729),i=r.t(o,2),s=r(5094),l=r(47674),c=r(98462),d=r(2256),u=r(16069),f=r(62409),m=r(8145);function p(){return()=>{}}var h="Avatar",[x,g]=(0,c.b)(h),[v,y]=x(h),b=o.forwardRef((e,t)=>{let{__scopeAvatar:r,...n}=e,[i,s]=o.useState("idle");return(0,a.jsx)(v,{scope:r,imageLoadingStatus:i,onImageLoadingStatusChange:s,children:(0,a.jsx)(f.WV.span,{...n,ref:t})})});b.displayName=h;var w="AvatarImage",j=o.forwardRef((e,t)=>{let{__scopeAvatar:r,src:n,onLoadingStatusChange:i=()=>{},...s}=e,l=y(w,r),c=function(e,{referrerPolicy:t,crossOrigin:r}){let n=(0,m.useSyncExternalStore)(p,()=>!0,()=>!1),a=o.useRef(null),i=n?(a.current||(a.current=new window.Image),a.current):null,[s,l]=o.useState(()=>k(i,e));return(0,u.b)(()=>{l(k(i,e))},[i,e]),(0,u.b)(()=>{let e=e=>()=>{l(e)};if(!i)return;let n=e("loaded"),a=e("error");return i.addEventListener("load",n),i.addEventListener("error",a),t&&(i.referrerPolicy=t),"string"==typeof r&&(i.crossOrigin=r),()=>{i.removeEventListener("load",n),i.removeEventListener("error",a)}},[i,r,t]),s}(n,s),h=(0,d.W)(e=>{i(e),l.onImageLoadingStatusChange(e)});return(0,u.b)(()=>{"idle"!==c&&h(c)},[c,h]),"loaded"===c?(0,a.jsx)(f.WV.img,{...s,ref:t,src:n}):null});j.displayName=w;var N="AvatarFallback",C=o.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:n,...i}=e,s=y(N,r),[l,c]=o.useState(void 0===n);return o.useEffect(()=>{if(void 0!==n){let e=window.setTimeout(()=>c(!0),n);return()=>window.clearTimeout(e)}},[n]),l&&"loaded"!==s.imageLoadingStatus?(0,a.jsx)(f.WV.span,{...i,ref:t}):null});function k(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}C.displayName=N;var E=r(11453);let R=o.forwardRef(({className:e,...t},r)=>a.jsx(b,{ref:r,className:(0,E.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...t}));R.displayName=b.displayName;let S=o.forwardRef(({className:e,...t},r)=>a.jsx(j,{ref:r,className:(0,E.cn)("aspect-square h-full w-full",e),...t}));S.displayName=j.displayName;let M=o.forwardRef(({className:e,...t},r)=>a.jsx(C,{ref:r,className:(0,E.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...t}));M.displayName=C.displayName;var T=r(85222),P=r(31405),A=r(33183),O=r(77411),L=r(3975),D=r(44155),I=0;function _(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var z="focusScope.autoFocusOnMount",Z="focusScope.autoFocusOnUnmount",W={bubbles:!1,cancelable:!0},F=o.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:i,onUnmountAutoFocus:s,...l}=e,[c,u]=o.useState(null),m=(0,d.W)(i),p=(0,d.W)(s),h=o.useRef(null),x=(0,P.e)(t,e=>u(e)),g=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(n){let e=function(e){if(g.paused||!c)return;let t=e.target;c.contains(t)?h.current=t:K(h.current,{select:!0})},t=function(e){if(g.paused||!c)return;let t=e.relatedTarget;null===t||c.contains(t)||K(h.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&K(c)});return c&&r.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,c,g.paused]),o.useEffect(()=>{if(c){H.add(g);let e=document.activeElement;if(!c.contains(e)){let t=new CustomEvent(z,W);c.addEventListener(z,m),c.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(K(n,{select:t}),document.activeElement!==r)return}(q(c).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&K(c))}return()=>{c.removeEventListener(z,m),setTimeout(()=>{let t=new CustomEvent(Z,W);c.addEventListener(Z,p),c.dispatchEvent(t),t.defaultPrevented||K(e??document.body,{select:!0}),c.removeEventListener(Z,p),H.remove(g)},0)}}},[c,m,p,g]);let v=o.useCallback(e=>{if(!r&&!n||g.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,a=document.activeElement;if(t&&a){let t=e.currentTarget,[n,o]=function(e){let t=q(e);return[V(t,e),V(t.reverse(),e)]}(t);n&&o?e.shiftKey||a!==o?e.shiftKey&&a===n&&(e.preventDefault(),r&&K(o,{select:!0})):(e.preventDefault(),r&&K(n,{select:!0})):a===t&&e.preventDefault()}},[r,n,g.paused]);return(0,a.jsx)(f.WV.div,{tabIndex:-1,...l,ref:x,onKeyDown:v})});function q(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function V(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function K(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}F.displayName="FocusScope";var H=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=B(e,t)).unshift(t)},remove(t){e=B(e,t),e[0]?.resume()}}}();function B(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}var U=i[" useId ".trim().toString()]||(()=>void 0),Y=0;function $(e){let[t,r]=o.useState(U());return(0,u.b)(()=>{e||r(e=>e??String(Y++))},[e]),e||(t?`radix-${t}`:"")}let X=["top","right","bottom","left"],G=Math.min,Q=Math.max,J=Math.round,ee=Math.floor,et=e=>({x:e,y:e}),er={left:"right",right:"left",bottom:"top",top:"bottom"},en={start:"end",end:"start"};function ea(e,t){return"function"==typeof e?e(t):e}function eo(e){return e.split("-")[0]}function ei(e){return e.split("-")[1]}function es(e){return"x"===e?"y":"x"}function el(e){return"y"===e?"height":"width"}function ec(e){return["top","bottom"].includes(eo(e))?"y":"x"}function ed(e){return e.replace(/start|end/g,e=>en[e])}function eu(e){return e.replace(/left|right|bottom|top/g,e=>er[e])}function ef(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function em(e){let{x:t,y:r,width:n,height:a}=e;return{width:n,height:a,top:r,left:t,right:t+n,bottom:r+a,x:t,y:r}}function ep(e,t,r){let n,{reference:a,floating:o}=e,i=ec(t),s=es(ec(t)),l=el(s),c=eo(t),d="y"===i,u=a.x+a.width/2-o.width/2,f=a.y+a.height/2-o.height/2,m=a[l]/2-o[l]/2;switch(c){case"top":n={x:u,y:a.y-o.height};break;case"bottom":n={x:u,y:a.y+a.height};break;case"right":n={x:a.x+a.width,y:f};break;case"left":n={x:a.x-o.width,y:f};break;default:n={x:a.x,y:a.y}}switch(ei(t)){case"start":n[s]-=m*(r&&d?-1:1);break;case"end":n[s]+=m*(r&&d?-1:1)}return n}let eh=async(e,t,r)=>{let{placement:n="bottom",strategy:a="absolute",middleware:o=[],platform:i}=r,s=o.filter(Boolean),l=await (null==i.isRTL?void 0:i.isRTL(t)),c=await i.getElementRects({reference:e,floating:t,strategy:a}),{x:d,y:u}=ep(c,n,l),f=n,m={},p=0;for(let r=0;r<s.length;r++){let{name:o,fn:h}=s[r],{x:x,y:g,data:v,reset:y}=await h({x:d,y:u,initialPlacement:n,placement:f,strategy:a,middlewareData:m,rects:c,platform:i,elements:{reference:e,floating:t}});d=null!=x?x:d,u=null!=g?g:u,m={...m,[o]:{...m[o],...v}},y&&p<=50&&(p++,"object"==typeof y&&(y.placement&&(f=y.placement),y.rects&&(c=!0===y.rects?await i.getElementRects({reference:e,floating:t,strategy:a}):y.rects),{x:d,y:u}=ep(c,f,l)),r=-1)}return{x:d,y:u,placement:f,strategy:a,middlewareData:m}};async function ex(e,t){var r;void 0===t&&(t={});let{x:n,y:a,platform:o,rects:i,elements:s,strategy:l}=e,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:u="floating",altBoundary:f=!1,padding:m=0}=ea(t,e),p=ef(m),h=s[f?"floating"===u?"reference":"floating":u],x=em(await o.getClippingRect({element:null==(r=await (null==o.isElement?void 0:o.isElement(h)))||r?h:h.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(s.floating)),boundary:c,rootBoundary:d,strategy:l})),g="floating"===u?{x:n,y:a,width:i.floating.width,height:i.floating.height}:i.reference,v=await (null==o.getOffsetParent?void 0:o.getOffsetParent(s.floating)),y=await (null==o.isElement?void 0:o.isElement(v))&&await (null==o.getScale?void 0:o.getScale(v))||{x:1,y:1},b=em(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:g,offsetParent:v,strategy:l}):g);return{top:(x.top-b.top+p.top)/y.y,bottom:(b.bottom-x.bottom+p.bottom)/y.y,left:(x.left-b.left+p.left)/y.x,right:(b.right-x.right+p.right)/y.x}}function eg(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ev(e){return X.some(t=>e[t]>=0)}async function ey(e,t){let{placement:r,platform:n,elements:a}=e,o=await (null==n.isRTL?void 0:n.isRTL(a.floating)),i=eo(r),s=ei(r),l="y"===ec(r),c=["left","top"].includes(i)?-1:1,d=o&&l?-1:1,u=ea(t,e),{mainAxis:f,crossAxis:m,alignmentAxis:p}="number"==typeof u?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return s&&"number"==typeof p&&(m="end"===s?-1*p:p),l?{x:m*d,y:f*c}:{x:f*c,y:m*d}}function eb(e){var t;return t=0,"#document"}function ew(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ej(e){var t,r;return null==(t=(r=0,e.document||window.document))?void 0:t.documentElement}function eN(e){let{overflow:t,overflowX:r,overflowY:n,display:a}=eR(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(a)}function eC(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function ek(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function eE(e){return["html","body","#document"].includes(eb(e))}function eR(e){return ew(e).getComputedStyle(e)}function eS(e){return{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eM(e){return"html"===eb(e)?e:e.assignedSlot||e.parentNode||ej(e)}function eT(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let a=function e(t){let r=eM(t);return eE(r)?t.ownerDocument?t.ownerDocument.body:t.body:e(r)}(e),o=a===(null==(n=e.ownerDocument)?void 0:n.body),i=ew(a);if(o){let e=eP(i);return t.concat(i,i.visualViewport||[],eN(a)?a:[],e&&r?eT(e):[])}return t.concat(a,eT(a,[],r))}function eP(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eA(e){return e.contextElement}function eO(e){return eA(e),et(1)}let eL=et(0);function eD(e){let t=ew(e);return ek()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eL}function eI(e,t,r,n){var a;void 0===t&&(t=!1),void 0===r&&(r=!1);let o=e.getBoundingClientRect(),i=eA(e),s=et(1);t&&(n||(s=eO(e)));let l=(void 0===(a=r)&&(a=!1),n&&(!a||n===ew(i))&&a)?eD(i):et(0),c=(o.left+l.x)/s.x,d=(o.top+l.y)/s.y,u=o.width/s.x,f=o.height/s.y;if(i){let e=ew(i),t=eP(e);for(;t&&n&&n!==e;){let r=eO(t),n=t.getBoundingClientRect(),a=eR(t),o=n.left+(t.clientLeft+parseFloat(a.paddingLeft))*r.x,i=n.top+(t.clientTop+parseFloat(a.paddingTop))*r.y;c*=r.x,d*=r.y,u*=r.x,f*=r.y,c+=o,d+=i,t=eP(e=ew(t))}}return em({width:u,height:f,x:c,y:d})}function e_(e,t){let r=eS(e).scrollLeft;return t?t.left+r:eI(ej(e)).left+r}function ez(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:e_(e,n)),y:n.top+t.scrollTop}}function eZ(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=ew(e),n=ej(e),a=r.visualViewport,o=n.clientWidth,i=n.clientHeight,s=0,l=0;if(a){o=a.width,i=a.height;let e=ek();(!e||e&&"fixed"===t)&&(s=a.offsetLeft,l=a.offsetTop)}return{width:o,height:i,x:s,y:l}}(e,r);else if("document"===t)n=function(e){let t=ej(e),r=eS(e),n=e.ownerDocument.body,a=Q(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),o=Q(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),i=-r.scrollLeft+e_(e),s=-r.scrollTop;return"rtl"===eR(n).direction&&(i+=Q(t.clientWidth,n.clientWidth)-a),{width:a,height:o,x:i,y:s}}(ej(e));else{let r=eD(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return em(n)}function eW(e,t){let r=ew(e);if(eC(e))return r;{let t=eM(e);for(;t&&!eE(t);)t=eM(t);return r}}let eF=async function(e){let t=this.getOffsetParent||eW,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=ej(t),a="fixed"===r,o=eI(e,!0,a,t),i={scrollLeft:0,scrollTop:0},s=et(0);if(!a){("body"!==eb(t)||eN(n))&&(i=eS(t));n&&(s.x=e_(n))}a&&n&&(s.x=e_(n));let l=!n||a?et(0):ez(n,i);return{x:o.left+i.scrollLeft-s.x-l.x,y:o.top+i.scrollTop-s.y-l.y,width:o.width,height:o.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},eq={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:a}=e,o="fixed"===a,i=ej(n),s=!!t&&eC(t.floating);if(n===i||s&&o)return r;let l={scrollLeft:0,scrollTop:0},c=et(1),d=et(0);o||("body"!==eb(n)||eN(i))&&(l=eS(n));let u=!i||o?et(0):ez(i,l,!0);return{width:r.width*c.x,height:r.height*c.y,x:r.x*c.x-l.scrollLeft*c.x+d.x+u.x,y:r.y*c.y-l.scrollTop*c.y+d.y+u.y}},getDocumentElement:ej,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:a}=e,o=[..."clippingAncestors"===r?eC(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=eT(e,[],!1).filter(e=>!1);return"fixed"===eR(e).position&&eM(e),t.set(e,n),n}(t,this._c):[].concat(r),n],i=o[0],s=o.reduce((e,r)=>{let n=eZ(t,r,a);return e.top=Q(n.top,e.top),e.right=G(n.right,e.right),e.bottom=G(n.bottom,e.bottom),e.left=Q(n.left,e.left),e},eZ(t,i,a));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:eW,getElementRects:eF,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=function(e){let t=eR(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,a=r,o=n,i=J(r)!==a||J(n)!==o;return i&&(r=a,n=o),{width:r,height:n,$:i}}(e);return{width:t,height:r}},getScale:eO,isElement:function(e){return!1},isRTL:function(e){return"rtl"===eR(e).direction}};function eV(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eK=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:a,rects:o,platform:i,elements:s,middlewareData:l}=t,{element:c,padding:d=0}=ea(e,t)||{};if(null==c)return{};let u=ef(d),f={x:r,y:n},m=es(ec(a)),p=el(m),h=await i.getDimensions(c),x="y"===m,g=x?"clientHeight":"clientWidth",v=o.reference[p]+o.reference[m]-f[m]-o.floating[p],y=f[m]-o.reference[m],b=await (null==i.getOffsetParent?void 0:i.getOffsetParent(c)),w=b?b[g]:0;w&&await (null==i.isElement?void 0:i.isElement(b))||(w=s.floating[g]||o.floating[p]);let j=w/2-h[p]/2-1,N=G(u[x?"top":"left"],j),C=G(u[x?"bottom":"right"],j),k=w-h[p]-C,E=w/2-h[p]/2+(v/2-y/2),R=Q(N,G(E,k)),S=!l.arrow&&null!=ei(a)&&E!==R&&o.reference[p]/2-(E<N?N:C)-h[p]/2<0,M=S?E<N?E-N:E-k:0;return{[m]:f[m]+M,data:{[m]:R,centerOffset:E-R-M,...S&&{alignmentOffset:M}},reset:S}}}),eH=(e,t,r)=>{let n=new Map,a={platform:eq,...r},o={...a.platform,_c:n};return eh(e,t,{...a,platform:o})};var eB=r(81202),eU="undefined"!=typeof document?o.useLayoutEffect:function(){};function eY(e,t){let r,n,a;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!eY(e[n],t[n]))return!1;return!0}if((r=(a=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,a[n]))return!1;for(n=r;0!=n--;){let r=a[n];if(("_owner"!==r||!e.$$typeof)&&!eY(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function e$(e,t){return Math.round(1*t)/1}function eX(e){let t=o.useRef(e);return eU(()=>{t.current=e}),t}let eG=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?eK({element:r.current,padding:n}).fn(t):{}:r?eK({element:r,padding:n}).fn(t):{}}}),eQ=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:a,y:o,placement:i,middlewareData:s}=t,l=await ey(t,e);return i===(null==(r=s.offset)?void 0:r.placement)&&null!=(n=s.arrow)&&n.alignmentOffset?{}:{x:a+l.x,y:o+l.y,data:{...l,placement:i}}}}}(e),options:[e,t]}),eJ=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:a}=t,{mainAxis:o=!0,crossAxis:i=!1,limiter:s={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...l}=ea(e,t),c={x:r,y:n},d=await ex(t,l),u=ec(eo(a)),f=es(u),m=c[f],p=c[u];if(o){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",r=m+d[e],n=m-d[t];m=Q(r,G(m,n))}if(i){let e="y"===u?"top":"left",t="y"===u?"bottom":"right",r=p+d[e],n=p-d[t];p=Q(r,G(p,n))}let h=s.fn({...t,[f]:m,[u]:p});return{...h,data:{x:h.x-r,y:h.y-n,enabled:{[f]:o,[u]:i}}}}}}(e),options:[e,t]}),e0=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:a,rects:o,middlewareData:i}=t,{offset:s=0,mainAxis:l=!0,crossAxis:c=!0}=ea(e,t),d={x:r,y:n},u=ec(a),f=es(u),m=d[f],p=d[u],h=ea(s,t),x="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(l){let e="y"===f?"height":"width",t=o.reference[f]-o.floating[e]+x.mainAxis,r=o.reference[f]+o.reference[e]-x.mainAxis;m<t?m=t:m>r&&(m=r)}if(c){var g,v;let e="y"===f?"width":"height",t=["top","left"].includes(eo(a)),r=o.reference[u]-o.floating[e]+(t&&(null==(g=i.offset)?void 0:g[u])||0)+(t?0:x.crossAxis),n=o.reference[u]+o.reference[e]+(t?0:(null==(v=i.offset)?void 0:v[u])||0)-(t?x.crossAxis:0);p<r?p=r:p>n&&(p=n)}return{[f]:m,[u]:p}}}}(e),options:[e,t]}),e1=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,a,o,i;let{placement:s,middlewareData:l,rects:c,initialPlacement:d,platform:u,elements:f}=t,{mainAxis:m=!0,crossAxis:p=!0,fallbackPlacements:h,fallbackStrategy:x="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:v=!0,...y}=ea(e,t);if(null!=(r=l.arrow)&&r.alignmentOffset)return{};let b=eo(s),w=ec(d),j=eo(d)===d,N=await (null==u.isRTL?void 0:u.isRTL(f.floating)),C=h||(j||!v?[eu(d)]:function(e){let t=eu(e);return[ed(e),t,ed(t)]}(d)),k="none"!==g;!h&&k&&C.push(...function(e,t,r,n){let a=ei(e),o=function(e,t,r){let n=["left","right"],a=["right","left"];switch(e){case"top":case"bottom":if(r)return t?a:n;return t?n:a;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(eo(e),"start"===r,n);return a&&(o=o.map(e=>e+"-"+a),t&&(o=o.concat(o.map(ed)))),o}(d,v,g,N));let E=[d,...C],R=await ex(t,y),S=[],M=(null==(n=l.flip)?void 0:n.overflows)||[];if(m&&S.push(R[b]),p){let e=function(e,t,r){void 0===r&&(r=!1);let n=ei(e),a=es(ec(e)),o=el(a),i="x"===a?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[o]>t.floating[o]&&(i=eu(i)),[i,eu(i)]}(s,c,N);S.push(R[e[0]],R[e[1]])}if(M=[...M,{placement:s,overflows:S}],!S.every(e=>e<=0)){let e=((null==(a=l.flip)?void 0:a.index)||0)+1,t=E[e];if(t&&(!("alignment"===p&&w!==ec(t))||M.every(e=>e.overflows[0]>0&&ec(e.placement)===w)))return{data:{index:e,overflows:M},reset:{placement:t}};let r=null==(o=M.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!r)switch(x){case"bestFit":{let e=null==(i=M.filter(e=>{if(k){let t=ec(e.placement);return t===w||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(r=e);break}case"initialPlacement":r=d}if(s!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),e2=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let a,o;let{placement:i,rects:s,platform:l,elements:c}=t,{apply:d=()=>{},...u}=ea(e,t),f=await ex(t,u),m=eo(i),p=ei(i),h="y"===ec(i),{width:x,height:g}=s.floating;"top"===m||"bottom"===m?(a=m,o=p===(await (null==l.isRTL?void 0:l.isRTL(c.floating))?"start":"end")?"left":"right"):(o=m,a="end"===p?"top":"bottom");let v=g-f.top-f.bottom,y=x-f.left-f.right,b=G(g-f[a],v),w=G(x-f[o],y),j=!t.middlewareData.shift,N=b,C=w;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(C=y),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(N=v),j&&!p){let e=Q(f.left,0),t=Q(f.right,0),r=Q(f.top,0),n=Q(f.bottom,0);h?C=x-2*(0!==e||0!==t?e+t:Q(f.left,f.right)):N=g-2*(0!==r||0!==n?r+n:Q(f.top,f.bottom))}await d({...t,availableWidth:C,availableHeight:N});let k=await l.getDimensions(c.floating);return x!==k.width||g!==k.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),e3=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...a}=ea(e,t);switch(n){case"referenceHidden":{let e=eg(await ex(t,{...a,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:ev(e)}}}case"escaped":{let e=eg(await ex(t,{...a,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:ev(e)}}}default:return{}}}}}(e),options:[e,t]}),e4=(e,t)=>({...eG(e),options:[e,t]});var e5=o.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,a.jsx)(f.WV.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,a.jsx)("polygon",{points:"0,0 30,0 15,10"})})});e5.displayName="Arrow";var e9="Popper",[e8,e7]=(0,c.b)(e9),[e6,te]=e8(e9),tt=e=>{let{__scopePopper:t,children:r}=e,[n,i]=o.useState(null);return(0,a.jsx)(e6,{scope:t,anchor:n,onAnchorChange:i,children:r})};tt.displayName=e9;var tr="PopperAnchor",tn=o.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:n,...i}=e,s=te(tr,r),l=o.useRef(null),c=(0,P.e)(t,l);return o.useEffect(()=>{s.onAnchorChange(n?.current||l.current)}),n?null:(0,a.jsx)(f.WV.div,{...i,ref:c})});tn.displayName=tr;var ta="PopperContent",[to,ti]=e8(ta),ts=o.forwardRef((e,t)=>{let{__scopePopper:r,side:n="bottom",sideOffset:i=0,align:s="center",alignOffset:l=0,arrowPadding:c=0,avoidCollisions:m=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:x="partial",hideWhenDetached:g=!1,updatePositionStrategy:v="optimized",onPlaced:y,...b}=e,w=te(ta,r),[j,N]=o.useState(null),C=(0,P.e)(t,e=>N(e)),[k,E]=o.useState(null),R=function(e){let[t,r]=o.useState(void 0);return(0,u.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,a;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,a=t.blockSize}else n=e.offsetWidth,a=e.offsetHeight;r({width:n,height:a})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(k),S=R?.width??0,M=R?.height??0,T="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},A=Array.isArray(p)?p:[p],O=A.length>0,L={padding:T,boundary:A.filter(tu),altBoundary:O},{refs:D,floatingStyles:I,placement:_,isPositioned:z,middlewareData:Z}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:n=[],platform:a,elements:{reference:i,floating:s}={},transform:l=!0,whileElementsMounted:c,open:d}=e,[u,f]=o.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[m,p]=o.useState(n);eY(m,n)||p(n);let[h,x]=o.useState(null),[g,v]=o.useState(null),y=o.useCallback(e=>{e!==N.current&&(N.current=e,x(e))},[]),b=o.useCallback(e=>{e!==C.current&&(C.current=e,v(e))},[]),w=i||h,j=s||g,N=o.useRef(null),C=o.useRef(null),k=o.useRef(u),E=null!=c,R=eX(c),S=eX(a),M=eX(d),T=o.useCallback(()=>{if(!N.current||!C.current)return;let e={placement:t,strategy:r,middleware:m};S.current&&(e.platform=S.current),eH(N.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==M.current};P.current&&!eY(k.current,t)&&(k.current=t,eB.flushSync(()=>{f(t)}))})},[m,t,r,S,M]);eU(()=>{!1===d&&k.current.isPositioned&&(k.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[d]);let P=o.useRef(!1);eU(()=>(P.current=!0,()=>{P.current=!1}),[]),eU(()=>{if(w&&(N.current=w),j&&(C.current=j),w&&j){if(R.current)return R.current(w,j,T);T()}},[w,j,T,R,E]);let A=o.useMemo(()=>({reference:N,floating:C,setReference:y,setFloating:b}),[y,b]),O=o.useMemo(()=>({reference:w,floating:j}),[w,j]),L=o.useMemo(()=>{let e={position:r,left:0,top:0};if(!O.floating)return e;let t=e$(O.floating,u.x),n=e$(O.floating,u.y);return l?{...e,transform:"translate("+t+"px, "+n+"px)",...(O.floating,!1)}:{position:r,left:t,top:n}},[r,l,O.floating,u.x,u.y]);return o.useMemo(()=>({...u,update:T,refs:A,elements:O,floatingStyles:L}),[u,T,A,O,L])}({strategy:"fixed",placement:n+("center"!==s?"-"+s:""),whileElementsMounted:(...e)=>(function(e,t,r,n){let a;void 0===n&&(n={});let{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:c=!1}=n,d=eA(e),u=o||i?[...d?eT(d):[],...eT(t)]:[];u.forEach(e=>{o&&e.addEventListener("scroll",r,{passive:!0}),i&&e.addEventListener("resize",r)});let f=d&&l?function(e,t){let r,n=null,a=ej(e);function o(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return function i(s,l){void 0===s&&(s=!1),void 0===l&&(l=1),o();let c=e.getBoundingClientRect(),{left:d,top:u,width:f,height:m}=c;if(s||t(),!f||!m)return;let p=ee(u),h=ee(a.clientWidth-(d+f)),x={rootMargin:-p+"px "+-h+"px "+-ee(a.clientHeight-(u+m))+"px "+-ee(d)+"px",threshold:Q(0,G(1,l))||1},g=!0;function v(t){let n=t[0].intersectionRatio;if(n!==l){if(!g)return i();n?i(!1,n):r=setTimeout(()=>{i(!1,1e-7)},1e3)}1!==n||eV(c,e.getBoundingClientRect())||i(),g=!1}try{n=new IntersectionObserver(v,{...x,root:a.ownerDocument})}catch(e){n=new IntersectionObserver(v,x)}n.observe(e)}(!0),o}(d,r):null,m=-1,p=null;s&&(p=new ResizeObserver(e=>{let[n]=e;n&&n.target===d&&p&&(p.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=p)||e.observe(t)})),r()}),d&&!c&&p.observe(d),p.observe(t));let h=c?eI(e):null;return c&&function t(){let n=eI(e);h&&!eV(h,n)&&r(),h=n,a=requestAnimationFrame(t)}(),r(),()=>{var e;u.forEach(e=>{o&&e.removeEventListener("scroll",r),i&&e.removeEventListener("resize",r)}),null==f||f(),null==(e=p)||e.disconnect(),p=null,c&&cancelAnimationFrame(a)}})(...e,{animationFrame:"always"===v}),elements:{reference:w.anchor},middleware:[eQ({mainAxis:i+M,alignmentAxis:l}),m&&eJ({mainAxis:!0,crossAxis:!1,limiter:"partial"===x?e0():void 0,...L}),m&&e1({...L}),e2({...L,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:a,height:o}=t.reference,i=e.floating.style;i.setProperty("--radix-popper-available-width",`${r}px`),i.setProperty("--radix-popper-available-height",`${n}px`),i.setProperty("--radix-popper-anchor-width",`${a}px`),i.setProperty("--radix-popper-anchor-height",`${o}px`)}}),k&&e4({element:k,padding:c}),tf({arrowWidth:S,arrowHeight:M}),g&&e3({strategy:"referenceHidden",...L})]}),[W,F]=tm(_),q=(0,d.W)(y);(0,u.b)(()=>{z&&q?.()},[z,q]);let V=Z.arrow?.x,K=Z.arrow?.y,H=Z.arrow?.centerOffset!==0,[B,U]=o.useState();return(0,u.b)(()=>{j&&U(window.getComputedStyle(j).zIndex)},[j]),(0,a.jsx)("div",{ref:D.setFloating,"data-radix-popper-content-wrapper":"",style:{...I,transform:z?I.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:B,"--radix-popper-transform-origin":[Z.transformOrigin?.x,Z.transformOrigin?.y].join(" "),...Z.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,a.jsx)(to,{scope:r,placedSide:W,onArrowChange:E,arrowX:V,arrowY:K,shouldHideArrow:H,children:(0,a.jsx)(f.WV.div,{"data-side":W,"data-align":F,...b,ref:C,style:{...b.style,animation:z?void 0:"none"}})})})});ts.displayName=ta;var tl="PopperArrow",tc={top:"bottom",right:"left",bottom:"top",left:"right"},td=o.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=ti(tl,r),i=tc[o.placedSide];return(0,a.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,a.jsx)(e5,{...n,ref:t,style:{...n.style,display:"block"}})})});function tu(e){return null!==e}td.displayName=tl;var tf=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:a}=t,o=a.arrow?.centerOffset!==0,i=o?0:e.arrowWidth,s=o?0:e.arrowHeight,[l,c]=tm(r),d={start:"0%",center:"50%",end:"100%"}[c],u=(a.arrow?.x??0)+i/2,f=(a.arrow?.y??0)+s/2,m="",p="";return"bottom"===l?(m=o?d:`${u}px`,p=`${-s}px`):"top"===l?(m=o?d:`${u}px`,p=`${n.floating.height+s}px`):"right"===l?(m=`${-s}px`,p=o?d:`${f}px`):"left"===l&&(m=`${n.floating.width+s}px`,p=o?d:`${f}px`),{data:{x:m,y:p}}}});function tm(e){let[t,r="center"]=e.split("-");return[t,r]}var tp=r(31179),th=r(43234),tx="rovingFocusGroup.onEntryFocus",tg={bubbles:!1,cancelable:!0},tv="RovingFocusGroup",[ty,tb,tw]=(0,O.B)(tv),[tj,tN]=(0,c.b)(tv,[tw]),[tC,tk]=tj(tv),tE=o.forwardRef((e,t)=>(0,a.jsx)(ty.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,a.jsx)(ty.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,a.jsx)(tR,{...e,ref:t})})}));tE.displayName=tv;var tR=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:n,loop:i=!1,dir:s,currentTabStopId:l,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:u,onEntryFocus:m,preventScrollOnEntryFocus:p=!1,...h}=e,x=o.useRef(null),g=(0,P.e)(t,x),v=(0,L.gm)(s),[y,b]=(0,A.T)({prop:l,defaultProp:c??null,onChange:u,caller:tv}),[w,j]=o.useState(!1),N=(0,d.W)(m),C=tb(r),k=o.useRef(!1),[E,R]=o.useState(0);return o.useEffect(()=>{let e=x.current;if(e)return e.addEventListener(tx,N),()=>e.removeEventListener(tx,N)},[N]),(0,a.jsx)(tC,{scope:r,orientation:n,dir:v,loop:i,currentTabStopId:y,onItemFocus:o.useCallback(e=>b(e),[b]),onItemShiftTab:o.useCallback(()=>j(!0),[]),onFocusableItemAdd:o.useCallback(()=>R(e=>e+1),[]),onFocusableItemRemove:o.useCallback(()=>R(e=>e-1),[]),children:(0,a.jsx)(f.WV.div,{tabIndex:w||0===E?-1:0,"data-orientation":n,...h,ref:g,style:{outline:"none",...e.style},onMouseDown:(0,T.M)(e.onMouseDown,()=>{k.current=!0}),onFocus:(0,T.M)(e.onFocus,e=>{let t=!k.current;if(e.target===e.currentTarget&&t&&!w){let t=new CustomEvent(tx,tg);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=C().filter(e=>e.focusable);tP([e.find(e=>e.active),e.find(e=>e.id===y),...e].filter(Boolean).map(e=>e.ref.current),p)}}k.current=!1}),onBlur:(0,T.M)(e.onBlur,()=>j(!1))})})}),tS="RovingFocusGroupItem",tM=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:n=!0,active:i=!1,tabStopId:s,children:l,...c}=e,d=$(),u=s||d,m=tk(tS,r),p=m.currentTabStopId===u,h=tb(r),{onFocusableItemAdd:x,onFocusableItemRemove:g,currentTabStopId:v}=m;return o.useEffect(()=>{if(n)return x(),()=>g()},[n,x,g]),(0,a.jsx)(ty.ItemSlot,{scope:r,id:u,focusable:n,active:i,children:(0,a.jsx)(f.WV.span,{tabIndex:p?0:-1,"data-orientation":m.orientation,...c,ref:t,onMouseDown:(0,T.M)(e.onMouseDown,e=>{n?m.onItemFocus(u):e.preventDefault()}),onFocus:(0,T.M)(e.onFocus,()=>m.onItemFocus(u)),onKeyDown:(0,T.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){m.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let a=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(a))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(a)))return tT[a]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=m.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>tP(r))}}),children:"function"==typeof l?l({isCurrentTabStop:p,hasTabStop:null!=v}):l})})});tM.displayName=tS;var tT={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function tP(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var tA=r(32751),tO=new WeakMap,tL=new WeakMap,tD={},tI=0,t_=function(e){return e&&(e.host||t_(e.parentNode))},tz=function(e,t,r,n){var a=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=t_(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tD[r]||(tD[r]=new WeakMap);var o=tD[r],i=[],s=new Set,l=new Set(a),c=function(e){!e||s.has(e)||(s.add(e),c(e.parentNode))};a.forEach(c);var d=function(e){!e||l.has(e)||Array.prototype.forEach.call(e.children,function(e){if(s.has(e))d(e);else try{var t=e.getAttribute(n),a=null!==t&&"false"!==t,l=(tO.get(e)||0)+1,c=(o.get(e)||0)+1;tO.set(e,l),o.set(e,c),i.push(e),1===l&&a&&tL.set(e,!0),1===c&&e.setAttribute(r,"true"),a||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return d(t),s.clear(),tI++,function(){i.forEach(function(e){var t=tO.get(e)-1,a=o.get(e)-1;tO.set(e,t),o.set(e,a),t||(tL.has(e)||e.removeAttribute(n),tL.delete(e)),a||e.removeAttribute(r)}),--tI||(tO=new WeakMap,tO=new WeakMap,tL=new WeakMap,tD={})}},tZ=function(e,t,r){void 0===r&&(r="data-aria-hidden");var n,a=Array.from(Array.isArray(e)?e:[e]),o=t||(n=e,"undefined"==typeof document?null:(Array.isArray(n)?n[0]:n).ownerDocument.body);return o?(a.push.apply(a,Array.from(o.querySelectorAll("[aria-live], script"))),tz(a,o,r,"aria-hidden")):function(){return null}},tW=function(){return(tW=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var a in t=arguments[r])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e}).apply(this,arguments)};function tF(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r}Object.create,Object.create;var tq=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),tV="width-before-scroll-bar";function tK(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tH=o.useEffect,tB=new WeakMap;function tU(e){return e}var tY=function(e){void 0===e&&(e={});var t,r,n,a=(void 0===t&&(t=tU),r=[],n=!1,{read:function(){if(n)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var a=t(e,n);return r.push(a),function(){r=r.filter(function(e){return e!==a})}},assignSyncMedium:function(e){for(n=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){n=!0;var t=[];if(r.length){var a=r;r=[],a.forEach(e),t=r}var o=function(){var r=t;t=[],r.forEach(e)},i=function(){return Promise.resolve().then(o)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return a.options=tW({async:!0,ssr:!1},e),a}(),t$=function(){},tX=o.forwardRef(function(e,t){var r,n,a,i,s=o.useRef(null),l=o.useState({onScrollCapture:t$,onWheelCapture:t$,onTouchMoveCapture:t$}),c=l[0],d=l[1],u=e.forwardProps,f=e.children,m=e.className,p=e.removeScrollBar,h=e.enabled,x=e.shards,g=e.sideCar,v=e.noRelative,y=e.noIsolation,b=e.inert,w=e.allowPinchZoom,j=e.as,N=e.gapMode,C=tF(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),k=(r=[s,t],n=function(e){return r.forEach(function(t){return tK(t,e)})},(a=(0,o.useState)(function(){return{value:null,callback:n,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=n,i=a.facade,tH(function(){var e=tB.get(i);if(e){var t=new Set(e),n=new Set(r),a=i.current;t.forEach(function(e){n.has(e)||tK(e,null)}),n.forEach(function(e){t.has(e)||tK(e,a)})}tB.set(i,r)},[r]),i),E=tW(tW({},C),c);return o.createElement(o.Fragment,null,h&&o.createElement(g,{sideCar:tY,removeScrollBar:p,shards:x,noRelative:v,noIsolation:y,inert:b,setCallbacks:d,allowPinchZoom:!!w,lockRef:s,gapMode:N}),u?o.cloneElement(o.Children.only(f),tW(tW({},E),{ref:k})):o.createElement(void 0===j?"div":j,tW({},E,{className:m,ref:k}),f))});tX.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},tX.classNames={fullWidth:tV,zeroRight:tq};var tG=function(e){var t=e.sideCar,r=tF(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return o.createElement(n,tW({},r))};tG.isSideCarExport=!0;var tQ=function(){var e=0,t=null;return{add:function(a){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=n||r.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=a:o.appendChild(document.createTextNode(a)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},tJ=function(){var e=tQ();return function(t,r){o.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},t0=function(){var e=tJ();return function(t){return e(t.styles,t.dynamic),null}},t1={left:0,top:0,right:0,gap:0},t2=t0(),t3="data-scroll-locked",t4=function(e,t,r,n){var a=e.left,o=e.top,i=e.right,s=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(s,"px ").concat(n,";\n  }\n  body[").concat(t3,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(a,"px;\n    padding-top: ").concat(o,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(s,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(s,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tq," {\n    right: ").concat(s,"px ").concat(n,";\n  }\n  \n  .").concat(tV," {\n    margin-right: ").concat(s,"px ").concat(n,";\n  }\n  \n  .").concat(tq," .").concat(tq," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(tV," .").concat(tV," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(t3,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(s,"px;\n  }\n")},t5=function(){var e=parseInt(document.body.getAttribute(t3)||"0",10);return isFinite(e)?e:0},t9=function(){o.useEffect(function(){return document.body.setAttribute(t3,(t5()+1).toString()),function(){var e=t5()-1;e<=0?document.body.removeAttribute(t3):document.body.setAttribute(t3,e.toString())}},[])},t8=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,a=void 0===n?"margin":n;t9();var i=o.useMemo(function(){return t1},[a]);return o.createElement(t2,{styles:t4(i,!t,a,r?"":"!important")})},t7=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&!(r.overflowY===r.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===r[t])},t6=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),re(e,n)){var a=rt(e,n);if(a[1]>a[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},re=function(e,t){return"v"===e?t7(t,"overflowY"):t7(t,"overflowX")},rt=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},rr=function(e,t,r,n,a){var o,i=(o=window.getComputedStyle(t).direction,"h"===e&&"rtl"===o?-1:1),s=i*n,l=r.target,c=t.contains(l),d=!1,u=s>0,f=0,m=0;do{if(!l)break;var p=rt(e,l),h=p[0],x=p[1]-p[2]-i*h;(h||x)&&re(e,l)&&(f+=x,m+=h);var g=l.parentNode;l=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&l!==document.body||c&&(t.contains(l)||t===l));return u&&(a&&1>Math.abs(f)||!a&&s>f)?d=!0:!u&&(a&&1>Math.abs(m)||!a&&-s>m)&&(d=!0),d},rn=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ra=function(e){return[e.deltaX,e.deltaY]},ro=function(e){return e&&"current"in e?e.current:e},ri=0,rs=[];let rl=(tY.useMedium(function(e){var t=o.useRef([]),r=o.useRef([0,0]),n=o.useRef(),a=o.useState(ri++)[0],i=o.useState(t0)[0],s=o.useRef(e);o.useEffect(function(){s.current=e},[e]),o.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,a=0,o=t.length;a<o;a++)!n&&a in t||(n||(n=Array.prototype.slice.call(t,0,a)),n[a]=t[a]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(ro),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(a))})}}},[e.inert,e.lockRef.current,e.shards]);var l=o.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!s.current.allowPinchZoom;var a,o=rn(e),i=r.current,l="deltaX"in e?e.deltaX:i[0]-o[0],c="deltaY"in e?e.deltaY:i[1]-o[1],d=e.target,u=Math.abs(l)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===u&&"range"===d.type)return!1;var f=t6(u,d);if(!f)return!0;if(f?a=u:(a="v"===u?"h":"v",f=t6(u,d)),!f)return!1;if(!n.current&&"changedTouches"in e&&(l||c)&&(n.current=a),!a)return!0;var m=n.current||a;return rr(m,t,e,"h"===m?l:c,!0)},[]),c=o.useCallback(function(e){if(rs.length&&rs[rs.length-1]===i){var r="deltaY"in e?ra(e):rn(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta)[0]===r[0]&&n[1]===r[1]})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var a=(s.current.shards||[]).map(ro).filter(Boolean).filter(function(t){return t.contains(e.target)});(a.length>0?l(e,a[0]):!s.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=o.useCallback(function(e,r,n,a){var o={name:e,delta:r,target:n,should:a,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(o),setTimeout(function(){t.current=t.current.filter(function(e){return e!==o})},1)},[]),u=o.useCallback(function(e){r.current=rn(e),n.current=void 0},[]),f=o.useCallback(function(t){d(t.type,ra(t),t.target,l(t,e.lockRef.current))},[]),m=o.useCallback(function(t){d(t.type,rn(t),t.target,l(t,e.lockRef.current))},[]);o.useEffect(function(){return rs.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:m}),document.addEventListener("wheel",c,!1),document.addEventListener("touchmove",c,!1),document.addEventListener("touchstart",u,!1),function(){rs=rs.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,!1),document.removeEventListener("touchmove",c,!1),document.removeEventListener("touchstart",u,!1)}},[]);var p=e.removeScrollBar,h=e.inert;return o.createElement(o.Fragment,null,h?o.createElement(i,{styles:"\n  .block-interactivity-".concat(a," {pointer-events: none;}\n  .allow-interactivity-").concat(a," {pointer-events: all;}\n")}):null,p?o.createElement(t8,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),tG);var rc=o.forwardRef(function(e,t){return o.createElement(tX,tW({},e,{ref:t,sideCar:rl}))});rc.classNames=tX.classNames;var rd=["Enter"," "],ru=["ArrowUp","PageDown","End"],rf=["ArrowDown","PageUp","Home",...ru],rm={ltr:[...rd,"ArrowRight"],rtl:[...rd,"ArrowLeft"]},rp={ltr:["ArrowLeft"],rtl:["ArrowRight"]},rh="Menu",[rx,rg,rv]=(0,O.B)(rh),[ry,rb]=(0,c.b)(rh,[rv,e7,tN]),rw=e7(),rj=tN(),[rN,rC]=ry(rh),[rk,rE]=ry(rh),rR=e=>{let{__scopeMenu:t,open:r=!1,children:n,dir:i,onOpenChange:s,modal:l=!0}=e,c=rw(t),[u,f]=o.useState(null),m=o.useRef(!1),p=(0,d.W)(s),h=(0,L.gm)(i);return o.useEffect(()=>{let e=()=>{m.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>m.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,a.jsx)(tt,{...c,children:(0,a.jsx)(rN,{scope:t,open:r,onOpenChange:p,content:u,onContentChange:f,children:(0,a.jsx)(rk,{scope:t,onClose:o.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:m,dir:h,modal:l,children:n})})})};rR.displayName=rh;var rS=o.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=rw(r);return(0,a.jsx)(tn,{...o,...n,ref:t})});rS.displayName="MenuAnchor";var rM="MenuPortal",[rT,rP]=ry(rM,{forceMount:void 0}),rA=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,i=rC(rM,t);return(0,a.jsx)(rT,{scope:t,forceMount:r,children:(0,a.jsx)(th.z,{present:r||i.open,children:(0,a.jsx)(tp.h,{asChild:!0,container:o,children:n})})})};rA.displayName=rM;var rO="MenuContent",[rL,rD]=ry(rO),rI=o.forwardRef((e,t)=>{let r=rP(rO,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,i=rC(rO,e.__scopeMenu),s=rE(rO,e.__scopeMenu);return(0,a.jsx)(rx.Provider,{scope:e.__scopeMenu,children:(0,a.jsx)(th.z,{present:n||i.open,children:(0,a.jsx)(rx.Slot,{scope:e.__scopeMenu,children:s.modal?(0,a.jsx)(r_,{...o,ref:t}):(0,a.jsx)(rz,{...o,ref:t})})})})}),r_=o.forwardRef((e,t)=>{let r=rC(rO,e.__scopeMenu),n=o.useRef(null),i=(0,P.e)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return tZ(e)},[]),(0,a.jsx)(rW,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,T.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),rz=o.forwardRef((e,t)=>{let r=rC(rO,e.__scopeMenu);return(0,a.jsx)(rW,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),rZ=(0,tA.Z8)("MenuContent.ScrollLock"),rW=o.forwardRef((e,t)=>{let{__scopeMenu:r,loop:n=!1,trapFocus:i,onOpenAutoFocus:s,onCloseAutoFocus:l,disableOutsidePointerEvents:c,onEntryFocus:d,onEscapeKeyDown:u,onPointerDownOutside:f,onFocusOutside:m,onInteractOutside:p,onDismiss:h,disableOutsideScroll:x,...g}=e,v=rC(rO,r),y=rE(rO,r),b=rw(r),w=rj(r),j=rg(r),[N,C]=o.useState(null),k=o.useRef(null),E=(0,P.e)(t,k,v.onContentChange),R=o.useRef(0),S=o.useRef(""),M=o.useRef(0),A=o.useRef(null),O=o.useRef("right"),L=o.useRef(0),z=x?rc:o.Fragment,Z=e=>{let t=S.current+e,r=j().filter(e=>!e.disabled),n=document.activeElement,a=r.find(e=>e.ref.current===n)?.textValue,o=function(e,t,r){var n;let a=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===a.length&&(o=o.filter(e=>e!==r));let i=o.find(e=>e.toLowerCase().startsWith(a.toLowerCase()));return i!==r?i:void 0}(r.map(e=>e.textValue),t,a),i=r.find(e=>e.textValue===o)?.ref.current;(function e(t){S.current=t,window.clearTimeout(R.current),""!==t&&(R.current=window.setTimeout(()=>e(""),1e3))})(t),i&&setTimeout(()=>i.focus())};o.useEffect(()=>()=>window.clearTimeout(R.current),[]),o.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??_()),document.body.insertAdjacentElement("beforeend",e[1]??_()),I++,()=>{1===I&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),I--}},[]);let W=o.useCallback(e=>O.current===A.current?.side&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,a=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let i=t[e],s=t[o],l=i.x,c=i.y,d=s.x,u=s.y;c>n!=u>n&&r<(d-l)*(n-c)/(u-c)+l&&(a=!a)}return a}({x:e.clientX,y:e.clientY},t)}(e,A.current?.area),[]);return(0,a.jsx)(rL,{scope:r,searchRef:S,onItemEnter:o.useCallback(e=>{W(e)&&e.preventDefault()},[W]),onItemLeave:o.useCallback(e=>{W(e)||(k.current?.focus(),C(null))},[W]),onTriggerLeave:o.useCallback(e=>{W(e)&&e.preventDefault()},[W]),pointerGraceTimerRef:M,onPointerGraceIntentChange:o.useCallback(e=>{A.current=e},[]),children:(0,a.jsx)(z,{...x?{as:rZ,allowPinchZoom:!0}:void 0,children:(0,a.jsx)(F,{asChild:!0,trapped:i,onMountAutoFocus:(0,T.M)(s,e=>{e.preventDefault(),k.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:l,children:(0,a.jsx)(D.XB,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:u,onPointerDownOutside:f,onFocusOutside:m,onInteractOutside:p,onDismiss:h,children:(0,a.jsx)(tE,{asChild:!0,...w,dir:y.dir,orientation:"vertical",loop:n,currentTabStopId:N,onCurrentTabStopIdChange:C,onEntryFocus:(0,T.M)(d,e=>{y.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,a.jsx)(ts,{role:"menu","aria-orientation":"vertical","data-state":nr(v.open),"data-radix-menu-content":"",dir:y.dir,...b,...g,ref:E,style:{outline:"none",...g.style},onKeyDown:(0,T.M)(g.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&Z(e.key));let a=k.current;if(e.target!==a||!rf.includes(e.key))return;e.preventDefault();let o=j().filter(e=>!e.disabled).map(e=>e.ref.current);ru.includes(e.key)&&o.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(o)}),onBlur:(0,T.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(R.current),S.current="")}),onPointerMove:(0,T.M)(e.onPointerMove,no(e=>{let t=e.target,r=L.current!==e.clientX;if(e.currentTarget.contains(t)&&r){let t=e.clientX>L.current?"right":"left";O.current=t,L.current=e.clientX}}))})})})})})})});rI.displayName=rO;var rF=o.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,a.jsx)(f.WV.div,{role:"group",...n,ref:t})});rF.displayName="MenuGroup";var rq=o.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,a.jsx)(f.WV.div,{...n,ref:t})});rq.displayName="MenuLabel";var rV="MenuItem",rK="menu.itemSelect",rH=o.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:n,...i}=e,s=o.useRef(null),l=rE(rV,e.__scopeMenu),c=rD(rV,e.__scopeMenu),d=(0,P.e)(t,s),u=o.useRef(!1);return(0,a.jsx)(rB,{...i,ref:d,disabled:r,onClick:(0,T.M)(e.onClick,()=>{let e=s.current;if(!r&&e){let t=new CustomEvent(rK,{bubbles:!0,cancelable:!0});e.addEventListener(rK,e=>n?.(e),{once:!0}),(0,f.jH)(e,t),t.defaultPrevented?u.current=!1:l.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),u.current=!0},onPointerUp:(0,T.M)(e.onPointerUp,e=>{u.current||e.currentTarget?.click()}),onKeyDown:(0,T.M)(e.onKeyDown,e=>{let t=""!==c.searchRef.current;!r&&(!t||" "!==e.key)&&rd.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});rH.displayName=rV;var rB=o.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:n=!1,textValue:i,...s}=e,l=rD(rV,r),c=rj(r),d=o.useRef(null),u=(0,P.e)(t,d),[m,p]=o.useState(!1),[h,x]=o.useState("");return o.useEffect(()=>{let e=d.current;e&&x((e.textContent??"").trim())},[s.children]),(0,a.jsx)(rx.ItemSlot,{scope:r,disabled:n,textValue:i??h,children:(0,a.jsx)(tM,{asChild:!0,...c,focusable:!n,children:(0,a.jsx)(f.WV.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":n||void 0,"data-disabled":n?"":void 0,...s,ref:u,onPointerMove:(0,T.M)(e.onPointerMove,no(e=>{n?l.onItemLeave(e):(l.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,T.M)(e.onPointerLeave,no(e=>l.onItemLeave(e))),onFocus:(0,T.M)(e.onFocus,()=>p(!0)),onBlur:(0,T.M)(e.onBlur,()=>p(!1))})})})}),rU=o.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...o}=e;return(0,a.jsx)(r1,{scope:e.__scopeMenu,checked:r,children:(0,a.jsx)(rH,{role:"menuitemcheckbox","aria-checked":nn(r)?"mixed":r,...o,ref:t,"data-state":na(r),onSelect:(0,T.M)(o.onSelect,()=>n?.(!!nn(r)||!r),{checkForDefaultPrevented:!1})})})});rU.displayName="MenuCheckboxItem";var rY="MenuRadioGroup",[r$,rX]=ry(rY,{value:void 0,onValueChange:()=>{}}),rG=o.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,i=(0,d.W)(n);return(0,a.jsx)(r$,{scope:e.__scopeMenu,value:r,onValueChange:i,children:(0,a.jsx)(rF,{...o,ref:t})})});rG.displayName=rY;var rQ="MenuRadioItem",rJ=o.forwardRef((e,t)=>{let{value:r,...n}=e,o=rX(rQ,e.__scopeMenu),i=r===o.value;return(0,a.jsx)(r1,{scope:e.__scopeMenu,checked:i,children:(0,a.jsx)(rH,{role:"menuitemradio","aria-checked":i,...n,ref:t,"data-state":na(i),onSelect:(0,T.M)(n.onSelect,()=>o.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});rJ.displayName=rQ;var r0="MenuItemIndicator",[r1,r2]=ry(r0,{checked:!1}),r3=o.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,i=r2(r0,r);return(0,a.jsx)(th.z,{present:n||nn(i.checked)||!0===i.checked,children:(0,a.jsx)(f.WV.span,{...o,ref:t,"data-state":na(i.checked)})})});r3.displayName=r0;var r4=o.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,a.jsx)(f.WV.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});r4.displayName="MenuSeparator";var r5=o.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=rw(r);return(0,a.jsx)(td,{...o,...n,ref:t})});r5.displayName="MenuArrow";var[r9,r8]=ry("MenuSub"),r7="MenuSubTrigger",r6=o.forwardRef((e,t)=>{let r=rC(r7,e.__scopeMenu),n=rE(r7,e.__scopeMenu),i=r8(r7,e.__scopeMenu),s=rD(r7,e.__scopeMenu),l=o.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=s,u={__scopeMenu:e.__scopeMenu},f=o.useCallback(()=>{l.current&&window.clearTimeout(l.current),l.current=null},[]);return o.useEffect(()=>f,[f]),o.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,a.jsx)(rS,{asChild:!0,...u,children:(0,a.jsx)(rB,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":i.contentId,"data-state":nr(r.open),...e,ref:(0,P.F)(t,i.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,T.M)(e.onPointerMove,no(t=>{s.onItemEnter(t),t.defaultPrevented||e.disabled||r.open||l.current||(s.onPointerGraceIntentChange(null),l.current=window.setTimeout(()=>{r.onOpenChange(!0),f()},100))})),onPointerLeave:(0,T.M)(e.onPointerLeave,no(e=>{f();let t=r.content?.getBoundingClientRect();if(t){let n=r.content?.dataset.side,a="right"===n,o=t[a?"left":"right"],i=t[a?"right":"left"];s.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:o,y:t.top},{x:i,y:t.top},{x:i,y:t.bottom},{x:o,y:t.bottom}],side:n}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(e),e.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:(0,T.M)(e.onKeyDown,t=>{let a=""!==s.searchRef.current;!e.disabled&&(!a||" "!==t.key)&&rm[n.dir].includes(t.key)&&(r.onOpenChange(!0),r.content?.focus(),t.preventDefault())})})})});r6.displayName=r7;var ne="MenuSubContent",nt=o.forwardRef((e,t)=>{let r=rP(rO,e.__scopeMenu),{forceMount:n=r.forceMount,...i}=e,s=rC(rO,e.__scopeMenu),l=rE(rO,e.__scopeMenu),c=r8(ne,e.__scopeMenu),d=o.useRef(null),u=(0,P.e)(t,d);return(0,a.jsx)(rx.Provider,{scope:e.__scopeMenu,children:(0,a.jsx)(th.z,{present:n||s.open,children:(0,a.jsx)(rx.Slot,{scope:e.__scopeMenu,children:(0,a.jsx)(rW,{id:c.contentId,"aria-labelledby":c.triggerId,...i,ref:u,align:"start",side:"rtl"===l.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{l.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,T.M)(e.onFocusOutside,e=>{e.target!==c.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:(0,T.M)(e.onEscapeKeyDown,e=>{l.onClose(),e.preventDefault()}),onKeyDown:(0,T.M)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=rp[l.dir].includes(e.key);t&&r&&(s.onOpenChange(!1),c.trigger?.focus(),e.preventDefault())})})})})})});function nr(e){return e?"open":"closed"}function nn(e){return"indeterminate"===e}function na(e){return nn(e)?"indeterminate":e?"checked":"unchecked"}function no(e){return t=>"mouse"===t.pointerType?e(t):void 0}nt.displayName=ne;var ni="DropdownMenu",[ns,nl]=(0,c.b)(ni,[rb]),nc=rb(),[nd,nu]=ns(ni),nf=e=>{let{__scopeDropdownMenu:t,children:r,dir:n,open:i,defaultOpen:s,onOpenChange:l,modal:c=!0}=e,d=nc(t),u=o.useRef(null),[f,m]=(0,A.T)({prop:i,defaultProp:s??!1,onChange:l,caller:ni});return(0,a.jsx)(nd,{scope:t,triggerId:$(),triggerRef:u,contentId:$(),open:f,onOpenChange:m,onOpenToggle:o.useCallback(()=>m(e=>!e),[m]),modal:c,children:(0,a.jsx)(rR,{...d,open:f,onOpenChange:m,dir:n,modal:c,children:r})})};nf.displayName=ni;var nm="DropdownMenuTrigger",np=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...o}=e,i=nu(nm,r),s=nc(r);return(0,a.jsx)(rS,{asChild:!0,...s,children:(0,a.jsx)(f.WV.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...o,ref:(0,P.F)(t,i.triggerRef),onPointerDown:(0,T.M)(e.onPointerDown,e=>{n||0!==e.button||!1!==e.ctrlKey||(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,T.M)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});np.displayName=nm;var nh=e=>{let{__scopeDropdownMenu:t,...r}=e,n=nc(t);return(0,a.jsx)(rA,{...n,...r})};nh.displayName="DropdownMenuPortal";var nx="DropdownMenuContent",ng=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=nu(nx,r),s=nc(r),l=o.useRef(!1);return(0,a.jsx)(rI,{id:i.contentId,"aria-labelledby":i.triggerId,...s,...n,ref:t,onCloseAutoFocus:(0,T.M)(e.onCloseAutoFocus,e=>{l.current||i.triggerRef.current?.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,T.M)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!i.modal||n)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});ng.displayName=nx,o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nc(r);return(0,a.jsx)(rF,{...o,...n,ref:t})}).displayName="DropdownMenuGroup";var nv=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nc(r);return(0,a.jsx)(rq,{...o,...n,ref:t})});nv.displayName="DropdownMenuLabel";var ny=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nc(r);return(0,a.jsx)(rH,{...o,...n,ref:t})});ny.displayName="DropdownMenuItem";var nb=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nc(r);return(0,a.jsx)(rU,{...o,...n,ref:t})});nb.displayName="DropdownMenuCheckboxItem",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nc(r);return(0,a.jsx)(rG,{...o,...n,ref:t})}).displayName="DropdownMenuRadioGroup";var nw=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nc(r);return(0,a.jsx)(rJ,{...o,...n,ref:t})});nw.displayName="DropdownMenuRadioItem";var nj=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nc(r);return(0,a.jsx)(r3,{...o,...n,ref:t})});nj.displayName="DropdownMenuItemIndicator";var nN=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nc(r);return(0,a.jsx)(r4,{...o,...n,ref:t})});nN.displayName="DropdownMenuSeparator",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nc(r);return(0,a.jsx)(r5,{...o,...n,ref:t})}).displayName="DropdownMenuArrow";var nC=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nc(r);return(0,a.jsx)(r6,{...o,...n,ref:t})});nC.displayName="DropdownMenuSubTrigger";var nk=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nc(r);return(0,a.jsx)(nt,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});nk.displayName="DropdownMenuSubContent";var nE=r(97751),nR=r(62312),nS=r(69224);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let nM=(0,nS.Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);o.forwardRef(({className:e,inset:t,children:r,...n},o)=>(0,a.jsxs)(nC,{ref:o,className:(0,E.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...n,children:[r,a.jsx(nE.Z,{className:"ml-auto h-4 w-4"})]})).displayName=nC.displayName,o.forwardRef(({className:e,...t},r)=>a.jsx(nk,{ref:r,className:(0,E.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})).displayName=nk.displayName;let nT=o.forwardRef(({className:e,sideOffset:t=4,...r},n)=>a.jsx(nh,{children:a.jsx(ng,{ref:n,sideOffset:t,className:(0,E.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})}));nT.displayName=ng.displayName;let nP=o.forwardRef(({className:e,inset:t,...r},n)=>a.jsx(ny,{ref:n,className:(0,E.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...r}));nP.displayName=ny.displayName,o.forwardRef(({className:e,children:t,checked:r,...n},o)=>(0,a.jsxs)(nb,{ref:o,className:(0,E.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:r,...n,children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(nj,{children:a.jsx(nR.Z,{className:"h-4 w-4"})})}),t]})).displayName=nb.displayName,o.forwardRef(({className:e,children:t,...r},n)=>(0,a.jsxs)(nw,{ref:n,className:(0,E.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(nj,{children:a.jsx(nM,{className:"h-2 w-2 fill-current"})})}),t]})).displayName=nw.displayName;let nA=o.forwardRef(({className:e,inset:t,...r},n)=>a.jsx(nv,{ref:n,className:(0,E.cn)("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...r}));nA.displayName=nv.displayName;let nO=o.forwardRef(({className:e,...t},r)=>a.jsx(nN,{ref:r,className:(0,E.cn)("-mx-1 my-1 h-px bg-muted",e),...t}));nO.displayName=nN.displayName;/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let nL=(0,nS.Z)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]);var nD=r(18822);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let nI=(0,nS.Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]),n_=(0,nS.Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),nz=(0,nS.Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);function nZ(){let{data:e,status:t}=(0,l.useSession)();if("loading"===t)return a.jsx("div",{className:"flex items-center space-x-2",children:a.jsx("div",{className:"h-8 w-8 bg-muted rounded-full animate-pulse"})});if(!e)return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(s.z,{variant:"ghost",onClick:()=>(0,l.signIn)(),children:[a.jsx(nL,{className:"h-4 w-4 mr-2"}),"Sign In"]}),a.jsx(s.z,{onClick:()=>(0,l.signIn)(),children:"Get Started"})]});let r=e.user?.name?.split(" ").map(e=>e[0]).join("").toUpperCase()||"U";return(0,a.jsxs)(nf,{children:[a.jsx(np,{asChild:!0,children:a.jsx(s.z,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,a.jsxs)(R,{className:"h-8 w-8",children:[a.jsx(S,{src:e.user?.image||"",alt:e.user?.name||""}),a.jsx(M,{children:r})]})})}),(0,a.jsxs)(nT,{className:"w-56",align:"end",forceMount:!0,children:[a.jsx(nA,{className:"font-normal",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[a.jsx("p",{className:"text-sm font-medium leading-none",children:e.user?.name||"User"}),a.jsx("p",{className:"text-xs leading-none text-muted-foreground",children:e.user?.email})]})}),a.jsx(nO,{}),(0,a.jsxs)(nP,{children:[a.jsx(nD.Z,{className:"mr-2 h-4 w-4"}),a.jsx("span",{children:"Profile"})]}),(0,a.jsxs)(nP,{children:[a.jsx(nI,{className:"mr-2 h-4 w-4"}),a.jsx("span",{children:"Billing"})]}),(0,a.jsxs)(nP,{children:[a.jsx(n_,{className:"mr-2 h-4 w-4"}),a.jsx("span",{children:"Settings"})]}),a.jsx(nO,{}),(0,a.jsxs)(nP,{onClick:()=>(0,l.signOut)(),children:[a.jsx(nz,{className:"mr-2 h-4 w-4"}),a.jsx("span",{children:"Log out"})]})]})]})}var nW=r(99046),nF=r(96213);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let nq=(0,nS.Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),nV=(0,nS.Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]]),nK=(0,nS.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),nH=(0,nS.Z)("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var nB=r(14513);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let nU=(0,nS.Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);var nY=r(20783),n$=r.n(nY);function nX({children:e}){let[t,r]=(0,o.useState)(!0),n=[{name:"Query Workspace",href:"/dashboard",icon:nW.Z,current:!0},{name:"Query History",href:"/dashboard/history",icon:nF.Z,current:!1},{name:"Connections",href:"/dashboard/connections",icon:nq,current:!1},{name:"Documentation",href:"/dashboard/docs",icon:nV,current:!1},{name:"Team",href:"/dashboard/team",icon:nK,current:!1}],i=[{name:"Settings",href:"/dashboard/settings",icon:n_},{name:"Help",href:"/dashboard/help",icon:nH},{name:"Profile",href:"/dashboard/profile",icon:nD.Z}];return(0,a.jsxs)("div",{className:"flex h-screen bg-background",children:[a.jsx("div",{className:`${t?"w-64":"w-16"} transition-all duration-300 ease-in-out`,children:(0,a.jsxs)("div",{className:"flex flex-col h-full bg-card border-r border-border",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(nW.Z,{className:"h-8 w-8 text-primary"}),t&&a.jsx("span",{className:"text-lg font-bold",children:"QueryCraft"})]}),a.jsx(s.z,{variant:"ghost",size:"icon",onClick:()=>r(!t),className:"h-8 w-8",children:t?a.jsx(nB.Z,{className:"h-4 w-4"}):a.jsx(nU,{className:"h-4 w-4"})})]}),a.jsx("nav",{className:"flex-1 p-4 space-y-2",children:n.map(e=>{let r=e.icon;return(0,a.jsxs)(n$(),{href:e.href,className:`flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ${e.current?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-muted"}`,children:[a.jsx(r,{className:"h-5 w-5 flex-shrink-0"}),t&&a.jsx("span",{children:e.name})]},e.name)})}),a.jsx("div",{className:"p-4 border-t border-border space-y-2",children:i.map(e=>{let r=e.icon;return(0,a.jsxs)(n$(),{href:e.href,className:"flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-muted transition-colors",children:[a.jsx(r,{className:"h-5 w-5 flex-shrink-0"}),t&&a.jsx("span",{children:e.name})]},e.name)})})]})}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[a.jsx("header",{className:"bg-card border-b border-border px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-foreground",children:"SQL Workspace"}),a.jsx("p",{className:"text-sm text-muted-foreground",children:"Build, debug, and optimize your SQL queries with AI assistance"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)(s.z,{variant:"outline",size:"sm",children:[a.jsx(nq,{className:"h-4 w-4 mr-2"}),"New Connection"]}),a.jsx(nZ,{})]})]})}),a.jsx("main",{className:"flex-1 overflow-hidden",children:e})]})]})}},35658:(e,t,r)=>{"use strict";r.r(t),r.d(t,{QueryWorkspace:()=>tl});var n=r(95344),a=r(3729),o=r(23673),i=r(5094),s=r(49247),l=r(11453);let c=(0,s.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,...r}){return n.jsx("div",{className:(0,l.cn)(c({variant:t}),e),...r})}var u=r(97909),f=r(69224);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let m=(0,f.Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]),p=(0,f.Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),h=(0,f.Z)("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z",key:"y3tblf"}]]),x=(0,f.Z)("ThumbsDown",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22h0a3.13 3.13 0 0 1-3-3.88Z",key:"s6e0r"}]]);var g=r(18822);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let v=(0,f.Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);var y=r(51540);function b({onQueryGenerated:e}){let{messages:t,isAILoading:r,generateQuery:s,setActiveTab:c,saveQueryToDatabase:f}=(0,y.l)(),[b,w]=(0,a.useState)(""),j=(0,a.useRef)(null),N=(0,a.useRef)(null),C=()=>{j.current?.scrollIntoView({behavior:"smooth"})};(0,a.useEffect)(()=>{C()},[t]);let k=async()=>{b.trim()&&!r&&(w(""),await s(b))},E=e=>{navigator.clipboard.writeText(e)},R=async(t,r,n)=>{if(e(t),c("editor"),r&&n)try{await f(r,t,n)}catch(e){console.error("Failed to save query to database:",e)}};return(0,n.jsxs)("div",{className:"flex flex-col h-full",children:[(0,n.jsxs)("div",{className:"flex-1 overflow-y-auto p-6 space-y-4",children:[t.map(e=>(0,n.jsxs)("div",{className:`flex gap-3 ${"user"===e.role?"justify-end":"justify-start"}`,children:["assistant"===e.role&&n.jsx("div",{className:"flex-shrink-0",children:n.jsx("div",{className:"h-8 w-8 bg-primary rounded-full flex items-center justify-center",children:n.jsx(u.Z,{className:"h-4 w-4 text-primary-foreground"})})}),n.jsx("div",{className:`max-w-2xl ${"user"===e.role?"order-first":""}`,children:(0,n.jsxs)(o.Zb,{className:`p-4 ${"user"===e.role?"bg-primary text-primary-foreground":""}`,children:[(0,n.jsxs)("div",{className:"space-y-3",children:[n.jsx("div",{className:"text-sm",children:e.content}),"query"===e.type&&e.metadata?.sql&&(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"bg-muted rounded-lg p-3",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,n.jsxs)(d,{variant:"secondary",className:"text-xs",children:[n.jsx(m,{className:"h-3 w-3 mr-1"}),"Generated SQL"]}),n.jsx("div",{className:"flex gap-1",children:n.jsx(i.z,{variant:"ghost",size:"sm",onClick:()=>E(e.metadata.sql),children:n.jsx(p,{className:"h-3 w-3"})})})]}),n.jsx("pre",{className:"text-xs font-mono overflow-x-auto",children:n.jsx("code",{children:e.metadata.sql})})]}),e.metadata.explanation&&(0,n.jsxs)("div",{className:"text-sm text-muted-foreground",children:[n.jsx("strong",{children:"Explanation:"})," ",e.metadata.explanation]}),(0,n.jsxs)("div",{className:"flex gap-2",children:[n.jsx(i.z,{size:"sm",onClick:()=>R(e.metadata.sql,e.metadata?.userInput,e.metadata?.explanation),children:"Use in Editor"}),(0,n.jsxs)(i.z,{variant:"outline",size:"sm",children:[n.jsx(h,{className:"h-3 w-3 mr-1"}),"Good"]}),(0,n.jsxs)(i.z,{variant:"outline",size:"sm",children:[n.jsx(x,{className:"h-3 w-3 mr-1"}),"Improve"]})]})]})]}),n.jsx("div",{className:"text-xs text-muted-foreground mt-2",children:(0,l.SY)(e.timestamp)})]})}),"user"===e.role&&n.jsx("div",{className:"flex-shrink-0",children:n.jsx("div",{className:"h-8 w-8 bg-muted rounded-full flex items-center justify-center",children:n.jsx(g.Z,{className:"h-4 w-4 text-muted-foreground"})})})]},e.id)),r&&(0,n.jsxs)("div",{className:"flex gap-3",children:[n.jsx("div",{className:"flex-shrink-0",children:n.jsx("div",{className:"h-8 w-8 bg-primary rounded-full flex items-center justify-center",children:n.jsx(u.Z,{className:"h-4 w-4 text-primary-foreground animate-pulse"})})}),n.jsx(o.Zb,{className:"p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[n.jsx("div",{className:"loading-spinner"}),n.jsx("span",{className:"text-sm text-muted-foreground",children:"AI is thinking..."})]})})]}),n.jsx("div",{ref:j})]}),(0,n.jsxs)("div",{className:"border-t border-border p-4",children:[(0,n.jsxs)("div",{className:"flex gap-3",children:[n.jsx("div",{className:"flex-1",children:n.jsx("textarea",{ref:N,value:b,onChange:e=>w(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),k())},placeholder:"Describe the data you want to query... (e.g., 'Show me the top 10 customers by revenue this month')",className:"querycraft-textarea resize-none",rows:3,disabled:r})}),n.jsx(i.z,{onClick:k,disabled:!b.trim()||r,size:"lg",children:n.jsx(v,{className:"h-4 w-4"})})]}),n.jsx("div",{className:"text-xs text-muted-foreground mt-2",children:"Press Enter to send, Shift+Enter for new line"})]})]})}function w(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function j(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?w(Object(r),!0).forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function N(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function C(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function k(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?C(Object(r),!0).forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):C(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function E(e){return function t(){for(var r=this,n=arguments.length,a=Array(n),o=0;o<n;o++)a[o]=arguments[o];return a.length>=e.length?e.apply(this,a):function(){for(var e=arguments.length,n=Array(e),o=0;o<e;o++)n[o]=arguments[o];return t.apply(r,[].concat(a,n))}}}function R(e){return({}).toString.call(e).includes("Object")}function S(e){return"function"==typeof e}var M=E(function(e,t){throw Error(e[t]||e.default)})({initialIsRequired:"initial state is required",initialType:"initial state should be an object",initialContent:"initial state shouldn't be an empty object",handlerType:"handler should be an object or a function",handlersType:"all handlers should be a functions",selectorType:"selector should be a function",changeType:"provided value of changes should be an object",changeField:'it seams you want to change a field in the state which is not specified in the "initial" state',default:"an unknown error accured in `state-local` package"}),T={changes:function(e,t){return R(t)||M("changeType"),Object.keys(t).some(function(t){return!Object.prototype.hasOwnProperty.call(e,t)})&&M("changeField"),t},selector:function(e){S(e)||M("selectorType")},handler:function(e){S(e)||R(e)||M("handlerType"),R(e)&&Object.values(e).some(function(e){return!S(e)})&&M("handlersType")},initial:function(e){e||M("initialIsRequired"),R(e)||M("initialType"),Object.keys(e).length||M("initialContent")}};function P(e,t){return S(t)?t(e.current):t}function A(e,t){return e.current=k(k({},e.current),t),t}function O(e,t,r){return S(t)?t(e.current):Object.keys(r).forEach(function(r){var n;return null===(n=t[r])||void 0===n?void 0:n.call(t,e.current[r])}),r}var L={configIsRequired:"the configuration object is required",configType:"the configuration object should be an object",default:"an unknown error accured in `@monaco-editor/loader` package",deprecation:"Deprecation warning!\n    You are using deprecated way of configuration.\n\n    Instead of using\n      monaco.config({ urls: { monacoBase: '...' } })\n    use\n      monaco.config({ paths: { vs: '...' } })\n\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\n  "},D=(function(e){return function t(){for(var r=this,n=arguments.length,a=Array(n),o=0;o<n;o++)a[o]=arguments[o];return a.length>=e.length?e.apply(this,a):function(){for(var e=arguments.length,n=Array(e),o=0;o<e;o++)n[o]=arguments[o];return t.apply(r,[].concat(a,n))}}})(function(e,t){throw Error(e[t]||e.default)})(L);let I={config:function(e){return e||D("configIsRequired"),({}).toString.call(e).includes("Object")||D("configType"),e.urls?(console.warn(L.deprecation),{paths:{vs:e.urls.monacoBase}}):e}},_=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){return t.reduceRight(function(e,t){return t(e)},e)}};var z={type:"cancelation",msg:"operation is manually canceled"};let Z=function(e){var t=!1,r=new Promise(function(r,n){e.then(function(e){return t?n(z):r(e)}),e.catch(n)});return r.cancel=function(){return t=!0},r};var W=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,a=!1,o=void 0;try{for(var i,s=e[Symbol.iterator]();!(n=(i=s.next()).done)&&(r.push(i.value),!t||r.length!==t);n=!0);}catch(e){a=!0,o=e}finally{try{n||null==s.return||s.return()}finally{if(a)throw o}}return r}}(e,t)||function(e,t){if(e){if("string"==typeof e)return N(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return N(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(({create:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};T.initial(e),T.handler(t);var r={current:e},n=E(O)(r,t),a=E(A)(r),o=E(T.changes)(e),i=E(P)(r);return[function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(e){return e};return T.selector(e),e(r.current)},function(e){(function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){return t.reduceRight(function(e,t){return t(e)},e)}})(n,a,o,i)(e)}]}}).create({config:{paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}},isInitialized:!1,resolve:null,reject:null,monaco:null}),2),F=W[0],q=W[1];function V(e){return document.body.appendChild(e)}function K(e){var t,r,n=F(function(e){return{config:e.config,reject:e.reject}}),a=(t="".concat(n.config.paths.vs,"/loader.js"),r=document.createElement("script"),t&&(r.src=t),r);return a.onload=function(){return e()},a.onerror=n.reject,a}function H(){var e=F(function(e){return{config:e.config,resolve:e.resolve,reject:e.reject}}),t=window.require;t.config(e.config),t(["vs/editor/editor.main"],function(t){B(t),e.resolve(t)},function(t){e.reject(t)})}function B(e){F().monaco||q({monaco:e})}var U=new Promise(function(e,t){return q({resolve:e,reject:t})});let Y={config:function(e){var t=I.config(e),r=t.monaco,n=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r,n,a={},o=Object.keys(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||(a[r]=e[r]);return a}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(t,["monaco"]);q(function(e){return{config:function e(t,r){return Object.keys(r).forEach(function(n){r[n]instanceof Object&&t[n]&&Object.assign(r[n],e(t[n],r[n]))}),j(j({},t),r)}(e.config,n),monaco:r}})},init:function(){var e=F(function(e){return{monaco:e.monaco,isInitialized:e.isInitialized,resolve:e.resolve}});if(!e.isInitialized){if(q({isInitialized:!0}),e.monaco)return e.resolve(e.monaco),Z(U);if(window.monaco&&window.monaco.editor)return B(window.monaco),e.resolve(window.monaco),Z(U);_(V,K)(H)}return Z(U)},__getMonacoInstance:function(){return F(function(e){return e.monaco})}};var $={wrapper:{display:"flex",position:"relative",textAlign:"initial"},fullWidth:{width:"100%"},hide:{display:"none"}},X={container:{display:"flex",height:"100%",width:"100%",justifyContent:"center",alignItems:"center"}},G=function({children:e}){return a.createElement("div",{style:X.container},e)},Q=(0,a.memo)(function({width:e,height:t,isEditorReady:r,loading:n,_ref:o,className:i,wrapperProps:s}){return a.createElement("section",{style:{...$.wrapper,width:e,height:t},...s},!r&&a.createElement(G,null,n),a.createElement("div",{ref:o,style:{...$.fullWidth,...!r&&$.hide},className:i}))}),J=function(e){(0,a.useEffect)(e,[])},ee=function(e,t,r=!0){let n=(0,a.useRef)(!0);(0,a.useEffect)(n.current||!r?()=>{n.current=!1}:e,t)};function et(){}function er(e,t,r,n){return e.editor.getModel(en(e,n))||e.editor.createModel(t,r,n?en(e,n):void 0)}function en(e,t){return e.Uri.parse(t)}var ea=function(e){let t=(0,a.useRef)();return(0,a.useEffect)(()=>{t.current=e},[e]),t.current},eo=new Map,ei=(0,a.memo)(function({defaultValue:e,defaultLanguage:t,defaultPath:r,value:n,language:o,path:i,theme:s="light",line:l,loading:c="Loading...",options:d={},overrideServices:u={},saveViewState:f=!0,keepCurrentModel:m=!1,width:p="100%",height:h="100%",className:x,wrapperProps:g={},beforeMount:v=et,onMount:y=et,onChange:b,onValidate:w=et}){let[j,N]=(0,a.useState)(!1),[C,k]=(0,a.useState)(!0),E=(0,a.useRef)(null),R=(0,a.useRef)(null),S=(0,a.useRef)(null),M=(0,a.useRef)(y),T=(0,a.useRef)(v),P=(0,a.useRef)(),A=(0,a.useRef)(n),O=ea(i),L=(0,a.useRef)(!1),D=(0,a.useRef)(!1);J(()=>{let e=Y.init();return e.then(e=>(E.current=e)&&k(!1)).catch(e=>e?.type!=="cancelation"&&console.error("Monaco initialization: error:",e)),()=>R.current?void(P.current?.dispose(),m?f&&eo.set(i,R.current.saveViewState()):R.current.getModel()?.dispose(),R.current.dispose()):e.cancel()}),ee(()=>{let a=er(E.current,e||n||"",t||o||"",i||r||"");a!==R.current?.getModel()&&(f&&eo.set(O,R.current?.saveViewState()),R.current?.setModel(a),f&&R.current?.restoreViewState(eo.get(i)))},[i],j),ee(()=>{R.current?.updateOptions(d)},[d],j),ee(()=>{R.current&&void 0!==n&&(R.current.getOption(E.current.editor.EditorOption.readOnly)?R.current.setValue(n):n===R.current.getValue()||(D.current=!0,R.current.executeEdits("",[{range:R.current.getModel().getFullModelRange(),text:n,forceMoveMarkers:!0}]),R.current.pushUndoStop(),D.current=!1))},[n],j),ee(()=>{let e=R.current?.getModel();e&&o&&E.current?.editor.setModelLanguage(e,o)},[o],j),ee(()=>{void 0!==l&&R.current?.revealLine(l)},[l],j),ee(()=>{E.current?.editor.setTheme(s)},[s],j);let I=(0,a.useCallback)(()=>{if(!(!S.current||!E.current)&&!L.current){T.current(E.current);let a=i||r,c=er(E.current,n||e||"",t||o||"",a||"");R.current=E.current?.editor.create(S.current,{model:c,automaticLayout:!0,...d},u),f&&R.current.restoreViewState(eo.get(a)),E.current.editor.setTheme(s),void 0!==l&&R.current.revealLine(l),N(!0),L.current=!0}},[e,t,r,n,o,i,d,u,f,s,l]);return(0,a.useEffect)(()=>{j&&M.current(R.current,E.current)},[j]),(0,a.useEffect)(()=>{C||j||I()},[C,j,I]),A.current=n,(0,a.useEffect)(()=>{j&&b&&(P.current?.dispose(),P.current=R.current?.onDidChangeModelContent(e=>{D.current||b(R.current.getValue(),e)}))},[j,b]),(0,a.useEffect)(()=>{if(j){let e=E.current.editor.onDidChangeMarkers(e=>{let t=R.current.getModel()?.uri;if(t&&e.find(e=>e.path===t.path)){let e=E.current.editor.getModelMarkers({resource:t});w?.(e)}});return()=>{e?.dispose()}}return()=>{}},[j,w]),a.createElement(Q,{width:p,height:h,isEditorReady:j,loading:c,_ref:S,className:x,wrapperProps:g})});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let es=(0,f.Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),el=(0,f.Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),ec=(0,f.Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),ed=(0,f.Z)("AlignLeft",[["line",{x1:"21",x2:"3",y1:"6",y2:"6",key:"1fp77t"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}],["line",{x1:"17",x2:"3",y1:"18",y2:"18",key:"1awlsn"}]]),eu=(0,f.Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]);function ef({value:e,onChange:t,language:r,readOnly:s=!1}){let l=(0,a.useRef)(null),c=(0,a.useCallback)((e,t)=>{l.current=e,t.languages.registerCompletionItemProvider("sql",{provideCompletionItems:(e,r)=>({suggestions:[{label:"SELECT",kind:t.languages.CompletionItemKind.Keyword,insertText:"SELECT ",documentation:"SELECT statement"},{label:"FROM",kind:t.languages.CompletionItemKind.Keyword,insertText:"FROM ",documentation:"FROM clause"},{label:"WHERE",kind:t.languages.CompletionItemKind.Keyword,insertText:"WHERE ",documentation:"WHERE clause"},{label:"JOIN",kind:t.languages.CompletionItemKind.Keyword,insertText:"JOIN ",documentation:"JOIN clause"},{label:"GROUP BY",kind:t.languages.CompletionItemKind.Keyword,insertText:"GROUP BY ",documentation:"GROUP BY clause"},{label:"ORDER BY",kind:t.languages.CompletionItemKind.Keyword,insertText:"ORDER BY ",documentation:"ORDER BY clause"}]})}),t.editor.defineTheme("querycraft-theme",{base:"vs",inherit:!0,rules:[{token:"keyword.sql",foreground:"3b82f6",fontStyle:"bold"},{token:"string.sql",foreground:"22c55e"},{token:"comment.sql",foreground:"6b7280",fontStyle:"italic"}],colors:{"editor.background":"#ffffff","editor.foreground":"#1f2937","editor.lineHighlightBackground":"#f9fafb","editor.selectionBackground":"#dbeafe"}}),t.editor.setTheme("querycraft-theme")},[]),{errors:u,warnings:f}=(()=>{let t=[],r=[];return e.trim()&&(e.toLowerCase().includes("select")||e.toLowerCase().includes("insert")||e.toLowerCase().includes("update")||e.toLowerCase().includes("delete")||t.push("Query should contain a valid SQL statement"),e.toLowerCase().includes("select *")&&r.push("Consider specifying column names instead of using SELECT *"),e.toLowerCase().includes("where")&&!e.toLowerCase().includes("limit")&&r.push("Consider adding a LIMIT clause for large datasets")),{errors:t,warnings:r}})();return(0,n.jsxs)("div",{className:"flex flex-col h-full space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[n.jsx("h3",{className:"font-semibold",children:"SQL Editor"}),n.jsx(d,{variant:"outline",children:r.toUpperCase()}),0===u.length&&0===f.length&&e.trim()&&(0,n.jsxs)(d,{variant:"default",className:"bg-green-100 text-green-800",children:[n.jsx(es,{className:"h-3 w-3 mr-1"}),"Valid"]}),u.length>0&&(0,n.jsxs)(d,{variant:"destructive",children:[n.jsx(el,{className:"h-3 w-3 mr-1"}),u.length," Error",u.length>1?"s":""]}),f.length>0&&(0,n.jsxs)(d,{variant:"secondary",className:"bg-yellow-100 text-yellow-800",children:[n.jsx(ec,{className:"h-3 w-3 mr-1"}),f.length," Warning",f.length>1?"s":""]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>{l.current&&l.current.getAction("editor.action.formatDocument").run()},children:[n.jsx(ed,{className:"h-4 w-4 mr-2"}),"Format"]}),(0,n.jsxs)(i.z,{variant:"outline",size:"sm",children:[n.jsx(eu,{className:"h-4 w-4 mr-2"}),"Optimize"]})]})]}),n.jsx(o.Zb,{className:"flex-1 overflow-hidden sql-editor",children:n.jsx(ei,{height:"100%",language:"sql",value:e,onChange:e=>t(e||""),onMount:c,options:{readOnly:s,minimap:{enabled:!1},fontSize:14,lineNumbers:"on",roundedSelection:!1,scrollBeyondLastLine:!1,automaticLayout:!0,wordWrap:"on",folding:!0,lineDecorationsWidth:10,lineNumbersMinChars:3,glyphMargin:!1,contextmenu:!0,mouseWheelZoom:!0,smoothScrolling:!0,cursorBlinking:"blink",cursorStyle:"line",renderWhitespace:"selection",renderControlCharacters:!1,fontFamily:"JetBrains Mono, Fira Code, Consolas, monospace",fontLigatures:!0,suggest:{showKeywords:!0,showSnippets:!0,showFunctions:!0},quickSuggestions:{other:!0,comments:!1,strings:!1},parameterHints:{enabled:!0},acceptSuggestionOnCommitCharacter:!0,acceptSuggestionOnEnter:"on",accessibilitySupport:"auto"}})}),(u.length>0||f.length>0)&&(0,n.jsxs)("div",{className:"space-y-2",children:[u.map((e,t)=>(0,n.jsxs)("div",{className:"flex items-center gap-2 text-sm text-red-600",children:[n.jsx(el,{className:"h-4 w-4"}),e]},`error-${t}`)),f.map((e,t)=>(0,n.jsxs)("div",{className:"flex items-center gap-2 text-sm text-yellow-600",children:[n.jsx(ec,{className:"h-4 w-4"}),e]},`warning-${t}`))]})]})}var em=r(99046);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let ep=(0,f.Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);var eh=r(97751);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let ex=(0,f.Z)("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]]),eg=(0,f.Z)("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]]);function ev({isConnected:e}){let[t,r]=(0,a.useState)(new Set),s={database:"ecommerce_db",tables:[{name:"customers",rowCount:15420,columns:[{name:"customer_id",type:"INT",isPrimaryKey:!0,isNullable:!1},{name:"name",type:"VARCHAR(255)",isPrimaryKey:!1,isNullable:!1},{name:"email",type:"VARCHAR(255)",isPrimaryKey:!1,isNullable:!1},{name:"phone",type:"VARCHAR(20)",isPrimaryKey:!1,isNullable:!0},{name:"created_at",type:"TIMESTAMP",isPrimaryKey:!1,isNullable:!1}]},{name:"orders",rowCount:45230,columns:[{name:"order_id",type:"INT",isPrimaryKey:!0,isNullable:!1},{name:"customer_id",type:"INT",isPrimaryKey:!1,isNullable:!1},{name:"order_date",type:"TIMESTAMP",isPrimaryKey:!1,isNullable:!1},{name:"total_amount",type:"DECIMAL(10,2)",isPrimaryKey:!1,isNullable:!1},{name:"status",type:"VARCHAR(50)",isPrimaryKey:!1,isNullable:!1}]},{name:"order_items",rowCount:128450,columns:[{name:"item_id",type:"INT",isPrimaryKey:!0,isNullable:!1},{name:"order_id",type:"INT",isPrimaryKey:!1,isNullable:!1},{name:"product_id",type:"INT",isPrimaryKey:!1,isNullable:!1},{name:"quantity",type:"INT",isPrimaryKey:!1,isNullable:!1},{name:"unit_price",type:"DECIMAL(10,2)",isPrimaryKey:!1,isNullable:!1}]},{name:"products",rowCount:2340,columns:[{name:"product_id",type:"INT",isPrimaryKey:!0,isNullable:!1},{name:"name",type:"VARCHAR(255)",isPrimaryKey:!1,isNullable:!1},{name:"description",type:"TEXT",isPrimaryKey:!1,isNullable:!0},{name:"price",type:"DECIMAL(10,2)",isPrimaryKey:!1,isNullable:!1},{name:"category_id",type:"INT",isPrimaryKey:!1,isNullable:!1}]}]},l=e=>{let n=new Set(t);n.has(e)?n.delete(e):n.add(e),r(n)};return e?(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"p-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[n.jsx(em.Z,{className:"h-4 w-4 text-primary"}),n.jsx("span",{className:"font-medium",children:s.database})]}),(0,n.jsxs)("div",{className:"text-xs text-muted-foreground",children:[s.tables.length," tables"]})]}),n.jsx("div",{className:"space-y-1",children:s.tables.map(e=>(0,n.jsxs)("div",{className:"schema-table",children:[(0,n.jsxs)("button",{onClick:()=>l(e.name),className:"schema-table-header w-full",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[t.has(e.name)?n.jsx(ep,{className:"h-4 w-4"}):n.jsx(eh.Z,{className:"h-4 w-4"}),n.jsx(ex,{className:"h-4 w-4 text-primary"}),n.jsx("span",{className:"font-medium",children:e.name})]}),n.jsx("div",{className:"flex items-center gap-2",children:(0,n.jsxs)(d,{variant:"secondary",className:"text-xs",children:[e.rowCount.toLocaleString()," rows"]})})]}),t.has(e.name)&&n.jsx("div",{className:"schema-table-content",children:e.columns.map(e=>(0,n.jsxs)("div",{className:"schema-column",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 flex-1",children:[e.isPrimaryKey&&n.jsx(eg,{className:"h-3 w-3 text-yellow-500"}),n.jsx("span",{className:"text-sm font-medium",children:e.name})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[n.jsx("span",{className:"schema-column-type text-xs",children:e.type}),!e.isNullable&&n.jsx(d,{variant:"outline",className:"text-xs px-1 py-0",children:"NOT NULL"})]})]},e.name))})]},e.name))}),(0,n.jsxs)(o.Zb,{className:"mx-4",children:[n.jsx(o.Ol,{className:"pb-3",children:(0,n.jsxs)(o.ll,{className:"text-sm flex items-center gap-2",children:[n.jsx(ec,{className:"h-4 w-4"}),"Schema Statistics"]})}),(0,n.jsxs)(o.aY,{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex justify-between text-sm",children:[n.jsx("span",{children:"Total Tables:"}),n.jsx("span",{className:"font-medium",children:s.tables.length})]}),(0,n.jsxs)("div",{className:"flex justify-between text-sm",children:[n.jsx("span",{children:"Total Columns:"}),n.jsx("span",{className:"font-medium",children:s.tables.reduce((e,t)=>e+t.columns.length,0)})]}),(0,n.jsxs)("div",{className:"flex justify-between text-sm",children:[n.jsx("span",{children:"Total Rows:"}),n.jsx("span",{className:"font-medium",children:s.tables.reduce((e,t)=>e+t.rowCount,0).toLocaleString()})]})]})]})]}):(0,n.jsxs)("div",{className:"p-4 text-center",children:[n.jsx(em.Z,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),n.jsx("p",{className:"text-sm text-muted-foreground mb-4",children:"Connect to a database to explore its schema"}),n.jsx(i.z,{variant:"outline",size:"sm",children:"Connect Database"})]})}/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let ey=(0,f.Z)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),eb=(0,f.Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),ew=(0,f.Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),ej=(0,f.Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);function eN(){let[e,t]=(0,a.useState)(!1),r={executionTime:245,rowsAffected:10,columns:[{name:"customer_id",type:"INT"},{name:"name",type:"VARCHAR"},{name:"email",type:"VARCHAR"},{name:"total_orders",type:"INT"},{name:"total_spent",type:"DECIMAL"}],data:[{customer_id:1,name:"John Doe",email:"<EMAIL>",total_orders:15,total_spent:2450.5},{customer_id:2,name:"Jane Smith",email:"<EMAIL>",total_orders:12,total_spent:1890.25},{customer_id:3,name:"Bob Johnson",email:"<EMAIL>",total_orders:8,total_spent:1234.75},{customer_id:4,name:"Alice Brown",email:"<EMAIL>",total_orders:6,total_spent:987.5},{customer_id:5,name:"Charlie Wilson",email:"<EMAIL>",total_orders:4,total_spent:567.25}]};return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[n.jsx("h3",{className:"text-lg font-semibold",children:"Query Results"}),n.jsx("p",{className:"text-sm text-muted-foreground",children:"Last executed: 2 minutes ago"})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>{t(!0),setTimeout(()=>t(!1),1e3)},disabled:e,children:[n.jsx(ey,{className:`h-4 w-4 mr-2 ${e?"animate-spin":""}`}),"Refresh"]}),(0,n.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>{console.log("Exporting results...")},children:[n.jsx(eb,{className:"h-4 w-4 mr-2"}),"Export"]})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[n.jsx(o.Zb,{children:n.jsx(o.aY,{className:"p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[n.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:n.jsx(ew,{className:"h-4 w-4 text-primary"})}),(0,n.jsxs)("div",{children:[n.jsx("div",{className:"text-sm text-muted-foreground",children:"Execution Time"}),(0,n.jsxs)("div",{className:"text-lg font-semibold",children:[r.executionTime,"ms"]})]})]})})}),n.jsx(o.Zb,{children:n.jsx(o.aY,{className:"p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[n.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:n.jsx(em.Z,{className:"h-4 w-4 text-green-600"})}),(0,n.jsxs)("div",{children:[n.jsx("div",{className:"text-sm text-muted-foreground",children:"Rows Returned"}),n.jsx("div",{className:"text-lg font-semibold",children:r.rowsAffected})]})]})})}),n.jsx(o.Zb,{children:n.jsx(o.aY,{className:"p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[n.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:n.jsx(ej,{className:"h-4 w-4 text-blue-600"})}),(0,n.jsxs)("div",{children:[n.jsx("div",{className:"text-sm text-muted-foreground",children:"Performance"}),n.jsx("div",{className:"text-lg font-semibold",children:n.jsx(d,{variant:"default",className:"bg-green-100 text-green-800",children:"Excellent"})})]})]})})})]}),(0,n.jsxs)(o.Zb,{children:[n.jsx(o.Ol,{children:n.jsx(o.ll,{className:"text-base",children:"Data Results"})}),n.jsx(o.aY,{children:n.jsx("div",{className:"overflow-x-auto",children:(0,n.jsxs)("table",{className:"w-full border-collapse",children:[n.jsx("thead",{children:n.jsx("tr",{className:"border-b border-border",children:r.columns.map(e=>n.jsx("th",{className:"text-left p-3 font-medium text-sm text-muted-foreground",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[e.name,n.jsx(d,{variant:"outline",className:"text-xs",children:e.type})]})},e.name))})}),n.jsx("tbody",{children:r.data.map((e,t)=>(0,n.jsxs)("tr",{className:"border-b border-border hover:bg-muted/50",children:[n.jsx("td",{className:"p-3 text-sm",children:e.customer_id}),n.jsx("td",{className:"p-3 text-sm font-medium",children:e.name}),n.jsx("td",{className:"p-3 text-sm text-muted-foreground",children:e.email}),n.jsx("td",{className:"p-3 text-sm",children:e.total_orders}),(0,n.jsxs)("td",{className:"p-3 text-sm font-medium",children:["$",e.total_spent.toFixed(2)]})]},t))})]})})})]}),(0,n.jsxs)(o.Zb,{children:[n.jsx(o.Ol,{children:(0,n.jsxs)(o.ll,{className:"text-base flex items-center gap-2",children:[n.jsx(ej,{className:"h-4 w-4"}),"Query Analysis"]})}),n.jsx(o.aY,{className:"space-y-4",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"font-medium text-sm mb-2",children:"Performance Insights"}),(0,n.jsxs)("ul",{className:"space-y-1 text-sm text-muted-foreground",children:[(0,n.jsxs)("li",{className:"flex items-center gap-2",children:[n.jsx("div",{className:"h-2 w-2 bg-green-500 rounded-full"}),"Query executed efficiently"]}),(0,n.jsxs)("li",{className:"flex items-center gap-2",children:[n.jsx("div",{className:"h-2 w-2 bg-green-500 rounded-full"}),"Proper index usage detected"]}),(0,n.jsxs)("li",{className:"flex items-center gap-2",children:[n.jsx("div",{className:"h-2 w-2 bg-yellow-500 rounded-full"}),"Consider adding LIMIT clause"]})]})]}),(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"font-medium text-sm mb-2",children:"Optimization Suggestions"}),(0,n.jsxs)("ul",{className:"space-y-1 text-sm text-muted-foreground",children:[(0,n.jsxs)("li",{className:"flex items-center gap-2",children:[n.jsx(el,{className:"h-3 w-3 text-blue-500"}),"Add composite index on (customer_id, order_date)"]}),(0,n.jsxs)("li",{className:"flex items-center gap-2",children:[n.jsx(el,{className:"h-3 w-3 text-blue-500"}),"Consider partitioning orders table by date"]})]})]})]})})]})]})}var eC=r(62409),ek=r(43234),eE=r(98462),eR=r(31405),eS=r(2256),eM=r(3975),eT=r(16069),eP=r(85222),eA="ScrollArea",[eO,eL]=(0,eE.b)(eA),[eD,eI]=eO(eA),e_=a.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:o="hover",dir:i,scrollHideDelay:s=600,...l}=e,[c,d]=a.useState(null),[u,f]=a.useState(null),[m,p]=a.useState(null),[h,x]=a.useState(null),[g,v]=a.useState(null),[y,b]=a.useState(0),[w,j]=a.useState(0),[N,C]=a.useState(!1),[k,E]=a.useState(!1),R=(0,eR.e)(t,e=>d(e)),S=(0,eM.gm)(i);return(0,n.jsx)(eD,{scope:r,type:o,dir:S,scrollHideDelay:s,scrollArea:c,viewport:u,onViewportChange:f,content:m,onContentChange:p,scrollbarX:h,onScrollbarXChange:x,scrollbarXEnabled:N,onScrollbarXEnabledChange:C,scrollbarY:g,onScrollbarYChange:v,scrollbarYEnabled:k,onScrollbarYEnabledChange:E,onCornerWidthChange:b,onCornerHeightChange:j,children:(0,n.jsx)(eC.WV.div,{dir:S,...l,ref:R,style:{position:"relative","--radix-scroll-area-corner-width":y+"px","--radix-scroll-area-corner-height":w+"px",...e.style}})})});e_.displayName=eA;var ez="ScrollAreaViewport",eZ=a.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:o,nonce:i,...s}=e,l=eI(ez,r),c=a.useRef(null),d=(0,eR.e)(t,c,l.onViewportChange);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,n.jsx)(eC.WV.div,{"data-radix-scroll-area-viewport":"",...s,ref:d,style:{overflowX:l.scrollbarXEnabled?"scroll":"hidden",overflowY:l.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,n.jsx)("div",{ref:l.onContentChange,style:{minWidth:"100%",display:"table"},children:o})})]})});eZ.displayName=ez;var eW="ScrollAreaScrollbar",eF=a.forwardRef((e,t)=>{let{forceMount:r,...o}=e,i=eI(eW,e.__scopeScrollArea),{onScrollbarXEnabledChange:s,onScrollbarYEnabledChange:l}=i,c="horizontal"===e.orientation;return a.useEffect(()=>(c?s(!0):l(!0),()=>{c?s(!1):l(!1)}),[c,s,l]),"hover"===i.type?(0,n.jsx)(eq,{...o,ref:t,forceMount:r}):"scroll"===i.type?(0,n.jsx)(eV,{...o,ref:t,forceMount:r}):"auto"===i.type?(0,n.jsx)(eK,{...o,ref:t,forceMount:r}):"always"===i.type?(0,n.jsx)(eH,{...o,ref:t}):null});eF.displayName=eW;var eq=a.forwardRef((e,t)=>{let{forceMount:r,...o}=e,i=eI(eW,e.__scopeScrollArea),[s,l]=a.useState(!1);return a.useEffect(()=>{let e=i.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),l(!0)},n=()=>{t=window.setTimeout(()=>l(!1),i.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[i.scrollArea,i.scrollHideDelay]),(0,n.jsx)(ek.z,{present:r||s,children:(0,n.jsx)(eK,{"data-state":s?"visible":"hidden",...o,ref:t})})}),eV=a.forwardRef((e,t)=>{var r;let{forceMount:o,...i}=e,s=eI(eW,e.__scopeScrollArea),l="horizontal"===e.orientation,c=e6(()=>u("SCROLL_END"),100),[d,u]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},a.useReducer((e,t)=>r[e][t]??e,"hidden"));return a.useEffect(()=>{if("idle"===d){let e=window.setTimeout(()=>u("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(e)}},[d,s.scrollHideDelay,u]),a.useEffect(()=>{let e=s.viewport,t=l?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(u("SCROLL"),c()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[s.viewport,l,u,c]),(0,n.jsx)(ek.z,{present:o||"hidden"!==d,children:(0,n.jsx)(eH,{"data-state":"hidden"===d?"hidden":"visible",...i,ref:t,onPointerEnter:(0,eP.M)(e.onPointerEnter,()=>u("POINTER_ENTER")),onPointerLeave:(0,eP.M)(e.onPointerLeave,()=>u("POINTER_LEAVE"))})})}),eK=a.forwardRef((e,t)=>{let r=eI(eW,e.__scopeScrollArea),{forceMount:o,...i}=e,[s,l]=a.useState(!1),c="horizontal"===e.orientation,d=e6(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;l(c?e:t)}},10);return te(r.viewport,d),te(r.content,d),(0,n.jsx)(ek.z,{present:o||s,children:(0,n.jsx)(eH,{"data-state":s?"visible":"hidden",...i,ref:t})})}),eH=a.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,i=eI(eW,e.__scopeScrollArea),s=a.useRef(null),l=a.useRef(0),[c,d]=a.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=e4(c.viewport,c.content),f={...o,sizes:c,onSizesChange:d,hasThumb:!!(u>0&&u<1),onThumbChange:e=>s.current=e,onThumbPointerUp:()=>l.current=0,onThumbPointerDown:e=>l.current=e};function m(e,t){return function(e,t,r,n="ltr"){let a=e5(r),o=t||a/2,i=r.scrollbar.paddingStart+o,s=r.scrollbar.size-r.scrollbar.paddingEnd-(a-o),l=r.content-r.viewport;return e8([i,s],"ltr"===n?[0,l]:[-1*l,0])(e)}(e,l.current,c,t)}return"horizontal"===r?(0,n.jsx)(eB,{...f,ref:t,onThumbPositionChange:()=>{if(i.viewport&&s.current){let e=e9(i.viewport.scrollLeft,c,i.dir);s.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollLeft=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollLeft=m(e,i.dir))}}):"vertical"===r?(0,n.jsx)(eU,{...f,ref:t,onThumbPositionChange:()=>{if(i.viewport&&s.current){let e=e9(i.viewport.scrollTop,c);s.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollTop=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollTop=m(e))}}):null}),eB=a.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...i}=e,s=eI(eW,e.__scopeScrollArea),[l,c]=a.useState(),d=a.useRef(null),u=(0,eR.e)(t,d,s.onScrollbarXChange);return a.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,n.jsx)(eX,{"data-orientation":"horizontal",...i,ref:u,sizes:r,style:{bottom:0,left:"rtl"===s.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===s.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":e5(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(s.viewport){let n=s.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&s.viewport&&l&&o({content:s.viewport.scrollWidth,viewport:s.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:e3(l.paddingLeft),paddingEnd:e3(l.paddingRight)}})}})}),eU=a.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...i}=e,s=eI(eW,e.__scopeScrollArea),[l,c]=a.useState(),d=a.useRef(null),u=(0,eR.e)(t,d,s.onScrollbarYChange);return a.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,n.jsx)(eX,{"data-orientation":"vertical",...i,ref:u,sizes:r,style:{top:0,right:"ltr"===s.dir?0:void 0,left:"rtl"===s.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":e5(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(s.viewport){let n=s.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&s.viewport&&l&&o({content:s.viewport.scrollHeight,viewport:s.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:e3(l.paddingTop),paddingEnd:e3(l.paddingBottom)}})}})}),[eY,e$]=eO(eW),eX=a.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:o,hasThumb:i,onThumbChange:s,onThumbPointerUp:l,onThumbPointerDown:c,onThumbPositionChange:d,onDragScroll:u,onWheelScroll:f,onResize:m,...p}=e,h=eI(eW,r),[x,g]=a.useState(null),v=(0,eR.e)(t,e=>g(e)),y=a.useRef(null),b=a.useRef(""),w=h.viewport,j=o.content-o.viewport,N=(0,eS.W)(f),C=(0,eS.W)(d),k=e6(m,10);function E(e){y.current&&u({x:e.clientX-y.current.left,y:e.clientY-y.current.top})}return a.useEffect(()=>{let e=e=>{let t=e.target;x?.contains(t)&&N(e,j)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[w,x,j,N]),a.useEffect(C,[o,C]),te(x,k),te(h.content,k),(0,n.jsx)(eY,{scope:r,scrollbar:x,hasThumb:i,onThumbChange:(0,eS.W)(s),onThumbPointerUp:(0,eS.W)(l),onThumbPositionChange:C,onThumbPointerDown:(0,eS.W)(c),children:(0,n.jsx)(eC.WV.div,{...p,ref:v,style:{position:"absolute",...p.style},onPointerDown:(0,eP.M)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),y.current=x.getBoundingClientRect(),b.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",h.viewport&&(h.viewport.style.scrollBehavior="auto"),E(e))}),onPointerMove:(0,eP.M)(e.onPointerMove,E),onPointerUp:(0,eP.M)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=b.current,h.viewport&&(h.viewport.style.scrollBehavior=""),y.current=null})})})}),eG="ScrollAreaThumb",eQ=a.forwardRef((e,t)=>{let{forceMount:r,...a}=e,o=e$(eG,e.__scopeScrollArea);return(0,n.jsx)(ek.z,{present:r||o.hasThumb,children:(0,n.jsx)(eJ,{ref:t,...a})})}),eJ=a.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:o,...i}=e,s=eI(eG,r),l=e$(eG,r),{onThumbPositionChange:c}=l,d=(0,eR.e)(t,e=>l.onThumbChange(e)),u=a.useRef(void 0),f=e6(()=>{u.current&&(u.current(),u.current=void 0)},100);return a.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{if(f(),!u.current){let t=e7(e,c);u.current=t,c()}};return c(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,f,c]),(0,n.jsx)(eC.WV.div,{"data-state":l.hasThumb?"visible":"hidden",...i,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...o},onPointerDownCapture:(0,eP.M)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;l.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,eP.M)(e.onPointerUp,l.onThumbPointerUp)})});eQ.displayName=eG;var e0="ScrollAreaCorner",e1=a.forwardRef((e,t)=>{let r=eI(e0,e.__scopeScrollArea),a=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&a?(0,n.jsx)(e2,{...e,ref:t}):null});e1.displayName=e0;var e2=a.forwardRef((e,t)=>{let{__scopeScrollArea:r,...o}=e,i=eI(e0,r),[s,l]=a.useState(0),[c,d]=a.useState(0),u=!!(s&&c);return te(i.scrollbarX,()=>{let e=i.scrollbarX?.offsetHeight||0;i.onCornerHeightChange(e),d(e)}),te(i.scrollbarY,()=>{let e=i.scrollbarY?.offsetWidth||0;i.onCornerWidthChange(e),l(e)}),u?(0,n.jsx)(eC.WV.div,{...o,ref:t,style:{width:s,height:c,position:"absolute",right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:0,...e.style}}):null});function e3(e){return e?parseInt(e,10):0}function e4(e,t){let r=e/t;return isNaN(r)?0:r}function e5(e){let t=e4(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function e9(e,t,r="ltr"){let n=e5(t),a=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,o=t.scrollbar.size-a,i=t.content-t.viewport,s=function(e,[t,r]){return Math.min(r,Math.max(t,e))}(e,"ltr"===r?[0,i]:[-1*i,0]);return e8([0,i],[0,o-n])(s)}function e8(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var e7=(e,t=()=>{})=>{let r={left:e.scrollLeft,top:e.scrollTop},n=0;return function a(){let o={left:e.scrollLeft,top:e.scrollTop},i=r.left!==o.left,s=r.top!==o.top;(i||s)&&t(),r=o,n=window.requestAnimationFrame(a)}(),()=>window.cancelAnimationFrame(n)};function e6(e,t){let r=(0,eS.W)(e),n=a.useRef(0);return a.useEffect(()=>()=>window.clearTimeout(n.current),[]),a.useCallback(()=>{window.clearTimeout(n.current),n.current=window.setTimeout(r,t)},[r,t])}function te(e,t){let r=(0,eS.W)(t);(0,eT.b)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}let tt=a.forwardRef(({className:e,children:t,...r},a)=>(0,n.jsxs)(e_,{ref:a,className:(0,l.cn)("relative overflow-hidden",e),...r,children:[n.jsx(eZ,{className:"h-full w-full rounded-[inherit]",children:t}),n.jsx(tr,{}),n.jsx(e1,{})]}));tt.displayName=e_.displayName;let tr=a.forwardRef(({className:e,orientation:t="vertical",...r},a)=>n.jsx(eF,{ref:a,orientation:t,className:(0,l.cn)("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...r,children:n.jsx(eQ,{className:"relative flex-1 rounded-full bg-border"})}));tr.displayName=eF.displayName;var tn=r(96213);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let ta=(0,f.Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]);function to({userId:e}){let[t,r]=(0,a.useState)([]),[s,c]=(0,a.useState)(!0),{setCurrentQuery:u,setActiveTab:f,currentUser:m}=(0,y.l)();(0,a.useEffect)(()=>{(async()=>{if(!e&&!m)return;let t=e||m?.id;if(t)try{c(!0);let e=await fetch(`/api/queries?type=history&userId=${t}&limit=50`);if(e.ok){let t=await e.json();r(t)}}catch(e){console.error("Failed to load query history:",e)}finally{c(!1)}})()},[e,m]);let x=e=>{u(e),f("editor")},g=e=>{navigator.clipboard.writeText(e)},v=e=>{switch(e){case"EXECUTED":return"bg-green-100 text-green-800";case"FAILED":return"bg-red-100 text-red-800";case"GENERATED":return"bg-blue-100 text-blue-800";case"OPTIMIZED":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}};return s?(0,n.jsxs)(o.Zb,{children:[n.jsx(o.Ol,{children:(0,n.jsxs)(o.ll,{className:"flex items-center gap-2",children:[n.jsx(tn.Z,{className:"h-5 w-5"}),"Query History"]})}),n.jsx(o.aY,{children:(0,n.jsxs)("div",{className:"flex items-center justify-center py-8",children:[n.jsx("div",{className:"loading-spinner"}),n.jsx("span",{className:"ml-2 text-sm text-muted-foreground",children:"Loading history..."})]})})]}):(0,n.jsxs)(o.Zb,{children:[n.jsx(o.Ol,{children:(0,n.jsxs)(o.ll,{className:"flex items-center gap-2",children:[n.jsx(tn.Z,{className:"h-5 w-5"}),"Query History",(0,n.jsxs)(d,{variant:"secondary",className:"ml-auto",children:[t.length," queries"]})]})}),n.jsx(o.aY,{children:n.jsx(tt,{className:"h-[600px]",children:0===t.length?(0,n.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[n.jsx(tn.Z,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),n.jsx("p",{children:"No queries in history yet"}),n.jsx("p",{className:"text-sm",children:"Start generating queries to see them here"})]}):n.jsx("div",{className:"space-y-4",children:t.map(e=>n.jsx(o.Zb,{className:"border-l-4 border-l-primary/20",children:n.jsx(o.aY,{className:"p-4",children:(0,n.jsxs)("div",{className:"space-y-3",children:[n.jsx("div",{className:"flex items-start justify-between",children:(0,n.jsxs)("div",{className:"flex-1",children:[n.jsx("p",{className:"text-sm font-medium text-foreground",children:e.userInput}),(0,n.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[n.jsx(d,{className:v(e.status),children:e.status}),(0,n.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[n.jsx(em.Z,{className:"h-3 w-3"}),e.databaseType]}),(0,n.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[n.jsx(ew,{className:"h-3 w-3"}),(0,l.SY)(new Date(e.createdAt))]})]})]})}),n.jsx("div",{className:"bg-muted rounded-lg p-3",children:n.jsx("pre",{className:"text-xs font-mono overflow-x-auto whitespace-pre-wrap",children:n.jsx("code",{children:e.generatedSQL})})}),e.explanation&&(0,n.jsxs)("div",{className:"text-sm text-muted-foreground",children:[n.jsx("strong",{children:"Explanation:"})," ",e.explanation]}),e.executionTime&&(0,n.jsxs)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[(0,n.jsxs)("span",{children:["Execution: ",e.executionTime,"ms"]}),e.rowsAffected&&(0,n.jsxs)("span",{children:["Rows: ",e.rowsAffected]}),e.userFeedback&&(0,n.jsxs)("div",{className:"flex items-center gap-1",children:[n.jsx("span",{children:"Rating:"}),n.jsx("div",{className:"flex",children:[void 0,void 0,void 0,void 0,void 0].map((t,r)=>n.jsx(h,{className:`h-3 w-3 ${r<e.userFeedback?"text-yellow-500 fill-current":"text-gray-300"}`},r))})]})]}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>x(e.generatedSQL),children:[n.jsx(ta,{className:"h-3 w-3 mr-1"}),"Use"]}),(0,n.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>g(e.generatedSQL),children:[n.jsx(p,{className:"h-3 w-3 mr-1"}),"Copy"]})]})]})})},e.id))})})})]})}/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let ti=(0,f.Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]),ts=(0,f.Z)("Share",[["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["polyline",{points:"16 6 12 2 8 6",key:"m901s6"}],["line",{x1:"12",x2:"12",y1:"2",y2:"15",key:"1p0rca"}]]);function tl(){let{activeTab:e,setActiveTab:t,currentQuery:r,setCurrentQuery:s,currentDatabase:l}=(0,y.l)(),[c,f]=(0,a.useState)(!1),m=[{id:"chat",label:"AI Assistant",icon:u.Z},{id:"editor",label:"SQL Editor",icon:em.Z},{id:"results",label:"Results",icon:ej},{id:"history",label:"History",icon:tn.Z}];return(0,n.jsxs)("div",{className:"flex h-full",children:[(0,n.jsxs)("div",{className:"w-80 border-r border-border bg-card",children:[(0,n.jsxs)("div",{className:"p-4 border-b border-border",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[n.jsx("h3",{className:"font-semibold",children:"Database Schema"}),n.jsx(d,{variant:c?"default":"secondary",children:c?"Connected":"Not Connected"})]}),!c&&(0,n.jsxs)(i.z,{variant:"outline",size:"sm",className:"w-full",onClick:()=>f(!0),children:[n.jsx(em.Z,{className:"h-4 w-4 mr-2"}),"Connect Database"]})]}),n.jsx("div",{className:"flex-1 overflow-auto",children:n.jsx(ev,{isConnected:c})})]}),(0,n.jsxs)("div",{className:"flex-1 flex flex-col",children:[n.jsx("div",{className:"border-b border-border bg-card",children:(0,n.jsxs)("div",{className:"flex items-center justify-between px-6 py-3",children:[n.jsx("div",{className:"flex space-x-1",children:m.map(r=>{let a=r.icon;return(0,n.jsxs)("button",{onClick:()=>t(r.id),className:`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${e===r.id?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-muted"}`,children:[n.jsx(a,{className:"h-4 w-4"}),r.label]},r.id)})}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)(i.z,{variant:"outline",size:"sm",children:[n.jsx(tn.Z,{className:"h-4 w-4 mr-2"}),"History"]}),(0,n.jsxs)(i.z,{variant:"outline",size:"sm",children:[n.jsx(ti,{className:"h-4 w-4 mr-2"}),"Save"]}),(0,n.jsxs)(i.z,{variant:"outline",size:"sm",children:[n.jsx(ts,{className:"h-4 w-4 mr-2"}),"Share"]}),"editor"===e&&(0,n.jsxs)(i.z,{size:"sm",children:[n.jsx(ta,{className:"h-4 w-4 mr-2"}),"Run Query"]})]})]})}),(0,n.jsxs)("div",{className:"flex-1 overflow-hidden",children:["chat"===e&&n.jsx(b,{onQueryGenerated:e=>{s(e)}}),"editor"===e&&n.jsx("div",{className:"h-full flex flex-col",children:n.jsx("div",{className:"flex-1 p-6",children:n.jsx(ef,{value:r,onChange:s,language:l?.databaseType||"postgresql"})})}),"results"===e&&n.jsx("div",{className:"h-full p-6",children:n.jsx(eN,{})}),"history"===e&&n.jsx("div",{className:"h-full p-6",children:n.jsx(to,{})})]})]}),(0,n.jsxs)("div",{className:"w-80 border-l border-border bg-card",children:[n.jsx("div",{className:"p-4 border-b border-border",children:(0,n.jsxs)("h3",{className:"font-semibold flex items-center gap-2",children:[n.jsx(eu,{className:"h-4 w-4"}),"AI Insights"]})}),(0,n.jsxs)("div",{className:"p-4 space-y-4",children:[(0,n.jsxs)(o.Zb,{children:[n.jsx(o.Ol,{className:"pb-3",children:n.jsx(o.ll,{className:"text-sm",children:"Query Suggestions"})}),n.jsx(o.aY,{className:"space-y-2",children:n.jsx("div",{className:"text-sm text-muted-foreground",children:"No active query to analyze. Start by describing what you want to query in the AI Assistant."})})]}),(0,n.jsxs)(o.Zb,{children:[n.jsx(o.Ol,{className:"pb-3",children:n.jsx(o.ll,{className:"text-sm",children:"Performance Tips"})}),n.jsx(o.aY,{className:"space-y-2",children:n.jsx("div",{className:"text-sm text-muted-foreground",children:"Connect to a database to get performance recommendations."})})]}),(0,n.jsxs)(o.Zb,{children:[n.jsx(o.Ol,{className:"pb-3",children:n.jsx(o.ll,{className:"text-sm",children:"Recent Activity"})}),n.jsx(o.aY,{className:"space-y-2",children:n.jsx("div",{className:"text-sm text-muted-foreground",children:"No recent queries found."})})]})]})]})]})}},5094:(e,t,r)=>{"use strict";r.d(t,{z:()=>c});var n=r(95344),a=r(3729),o=r(32751),i=r(49247),s=r(11453);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...i},c)=>{let d=a?o.g7:"button";return n.jsx(d,{className:(0,s.cn)(l({variant:t,size:r,className:e})),ref:c,...i})});c.displayName="Button"},23673:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>s,Zb:()=>i,aY:()=>c,ll:()=>l});var n=r(95344),a=r(3729),o=r(11453);let i=a.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let s=a.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",e),...t}));s.displayName="CardHeader";let l=a.forwardRef(({className:e,...t},r)=>n.jsx("h3",{ref:r,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle",a.forwardRef(({className:e,...t},r)=>n.jsx("p",{ref:r,className:(0,o.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription";let c=a.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:(0,o.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent",a.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:(0,o.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},69194:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p,metadata:()=>m});var n=r(25036),a=r(86843);let o=(0,a.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/dashboard-layout.tsx`),{__esModule:i,$$typeof:s}=o;o.default;let l=(0,a.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/dashboard-layout.tsx#DashboardLayout`),c=(0,a.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx`),{__esModule:d,$$typeof:u}=c;c.default;let f=(0,a.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx#QueryWorkspace`),m={title:"Dashboard - QueryCraft Studio",description:"AI-powered SQL development workspace"};function p(){return n.jsx(l,{children:n.jsx(f,{})})}},3975:(e,t,r)=>{"use strict";r.d(t,{gm:()=>o});var n=r(3729);r(95344);var a=n.createContext(void 0);function o(e){let t=n.useContext(a);return e||t||"ltr"}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[638,175,476,162],()=>r(9334));module.exports=n})();