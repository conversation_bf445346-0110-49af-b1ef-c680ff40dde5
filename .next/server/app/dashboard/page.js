(()=>{var e={};e.id=702,e.ids=[702],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9334:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>f,tree:()=>c});var n=t(50482),s=t(69108),a=t(62563),i=t.n(a),o=t(68300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let c=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,69194)),"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/dashboard/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,98890)),"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/dashboard/page.tsx"],u="/dashboard/page",m={require:t,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},36950:(e,r,t)=>{Promise.resolve().then(t.bind(t,42341)),Promise.resolve().then(t.bind(t,94840))},97909:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(69224).Z)("BrainCircuit",[["path",{d:"M12 4.5a2.5 2.5 0 0 0-4.96-.46 2.5 2.5 0 0 0-1.98 3 2.5 2.5 0 0 0-1.32 4.24 3 3 0 0 0 .34 5.58 2.5 2.5 0 0 0 2.96 3.08 2.5 2.5 0 0 0 4.91.05L12 20V4.5Z",key:"ixwj2a"}],["path",{d:"M16 8V5c0-1.1.9-2 2-2",key:"13dx7u"}],["path",{d:"M12 13h4",key:"1ku699"}],["path",{d:"M12 18h6a2 2 0 0 1 2 2v1",key:"105ag5"}],["path",{d:"M12 8h8",key:"1lhi5i"}],["path",{d:"M20.5 8a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"1s25gz"}],["path",{d:"M16.5 13a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"127460"}],["path",{d:"M20.5 21a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"fys062"}],["path",{d:"M18.5 3a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"1vib61"}]])},96213:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(69224).Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},20783:(e,r,t)=>{e.exports=t(61476)},42341:(e,r,t)=>{"use strict";t.r(r),t.d(r,{DashboardLayout:()=>rW});var n=t(95344),s=t(3729),a=t(5094),i=t(47674),o=t(98462),l=t(2256),c=t(16069),d=t(62409),u=t(8145);function m(){return()=>{}}var f="Avatar",[x,p]=(0,o.b)(f),[h,g]=x(f),y=s.forwardRef((e,r)=>{let{__scopeAvatar:t,...a}=e,[i,o]=s.useState("idle");return(0,n.jsx)(h,{scope:t,imageLoadingStatus:i,onImageLoadingStatusChange:o,children:(0,n.jsx)(d.WV.span,{...a,ref:r})})});y.displayName=f;var v="AvatarImage",j=s.forwardRef((e,r)=>{let{__scopeAvatar:t,src:a,onLoadingStatusChange:i=()=>{},...o}=e,f=g(v,t),x=function(e,{referrerPolicy:r,crossOrigin:t}){let n=(0,u.useSyncExternalStore)(m,()=>!0,()=>!1),a=s.useRef(null),i=n?(a.current||(a.current=new window.Image),a.current):null,[o,l]=s.useState(()=>N(i,e));return(0,c.b)(()=>{l(N(i,e))},[i,e]),(0,c.b)(()=>{let e=e=>()=>{l(e)};if(!i)return;let n=e("loaded"),s=e("error");return i.addEventListener("load",n),i.addEventListener("error",s),r&&(i.referrerPolicy=r),"string"==typeof t&&(i.crossOrigin=t),()=>{i.removeEventListener("load",n),i.removeEventListener("error",s)}},[i,t,r]),o}(a,o),p=(0,l.W)(e=>{i(e),f.onImageLoadingStatusChange(e)});return(0,c.b)(()=>{"idle"!==x&&p(x)},[x,p]),"loaded"===x?(0,n.jsx)(d.WV.img,{...o,ref:r,src:a}):null});j.displayName=v;var b="AvatarFallback",w=s.forwardRef((e,r)=>{let{__scopeAvatar:t,delayMs:a,...i}=e,o=g(b,t),[l,c]=s.useState(void 0===a);return s.useEffect(()=>{if(void 0!==a){let e=window.setTimeout(()=>c(!0),a);return()=>window.clearTimeout(e)}},[a]),l&&"loaded"!==o.imageLoadingStatus?(0,n.jsx)(d.WV.span,{...i,ref:r}):null});function N(e,r){return e?r?(e.src!==r&&(e.src=r),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}w.displayName=b;var C=t(11453);let k=s.forwardRef(({className:e,...r},t)=>n.jsx(y,{ref:t,className:(0,C.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...r}));k.displayName=y.displayName;let M=s.forwardRef(({className:e,...r},t)=>n.jsx(j,{ref:t,className:(0,C.cn)("aspect-square h-full w-full",e),...r}));M.displayName=j.displayName;let R=s.forwardRef(({className:e,...r},t)=>n.jsx(w,{ref:t,className:(0,C.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...r}));R.displayName=w.displayName;var S=t(85222),E=t(31405),P=t(33183),O=t(77411),I=t(3975),T=t(44155),D=t(1106),_=t(27386),A=t(99048),L=t(37574),Z=t(31179),z=t(43234),q="rovingFocusGroup.onEntryFocus",K={bubbles:!1,cancelable:!0},F="RovingFocusGroup",[V,U,W]=(0,O.B)(F),[B,H]=(0,o.b)(F,[W]),[G,Q]=B(F),Y=s.forwardRef((e,r)=>(0,n.jsx)(V.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,n.jsx)(V.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,n.jsx)($,{...e,ref:r})})}));Y.displayName=F;var $=s.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,orientation:a,loop:i=!1,dir:o,currentTabStopId:c,defaultCurrentTabStopId:u,onCurrentTabStopIdChange:m,onEntryFocus:f,preventScrollOnEntryFocus:x=!1,...p}=e,h=s.useRef(null),g=(0,E.e)(r,h),y=(0,I.gm)(o),[v,j]=(0,P.T)({prop:c,defaultProp:u??null,onChange:m,caller:F}),[b,w]=s.useState(!1),N=(0,l.W)(f),C=U(t),k=s.useRef(!1),[M,R]=s.useState(0);return s.useEffect(()=>{let e=h.current;if(e)return e.addEventListener(q,N),()=>e.removeEventListener(q,N)},[N]),(0,n.jsx)(G,{scope:t,orientation:a,dir:y,loop:i,currentTabStopId:v,onItemFocus:s.useCallback(e=>j(e),[j]),onItemShiftTab:s.useCallback(()=>w(!0),[]),onFocusableItemAdd:s.useCallback(()=>R(e=>e+1),[]),onFocusableItemRemove:s.useCallback(()=>R(e=>e-1),[]),children:(0,n.jsx)(d.WV.div,{tabIndex:b||0===M?-1:0,"data-orientation":a,...p,ref:g,style:{outline:"none",...e.style},onMouseDown:(0,S.M)(e.onMouseDown,()=>{k.current=!0}),onFocus:(0,S.M)(e.onFocus,e=>{let r=!k.current;if(e.target===e.currentTarget&&r&&!b){let r=new CustomEvent(q,K);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=C().filter(e=>e.focusable);er([e.find(e=>e.active),e.find(e=>e.id===v),...e].filter(Boolean).map(e=>e.ref.current),x)}}k.current=!1}),onBlur:(0,S.M)(e.onBlur,()=>w(!1))})})}),X="RovingFocusGroupItem",J=s.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,focusable:a=!0,active:i=!1,tabStopId:o,children:l,...c}=e,u=(0,A.M)(),m=o||u,f=Q(X,t),x=f.currentTabStopId===m,p=U(t),{onFocusableItemAdd:h,onFocusableItemRemove:g,currentTabStopId:y}=f;return s.useEffect(()=>{if(a)return h(),()=>g()},[a,h,g]),(0,n.jsx)(V.ItemSlot,{scope:t,id:m,focusable:a,active:i,children:(0,n.jsx)(d.WV.span,{tabIndex:x?0:-1,"data-orientation":f.orientation,...c,ref:r,onMouseDown:(0,S.M)(e.onMouseDown,e=>{a?f.onItemFocus(m):e.preventDefault()}),onFocus:(0,S.M)(e.onFocus,()=>f.onItemFocus(m)),onKeyDown:(0,S.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){f.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let r=function(e,r,t){var n;let s=(n=e.key,"rtl"!==t?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(s))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(s)))return ee[s]}(e,f.orientation,f.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let t=p().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)t.reverse();else if("prev"===r||"next"===r){"prev"===r&&t.reverse();let n=t.indexOf(e.currentTarget);t=f.loop?function(e,r){return e.map((t,n)=>e[(r+n)%e.length])}(t,n+1):t.slice(n+1)}setTimeout(()=>er(t))}}),children:"function"==typeof l?l({isCurrentTabStop:x,hasTabStop:null!=y}):l})})});J.displayName=X;var ee={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function er(e,r=!1){let t=document.activeElement;for(let n of e)if(n===t||(n.focus({preventScroll:r}),document.activeElement!==t))return}var et=t(32751),en=t(45904),es=t(71210),ea=["Enter"," "],ei=["ArrowUp","PageDown","End"],eo=["ArrowDown","PageUp","Home",...ei],el={ltr:[...ea,"ArrowRight"],rtl:[...ea,"ArrowLeft"]},ec={ltr:["ArrowLeft"],rtl:["ArrowRight"]},ed="Menu",[eu,em,ef]=(0,O.B)(ed),[ex,ep]=(0,o.b)(ed,[ef,L.D7,H]),eh=(0,L.D7)(),eg=H(),[ey,ev]=ex(ed),[ej,eb]=ex(ed),ew=e=>{let{__scopeMenu:r,open:t=!1,children:a,dir:i,onOpenChange:o,modal:c=!0}=e,d=eh(r),[u,m]=s.useState(null),f=s.useRef(!1),x=(0,l.W)(o),p=(0,I.gm)(i);return s.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,n.jsx)(L.fC,{...d,children:(0,n.jsx)(ey,{scope:r,open:t,onOpenChange:x,content:u,onContentChange:m,children:(0,n.jsx)(ej,{scope:r,onClose:s.useCallback(()=>x(!1),[x]),isUsingKeyboardRef:f,dir:p,modal:c,children:a})})})};ew.displayName=ed;var eN=s.forwardRef((e,r)=>{let{__scopeMenu:t,...s}=e,a=eh(t);return(0,n.jsx)(L.ee,{...a,...s,ref:r})});eN.displayName="MenuAnchor";var eC="MenuPortal",[ek,eM]=ex(eC,{forceMount:void 0}),eR=e=>{let{__scopeMenu:r,forceMount:t,children:s,container:a}=e,i=ev(eC,r);return(0,n.jsx)(ek,{scope:r,forceMount:t,children:(0,n.jsx)(z.z,{present:t||i.open,children:(0,n.jsx)(Z.h,{asChild:!0,container:a,children:s})})})};eR.displayName=eC;var eS="MenuContent",[eE,eP]=ex(eS),eO=s.forwardRef((e,r)=>{let t=eM(eS,e.__scopeMenu),{forceMount:s=t.forceMount,...a}=e,i=ev(eS,e.__scopeMenu),o=eb(eS,e.__scopeMenu);return(0,n.jsx)(eu.Provider,{scope:e.__scopeMenu,children:(0,n.jsx)(z.z,{present:s||i.open,children:(0,n.jsx)(eu.Slot,{scope:e.__scopeMenu,children:o.modal?(0,n.jsx)(eI,{...a,ref:r}):(0,n.jsx)(eT,{...a,ref:r})})})})}),eI=s.forwardRef((e,r)=>{let t=ev(eS,e.__scopeMenu),a=s.useRef(null),i=(0,E.e)(r,a);return s.useEffect(()=>{let e=a.current;if(e)return(0,en.Ry)(e)},[]),(0,n.jsx)(e_,{...e,ref:i,trapFocus:t.open,disableOutsidePointerEvents:t.open,disableOutsideScroll:!0,onFocusOutside:(0,S.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>t.onOpenChange(!1)})}),eT=s.forwardRef((e,r)=>{let t=ev(eS,e.__scopeMenu);return(0,n.jsx)(e_,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>t.onOpenChange(!1)})}),eD=(0,et.Z8)("MenuContent.ScrollLock"),e_=s.forwardRef((e,r)=>{let{__scopeMenu:t,loop:a=!1,trapFocus:i,onOpenAutoFocus:o,onCloseAutoFocus:l,disableOutsidePointerEvents:c,onEntryFocus:d,onEscapeKeyDown:u,onPointerDownOutside:m,onFocusOutside:f,onInteractOutside:x,onDismiss:p,disableOutsideScroll:h,...g}=e,y=ev(eS,t),v=eb(eS,t),j=eh(t),b=eg(t),w=em(t),[N,C]=s.useState(null),k=s.useRef(null),M=(0,E.e)(r,k,y.onContentChange),R=s.useRef(0),P=s.useRef(""),O=s.useRef(0),I=s.useRef(null),A=s.useRef("right"),Z=s.useRef(0),z=h?es.Z:s.Fragment,q=e=>{let r=P.current+e,t=w().filter(e=>!e.disabled),n=document.activeElement,s=t.find(e=>e.ref.current===n)?.textValue,a=function(e,r,t){var n;let s=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,a=(n=Math.max(t?e.indexOf(t):-1,0),e.map((r,t)=>e[(n+t)%e.length]));1===s.length&&(a=a.filter(e=>e!==t));let i=a.find(e=>e.toLowerCase().startsWith(s.toLowerCase()));return i!==t?i:void 0}(t.map(e=>e.textValue),r,s),i=t.find(e=>e.textValue===a)?.ref.current;(function e(r){P.current=r,window.clearTimeout(R.current),""!==r&&(R.current=window.setTimeout(()=>e(""),1e3))})(r),i&&setTimeout(()=>i.focus())};s.useEffect(()=>()=>window.clearTimeout(R.current),[]),(0,D.EW)();let K=s.useCallback(e=>A.current===I.current?.side&&function(e,r){return!!r&&function(e,r){let{x:t,y:n}=e,s=!1;for(let e=0,a=r.length-1;e<r.length;a=e++){let i=r[e],o=r[a],l=i.x,c=i.y,d=o.x,u=o.y;c>n!=u>n&&t<(d-l)*(n-c)/(u-c)+l&&(s=!s)}return s}({x:e.clientX,y:e.clientY},r)}(e,I.current?.area),[]);return(0,n.jsx)(eE,{scope:t,searchRef:P,onItemEnter:s.useCallback(e=>{K(e)&&e.preventDefault()},[K]),onItemLeave:s.useCallback(e=>{K(e)||(k.current?.focus(),C(null))},[K]),onTriggerLeave:s.useCallback(e=>{K(e)&&e.preventDefault()},[K]),pointerGraceTimerRef:O,onPointerGraceIntentChange:s.useCallback(e=>{I.current=e},[]),children:(0,n.jsx)(z,{...h?{as:eD,allowPinchZoom:!0}:void 0,children:(0,n.jsx)(_.M,{asChild:!0,trapped:i,onMountAutoFocus:(0,S.M)(o,e=>{e.preventDefault(),k.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:l,children:(0,n.jsx)(T.XB,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:u,onPointerDownOutside:m,onFocusOutside:f,onInteractOutside:x,onDismiss:p,children:(0,n.jsx)(Y,{asChild:!0,...b,dir:v.dir,orientation:"vertical",loop:a,currentTabStopId:N,onCurrentTabStopIdChange:C,onEntryFocus:(0,S.M)(d,e=>{v.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,n.jsx)(L.VY,{role:"menu","aria-orientation":"vertical","data-state":e8(y.open),"data-radix-menu-content":"",dir:v.dir,...j,...g,ref:M,style:{outline:"none",...g.style},onKeyDown:(0,S.M)(g.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,t=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!t&&n&&q(e.key));let s=k.current;if(e.target!==s||!eo.includes(e.key))return;e.preventDefault();let a=w().filter(e=>!e.disabled).map(e=>e.ref.current);ei.includes(e.key)&&a.reverse(),function(e){let r=document.activeElement;for(let t of e)if(t===r||(t.focus(),document.activeElement!==r))return}(a)}),onBlur:(0,S.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(R.current),P.current="")}),onPointerMove:(0,S.M)(e.onPointerMove,re(e=>{let r=e.target,t=Z.current!==e.clientX;if(e.currentTarget.contains(r)&&t){let r=e.clientX>Z.current?"right":"left";A.current=r,Z.current=e.clientX}}))})})})})})})});eO.displayName=eS;var eA=s.forwardRef((e,r)=>{let{__scopeMenu:t,...s}=e;return(0,n.jsx)(d.WV.div,{role:"group",...s,ref:r})});eA.displayName="MenuGroup";var eL=s.forwardRef((e,r)=>{let{__scopeMenu:t,...s}=e;return(0,n.jsx)(d.WV.div,{...s,ref:r})});eL.displayName="MenuLabel";var eZ="MenuItem",ez="menu.itemSelect",eq=s.forwardRef((e,r)=>{let{disabled:t=!1,onSelect:a,...i}=e,o=s.useRef(null),l=eb(eZ,e.__scopeMenu),c=eP(eZ,e.__scopeMenu),u=(0,E.e)(r,o),m=s.useRef(!1);return(0,n.jsx)(eK,{...i,ref:u,disabled:t,onClick:(0,S.M)(e.onClick,()=>{let e=o.current;if(!t&&e){let r=new CustomEvent(ez,{bubbles:!0,cancelable:!0});e.addEventListener(ez,e=>a?.(e),{once:!0}),(0,d.jH)(e,r),r.defaultPrevented?m.current=!1:l.onClose()}}),onPointerDown:r=>{e.onPointerDown?.(r),m.current=!0},onPointerUp:(0,S.M)(e.onPointerUp,e=>{m.current||e.currentTarget?.click()}),onKeyDown:(0,S.M)(e.onKeyDown,e=>{let r=""!==c.searchRef.current;!t&&(!r||" "!==e.key)&&ea.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eq.displayName=eZ;var eK=s.forwardRef((e,r)=>{let{__scopeMenu:t,disabled:a=!1,textValue:i,...o}=e,l=eP(eZ,t),c=eg(t),u=s.useRef(null),m=(0,E.e)(r,u),[f,x]=s.useState(!1),[p,h]=s.useState("");return s.useEffect(()=>{let e=u.current;e&&h((e.textContent??"").trim())},[o.children]),(0,n.jsx)(eu.ItemSlot,{scope:t,disabled:a,textValue:i??p,children:(0,n.jsx)(J,{asChild:!0,...c,focusable:!a,children:(0,n.jsx)(d.WV.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...o,ref:m,onPointerMove:(0,S.M)(e.onPointerMove,re(e=>{a?l.onItemLeave(e):(l.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,S.M)(e.onPointerLeave,re(e=>l.onItemLeave(e))),onFocus:(0,S.M)(e.onFocus,()=>x(!0)),onBlur:(0,S.M)(e.onBlur,()=>x(!1))})})})}),eF=s.forwardRef((e,r)=>{let{checked:t=!1,onCheckedChange:s,...a}=e;return(0,n.jsx)(eY,{scope:e.__scopeMenu,checked:t,children:(0,n.jsx)(eq,{role:"menuitemcheckbox","aria-checked":e7(t)?"mixed":t,...a,ref:r,"data-state":e6(t),onSelect:(0,S.M)(a.onSelect,()=>s?.(!!e7(t)||!t),{checkForDefaultPrevented:!1})})})});eF.displayName="MenuCheckboxItem";var eV="MenuRadioGroup",[eU,eW]=ex(eV,{value:void 0,onValueChange:()=>{}}),eB=s.forwardRef((e,r)=>{let{value:t,onValueChange:s,...a}=e,i=(0,l.W)(s);return(0,n.jsx)(eU,{scope:e.__scopeMenu,value:t,onValueChange:i,children:(0,n.jsx)(eA,{...a,ref:r})})});eB.displayName=eV;var eH="MenuRadioItem",eG=s.forwardRef((e,r)=>{let{value:t,...s}=e,a=eW(eH,e.__scopeMenu),i=t===a.value;return(0,n.jsx)(eY,{scope:e.__scopeMenu,checked:i,children:(0,n.jsx)(eq,{role:"menuitemradio","aria-checked":i,...s,ref:r,"data-state":e6(i),onSelect:(0,S.M)(s.onSelect,()=>a.onValueChange?.(t),{checkForDefaultPrevented:!1})})})});eG.displayName=eH;var eQ="MenuItemIndicator",[eY,e$]=ex(eQ,{checked:!1}),eX=s.forwardRef((e,r)=>{let{__scopeMenu:t,forceMount:s,...a}=e,i=e$(eQ,t);return(0,n.jsx)(z.z,{present:s||e7(i.checked)||!0===i.checked,children:(0,n.jsx)(d.WV.span,{...a,ref:r,"data-state":e6(i.checked)})})});eX.displayName=eQ;var eJ=s.forwardRef((e,r)=>{let{__scopeMenu:t,...s}=e;return(0,n.jsx)(d.WV.div,{role:"separator","aria-orientation":"horizontal",...s,ref:r})});eJ.displayName="MenuSeparator";var e0=s.forwardRef((e,r)=>{let{__scopeMenu:t,...s}=e,a=eh(t);return(0,n.jsx)(L.Eh,{...a,...s,ref:r})});e0.displayName="MenuArrow";var[e1,e2]=ex("MenuSub"),e4="MenuSubTrigger",e3=s.forwardRef((e,r)=>{let t=ev(e4,e.__scopeMenu),a=eb(e4,e.__scopeMenu),i=e2(e4,e.__scopeMenu),o=eP(e4,e.__scopeMenu),l=s.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=o,u={__scopeMenu:e.__scopeMenu},m=s.useCallback(()=>{l.current&&window.clearTimeout(l.current),l.current=null},[]);return s.useEffect(()=>m,[m]),s.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,n.jsx)(eN,{asChild:!0,...u,children:(0,n.jsx)(eK,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":t.open,"aria-controls":i.contentId,"data-state":e8(t.open),...e,ref:(0,E.F)(r,i.onTriggerChange),onClick:r=>{e.onClick?.(r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),t.open||t.onOpenChange(!0))},onPointerMove:(0,S.M)(e.onPointerMove,re(r=>{o.onItemEnter(r),r.defaultPrevented||e.disabled||t.open||l.current||(o.onPointerGraceIntentChange(null),l.current=window.setTimeout(()=>{t.onOpenChange(!0),m()},100))})),onPointerLeave:(0,S.M)(e.onPointerLeave,re(e=>{m();let r=t.content?.getBoundingClientRect();if(r){let n=t.content?.dataset.side,s="right"===n,a=r[s?"left":"right"],i=r[s?"right":"left"];o.onPointerGraceIntentChange({area:[{x:e.clientX+(s?-5:5),y:e.clientY},{x:a,y:r.top},{x:i,y:r.top},{x:i,y:r.bottom},{x:a,y:r.bottom}],side:n}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>o.onPointerGraceIntentChange(null),300)}else{if(o.onTriggerLeave(e),e.defaultPrevented)return;o.onPointerGraceIntentChange(null)}})),onKeyDown:(0,S.M)(e.onKeyDown,r=>{let n=""!==o.searchRef.current;!e.disabled&&(!n||" "!==r.key)&&el[a.dir].includes(r.key)&&(t.onOpenChange(!0),t.content?.focus(),r.preventDefault())})})})});e3.displayName=e4;var e5="MenuSubContent",e9=s.forwardRef((e,r)=>{let t=eM(eS,e.__scopeMenu),{forceMount:a=t.forceMount,...i}=e,o=ev(eS,e.__scopeMenu),l=eb(eS,e.__scopeMenu),c=e2(e5,e.__scopeMenu),d=s.useRef(null),u=(0,E.e)(r,d);return(0,n.jsx)(eu.Provider,{scope:e.__scopeMenu,children:(0,n.jsx)(z.z,{present:a||o.open,children:(0,n.jsx)(eu.Slot,{scope:e.__scopeMenu,children:(0,n.jsx)(e_,{id:c.contentId,"aria-labelledby":c.triggerId,...i,ref:u,align:"start",side:"rtl"===l.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{l.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,S.M)(e.onFocusOutside,e=>{e.target!==c.trigger&&o.onOpenChange(!1)}),onEscapeKeyDown:(0,S.M)(e.onEscapeKeyDown,e=>{l.onClose(),e.preventDefault()}),onKeyDown:(0,S.M)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),t=ec[l.dir].includes(e.key);r&&t&&(o.onOpenChange(!1),c.trigger?.focus(),e.preventDefault())})})})})})});function e8(e){return e?"open":"closed"}function e7(e){return"indeterminate"===e}function e6(e){return e7(e)?"indeterminate":e?"checked":"unchecked"}function re(e){return r=>"mouse"===r.pointerType?e(r):void 0}e9.displayName=e5;var rr="DropdownMenu",[rt,rn]=(0,o.b)(rr,[ep]),rs=ep(),[ra,ri]=rt(rr),ro=e=>{let{__scopeDropdownMenu:r,children:t,dir:a,open:i,defaultOpen:o,onOpenChange:l,modal:c=!0}=e,d=rs(r),u=s.useRef(null),[m,f]=(0,P.T)({prop:i,defaultProp:o??!1,onChange:l,caller:rr});return(0,n.jsx)(ra,{scope:r,triggerId:(0,A.M)(),triggerRef:u,contentId:(0,A.M)(),open:m,onOpenChange:f,onOpenToggle:s.useCallback(()=>f(e=>!e),[f]),modal:c,children:(0,n.jsx)(ew,{...d,open:m,onOpenChange:f,dir:a,modal:c,children:t})})};ro.displayName=rr;var rl="DropdownMenuTrigger",rc=s.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,disabled:s=!1,...a}=e,i=ri(rl,t),o=rs(t);return(0,n.jsx)(eN,{asChild:!0,...o,children:(0,n.jsx)(d.WV.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":s?"":void 0,disabled:s,...a,ref:(0,E.F)(r,i.triggerRef),onPointerDown:(0,S.M)(e.onPointerDown,e=>{s||0!==e.button||!1!==e.ctrlKey||(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,S.M)(e.onKeyDown,e=>{!s&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});rc.displayName=rl;var rd=e=>{let{__scopeDropdownMenu:r,...t}=e,s=rs(r);return(0,n.jsx)(eR,{...s,...t})};rd.displayName="DropdownMenuPortal";var ru="DropdownMenuContent",rm=s.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...a}=e,i=ri(ru,t),o=rs(t),l=s.useRef(!1);return(0,n.jsx)(eO,{id:i.contentId,"aria-labelledby":i.triggerId,...o,...a,ref:r,onCloseAutoFocus:(0,S.M)(e.onCloseAutoFocus,e=>{l.current||i.triggerRef.current?.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,S.M)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,t=0===r.button&&!0===r.ctrlKey,n=2===r.button||t;(!i.modal||n)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});rm.displayName=ru,s.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...s}=e,a=rs(t);return(0,n.jsx)(eA,{...a,...s,ref:r})}).displayName="DropdownMenuGroup";var rf=s.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...s}=e,a=rs(t);return(0,n.jsx)(eL,{...a,...s,ref:r})});rf.displayName="DropdownMenuLabel";var rx=s.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...s}=e,a=rs(t);return(0,n.jsx)(eq,{...a,...s,ref:r})});rx.displayName="DropdownMenuItem";var rp=s.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...s}=e,a=rs(t);return(0,n.jsx)(eF,{...a,...s,ref:r})});rp.displayName="DropdownMenuCheckboxItem",s.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...s}=e,a=rs(t);return(0,n.jsx)(eB,{...a,...s,ref:r})}).displayName="DropdownMenuRadioGroup";var rh=s.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...s}=e,a=rs(t);return(0,n.jsx)(eG,{...a,...s,ref:r})});rh.displayName="DropdownMenuRadioItem";var rg=s.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...s}=e,a=rs(t);return(0,n.jsx)(eX,{...a,...s,ref:r})});rg.displayName="DropdownMenuItemIndicator";var ry=s.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...s}=e,a=rs(t);return(0,n.jsx)(eJ,{...a,...s,ref:r})});ry.displayName="DropdownMenuSeparator",s.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...s}=e,a=rs(t);return(0,n.jsx)(e0,{...a,...s,ref:r})}).displayName="DropdownMenuArrow";var rv=s.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...s}=e,a=rs(t);return(0,n.jsx)(e3,{...a,...s,ref:r})});rv.displayName="DropdownMenuSubTrigger";var rj=s.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...s}=e,a=rs(t);return(0,n.jsx)(e9,{...a,...s,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});rj.displayName="DropdownMenuSubContent";var rb=t(97751),rw=t(62312),rN=t(69224);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let rC=(0,rN.Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);s.forwardRef(({className:e,inset:r,children:t,...s},a)=>(0,n.jsxs)(rv,{ref:a,className:(0,C.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",r&&"pl-8",e),...s,children:[t,n.jsx(rb.Z,{className:"ml-auto h-4 w-4"})]})).displayName=rv.displayName,s.forwardRef(({className:e,...r},t)=>n.jsx(rj,{ref:t,className:(0,C.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})).displayName=rj.displayName;let rk=s.forwardRef(({className:e,sideOffset:r=4,...t},s)=>n.jsx(rd,{children:n.jsx(rm,{ref:s,sideOffset:r,className:(0,C.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})}));rk.displayName=rm.displayName;let rM=s.forwardRef(({className:e,inset:r,...t},s)=>n.jsx(rx,{ref:s,className:(0,C.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r&&"pl-8",e),...t}));rM.displayName=rx.displayName,s.forwardRef(({className:e,children:r,checked:t,...s},a)=>(0,n.jsxs)(rp,{ref:a,className:(0,C.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:t,...s,children:[n.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:n.jsx(rg,{children:n.jsx(rw.Z,{className:"h-4 w-4"})})}),r]})).displayName=rp.displayName,s.forwardRef(({className:e,children:r,...t},s)=>(0,n.jsxs)(rh,{ref:s,className:(0,C.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[n.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:n.jsx(rg,{children:n.jsx(rC,{className:"h-2 w-2 fill-current"})})}),r]})).displayName=rh.displayName;let rR=s.forwardRef(({className:e,inset:r,...t},s)=>n.jsx(rf,{ref:s,className:(0,C.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",e),...t}));rR.displayName=rf.displayName;let rS=s.forwardRef(({className:e,...r},t)=>n.jsx(ry,{ref:t,className:(0,C.cn)("-mx-1 my-1 h-px bg-muted",e),...r}));rS.displayName=ry.displayName;/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let rE=(0,rN.Z)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]);var rP=t(18822);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let rO=(0,rN.Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);var rI=t(13746);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let rT=(0,rN.Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);function rD(){let{data:e,status:r}=(0,i.useSession)();if("loading"===r)return n.jsx("div",{className:"flex items-center space-x-2",children:n.jsx("div",{className:"h-8 w-8 bg-muted rounded-full animate-pulse"})});if(!e)return(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsxs)(a.z,{variant:"ghost",onClick:()=>(0,i.signIn)(),children:[n.jsx(rE,{className:"h-4 w-4 mr-2"}),"Sign In"]}),n.jsx(a.z,{onClick:()=>(0,i.signIn)(),children:"Get Started"})]});let t=e.user?.name?.split(" ").map(e=>e[0]).join("").toUpperCase()||"U";return(0,n.jsxs)(ro,{children:[n.jsx(rc,{asChild:!0,children:n.jsx(a.z,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,n.jsxs)(k,{className:"h-8 w-8",children:[n.jsx(M,{src:e.user?.image||"",alt:e.user?.name||""}),n.jsx(R,{children:t})]})})}),(0,n.jsxs)(rk,{className:"w-56",align:"end",forceMount:!0,children:[n.jsx(rR,{className:"font-normal",children:(0,n.jsxs)("div",{className:"flex flex-col space-y-1",children:[n.jsx("p",{className:"text-sm font-medium leading-none",children:e.user?.name||"User"}),n.jsx("p",{className:"text-xs leading-none text-muted-foreground",children:e.user?.email})]})}),n.jsx(rS,{}),(0,n.jsxs)(rM,{children:[n.jsx(rP.Z,{className:"mr-2 h-4 w-4"}),n.jsx("span",{children:"Profile"})]}),(0,n.jsxs)(rM,{children:[n.jsx(rO,{className:"mr-2 h-4 w-4"}),n.jsx("span",{children:"Billing"})]}),(0,n.jsxs)(rM,{children:[n.jsx(rI.Z,{className:"mr-2 h-4 w-4"}),n.jsx("span",{children:"Settings"})]}),n.jsx(rS,{}),(0,n.jsxs)(rM,{onClick:()=>(0,i.signOut)(),children:[n.jsx(rT,{className:"mr-2 h-4 w-4"}),n.jsx("span",{children:"Log out"})]})]})]})}var r_=t(99046),rA=t(96213);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let rL=(0,rN.Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),rZ=(0,rN.Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]]);var rz=t(89895);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let rq=(0,rN.Z)("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var rK=t(14513);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let rF=(0,rN.Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);var rV=t(20783),rU=t.n(rV);function rW({children:e}){let[r,t]=(0,s.useState)(!0),i=[{name:"Query Workspace",href:"/dashboard",icon:r_.Z,current:!0},{name:"Query History",href:"/dashboard/history",icon:rA.Z,current:!1},{name:"Connections",href:"/dashboard/connections",icon:rL,current:!1},{name:"Documentation",href:"/dashboard/docs",icon:rZ,current:!1},{name:"Team",href:"/dashboard/team",icon:rz.Z,current:!1}],o=[{name:"Settings",href:"/dashboard/settings",icon:rI.Z},{name:"Help",href:"/dashboard/help",icon:rq},{name:"Profile",href:"/dashboard/profile",icon:rP.Z}];return(0,n.jsxs)("div",{className:"flex h-screen bg-background",children:[n.jsx("div",{className:`${r?"w-64":"w-16"} transition-all duration-300 ease-in-out`,children:(0,n.jsxs)("div",{className:"flex flex-col h-full bg-card border-r border-border",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-border",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[n.jsx(r_.Z,{className:"h-8 w-8 text-primary"}),r&&n.jsx("span",{className:"text-lg font-bold",children:"QueryCraft"})]}),n.jsx(a.z,{variant:"ghost",size:"icon",onClick:()=>t(!r),className:"h-8 w-8",children:r?n.jsx(rK.Z,{className:"h-4 w-4"}):n.jsx(rF,{className:"h-4 w-4"})})]}),n.jsx("nav",{className:"flex-1 p-4 space-y-2",children:i.map(e=>{let t=e.icon;return(0,n.jsxs)(rU(),{href:e.href,className:`flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ${e.current?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-muted"}`,children:[n.jsx(t,{className:"h-5 w-5 flex-shrink-0"}),r&&n.jsx("span",{children:e.name})]},e.name)})}),n.jsx("div",{className:"p-4 border-t border-border space-y-2",children:o.map(e=>{let t=e.icon;return(0,n.jsxs)(rU(),{href:e.href,className:"flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-muted transition-colors",children:[n.jsx(t,{className:"h-5 w-5 flex-shrink-0"}),r&&n.jsx("span",{children:e.name})]},e.name)})})]})}),(0,n.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[n.jsx("header",{className:"bg-card border-b border-border px-6 py-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[n.jsx("h1",{className:"text-2xl font-bold text-foreground",children:"SQL Workspace"}),n.jsx("p",{className:"text-sm text-muted-foreground",children:"Build, debug, and optimize your SQL queries with AI assistance"})]}),(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsxs)(a.z,{variant:"outline",size:"sm",children:[n.jsx(rL,{className:"h-4 w-4 mr-2"}),"New Connection"]}),n.jsx(rD,{})]})]})}),n.jsx("main",{className:"flex-1 overflow-hidden",children:e})]})]})}},94840:(e,r,t)=>{"use strict";t.r(r),t.d(r,{QueryWorkspace:()=>eS});var n=t(95344),s=t(3729),a=t(23673),i=t(5094),o=t(19591),l=t(97909),c=t(69224);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,c.Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]);var u=t(1960);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let m=(0,c.Z)("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z",key:"y3tblf"}]]),f=(0,c.Z)("ThumbsDown",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22h0a3.13 3.13 0 0 1-3-3.88Z",key:"s6e0r"}]]);var x=t(18822),p=t(36135),h=t(11453),g=t(51540);function y({onQueryGenerated:e}){let{messages:r,isAILoading:t,generateQuery:c,setActiveTab:y,saveQueryToDatabase:v}=(0,g.l)(),[j,b]=(0,s.useState)(""),w=(0,s.useRef)(null),N=(0,s.useRef)(null),C=()=>{w.current?.scrollIntoView({behavior:"smooth"})};(0,s.useEffect)(()=>{C()},[r]);let k=async()=>{j.trim()&&!t&&(b(""),await c(j))},M=e=>{navigator.clipboard.writeText(e)},R=async(r,t,n)=>{if(e(r),y("editor"),t&&n)try{await v(t,r,n)}catch(e){console.error("Failed to save query to database:",e)}};return(0,n.jsxs)("div",{className:"flex flex-col h-full",children:[(0,n.jsxs)("div",{className:"flex-1 overflow-y-auto p-6 space-y-4",children:[r.map(e=>(0,n.jsxs)("div",{className:`flex gap-3 ${"user"===e.role?"justify-end":"justify-start"}`,children:["assistant"===e.role&&n.jsx("div",{className:"flex-shrink-0",children:n.jsx("div",{className:"h-8 w-8 bg-primary rounded-full flex items-center justify-center",children:n.jsx(l.Z,{className:"h-4 w-4 text-primary-foreground"})})}),n.jsx("div",{className:`max-w-2xl ${"user"===e.role?"order-first":""}`,children:(0,n.jsxs)(a.Zb,{className:`p-4 ${"user"===e.role?"bg-primary text-primary-foreground":""}`,children:[(0,n.jsxs)("div",{className:"space-y-3",children:[n.jsx("div",{className:"text-sm",children:e.content}),"query"===e.type&&e.metadata?.sql&&(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"bg-muted rounded-lg p-3",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,n.jsxs)(o.C,{variant:"secondary",className:"text-xs",children:[n.jsx(d,{className:"h-3 w-3 mr-1"}),"Generated SQL"]}),n.jsx("div",{className:"flex gap-1",children:n.jsx(i.z,{variant:"ghost",size:"sm",onClick:()=>M(e.metadata.sql),children:n.jsx(u.Z,{className:"h-3 w-3"})})})]}),n.jsx("pre",{className:"text-xs font-mono overflow-x-auto",children:n.jsx("code",{children:e.metadata.sql})})]}),e.metadata.explanation&&(0,n.jsxs)("div",{className:"text-sm text-muted-foreground",children:[n.jsx("strong",{children:"Explanation:"})," ",e.metadata.explanation]}),(0,n.jsxs)("div",{className:"flex gap-2",children:[n.jsx(i.z,{size:"sm",onClick:()=>R(e.metadata.sql,e.metadata?.userInput,e.metadata?.explanation),children:"Use in Editor"}),(0,n.jsxs)(i.z,{variant:"outline",size:"sm",children:[n.jsx(m,{className:"h-3 w-3 mr-1"}),"Good"]}),(0,n.jsxs)(i.z,{variant:"outline",size:"sm",children:[n.jsx(f,{className:"h-3 w-3 mr-1"}),"Improve"]})]})]})]}),n.jsx("div",{className:"text-xs text-muted-foreground mt-2",children:(0,h.SY)(e.timestamp)})]})}),"user"===e.role&&n.jsx("div",{className:"flex-shrink-0",children:n.jsx("div",{className:"h-8 w-8 bg-muted rounded-full flex items-center justify-center",children:n.jsx(x.Z,{className:"h-4 w-4 text-muted-foreground"})})})]},e.id)),t&&(0,n.jsxs)("div",{className:"flex gap-3",children:[n.jsx("div",{className:"flex-shrink-0",children:n.jsx("div",{className:"h-8 w-8 bg-primary rounded-full flex items-center justify-center",children:n.jsx(l.Z,{className:"h-4 w-4 text-primary-foreground animate-pulse"})})}),n.jsx(a.Zb,{className:"p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[n.jsx("div",{className:"loading-spinner"}),n.jsx("span",{className:"text-sm text-muted-foreground",children:"AI is thinking..."})]})})]}),n.jsx("div",{ref:w})]}),(0,n.jsxs)("div",{className:"border-t border-border p-4",children:[(0,n.jsxs)("div",{className:"flex gap-3",children:[n.jsx("div",{className:"flex-1",children:n.jsx("textarea",{ref:N,value:j,onChange:e=>b(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),k())},placeholder:"Describe the data you want to query... (e.g., 'Show me the top 10 customers by revenue this month')",className:"querycraft-textarea resize-none",rows:3,disabled:t})}),n.jsx(i.z,{onClick:k,disabled:!j.trim()||t,size:"lg",children:n.jsx(p.Z,{className:"h-4 w-4"})})]}),n.jsx("div",{className:"text-xs text-muted-foreground mt-2",children:"Press Enter to send, Shift+Enter for new line"})]})]})}function v(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function j(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?v(Object(t),!0).forEach(function(r){var n;n=t[r],r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):v(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function b(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=Array(r);t<r;t++)n[t]=e[t];return n}function w(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function N(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?w(Object(t),!0).forEach(function(r){var n;n=t[r],r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):w(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function C(e){return function r(){for(var t=this,n=arguments.length,s=Array(n),a=0;a<n;a++)s[a]=arguments[a];return s.length>=e.length?e.apply(this,s):function(){for(var e=arguments.length,n=Array(e),a=0;a<e;a++)n[a]=arguments[a];return r.apply(t,[].concat(s,n))}}}function k(e){return({}).toString.call(e).includes("Object")}function M(e){return"function"==typeof e}var R=C(function(e,r){throw Error(e[r]||e.default)})({initialIsRequired:"initial state is required",initialType:"initial state should be an object",initialContent:"initial state shouldn't be an empty object",handlerType:"handler should be an object or a function",handlersType:"all handlers should be a functions",selectorType:"selector should be a function",changeType:"provided value of changes should be an object",changeField:'it seams you want to change a field in the state which is not specified in the "initial" state',default:"an unknown error accured in `state-local` package"}),S={changes:function(e,r){return k(r)||R("changeType"),Object.keys(r).some(function(r){return!Object.prototype.hasOwnProperty.call(e,r)})&&R("changeField"),r},selector:function(e){M(e)||R("selectorType")},handler:function(e){M(e)||k(e)||R("handlerType"),k(e)&&Object.values(e).some(function(e){return!M(e)})&&R("handlersType")},initial:function(e){e||R("initialIsRequired"),k(e)||R("initialType"),Object.keys(e).length||R("initialContent")}};function E(e,r){return M(r)?r(e.current):r}function P(e,r){return e.current=N(N({},e.current),r),r}function O(e,r,t){return M(r)?r(e.current):Object.keys(t).forEach(function(t){var n;return null===(n=r[t])||void 0===n?void 0:n.call(r,e.current[t])}),t}var I={configIsRequired:"the configuration object is required",configType:"the configuration object should be an object",default:"an unknown error accured in `@monaco-editor/loader` package",deprecation:"Deprecation warning!\n    You are using deprecated way of configuration.\n\n    Instead of using\n      monaco.config({ urls: { monacoBase: '...' } })\n    use\n      monaco.config({ paths: { vs: '...' } })\n\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\n  "},T=(function(e){return function r(){for(var t=this,n=arguments.length,s=Array(n),a=0;a<n;a++)s[a]=arguments[a];return s.length>=e.length?e.apply(this,s):function(){for(var e=arguments.length,n=Array(e),a=0;a<e;a++)n[a]=arguments[a];return r.apply(t,[].concat(s,n))}}})(function(e,r){throw Error(e[r]||e.default)})(I);let D={config:function(e){return e||T("configIsRequired"),({}).toString.call(e).includes("Object")||T("configType"),e.urls?(console.warn(I.deprecation),{paths:{vs:e.urls.monacoBase}}):e}},_=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return function(e){return r.reduceRight(function(e,r){return r(e)},e)}};var A={type:"cancelation",msg:"operation is manually canceled"};let L=function(e){var r=!1,t=new Promise(function(t,n){e.then(function(e){return r?n(A):t(e)}),e.catch(n)});return t.cancel=function(){return r=!0},t};var Z=function(e,r){return function(e){if(Array.isArray(e))return e}(e)||function(e,r){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var t=[],n=!0,s=!1,a=void 0;try{for(var i,o=e[Symbol.iterator]();!(n=(i=o.next()).done)&&(t.push(i.value),!r||t.length!==r);n=!0);}catch(e){s=!0,a=e}finally{try{n||null==o.return||o.return()}finally{if(s)throw a}}return t}}(e,r)||function(e,r){if(e){if("string"==typeof e)return b(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);if("Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return b(e,r)}}(e,r)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(({create:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};S.initial(e),S.handler(r);var t={current:e},n=C(O)(t,r),s=C(P)(t),a=C(S.changes)(e),i=C(E)(t);return[function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(e){return e};return S.selector(e),e(t.current)},function(e){(function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return function(e){return r.reduceRight(function(e,r){return r(e)},e)}})(n,s,a,i)(e)}]}}).create({config:{paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}},isInitialized:!1,resolve:null,reject:null,monaco:null}),2),z=Z[0],q=Z[1];function K(e){return document.body.appendChild(e)}function F(e){var r,t,n=z(function(e){return{config:e.config,reject:e.reject}}),s=(r="".concat(n.config.paths.vs,"/loader.js"),t=document.createElement("script"),r&&(t.src=r),t);return s.onload=function(){return e()},s.onerror=n.reject,s}function V(){var e=z(function(e){return{config:e.config,resolve:e.resolve,reject:e.reject}}),r=window.require;r.config(e.config),r(["vs/editor/editor.main"],function(r){U(r),e.resolve(r)},function(r){e.reject(r)})}function U(e){z().monaco||q({monaco:e})}var W=new Promise(function(e,r){return q({resolve:e,reject:r})});let B={config:function(e){var r=D.config(e),t=r.monaco,n=function(e,r){if(null==e)return{};var t,n,s=function(e,r){if(null==e)return{};var t,n,s={},a=Object.keys(e);for(n=0;n<a.length;n++)t=a[n],r.indexOf(t)>=0||(s[t]=e[t]);return s}(e,r);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)t=a[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(s[t]=e[t])}return s}(r,["monaco"]);q(function(e){return{config:function e(r,t){return Object.keys(t).forEach(function(n){t[n]instanceof Object&&r[n]&&Object.assign(t[n],e(r[n],t[n]))}),j(j({},r),t)}(e.config,n),monaco:t}})},init:function(){var e=z(function(e){return{monaco:e.monaco,isInitialized:e.isInitialized,resolve:e.resolve}});if(!e.isInitialized){if(q({isInitialized:!0}),e.monaco)return e.resolve(e.monaco),L(W);if(window.monaco&&window.monaco.editor)return U(window.monaco),e.resolve(window.monaco),L(W);_(K,F)(V)}return L(W)},__getMonacoInstance:function(){return z(function(e){return e.monaco})}};var H={wrapper:{display:"flex",position:"relative",textAlign:"initial"},fullWidth:{width:"100%"},hide:{display:"none"}},G={container:{display:"flex",height:"100%",width:"100%",justifyContent:"center",alignItems:"center"}},Q=function({children:e}){return s.createElement("div",{style:G.container},e)},Y=(0,s.memo)(function({width:e,height:r,isEditorReady:t,loading:n,_ref:a,className:i,wrapperProps:o}){return s.createElement("section",{style:{...H.wrapper,width:e,height:r},...o},!t&&s.createElement(Q,null,n),s.createElement("div",{ref:a,style:{...H.fullWidth,...!t&&H.hide},className:i}))}),$=function(e){(0,s.useEffect)(e,[])},X=function(e,r,t=!0){let n=(0,s.useRef)(!0);(0,s.useEffect)(n.current||!t?()=>{n.current=!1}:e,r)};function J(){}function ee(e,r,t,n){return e.editor.getModel(er(e,n))||e.editor.createModel(r,t,n?er(e,n):void 0)}function er(e,r){return e.Uri.parse(r)}var et=function(e){let r=(0,s.useRef)();return(0,s.useEffect)(()=>{r.current=e},[e]),r.current},en=new Map,es=(0,s.memo)(function({defaultValue:e,defaultLanguage:r,defaultPath:t,value:n,language:a,path:i,theme:o="light",line:l,loading:c="Loading...",options:d={},overrideServices:u={},saveViewState:m=!0,keepCurrentModel:f=!1,width:x="100%",height:p="100%",className:h,wrapperProps:g={},beforeMount:y=J,onMount:v=J,onChange:j,onValidate:b=J}){let[w,N]=(0,s.useState)(!1),[C,k]=(0,s.useState)(!0),M=(0,s.useRef)(null),R=(0,s.useRef)(null),S=(0,s.useRef)(null),E=(0,s.useRef)(v),P=(0,s.useRef)(y),O=(0,s.useRef)(),I=(0,s.useRef)(n),T=et(i),D=(0,s.useRef)(!1),_=(0,s.useRef)(!1);$(()=>{let e=B.init();return e.then(e=>(M.current=e)&&k(!1)).catch(e=>e?.type!=="cancelation"&&console.error("Monaco initialization: error:",e)),()=>R.current?void(O.current?.dispose(),f?m&&en.set(i,R.current.saveViewState()):R.current.getModel()?.dispose(),R.current.dispose()):e.cancel()}),X(()=>{let s=ee(M.current,e||n||"",r||a||"",i||t||"");s!==R.current?.getModel()&&(m&&en.set(T,R.current?.saveViewState()),R.current?.setModel(s),m&&R.current?.restoreViewState(en.get(i)))},[i],w),X(()=>{R.current?.updateOptions(d)},[d],w),X(()=>{R.current&&void 0!==n&&(R.current.getOption(M.current.editor.EditorOption.readOnly)?R.current.setValue(n):n===R.current.getValue()||(_.current=!0,R.current.executeEdits("",[{range:R.current.getModel().getFullModelRange(),text:n,forceMoveMarkers:!0}]),R.current.pushUndoStop(),_.current=!1))},[n],w),X(()=>{let e=R.current?.getModel();e&&a&&M.current?.editor.setModelLanguage(e,a)},[a],w),X(()=>{void 0!==l&&R.current?.revealLine(l)},[l],w),X(()=>{M.current?.editor.setTheme(o)},[o],w);let A=(0,s.useCallback)(()=>{if(!(!S.current||!M.current)&&!D.current){P.current(M.current);let s=i||t,c=ee(M.current,n||e||"",r||a||"",s||"");R.current=M.current?.editor.create(S.current,{model:c,automaticLayout:!0,...d},u),m&&R.current.restoreViewState(en.get(s)),M.current.editor.setTheme(o),void 0!==l&&R.current.revealLine(l),N(!0),D.current=!0}},[e,r,t,n,a,i,d,u,m,o,l]);return(0,s.useEffect)(()=>{w&&E.current(R.current,M.current)},[w]),(0,s.useEffect)(()=>{C||w||A()},[C,w,A]),I.current=n,(0,s.useEffect)(()=>{w&&j&&(O.current?.dispose(),O.current=R.current?.onDidChangeModelContent(e=>{_.current||j(R.current.getValue(),e)}))},[w,j]),(0,s.useEffect)(()=>{if(w){let e=M.current.editor.onDidChangeMarkers(e=>{let r=R.current.getModel()?.uri;if(r&&e.find(e=>e.path===r.path)){let e=M.current.editor.getModelMarkers({resource:r});b?.(e)}});return()=>{e?.dispose()}}return()=>{}},[w,b]),s.createElement(Y,{width:x,height:p,isEditorReady:w,loading:c,_ref:S,className:h,wrapperProps:g})}),ea=t(7060);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let ei=(0,c.Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var eo=t(91991);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let el=(0,c.Z)("AlignLeft",[["line",{x1:"21",x2:"3",y1:"6",y2:"6",key:"1fp77t"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}],["line",{x1:"17",x2:"3",y1:"18",y2:"18",key:"1awlsn"}]]),ec=(0,c.Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]);function ed({value:e,onChange:r,language:t,readOnly:l=!1}){let c=(0,s.useRef)(null),d=(0,s.useCallback)((e,r)=>{c.current=e,r.languages.registerCompletionItemProvider("sql",{provideCompletionItems:(e,t)=>({suggestions:[{label:"SELECT",kind:r.languages.CompletionItemKind.Keyword,insertText:"SELECT ",documentation:"SELECT statement"},{label:"FROM",kind:r.languages.CompletionItemKind.Keyword,insertText:"FROM ",documentation:"FROM clause"},{label:"WHERE",kind:r.languages.CompletionItemKind.Keyword,insertText:"WHERE ",documentation:"WHERE clause"},{label:"JOIN",kind:r.languages.CompletionItemKind.Keyword,insertText:"JOIN ",documentation:"JOIN clause"},{label:"GROUP BY",kind:r.languages.CompletionItemKind.Keyword,insertText:"GROUP BY ",documentation:"GROUP BY clause"},{label:"ORDER BY",kind:r.languages.CompletionItemKind.Keyword,insertText:"ORDER BY ",documentation:"ORDER BY clause"}]})}),r.editor.defineTheme("querycraft-theme",{base:"vs",inherit:!0,rules:[{token:"keyword.sql",foreground:"3b82f6",fontStyle:"bold"},{token:"string.sql",foreground:"22c55e"},{token:"comment.sql",foreground:"6b7280",fontStyle:"italic"}],colors:{"editor.background":"#ffffff","editor.foreground":"#1f2937","editor.lineHighlightBackground":"#f9fafb","editor.selectionBackground":"#dbeafe"}}),r.editor.setTheme("querycraft-theme")},[]),{errors:u,warnings:m}=(()=>{let r=[],t=[];return e.trim()&&(e.toLowerCase().includes("select")||e.toLowerCase().includes("insert")||e.toLowerCase().includes("update")||e.toLowerCase().includes("delete")||r.push("Query should contain a valid SQL statement"),e.toLowerCase().includes("select *")&&t.push("Consider specifying column names instead of using SELECT *"),e.toLowerCase().includes("where")&&!e.toLowerCase().includes("limit")&&t.push("Consider adding a LIMIT clause for large datasets")),{errors:r,warnings:t}})();return(0,n.jsxs)("div",{className:"flex flex-col h-full space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[n.jsx("h3",{className:"font-semibold",children:"SQL Editor"}),n.jsx(o.C,{variant:"outline",children:t.toUpperCase()}),0===u.length&&0===m.length&&e.trim()&&(0,n.jsxs)(o.C,{variant:"default",className:"bg-green-100 text-green-800",children:[n.jsx(ea.Z,{className:"h-3 w-3 mr-1"}),"Valid"]}),u.length>0&&(0,n.jsxs)(o.C,{variant:"destructive",children:[n.jsx(ei,{className:"h-3 w-3 mr-1"}),u.length," Error",u.length>1?"s":""]}),m.length>0&&(0,n.jsxs)(o.C,{variant:"secondary",className:"bg-yellow-100 text-yellow-800",children:[n.jsx(eo.Z,{className:"h-3 w-3 mr-1"}),m.length," Warning",m.length>1?"s":""]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>{c.current&&c.current.getAction("editor.action.formatDocument").run()},children:[n.jsx(el,{className:"h-4 w-4 mr-2"}),"Format"]}),(0,n.jsxs)(i.z,{variant:"outline",size:"sm",children:[n.jsx(ec,{className:"h-4 w-4 mr-2"}),"Optimize"]})]})]}),n.jsx(a.Zb,{className:"flex-1 overflow-hidden sql-editor",children:n.jsx(es,{height:"100%",language:"sql",value:e,onChange:e=>r(e||""),onMount:d,options:{readOnly:l,minimap:{enabled:!1},fontSize:14,lineNumbers:"on",roundedSelection:!1,scrollBeyondLastLine:!1,automaticLayout:!0,wordWrap:"on",folding:!0,lineDecorationsWidth:10,lineNumbersMinChars:3,glyphMargin:!1,contextmenu:!0,mouseWheelZoom:!0,smoothScrolling:!0,cursorBlinking:"blink",cursorStyle:"line",renderWhitespace:"selection",renderControlCharacters:!1,fontFamily:"JetBrains Mono, Fira Code, Consolas, monospace",fontLigatures:!0,suggest:{showKeywords:!0,showSnippets:!0,showFunctions:!0},quickSuggestions:{other:!0,comments:!1,strings:!1},parameterHints:{enabled:!0},acceptSuggestionOnCommitCharacter:!0,acceptSuggestionOnEnter:"on",accessibilitySupport:"auto"}})}),(u.length>0||m.length>0)&&(0,n.jsxs)("div",{className:"space-y-2",children:[u.map((e,r)=>(0,n.jsxs)("div",{className:"flex items-center gap-2 text-sm text-red-600",children:[n.jsx(ei,{className:"h-4 w-4"}),e]},`error-${r}`)),m.map((e,r)=>(0,n.jsxs)("div",{className:"flex items-center gap-2 text-sm text-yellow-600",children:[n.jsx(eo.Z,{className:"h-4 w-4"}),e]},`warning-${r}`))]})]})}var eu=t(99046),em=t(25390),ef=t(97751),ex=t(83389),ep=t(82965);function eh({isConnected:e}){let[r,t]=(0,s.useState)(new Set),l={database:"ecommerce_db",tables:[{name:"customers",rowCount:15420,columns:[{name:"customer_id",type:"INT",isPrimaryKey:!0,isNullable:!1},{name:"name",type:"VARCHAR(255)",isPrimaryKey:!1,isNullable:!1},{name:"email",type:"VARCHAR(255)",isPrimaryKey:!1,isNullable:!1},{name:"phone",type:"VARCHAR(20)",isPrimaryKey:!1,isNullable:!0},{name:"created_at",type:"TIMESTAMP",isPrimaryKey:!1,isNullable:!1}]},{name:"orders",rowCount:45230,columns:[{name:"order_id",type:"INT",isPrimaryKey:!0,isNullable:!1},{name:"customer_id",type:"INT",isPrimaryKey:!1,isNullable:!1},{name:"order_date",type:"TIMESTAMP",isPrimaryKey:!1,isNullable:!1},{name:"total_amount",type:"DECIMAL(10,2)",isPrimaryKey:!1,isNullable:!1},{name:"status",type:"VARCHAR(50)",isPrimaryKey:!1,isNullable:!1}]},{name:"order_items",rowCount:128450,columns:[{name:"item_id",type:"INT",isPrimaryKey:!0,isNullable:!1},{name:"order_id",type:"INT",isPrimaryKey:!1,isNullable:!1},{name:"product_id",type:"INT",isPrimaryKey:!1,isNullable:!1},{name:"quantity",type:"INT",isPrimaryKey:!1,isNullable:!1},{name:"unit_price",type:"DECIMAL(10,2)",isPrimaryKey:!1,isNullable:!1}]},{name:"products",rowCount:2340,columns:[{name:"product_id",type:"INT",isPrimaryKey:!0,isNullable:!1},{name:"name",type:"VARCHAR(255)",isPrimaryKey:!1,isNullable:!1},{name:"description",type:"TEXT",isPrimaryKey:!1,isNullable:!0},{name:"price",type:"DECIMAL(10,2)",isPrimaryKey:!1,isNullable:!1},{name:"category_id",type:"INT",isPrimaryKey:!1,isNullable:!1}]}]},c=e=>{let n=new Set(r);n.has(e)?n.delete(e):n.add(e),t(n)};return e?(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"p-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[n.jsx(eu.Z,{className:"h-4 w-4 text-primary"}),n.jsx("span",{className:"font-medium",children:l.database})]}),(0,n.jsxs)("div",{className:"text-xs text-muted-foreground",children:[l.tables.length," tables"]})]}),n.jsx("div",{className:"space-y-1",children:l.tables.map(e=>(0,n.jsxs)("div",{className:"schema-table",children:[(0,n.jsxs)("button",{onClick:()=>c(e.name),className:"schema-table-header w-full",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[r.has(e.name)?n.jsx(em.Z,{className:"h-4 w-4"}):n.jsx(ef.Z,{className:"h-4 w-4"}),n.jsx(ex.Z,{className:"h-4 w-4 text-primary"}),n.jsx("span",{className:"font-medium",children:e.name})]}),n.jsx("div",{className:"flex items-center gap-2",children:(0,n.jsxs)(o.C,{variant:"secondary",className:"text-xs",children:[e.rowCount.toLocaleString()," rows"]})})]}),r.has(e.name)&&n.jsx("div",{className:"schema-table-content",children:e.columns.map(e=>(0,n.jsxs)("div",{className:"schema-column",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 flex-1",children:[e.isPrimaryKey&&n.jsx(ep.Z,{className:"h-3 w-3 text-yellow-500"}),n.jsx("span",{className:"text-sm font-medium",children:e.name})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[n.jsx("span",{className:"schema-column-type text-xs",children:e.type}),!e.isNullable&&n.jsx(o.C,{variant:"outline",className:"text-xs px-1 py-0",children:"NOT NULL"})]})]},e.name))})]},e.name))}),(0,n.jsxs)(a.Zb,{className:"mx-4",children:[n.jsx(a.Ol,{className:"pb-3",children:(0,n.jsxs)(a.ll,{className:"text-sm flex items-center gap-2",children:[n.jsx(eo.Z,{className:"h-4 w-4"}),"Schema Statistics"]})}),(0,n.jsxs)(a.aY,{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex justify-between text-sm",children:[n.jsx("span",{children:"Total Tables:"}),n.jsx("span",{className:"font-medium",children:l.tables.length})]}),(0,n.jsxs)("div",{className:"flex justify-between text-sm",children:[n.jsx("span",{children:"Total Columns:"}),n.jsx("span",{className:"font-medium",children:l.tables.reduce((e,r)=>e+r.columns.length,0)})]}),(0,n.jsxs)("div",{className:"flex justify-between text-sm",children:[n.jsx("span",{children:"Total Rows:"}),n.jsx("span",{className:"font-medium",children:l.tables.reduce((e,r)=>e+r.rowCount,0).toLocaleString()})]})]})]})]}):(0,n.jsxs)("div",{className:"p-4 text-center",children:[n.jsx(eu.Z,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),n.jsx("p",{className:"text-sm text-muted-foreground mb-4",children:"Connect to a database to explore its schema"}),n.jsx(i.z,{variant:"outline",size:"sm",children:"Connect Database"})]})}/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let eg=(0,c.Z)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);var ey=t(96885),ev=t(25545);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let ej=(0,c.Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);function eb(){let[e,r]=(0,s.useState)(!1),t={executionTime:245,rowsAffected:10,columns:[{name:"customer_id",type:"INT"},{name:"name",type:"VARCHAR"},{name:"email",type:"VARCHAR"},{name:"total_orders",type:"INT"},{name:"total_spent",type:"DECIMAL"}],data:[{customer_id:1,name:"John Doe",email:"<EMAIL>",total_orders:15,total_spent:2450.5},{customer_id:2,name:"Jane Smith",email:"<EMAIL>",total_orders:12,total_spent:1890.25},{customer_id:3,name:"Bob Johnson",email:"<EMAIL>",total_orders:8,total_spent:1234.75},{customer_id:4,name:"Alice Brown",email:"<EMAIL>",total_orders:6,total_spent:987.5},{customer_id:5,name:"Charlie Wilson",email:"<EMAIL>",total_orders:4,total_spent:567.25}]};return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[n.jsx("h3",{className:"text-lg font-semibold",children:"Query Results"}),n.jsx("p",{className:"text-sm text-muted-foreground",children:"Last executed: 2 minutes ago"})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>{r(!0),setTimeout(()=>r(!1),1e3)},disabled:e,children:[n.jsx(eg,{className:`h-4 w-4 mr-2 ${e?"animate-spin":""}`}),"Refresh"]}),(0,n.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>{console.log("Exporting results...")},children:[n.jsx(ey.Z,{className:"h-4 w-4 mr-2"}),"Export"]})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[n.jsx(a.Zb,{children:n.jsx(a.aY,{className:"p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[n.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:n.jsx(ev.Z,{className:"h-4 w-4 text-primary"})}),(0,n.jsxs)("div",{children:[n.jsx("div",{className:"text-sm text-muted-foreground",children:"Execution Time"}),(0,n.jsxs)("div",{className:"text-lg font-semibold",children:[t.executionTime,"ms"]})]})]})})}),n.jsx(a.Zb,{children:n.jsx(a.aY,{className:"p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[n.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:n.jsx(eu.Z,{className:"h-4 w-4 text-green-600"})}),(0,n.jsxs)("div",{children:[n.jsx("div",{className:"text-sm text-muted-foreground",children:"Rows Returned"}),n.jsx("div",{className:"text-lg font-semibold",children:t.rowsAffected})]})]})})}),n.jsx(a.Zb,{children:n.jsx(a.aY,{className:"p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[n.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:n.jsx(ej,{className:"h-4 w-4 text-blue-600"})}),(0,n.jsxs)("div",{children:[n.jsx("div",{className:"text-sm text-muted-foreground",children:"Performance"}),n.jsx("div",{className:"text-lg font-semibold",children:n.jsx(o.C,{variant:"default",className:"bg-green-100 text-green-800",children:"Excellent"})})]})]})})})]}),(0,n.jsxs)(a.Zb,{children:[n.jsx(a.Ol,{children:n.jsx(a.ll,{className:"text-base",children:"Data Results"})}),n.jsx(a.aY,{children:n.jsx("div",{className:"overflow-x-auto",children:(0,n.jsxs)("table",{className:"w-full border-collapse",children:[n.jsx("thead",{children:n.jsx("tr",{className:"border-b border-border",children:t.columns.map(e=>n.jsx("th",{className:"text-left p-3 font-medium text-sm text-muted-foreground",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[e.name,n.jsx(o.C,{variant:"outline",className:"text-xs",children:e.type})]})},e.name))})}),n.jsx("tbody",{children:t.data.map((e,r)=>(0,n.jsxs)("tr",{className:"border-b border-border hover:bg-muted/50",children:[n.jsx("td",{className:"p-3 text-sm",children:e.customer_id}),n.jsx("td",{className:"p-3 text-sm font-medium",children:e.name}),n.jsx("td",{className:"p-3 text-sm text-muted-foreground",children:e.email}),n.jsx("td",{className:"p-3 text-sm",children:e.total_orders}),(0,n.jsxs)("td",{className:"p-3 text-sm font-medium",children:["$",e.total_spent.toFixed(2)]})]},r))})]})})})]}),(0,n.jsxs)(a.Zb,{children:[n.jsx(a.Ol,{children:(0,n.jsxs)(a.ll,{className:"text-base flex items-center gap-2",children:[n.jsx(ej,{className:"h-4 w-4"}),"Query Analysis"]})}),n.jsx(a.aY,{className:"space-y-4",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"font-medium text-sm mb-2",children:"Performance Insights"}),(0,n.jsxs)("ul",{className:"space-y-1 text-sm text-muted-foreground",children:[(0,n.jsxs)("li",{className:"flex items-center gap-2",children:[n.jsx("div",{className:"h-2 w-2 bg-green-500 rounded-full"}),"Query executed efficiently"]}),(0,n.jsxs)("li",{className:"flex items-center gap-2",children:[n.jsx("div",{className:"h-2 w-2 bg-green-500 rounded-full"}),"Proper index usage detected"]}),(0,n.jsxs)("li",{className:"flex items-center gap-2",children:[n.jsx("div",{className:"h-2 w-2 bg-yellow-500 rounded-full"}),"Consider adding LIMIT clause"]})]})]}),(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"font-medium text-sm mb-2",children:"Optimization Suggestions"}),(0,n.jsxs)("ul",{className:"space-y-1 text-sm text-muted-foreground",children:[(0,n.jsxs)("li",{className:"flex items-center gap-2",children:[n.jsx(ei,{className:"h-3 w-3 text-blue-500"}),"Add composite index on (customer_id, order_date)"]}),(0,n.jsxs)("li",{className:"flex items-center gap-2",children:[n.jsx(ei,{className:"h-3 w-3 text-blue-500"}),"Consider partitioning orders table by date"]})]})]})]})})]})]})}var ew=t(74218),eN=t(96213),eC=t(57320);function ek({userId:e}){let[r,t]=(0,s.useState)([]),[l,c]=(0,s.useState)(!0),{setCurrentQuery:d,setActiveTab:f,currentUser:x}=(0,g.l)();(0,s.useEffect)(()=>{(async()=>{if(!e&&!x)return;let r=e||x?.id;if(r)try{c(!0);let e=await fetch(`/api/queries?type=history&userId=${r}&limit=50`);if(e.ok){let r=await e.json();t(r)}}catch(e){console.error("Failed to load query history:",e)}finally{c(!1)}})()},[e,x]);let p=e=>{d(e),f("editor")},y=e=>{navigator.clipboard.writeText(e)},v=e=>{switch(e){case"EXECUTED":return"bg-green-100 text-green-800";case"FAILED":return"bg-red-100 text-red-800";case"GENERATED":return"bg-blue-100 text-blue-800";case"OPTIMIZED":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}};return l?(0,n.jsxs)(a.Zb,{children:[n.jsx(a.Ol,{children:(0,n.jsxs)(a.ll,{className:"flex items-center gap-2",children:[n.jsx(eN.Z,{className:"h-5 w-5"}),"Query History"]})}),n.jsx(a.aY,{children:(0,n.jsxs)("div",{className:"flex items-center justify-center py-8",children:[n.jsx("div",{className:"loading-spinner"}),n.jsx("span",{className:"ml-2 text-sm text-muted-foreground",children:"Loading history..."})]})})]}):(0,n.jsxs)(a.Zb,{children:[n.jsx(a.Ol,{children:(0,n.jsxs)(a.ll,{className:"flex items-center gap-2",children:[n.jsx(eN.Z,{className:"h-5 w-5"}),"Query History",(0,n.jsxs)(o.C,{variant:"secondary",className:"ml-auto",children:[r.length," queries"]})]})}),n.jsx(a.aY,{children:n.jsx(ew.x,{className:"h-[600px]",children:0===r.length?(0,n.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[n.jsx(eN.Z,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),n.jsx("p",{children:"No queries in history yet"}),n.jsx("p",{className:"text-sm",children:"Start generating queries to see them here"})]}):n.jsx("div",{className:"space-y-4",children:r.map(e=>n.jsx(a.Zb,{className:"border-l-4 border-l-primary/20",children:n.jsx(a.aY,{className:"p-4",children:(0,n.jsxs)("div",{className:"space-y-3",children:[n.jsx("div",{className:"flex items-start justify-between",children:(0,n.jsxs)("div",{className:"flex-1",children:[n.jsx("p",{className:"text-sm font-medium text-foreground",children:e.userInput}),(0,n.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[n.jsx(o.C,{className:v(e.status),children:e.status}),(0,n.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[n.jsx(eu.Z,{className:"h-3 w-3"}),e.databaseType]}),(0,n.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[n.jsx(ev.Z,{className:"h-3 w-3"}),(0,h.SY)(new Date(e.createdAt))]})]})]})}),n.jsx("div",{className:"bg-muted rounded-lg p-3",children:n.jsx("pre",{className:"text-xs font-mono overflow-x-auto whitespace-pre-wrap",children:n.jsx("code",{children:e.generatedSQL})})}),e.explanation&&(0,n.jsxs)("div",{className:"text-sm text-muted-foreground",children:[n.jsx("strong",{children:"Explanation:"})," ",e.explanation]}),e.executionTime&&(0,n.jsxs)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[(0,n.jsxs)("span",{children:["Execution: ",e.executionTime,"ms"]}),e.rowsAffected&&(0,n.jsxs)("span",{children:["Rows: ",e.rowsAffected]}),e.userFeedback&&(0,n.jsxs)("div",{className:"flex items-center gap-1",children:[n.jsx("span",{children:"Rating:"}),n.jsx("div",{className:"flex",children:[void 0,void 0,void 0,void 0,void 0].map((r,t)=>n.jsx(m,{className:`h-3 w-3 ${t<e.userFeedback?"text-yellow-500 fill-current":"text-gray-300"}`},t))})]})]}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>p(e.generatedSQL),children:[n.jsx(eC.Z,{className:"h-3 w-3 mr-1"}),"Use"]}),(0,n.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>y(e.generatedSQL),children:[n.jsx(u.Z,{className:"h-3 w-3 mr-1"}),"Copy"]})]})]})})},e.id))})})})]})}/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let eM=(0,c.Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);var eR=t(11542);function eS(){let{activeTab:e,setActiveTab:r,currentQuery:t,setCurrentQuery:c,currentDatabase:d}=(0,g.l)(),[u,m]=(0,s.useState)(!1),f=[{id:"chat",label:"AI Assistant",icon:l.Z},{id:"editor",label:"SQL Editor",icon:eu.Z},{id:"results",label:"Results",icon:ej},{id:"history",label:"History",icon:eN.Z}];return(0,n.jsxs)("div",{className:"flex h-full",children:[(0,n.jsxs)("div",{className:"w-80 border-r border-border bg-card",children:[(0,n.jsxs)("div",{className:"p-4 border-b border-border",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[n.jsx("h3",{className:"font-semibold",children:"Database Schema"}),n.jsx(o.C,{variant:u?"default":"secondary",children:u?"Connected":"Not Connected"})]}),!u&&(0,n.jsxs)(i.z,{variant:"outline",size:"sm",className:"w-full",onClick:()=>m(!0),children:[n.jsx(eu.Z,{className:"h-4 w-4 mr-2"}),"Connect Database"]})]}),n.jsx("div",{className:"flex-1 overflow-auto",children:n.jsx(eh,{isConnected:u})})]}),(0,n.jsxs)("div",{className:"flex-1 flex flex-col",children:[n.jsx("div",{className:"border-b border-border bg-card",children:(0,n.jsxs)("div",{className:"flex items-center justify-between px-6 py-3",children:[n.jsx("div",{className:"flex space-x-1",children:f.map(t=>{let s=t.icon;return(0,n.jsxs)("button",{onClick:()=>r(t.id),className:`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${e===t.id?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-muted"}`,children:[n.jsx(s,{className:"h-4 w-4"}),t.label]},t.id)})}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)(i.z,{variant:"outline",size:"sm",children:[n.jsx(eN.Z,{className:"h-4 w-4 mr-2"}),"History"]}),(0,n.jsxs)(i.z,{variant:"outline",size:"sm",children:[n.jsx(eM,{className:"h-4 w-4 mr-2"}),"Save"]}),(0,n.jsxs)(i.z,{variant:"outline",size:"sm",children:[n.jsx(eR.Z,{className:"h-4 w-4 mr-2"}),"Share"]}),"editor"===e&&(0,n.jsxs)(i.z,{size:"sm",children:[n.jsx(eC.Z,{className:"h-4 w-4 mr-2"}),"Run Query"]})]})]})}),(0,n.jsxs)("div",{className:"flex-1 overflow-hidden",children:["chat"===e&&n.jsx(y,{onQueryGenerated:e=>{c(e)}}),"editor"===e&&n.jsx("div",{className:"h-full flex flex-col",children:n.jsx("div",{className:"flex-1 p-6",children:n.jsx(ed,{value:t,onChange:c,language:d?.databaseType||"postgresql"})})}),"results"===e&&n.jsx("div",{className:"h-full p-6",children:n.jsx(eb,{})}),"history"===e&&n.jsx("div",{className:"h-full p-6",children:n.jsx(ek,{})})]})]}),(0,n.jsxs)("div",{className:"w-80 border-l border-border bg-card",children:[n.jsx("div",{className:"p-4 border-b border-border",children:(0,n.jsxs)("h3",{className:"font-semibold flex items-center gap-2",children:[n.jsx(ec,{className:"h-4 w-4"}),"AI Insights"]})}),(0,n.jsxs)("div",{className:"p-4 space-y-4",children:[(0,n.jsxs)(a.Zb,{children:[n.jsx(a.Ol,{className:"pb-3",children:n.jsx(a.ll,{className:"text-sm",children:"Query Suggestions"})}),n.jsx(a.aY,{className:"space-y-2",children:n.jsx("div",{className:"text-sm text-muted-foreground",children:"No active query to analyze. Start by describing what you want to query in the AI Assistant."})})]}),(0,n.jsxs)(a.Zb,{children:[n.jsx(a.Ol,{className:"pb-3",children:n.jsx(a.ll,{className:"text-sm",children:"Performance Tips"})}),n.jsx(a.aY,{className:"space-y-2",children:n.jsx("div",{className:"text-sm text-muted-foreground",children:"Connect to a database to get performance recommendations."})})]}),(0,n.jsxs)(a.Zb,{children:[n.jsx(a.Ol,{className:"pb-3",children:n.jsx(a.ll,{className:"text-sm",children:"Recent Activity"})}),n.jsx(a.aY,{className:"space-y-2",children:n.jsx("div",{className:"text-sm text-muted-foreground",children:"No recent queries found."})})]})]})]})]})}},19591:(e,r,t)=>{"use strict";t.d(r,{C:()=>o});var n=t(95344);t(3729);var s=t(49247),a=t(11453);let i=(0,s.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:r,...t}){return n.jsx("div",{className:(0,a.cn)(i({variant:r}),e),...t})}},5094:(e,r,t)=>{"use strict";t.d(r,{z:()=>c});var n=t(95344),s=t(3729),a=t(32751),i=t(49247),o=t(11453);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef(({className:e,variant:r,size:t,asChild:s=!1,...i},c)=>{let d=s?a.g7:"button";return n.jsx(d,{className:(0,o.cn)(l({variant:r,size:t,className:e})),ref:c,...i})});c.displayName="Button"},23673:(e,r,t)=>{"use strict";t.d(r,{Ol:()=>o,Zb:()=>i,aY:()=>c,ll:()=>l});var n=t(95344),s=t(3729),a=t(11453);let i=s.forwardRef(({className:e,...r},t)=>n.jsx("div",{ref:t,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));i.displayName="Card";let o=s.forwardRef(({className:e,...r},t)=>n.jsx("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...r}));o.displayName="CardHeader";let l=s.forwardRef(({className:e,...r},t)=>n.jsx("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));l.displayName="CardTitle",s.forwardRef(({className:e,...r},t)=>n.jsx("p",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",e),...r})).displayName="CardDescription";let c=s.forwardRef(({className:e,...r},t)=>n.jsx("div",{ref:t,className:(0,a.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent",s.forwardRef(({className:e,...r},t)=>n.jsx("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},74218:(e,r,t)=>{"use strict";t.d(r,{x:()=>o});var n=t(95344),s=t(3729),a=t(49027),i=t(11453);let o=s.forwardRef(({className:e,children:r,...t},s)=>(0,n.jsxs)(a.fC,{ref:s,className:(0,i.cn)("relative overflow-hidden",e),...t,children:[n.jsx(a.l_,{className:"h-full w-full rounded-[inherit]",children:r}),n.jsx(l,{}),n.jsx(a.Ns,{})]}));o.displayName=a.fC.displayName;let l=s.forwardRef(({className:e,orientation:r="vertical",...t},s)=>n.jsx(a.gb,{ref:s,orientation:r,className:(0,i.cn)("flex touch-none select-none transition-colors","vertical"===r&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===r&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...t,children:n.jsx(a.q4,{className:"relative flex-1 rounded-full bg-border"})}));l.displayName=a.gb.displayName},69194:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x,metadata:()=>f});var n=t(25036),s=t(86843);let a=(0,s.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/dashboard-layout.tsx`),{__esModule:i,$$typeof:o}=a;a.default;let l=(0,s.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/dashboard-layout.tsx#DashboardLayout`),c=(0,s.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx`),{__esModule:d,$$typeof:u}=c;c.default;let m=(0,s.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx#QueryWorkspace`),f={title:"Dashboard - QueryCraft Studio",description:"AI-powered SQL development workspace"};function x(){return n.jsx(l,{children:n.jsx(m,{})})}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[638,599,476,305,162],()=>t(9334));module.exports=n})();