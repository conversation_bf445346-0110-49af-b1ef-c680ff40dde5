(()=>{var e={};e.id=702,e.ids=[702],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9334:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>f,tree:()=>c});var n=t(50482),a=t(69108),s=t(62563),o=t.n(s),l=t(68300),i={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(r,i);let c=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,69194)),"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/dashboard/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,98890)),"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/dashboard/page.tsx"],u="/dashboard/page",m={require:t,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},36950:(e,r,t)=>{Promise.resolve().then(t.bind(t,44698)),Promise.resolve().then(t.bind(t,88435))},97909:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(69224).Z)("BrainCircuit",[["path",{d:"M12 4.5a2.5 2.5 0 0 0-4.96-.46 2.5 2.5 0 0 0-1.98 3 2.5 2.5 0 0 0-1.32 4.24 3 3 0 0 0 .34 5.58 2.5 2.5 0 0 0 2.96 3.08 2.5 2.5 0 0 0 4.91.05L12 20V4.5Z",key:"ixwj2a"}],["path",{d:"M16 8V5c0-1.1.9-2 2-2",key:"13dx7u"}],["path",{d:"M12 13h4",key:"1ku699"}],["path",{d:"M12 18h6a2 2 0 0 1 2 2v1",key:"105ag5"}],["path",{d:"M12 8h8",key:"1lhi5i"}],["path",{d:"M20.5 8a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"1s25gz"}],["path",{d:"M16.5 13a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"127460"}],["path",{d:"M20.5 21a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"fys062"}],["path",{d:"M18.5 3a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"1vib61"}]])},96213:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(69224).Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},18822:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(69224).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},20783:(e,r,t)=>{e.exports=t(61476)},44698:(e,r,t)=>{"use strict";t.r(r),t.d(r,{DashboardLayout:()=>rH});var n=t(95344),a=t(3729),s=t(5094),o=t(47674),l=t(98462),i=t(2256),c=t(16069),d=t(62409),u=t(8145);function m(){return()=>{}}var f="Avatar",[p,h]=(0,l.b)(f),[x,g]=p(f),y=a.forwardRef((e,r)=>{let{__scopeAvatar:t,...s}=e,[o,l]=a.useState("idle");return(0,n.jsx)(x,{scope:t,imageLoadingStatus:o,onImageLoadingStatusChange:l,children:(0,n.jsx)(d.WV.span,{...s,ref:r})})});y.displayName=f;var v="AvatarImage",b=a.forwardRef((e,r)=>{let{__scopeAvatar:t,src:s,onLoadingStatusChange:o=()=>{},...l}=e,f=g(v,t),p=function(e,{referrerPolicy:r,crossOrigin:t}){let n=(0,u.useSyncExternalStore)(m,()=>!0,()=>!1),s=a.useRef(null),o=n?(s.current||(s.current=new window.Image),s.current):null,[l,i]=a.useState(()=>N(o,e));return(0,c.b)(()=>{i(N(o,e))},[o,e]),(0,c.b)(()=>{let e=e=>()=>{i(e)};if(!o)return;let n=e("loaded"),a=e("error");return o.addEventListener("load",n),o.addEventListener("error",a),r&&(o.referrerPolicy=r),"string"==typeof t&&(o.crossOrigin=t),()=>{o.removeEventListener("load",n),o.removeEventListener("error",a)}},[o,t,r]),l}(s,l),h=(0,i.W)(e=>{o(e),f.onImageLoadingStatusChange(e)});return(0,c.b)(()=>{"idle"!==p&&h(p)},[p,h]),"loaded"===p?(0,n.jsx)(d.WV.img,{...l,ref:r,src:s}):null});b.displayName=v;var j="AvatarFallback",w=a.forwardRef((e,r)=>{let{__scopeAvatar:t,delayMs:s,...o}=e,l=g(j,t),[i,c]=a.useState(void 0===s);return a.useEffect(()=>{if(void 0!==s){let e=window.setTimeout(()=>c(!0),s);return()=>window.clearTimeout(e)}},[s]),i&&"loaded"!==l.imageLoadingStatus?(0,n.jsx)(d.WV.span,{...o,ref:r}):null});function N(e,r){return e?r?(e.src!==r&&(e.src=r),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}w.displayName=j;var C=t(11453);let k=a.forwardRef(({className:e,...r},t)=>n.jsx(y,{ref:t,className:(0,C.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...r}));k.displayName=y.displayName;let R=a.forwardRef(({className:e,...r},t)=>n.jsx(b,{ref:t,className:(0,C.cn)("aspect-square h-full w-full",e),...r}));R.displayName=b.displayName;let M=a.forwardRef(({className:e,...r},t)=>n.jsx(w,{ref:t,className:(0,C.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...r}));M.displayName=w.displayName;var S=t(85222),E=t(31405),P=t(33183),T=t(77411),D=t(3975),_=t(44155),O=t(1106),I=t(27386),L=t(99048),A=t(37574),z=t(31179),Z=t(43234),q="rovingFocusGroup.onEntryFocus",F={bubbles:!1,cancelable:!0},K="RovingFocusGroup",[W,V,H]=(0,T.B)(K),[U,B]=(0,l.b)(K,[H]),[Y,G]=U(K),Q=a.forwardRef((e,r)=>(0,n.jsx)(W.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,n.jsx)(W.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,n.jsx)(X,{...e,ref:r})})}));Q.displayName=K;var X=a.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,orientation:s,loop:o=!1,dir:l,currentTabStopId:c,defaultCurrentTabStopId:u,onCurrentTabStopIdChange:m,onEntryFocus:f,preventScrollOnEntryFocus:p=!1,...h}=e,x=a.useRef(null),g=(0,E.e)(r,x),y=(0,D.gm)(l),[v,b]=(0,P.T)({prop:c,defaultProp:u??null,onChange:m,caller:K}),[j,w]=a.useState(!1),N=(0,i.W)(f),C=V(t),k=a.useRef(!1),[R,M]=a.useState(0);return a.useEffect(()=>{let e=x.current;if(e)return e.addEventListener(q,N),()=>e.removeEventListener(q,N)},[N]),(0,n.jsx)(Y,{scope:t,orientation:s,dir:y,loop:o,currentTabStopId:v,onItemFocus:a.useCallback(e=>b(e),[b]),onItemShiftTab:a.useCallback(()=>w(!0),[]),onFocusableItemAdd:a.useCallback(()=>M(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>M(e=>e-1),[]),children:(0,n.jsx)(d.WV.div,{tabIndex:j||0===R?-1:0,"data-orientation":s,...h,ref:g,style:{outline:"none",...e.style},onMouseDown:(0,S.M)(e.onMouseDown,()=>{k.current=!0}),onFocus:(0,S.M)(e.onFocus,e=>{let r=!k.current;if(e.target===e.currentTarget&&r&&!j){let r=new CustomEvent(q,F);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=C().filter(e=>e.focusable);er([e.find(e=>e.active),e.find(e=>e.id===v),...e].filter(Boolean).map(e=>e.ref.current),p)}}k.current=!1}),onBlur:(0,S.M)(e.onBlur,()=>w(!1))})})}),$="RovingFocusGroupItem",J=a.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,focusable:s=!0,active:o=!1,tabStopId:l,children:i,...c}=e,u=(0,L.M)(),m=l||u,f=G($,t),p=f.currentTabStopId===m,h=V(t),{onFocusableItemAdd:x,onFocusableItemRemove:g,currentTabStopId:y}=f;return a.useEffect(()=>{if(s)return x(),()=>g()},[s,x,g]),(0,n.jsx)(W.ItemSlot,{scope:t,id:m,focusable:s,active:o,children:(0,n.jsx)(d.WV.span,{tabIndex:p?0:-1,"data-orientation":f.orientation,...c,ref:r,onMouseDown:(0,S.M)(e.onMouseDown,e=>{s?f.onItemFocus(m):e.preventDefault()}),onFocus:(0,S.M)(e.onFocus,()=>f.onItemFocus(m)),onKeyDown:(0,S.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){f.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let r=function(e,r,t){var n;let a=(n=e.key,"rtl"!==t?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(a))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(a)))return ee[a]}(e,f.orientation,f.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let t=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)t.reverse();else if("prev"===r||"next"===r){"prev"===r&&t.reverse();let n=t.indexOf(e.currentTarget);t=f.loop?function(e,r){return e.map((t,n)=>e[(r+n)%e.length])}(t,n+1):t.slice(n+1)}setTimeout(()=>er(t))}}),children:"function"==typeof i?i({isCurrentTabStop:p,hasTabStop:null!=y}):i})})});J.displayName=$;var ee={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function er(e,r=!1){let t=document.activeElement;for(let n of e)if(n===t||(n.focus({preventScroll:r}),document.activeElement!==t))return}var et=t(32751),en=t(45904),ea=t(71210),es=["Enter"," "],eo=["ArrowUp","PageDown","End"],el=["ArrowDown","PageUp","Home",...eo],ei={ltr:[...es,"ArrowRight"],rtl:[...es,"ArrowLeft"]},ec={ltr:["ArrowLeft"],rtl:["ArrowRight"]},ed="Menu",[eu,em,ef]=(0,T.B)(ed),[ep,eh]=(0,l.b)(ed,[ef,A.D7,B]),ex=(0,A.D7)(),eg=B(),[ey,ev]=ep(ed),[eb,ej]=ep(ed),ew=e=>{let{__scopeMenu:r,open:t=!1,children:s,dir:o,onOpenChange:l,modal:c=!0}=e,d=ex(r),[u,m]=a.useState(null),f=a.useRef(!1),p=(0,i.W)(l),h=(0,D.gm)(o);return a.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,n.jsx)(A.fC,{...d,children:(0,n.jsx)(ey,{scope:r,open:t,onOpenChange:p,content:u,onContentChange:m,children:(0,n.jsx)(eb,{scope:r,onClose:a.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:h,modal:c,children:s})})})};ew.displayName=ed;var eN=a.forwardRef((e,r)=>{let{__scopeMenu:t,...a}=e,s=ex(t);return(0,n.jsx)(A.ee,{...s,...a,ref:r})});eN.displayName="MenuAnchor";var eC="MenuPortal",[ek,eR]=ep(eC,{forceMount:void 0}),eM=e=>{let{__scopeMenu:r,forceMount:t,children:a,container:s}=e,o=ev(eC,r);return(0,n.jsx)(ek,{scope:r,forceMount:t,children:(0,n.jsx)(Z.z,{present:t||o.open,children:(0,n.jsx)(z.h,{asChild:!0,container:s,children:a})})})};eM.displayName=eC;var eS="MenuContent",[eE,eP]=ep(eS),eT=a.forwardRef((e,r)=>{let t=eR(eS,e.__scopeMenu),{forceMount:a=t.forceMount,...s}=e,o=ev(eS,e.__scopeMenu),l=ej(eS,e.__scopeMenu);return(0,n.jsx)(eu.Provider,{scope:e.__scopeMenu,children:(0,n.jsx)(Z.z,{present:a||o.open,children:(0,n.jsx)(eu.Slot,{scope:e.__scopeMenu,children:l.modal?(0,n.jsx)(eD,{...s,ref:r}):(0,n.jsx)(e_,{...s,ref:r})})})})}),eD=a.forwardRef((e,r)=>{let t=ev(eS,e.__scopeMenu),s=a.useRef(null),o=(0,E.e)(r,s);return a.useEffect(()=>{let e=s.current;if(e)return(0,en.Ry)(e)},[]),(0,n.jsx)(eI,{...e,ref:o,trapFocus:t.open,disableOutsidePointerEvents:t.open,disableOutsideScroll:!0,onFocusOutside:(0,S.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>t.onOpenChange(!1)})}),e_=a.forwardRef((e,r)=>{let t=ev(eS,e.__scopeMenu);return(0,n.jsx)(eI,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>t.onOpenChange(!1)})}),eO=(0,et.Z8)("MenuContent.ScrollLock"),eI=a.forwardRef((e,r)=>{let{__scopeMenu:t,loop:s=!1,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,disableOutsidePointerEvents:c,onEntryFocus:d,onEscapeKeyDown:u,onPointerDownOutside:m,onFocusOutside:f,onInteractOutside:p,onDismiss:h,disableOutsideScroll:x,...g}=e,y=ev(eS,t),v=ej(eS,t),b=ex(t),j=eg(t),w=em(t),[N,C]=a.useState(null),k=a.useRef(null),R=(0,E.e)(r,k,y.onContentChange),M=a.useRef(0),P=a.useRef(""),T=a.useRef(0),D=a.useRef(null),L=a.useRef("right"),z=a.useRef(0),Z=x?ea.Z:a.Fragment,q=e=>{let r=P.current+e,t=w().filter(e=>!e.disabled),n=document.activeElement,a=t.find(e=>e.ref.current===n)?.textValue,s=function(e,r,t){var n;let a=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,s=(n=Math.max(t?e.indexOf(t):-1,0),e.map((r,t)=>e[(n+t)%e.length]));1===a.length&&(s=s.filter(e=>e!==t));let o=s.find(e=>e.toLowerCase().startsWith(a.toLowerCase()));return o!==t?o:void 0}(t.map(e=>e.textValue),r,a),o=t.find(e=>e.textValue===s)?.ref.current;(function e(r){P.current=r,window.clearTimeout(M.current),""!==r&&(M.current=window.setTimeout(()=>e(""),1e3))})(r),o&&setTimeout(()=>o.focus())};a.useEffect(()=>()=>window.clearTimeout(M.current),[]),(0,O.EW)();let F=a.useCallback(e=>L.current===D.current?.side&&function(e,r){return!!r&&function(e,r){let{x:t,y:n}=e,a=!1;for(let e=0,s=r.length-1;e<r.length;s=e++){let o=r[e],l=r[s],i=o.x,c=o.y,d=l.x,u=l.y;c>n!=u>n&&t<(d-i)*(n-c)/(u-c)+i&&(a=!a)}return a}({x:e.clientX,y:e.clientY},r)}(e,D.current?.area),[]);return(0,n.jsx)(eE,{scope:t,searchRef:P,onItemEnter:a.useCallback(e=>{F(e)&&e.preventDefault()},[F]),onItemLeave:a.useCallback(e=>{F(e)||(k.current?.focus(),C(null))},[F]),onTriggerLeave:a.useCallback(e=>{F(e)&&e.preventDefault()},[F]),pointerGraceTimerRef:T,onPointerGraceIntentChange:a.useCallback(e=>{D.current=e},[]),children:(0,n.jsx)(Z,{...x?{as:eO,allowPinchZoom:!0}:void 0,children:(0,n.jsx)(I.M,{asChild:!0,trapped:o,onMountAutoFocus:(0,S.M)(l,e=>{e.preventDefault(),k.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:i,children:(0,n.jsx)(_.XB,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:u,onPointerDownOutside:m,onFocusOutside:f,onInteractOutside:p,onDismiss:h,children:(0,n.jsx)(Q,{asChild:!0,...j,dir:v.dir,orientation:"vertical",loop:s,currentTabStopId:N,onCurrentTabStopIdChange:C,onEntryFocus:(0,S.M)(d,e=>{v.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,n.jsx)(A.VY,{role:"menu","aria-orientation":"vertical","data-state":e8(y.open),"data-radix-menu-content":"",dir:v.dir,...b,...g,ref:R,style:{outline:"none",...g.style},onKeyDown:(0,S.M)(g.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,t=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!t&&n&&q(e.key));let a=k.current;if(e.target!==a||!el.includes(e.key))return;e.preventDefault();let s=w().filter(e=>!e.disabled).map(e=>e.ref.current);eo.includes(e.key)&&s.reverse(),function(e){let r=document.activeElement;for(let t of e)if(t===r||(t.focus(),document.activeElement!==r))return}(s)}),onBlur:(0,S.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(M.current),P.current="")}),onPointerMove:(0,S.M)(e.onPointerMove,re(e=>{let r=e.target,t=z.current!==e.clientX;if(e.currentTarget.contains(r)&&t){let r=e.clientX>z.current?"right":"left";L.current=r,z.current=e.clientX}}))})})})})})})});eT.displayName=eS;var eL=a.forwardRef((e,r)=>{let{__scopeMenu:t,...a}=e;return(0,n.jsx)(d.WV.div,{role:"group",...a,ref:r})});eL.displayName="MenuGroup";var eA=a.forwardRef((e,r)=>{let{__scopeMenu:t,...a}=e;return(0,n.jsx)(d.WV.div,{...a,ref:r})});eA.displayName="MenuLabel";var ez="MenuItem",eZ="menu.itemSelect",eq=a.forwardRef((e,r)=>{let{disabled:t=!1,onSelect:s,...o}=e,l=a.useRef(null),i=ej(ez,e.__scopeMenu),c=eP(ez,e.__scopeMenu),u=(0,E.e)(r,l),m=a.useRef(!1);return(0,n.jsx)(eF,{...o,ref:u,disabled:t,onClick:(0,S.M)(e.onClick,()=>{let e=l.current;if(!t&&e){let r=new CustomEvent(eZ,{bubbles:!0,cancelable:!0});e.addEventListener(eZ,e=>s?.(e),{once:!0}),(0,d.jH)(e,r),r.defaultPrevented?m.current=!1:i.onClose()}}),onPointerDown:r=>{e.onPointerDown?.(r),m.current=!0},onPointerUp:(0,S.M)(e.onPointerUp,e=>{m.current||e.currentTarget?.click()}),onKeyDown:(0,S.M)(e.onKeyDown,e=>{let r=""!==c.searchRef.current;!t&&(!r||" "!==e.key)&&es.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eq.displayName=ez;var eF=a.forwardRef((e,r)=>{let{__scopeMenu:t,disabled:s=!1,textValue:o,...l}=e,i=eP(ez,t),c=eg(t),u=a.useRef(null),m=(0,E.e)(r,u),[f,p]=a.useState(!1),[h,x]=a.useState("");return a.useEffect(()=>{let e=u.current;e&&x((e.textContent??"").trim())},[l.children]),(0,n.jsx)(eu.ItemSlot,{scope:t,disabled:s,textValue:o??h,children:(0,n.jsx)(J,{asChild:!0,...c,focusable:!s,children:(0,n.jsx)(d.WV.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":s||void 0,"data-disabled":s?"":void 0,...l,ref:m,onPointerMove:(0,S.M)(e.onPointerMove,re(e=>{s?i.onItemLeave(e):(i.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,S.M)(e.onPointerLeave,re(e=>i.onItemLeave(e))),onFocus:(0,S.M)(e.onFocus,()=>p(!0)),onBlur:(0,S.M)(e.onBlur,()=>p(!1))})})})}),eK=a.forwardRef((e,r)=>{let{checked:t=!1,onCheckedChange:a,...s}=e;return(0,n.jsx)(eQ,{scope:e.__scopeMenu,checked:t,children:(0,n.jsx)(eq,{role:"menuitemcheckbox","aria-checked":e6(t)?"mixed":t,...s,ref:r,"data-state":e7(t),onSelect:(0,S.M)(s.onSelect,()=>a?.(!!e6(t)||!t),{checkForDefaultPrevented:!1})})})});eK.displayName="MenuCheckboxItem";var eW="MenuRadioGroup",[eV,eH]=ep(eW,{value:void 0,onValueChange:()=>{}}),eU=a.forwardRef((e,r)=>{let{value:t,onValueChange:a,...s}=e,o=(0,i.W)(a);return(0,n.jsx)(eV,{scope:e.__scopeMenu,value:t,onValueChange:o,children:(0,n.jsx)(eL,{...s,ref:r})})});eU.displayName=eW;var eB="MenuRadioItem",eY=a.forwardRef((e,r)=>{let{value:t,...a}=e,s=eH(eB,e.__scopeMenu),o=t===s.value;return(0,n.jsx)(eQ,{scope:e.__scopeMenu,checked:o,children:(0,n.jsx)(eq,{role:"menuitemradio","aria-checked":o,...a,ref:r,"data-state":e7(o),onSelect:(0,S.M)(a.onSelect,()=>s.onValueChange?.(t),{checkForDefaultPrevented:!1})})})});eY.displayName=eB;var eG="MenuItemIndicator",[eQ,eX]=ep(eG,{checked:!1}),e$=a.forwardRef((e,r)=>{let{__scopeMenu:t,forceMount:a,...s}=e,o=eX(eG,t);return(0,n.jsx)(Z.z,{present:a||e6(o.checked)||!0===o.checked,children:(0,n.jsx)(d.WV.span,{...s,ref:r,"data-state":e7(o.checked)})})});e$.displayName=eG;var eJ=a.forwardRef((e,r)=>{let{__scopeMenu:t,...a}=e;return(0,n.jsx)(d.WV.div,{role:"separator","aria-orientation":"horizontal",...a,ref:r})});eJ.displayName="MenuSeparator";var e0=a.forwardRef((e,r)=>{let{__scopeMenu:t,...a}=e,s=ex(t);return(0,n.jsx)(A.Eh,{...s,...a,ref:r})});e0.displayName="MenuArrow";var[e1,e2]=ep("MenuSub"),e4="MenuSubTrigger",e3=a.forwardRef((e,r)=>{let t=ev(e4,e.__scopeMenu),s=ej(e4,e.__scopeMenu),o=e2(e4,e.__scopeMenu),l=eP(e4,e.__scopeMenu),i=a.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=l,u={__scopeMenu:e.__scopeMenu},m=a.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return a.useEffect(()=>m,[m]),a.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,n.jsx)(eN,{asChild:!0,...u,children:(0,n.jsx)(eF,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":t.open,"aria-controls":o.contentId,"data-state":e8(t.open),...e,ref:(0,E.F)(r,o.onTriggerChange),onClick:r=>{e.onClick?.(r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),t.open||t.onOpenChange(!0))},onPointerMove:(0,S.M)(e.onPointerMove,re(r=>{l.onItemEnter(r),r.defaultPrevented||e.disabled||t.open||i.current||(l.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{t.onOpenChange(!0),m()},100))})),onPointerLeave:(0,S.M)(e.onPointerLeave,re(e=>{m();let r=t.content?.getBoundingClientRect();if(r){let n=t.content?.dataset.side,a="right"===n,s=r[a?"left":"right"],o=r[a?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:s,y:r.top},{x:o,y:r.top},{x:o,y:r.bottom},{x:s,y:r.bottom}],side:n}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,S.M)(e.onKeyDown,r=>{let n=""!==l.searchRef.current;!e.disabled&&(!n||" "!==r.key)&&ei[s.dir].includes(r.key)&&(t.onOpenChange(!0),t.content?.focus(),r.preventDefault())})})})});e3.displayName=e4;var e5="MenuSubContent",e9=a.forwardRef((e,r)=>{let t=eR(eS,e.__scopeMenu),{forceMount:s=t.forceMount,...o}=e,l=ev(eS,e.__scopeMenu),i=ej(eS,e.__scopeMenu),c=e2(e5,e.__scopeMenu),d=a.useRef(null),u=(0,E.e)(r,d);return(0,n.jsx)(eu.Provider,{scope:e.__scopeMenu,children:(0,n.jsx)(Z.z,{present:s||l.open,children:(0,n.jsx)(eu.Slot,{scope:e.__scopeMenu,children:(0,n.jsx)(eI,{id:c.contentId,"aria-labelledby":c.triggerId,...o,ref:u,align:"start",side:"rtl"===i.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{i.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,S.M)(e.onFocusOutside,e=>{e.target!==c.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,S.M)(e.onEscapeKeyDown,e=>{i.onClose(),e.preventDefault()}),onKeyDown:(0,S.M)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),t=ec[i.dir].includes(e.key);r&&t&&(l.onOpenChange(!1),c.trigger?.focus(),e.preventDefault())})})})})})});function e8(e){return e?"open":"closed"}function e6(e){return"indeterminate"===e}function e7(e){return e6(e)?"indeterminate":e?"checked":"unchecked"}function re(e){return r=>"mouse"===r.pointerType?e(r):void 0}e9.displayName=e5;var rr="DropdownMenu",[rt,rn]=(0,l.b)(rr,[eh]),ra=eh(),[rs,ro]=rt(rr),rl=e=>{let{__scopeDropdownMenu:r,children:t,dir:s,open:o,defaultOpen:l,onOpenChange:i,modal:c=!0}=e,d=ra(r),u=a.useRef(null),[m,f]=(0,P.T)({prop:o,defaultProp:l??!1,onChange:i,caller:rr});return(0,n.jsx)(rs,{scope:r,triggerId:(0,L.M)(),triggerRef:u,contentId:(0,L.M)(),open:m,onOpenChange:f,onOpenToggle:a.useCallback(()=>f(e=>!e),[f]),modal:c,children:(0,n.jsx)(ew,{...d,open:m,onOpenChange:f,dir:s,modal:c,children:t})})};rl.displayName=rr;var ri="DropdownMenuTrigger",rc=a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,disabled:a=!1,...s}=e,o=ro(ri,t),l=ra(t);return(0,n.jsx)(eN,{asChild:!0,...l,children:(0,n.jsx)(d.WV.button,{type:"button",id:o.triggerId,"aria-haspopup":"menu","aria-expanded":o.open,"aria-controls":o.open?o.contentId:void 0,"data-state":o.open?"open":"closed","data-disabled":a?"":void 0,disabled:a,...s,ref:(0,E.F)(r,o.triggerRef),onPointerDown:(0,S.M)(e.onPointerDown,e=>{a||0!==e.button||!1!==e.ctrlKey||(o.onOpenToggle(),o.open||e.preventDefault())}),onKeyDown:(0,S.M)(e.onKeyDown,e=>{!a&&(["Enter"," "].includes(e.key)&&o.onOpenToggle(),"ArrowDown"===e.key&&o.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});rc.displayName=ri;var rd=e=>{let{__scopeDropdownMenu:r,...t}=e,a=ra(r);return(0,n.jsx)(eM,{...a,...t})};rd.displayName="DropdownMenuPortal";var ru="DropdownMenuContent",rm=a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...s}=e,o=ro(ru,t),l=ra(t),i=a.useRef(!1);return(0,n.jsx)(eT,{id:o.contentId,"aria-labelledby":o.triggerId,...l,...s,ref:r,onCloseAutoFocus:(0,S.M)(e.onCloseAutoFocus,e=>{i.current||o.triggerRef.current?.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,S.M)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,t=0===r.button&&!0===r.ctrlKey,n=2===r.button||t;(!o.modal||n)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});rm.displayName=ru,a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...a}=e,s=ra(t);return(0,n.jsx)(eL,{...s,...a,ref:r})}).displayName="DropdownMenuGroup";var rf=a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...a}=e,s=ra(t);return(0,n.jsx)(eA,{...s,...a,ref:r})});rf.displayName="DropdownMenuLabel";var rp=a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...a}=e,s=ra(t);return(0,n.jsx)(eq,{...s,...a,ref:r})});rp.displayName="DropdownMenuItem";var rh=a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...a}=e,s=ra(t);return(0,n.jsx)(eK,{...s,...a,ref:r})});rh.displayName="DropdownMenuCheckboxItem",a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...a}=e,s=ra(t);return(0,n.jsx)(eU,{...s,...a,ref:r})}).displayName="DropdownMenuRadioGroup";var rx=a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...a}=e,s=ra(t);return(0,n.jsx)(eY,{...s,...a,ref:r})});rx.displayName="DropdownMenuRadioItem";var rg=a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...a}=e,s=ra(t);return(0,n.jsx)(e$,{...s,...a,ref:r})});rg.displayName="DropdownMenuItemIndicator";var ry=a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...a}=e,s=ra(t);return(0,n.jsx)(eJ,{...s,...a,ref:r})});ry.displayName="DropdownMenuSeparator",a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...a}=e,s=ra(t);return(0,n.jsx)(e0,{...s,...a,ref:r})}).displayName="DropdownMenuArrow";var rv=a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...a}=e,s=ra(t);return(0,n.jsx)(e3,{...s,...a,ref:r})});rv.displayName="DropdownMenuSubTrigger";var rb=a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...a}=e,s=ra(t);return(0,n.jsx)(e9,{...s,...a,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});rb.displayName="DropdownMenuSubContent";var rj=t(97751),rw=t(62312),rN=t(69224);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let rC=(0,rN.Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);a.forwardRef(({className:e,inset:r,children:t,...a},s)=>(0,n.jsxs)(rv,{ref:s,className:(0,C.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",r&&"pl-8",e),...a,children:[t,n.jsx(rj.Z,{className:"ml-auto h-4 w-4"})]})).displayName=rv.displayName,a.forwardRef(({className:e,...r},t)=>n.jsx(rb,{ref:t,className:(0,C.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})).displayName=rb.displayName;let rk=a.forwardRef(({className:e,sideOffset:r=4,...t},a)=>n.jsx(rd,{children:n.jsx(rm,{ref:a,sideOffset:r,className:(0,C.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})}));rk.displayName=rm.displayName;let rR=a.forwardRef(({className:e,inset:r,...t},a)=>n.jsx(rp,{ref:a,className:(0,C.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r&&"pl-8",e),...t}));rR.displayName=rp.displayName,a.forwardRef(({className:e,children:r,checked:t,...a},s)=>(0,n.jsxs)(rh,{ref:s,className:(0,C.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:t,...a,children:[n.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:n.jsx(rg,{children:n.jsx(rw.Z,{className:"h-4 w-4"})})}),r]})).displayName=rh.displayName,a.forwardRef(({className:e,children:r,...t},a)=>(0,n.jsxs)(rx,{ref:a,className:(0,C.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[n.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:n.jsx(rg,{children:n.jsx(rC,{className:"h-2 w-2 fill-current"})})}),r]})).displayName=rx.displayName;let rM=a.forwardRef(({className:e,inset:r,...t},a)=>n.jsx(rf,{ref:a,className:(0,C.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",e),...t}));rM.displayName=rf.displayName;let rS=a.forwardRef(({className:e,...r},t)=>n.jsx(ry,{ref:t,className:(0,C.cn)("-mx-1 my-1 h-px bg-muted",e),...r}));rS.displayName=ry.displayName;/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let rE=(0,rN.Z)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]);var rP=t(18822);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let rT=(0,rN.Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);var rD=t(13746);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r_=(0,rN.Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);function rO(){let{data:e,status:r}=(0,o.useSession)();if("loading"===r)return n.jsx("div",{className:"flex items-center space-x-2",children:n.jsx("div",{className:"h-8 w-8 bg-muted rounded-full animate-pulse"})});if(!e)return(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsxs)(s.z,{variant:"ghost",onClick:()=>(0,o.signIn)(),children:[n.jsx(rE,{className:"h-4 w-4 mr-2"}),"Sign In"]}),n.jsx(s.z,{onClick:()=>(0,o.signIn)(),children:"Get Started"})]});let t=e.user?.name?.split(" ").map(e=>e[0]).join("").toUpperCase()||"U";return(0,n.jsxs)(rl,{children:[n.jsx(rc,{asChild:!0,children:n.jsx(s.z,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,n.jsxs)(k,{className:"h-8 w-8",children:[n.jsx(R,{src:e.user?.image||"",alt:e.user?.name||""}),n.jsx(M,{children:t})]})})}),(0,n.jsxs)(rk,{className:"w-56",align:"end",forceMount:!0,children:[n.jsx(rM,{className:"font-normal",children:(0,n.jsxs)("div",{className:"flex flex-col space-y-1",children:[n.jsx("p",{className:"text-sm font-medium leading-none",children:e.user?.name||"User"}),n.jsx("p",{className:"text-xs leading-none text-muted-foreground",children:e.user?.email})]})}),n.jsx(rS,{}),(0,n.jsxs)(rR,{children:[n.jsx(rP.Z,{className:"mr-2 h-4 w-4"}),n.jsx("span",{children:"Profile"})]}),(0,n.jsxs)(rR,{children:[n.jsx(rT,{className:"mr-2 h-4 w-4"}),n.jsx("span",{children:"Billing"})]}),(0,n.jsxs)(rR,{children:[n.jsx(rD.Z,{className:"mr-2 h-4 w-4"}),n.jsx("span",{children:"Settings"})]}),n.jsx(rS,{}),(0,n.jsxs)(rR,{onClick:()=>(0,o.signOut)(),children:[n.jsx(r_,{className:"mr-2 h-4 w-4"}),n.jsx("span",{children:"Log out"})]})]})]})}var rI=t(99046),rL=t(96213);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let rA=(0,rN.Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),rz=(0,rN.Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]]),rZ=(0,rN.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),rq=(0,rN.Z)("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var rF=t(14513);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let rK=(0,rN.Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);var rW=t(20783),rV=t.n(rW);function rH({children:e}){let[r,t]=(0,a.useState)(!0),o=[{name:"Query Workspace",href:"/dashboard",icon:rI.Z,current:!0},{name:"Query History",href:"/dashboard/history",icon:rL.Z,current:!1},{name:"Connections",href:"/dashboard/connections",icon:rA,current:!1},{name:"Documentation",href:"/dashboard/docs",icon:rz,current:!1},{name:"Team",href:"/dashboard/team",icon:rZ,current:!1}],l=[{name:"Settings",href:"/dashboard/settings",icon:rD.Z},{name:"Help",href:"/dashboard/help",icon:rq},{name:"Profile",href:"/dashboard/profile",icon:rP.Z}];return(0,n.jsxs)("div",{className:"flex h-screen bg-background",children:[n.jsx("div",{className:`${r?"w-64":"w-16"} transition-all duration-300 ease-in-out`,children:(0,n.jsxs)("div",{className:"flex flex-col h-full bg-card border-r border-border",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-border",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[n.jsx(rI.Z,{className:"h-8 w-8 text-primary"}),r&&n.jsx("span",{className:"text-lg font-bold",children:"QueryCraft"})]}),n.jsx(s.z,{variant:"ghost",size:"icon",onClick:()=>t(!r),className:"h-8 w-8",children:r?n.jsx(rF.Z,{className:"h-4 w-4"}):n.jsx(rK,{className:"h-4 w-4"})})]}),n.jsx("nav",{className:"flex-1 p-4 space-y-2",children:o.map(e=>{let t=e.icon;return(0,n.jsxs)(rV(),{href:e.href,className:`flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ${e.current?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-muted"}`,children:[n.jsx(t,{className:"h-5 w-5 flex-shrink-0"}),r&&n.jsx("span",{children:e.name})]},e.name)})}),n.jsx("div",{className:"p-4 border-t border-border space-y-2",children:l.map(e=>{let t=e.icon;return(0,n.jsxs)(rV(),{href:e.href,className:"flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-muted transition-colors",children:[n.jsx(t,{className:"h-5 w-5 flex-shrink-0"}),r&&n.jsx("span",{children:e.name})]},e.name)})})]})}),(0,n.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[n.jsx("header",{className:"bg-card border-b border-border px-6 py-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[n.jsx("h1",{className:"text-2xl font-bold text-foreground",children:"SQL Workspace"}),n.jsx("p",{className:"text-sm text-muted-foreground",children:"Build, debug, and optimize your SQL queries with AI assistance"})]}),(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsxs)(s.z,{variant:"outline",size:"sm",children:[n.jsx(rA,{className:"h-4 w-4 mr-2"}),"New Connection"]}),n.jsx(rO,{})]})]})}),n.jsx("main",{className:"flex-1 overflow-hidden",children:e})]})]})}},88435:(e,r,t)=>{"use strict";t.r(r),t.d(r,{QueryWorkspace:()=>rl});var n=t(95344),a=t(3729),s=t(23673),o=t(5094),l=t(19591),i=t(97909),c=t(69224);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,c.Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]),u=(0,c.Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),m=(0,c.Z)("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z",key:"y3tblf"}]]),f=(0,c.Z)("ThumbsDown",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22h0a3.13 3.13 0 0 1-3-3.88Z",key:"s6e0r"}]]);var p=t(18822);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let h=(0,c.Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);var x=t(11453),g=t(51540);function y({onQueryGenerated:e}){let{messages:r,isAILoading:t,generateQuery:c,setActiveTab:y,saveQueryToDatabase:v}=(0,g.l)(),[b,j]=(0,a.useState)(""),w=(0,a.useRef)(null),N=(0,a.useRef)(null),C=()=>{w.current?.scrollIntoView({behavior:"smooth"})};(0,a.useEffect)(()=>{C()},[r]);let k=async()=>{b.trim()&&!t&&(j(""),await c(b))},R=e=>{navigator.clipboard.writeText(e)},M=async(r,t,n)=>{if(e(r),y("editor"),t&&n)try{await v(t,r,n)}catch(e){console.error("Failed to save query to database:",e)}};return(0,n.jsxs)("div",{className:"flex flex-col h-full",children:[(0,n.jsxs)("div",{className:"flex-1 overflow-y-auto p-6 space-y-4",children:[r.map(e=>(0,n.jsxs)("div",{className:`flex gap-3 ${"user"===e.role?"justify-end":"justify-start"}`,children:["assistant"===e.role&&n.jsx("div",{className:"flex-shrink-0",children:n.jsx("div",{className:"h-8 w-8 bg-primary rounded-full flex items-center justify-center",children:n.jsx(i.Z,{className:"h-4 w-4 text-primary-foreground"})})}),n.jsx("div",{className:`max-w-2xl ${"user"===e.role?"order-first":""}`,children:(0,n.jsxs)(s.Zb,{className:`p-4 ${"user"===e.role?"bg-primary text-primary-foreground":""}`,children:[(0,n.jsxs)("div",{className:"space-y-3",children:[n.jsx("div",{className:"text-sm",children:e.content}),"query"===e.type&&e.metadata?.sql&&(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"bg-muted rounded-lg p-3",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,n.jsxs)(l.C,{variant:"secondary",className:"text-xs",children:[n.jsx(d,{className:"h-3 w-3 mr-1"}),"Generated SQL"]}),n.jsx("div",{className:"flex gap-1",children:n.jsx(o.z,{variant:"ghost",size:"sm",onClick:()=>R(e.metadata.sql),children:n.jsx(u,{className:"h-3 w-3"})})})]}),n.jsx("pre",{className:"text-xs font-mono overflow-x-auto",children:n.jsx("code",{children:e.metadata.sql})})]}),e.metadata.explanation&&(0,n.jsxs)("div",{className:"text-sm text-muted-foreground",children:[n.jsx("strong",{children:"Explanation:"})," ",e.metadata.explanation]}),(0,n.jsxs)("div",{className:"flex gap-2",children:[n.jsx(o.z,{size:"sm",onClick:()=>M(e.metadata.sql,e.metadata?.userInput,e.metadata?.explanation),children:"Use in Editor"}),(0,n.jsxs)(o.z,{variant:"outline",size:"sm",children:[n.jsx(m,{className:"h-3 w-3 mr-1"}),"Good"]}),(0,n.jsxs)(o.z,{variant:"outline",size:"sm",children:[n.jsx(f,{className:"h-3 w-3 mr-1"}),"Improve"]})]})]})]}),n.jsx("div",{className:"text-xs text-muted-foreground mt-2",children:(0,x.SY)(e.timestamp)})]})}),"user"===e.role&&n.jsx("div",{className:"flex-shrink-0",children:n.jsx("div",{className:"h-8 w-8 bg-muted rounded-full flex items-center justify-center",children:n.jsx(p.Z,{className:"h-4 w-4 text-muted-foreground"})})})]},e.id)),t&&(0,n.jsxs)("div",{className:"flex gap-3",children:[n.jsx("div",{className:"flex-shrink-0",children:n.jsx("div",{className:"h-8 w-8 bg-primary rounded-full flex items-center justify-center",children:n.jsx(i.Z,{className:"h-4 w-4 text-primary-foreground animate-pulse"})})}),n.jsx(s.Zb,{className:"p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[n.jsx("div",{className:"loading-spinner"}),n.jsx("span",{className:"text-sm text-muted-foreground",children:"AI is thinking..."})]})})]}),n.jsx("div",{ref:w})]}),(0,n.jsxs)("div",{className:"border-t border-border p-4",children:[(0,n.jsxs)("div",{className:"flex gap-3",children:[n.jsx("div",{className:"flex-1",children:n.jsx("textarea",{ref:N,value:b,onChange:e=>j(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),k())},placeholder:"Describe the data you want to query... (e.g., 'Show me the top 10 customers by revenue this month')",className:"querycraft-textarea resize-none",rows:3,disabled:t})}),n.jsx(o.z,{onClick:k,disabled:!b.trim()||t,size:"lg",children:n.jsx(h,{className:"h-4 w-4"})})]}),n.jsx("div",{className:"text-xs text-muted-foreground mt-2",children:"Press Enter to send, Shift+Enter for new line"})]})]})}function v(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function b(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?v(Object(t),!0).forEach(function(r){var n;n=t[r],r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):v(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function j(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=Array(r);t<r;t++)n[t]=e[t];return n}function w(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function N(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?w(Object(t),!0).forEach(function(r){var n;n=t[r],r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):w(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function C(e){return function r(){for(var t=this,n=arguments.length,a=Array(n),s=0;s<n;s++)a[s]=arguments[s];return a.length>=e.length?e.apply(this,a):function(){for(var e=arguments.length,n=Array(e),s=0;s<e;s++)n[s]=arguments[s];return r.apply(t,[].concat(a,n))}}}function k(e){return({}).toString.call(e).includes("Object")}function R(e){return"function"==typeof e}var M=C(function(e,r){throw Error(e[r]||e.default)})({initialIsRequired:"initial state is required",initialType:"initial state should be an object",initialContent:"initial state shouldn't be an empty object",handlerType:"handler should be an object or a function",handlersType:"all handlers should be a functions",selectorType:"selector should be a function",changeType:"provided value of changes should be an object",changeField:'it seams you want to change a field in the state which is not specified in the "initial" state',default:"an unknown error accured in `state-local` package"}),S={changes:function(e,r){return k(r)||M("changeType"),Object.keys(r).some(function(r){return!Object.prototype.hasOwnProperty.call(e,r)})&&M("changeField"),r},selector:function(e){R(e)||M("selectorType")},handler:function(e){R(e)||k(e)||M("handlerType"),k(e)&&Object.values(e).some(function(e){return!R(e)})&&M("handlersType")},initial:function(e){e||M("initialIsRequired"),k(e)||M("initialType"),Object.keys(e).length||M("initialContent")}};function E(e,r){return R(r)?r(e.current):r}function P(e,r){return e.current=N(N({},e.current),r),r}function T(e,r,t){return R(r)?r(e.current):Object.keys(t).forEach(function(t){var n;return null===(n=r[t])||void 0===n?void 0:n.call(r,e.current[t])}),t}var D={configIsRequired:"the configuration object is required",configType:"the configuration object should be an object",default:"an unknown error accured in `@monaco-editor/loader` package",deprecation:"Deprecation warning!\n    You are using deprecated way of configuration.\n\n    Instead of using\n      monaco.config({ urls: { monacoBase: '...' } })\n    use\n      monaco.config({ paths: { vs: '...' } })\n\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\n  "},_=(function(e){return function r(){for(var t=this,n=arguments.length,a=Array(n),s=0;s<n;s++)a[s]=arguments[s];return a.length>=e.length?e.apply(this,a):function(){for(var e=arguments.length,n=Array(e),s=0;s<e;s++)n[s]=arguments[s];return r.apply(t,[].concat(a,n))}}})(function(e,r){throw Error(e[r]||e.default)})(D);let O={config:function(e){return e||_("configIsRequired"),({}).toString.call(e).includes("Object")||_("configType"),e.urls?(console.warn(D.deprecation),{paths:{vs:e.urls.monacoBase}}):e}},I=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return function(e){return r.reduceRight(function(e,r){return r(e)},e)}};var L={type:"cancelation",msg:"operation is manually canceled"};let A=function(e){var r=!1,t=new Promise(function(t,n){e.then(function(e){return r?n(L):t(e)}),e.catch(n)});return t.cancel=function(){return r=!0},t};var z=function(e,r){return function(e){if(Array.isArray(e))return e}(e)||function(e,r){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var t=[],n=!0,a=!1,s=void 0;try{for(var o,l=e[Symbol.iterator]();!(n=(o=l.next()).done)&&(t.push(o.value),!r||t.length!==r);n=!0);}catch(e){a=!0,s=e}finally{try{n||null==l.return||l.return()}finally{if(a)throw s}}return t}}(e,r)||function(e,r){if(e){if("string"==typeof e)return j(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);if("Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return j(e,r)}}(e,r)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(({create:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};S.initial(e),S.handler(r);var t={current:e},n=C(T)(t,r),a=C(P)(t),s=C(S.changes)(e),o=C(E)(t);return[function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(e){return e};return S.selector(e),e(t.current)},function(e){(function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return function(e){return r.reduceRight(function(e,r){return r(e)},e)}})(n,a,s,o)(e)}]}}).create({config:{paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}},isInitialized:!1,resolve:null,reject:null,monaco:null}),2),Z=z[0],q=z[1];function F(e){return document.body.appendChild(e)}function K(e){var r,t,n=Z(function(e){return{config:e.config,reject:e.reject}}),a=(r="".concat(n.config.paths.vs,"/loader.js"),t=document.createElement("script"),r&&(t.src=r),t);return a.onload=function(){return e()},a.onerror=n.reject,a}function W(){var e=Z(function(e){return{config:e.config,resolve:e.resolve,reject:e.reject}}),r=window.require;r.config(e.config),r(["vs/editor/editor.main"],function(r){V(r),e.resolve(r)},function(r){e.reject(r)})}function V(e){Z().monaco||q({monaco:e})}var H=new Promise(function(e,r){return q({resolve:e,reject:r})});let U={config:function(e){var r=O.config(e),t=r.monaco,n=function(e,r){if(null==e)return{};var t,n,a=function(e,r){if(null==e)return{};var t,n,a={},s=Object.keys(e);for(n=0;n<s.length;n++)t=s[n],r.indexOf(t)>=0||(a[t]=e[t]);return a}(e,r);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)t=s[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(r,["monaco"]);q(function(e){return{config:function e(r,t){return Object.keys(t).forEach(function(n){t[n]instanceof Object&&r[n]&&Object.assign(t[n],e(r[n],t[n]))}),b(b({},r),t)}(e.config,n),monaco:t}})},init:function(){var e=Z(function(e){return{monaco:e.monaco,isInitialized:e.isInitialized,resolve:e.resolve}});if(!e.isInitialized){if(q({isInitialized:!0}),e.monaco)return e.resolve(e.monaco),A(H);if(window.monaco&&window.monaco.editor)return V(window.monaco),e.resolve(window.monaco),A(H);I(F,K)(W)}return A(H)},__getMonacoInstance:function(){return Z(function(e){return e.monaco})}};var B={wrapper:{display:"flex",position:"relative",textAlign:"initial"},fullWidth:{width:"100%"},hide:{display:"none"}},Y={container:{display:"flex",height:"100%",width:"100%",justifyContent:"center",alignItems:"center"}},G=function({children:e}){return a.createElement("div",{style:Y.container},e)},Q=(0,a.memo)(function({width:e,height:r,isEditorReady:t,loading:n,_ref:s,className:o,wrapperProps:l}){return a.createElement("section",{style:{...B.wrapper,width:e,height:r},...l},!t&&a.createElement(G,null,n),a.createElement("div",{ref:s,style:{...B.fullWidth,...!t&&B.hide},className:o}))}),X=function(e){(0,a.useEffect)(e,[])},$=function(e,r,t=!0){let n=(0,a.useRef)(!0);(0,a.useEffect)(n.current||!t?()=>{n.current=!1}:e,r)};function J(){}function ee(e,r,t,n){return e.editor.getModel(er(e,n))||e.editor.createModel(r,t,n?er(e,n):void 0)}function er(e,r){return e.Uri.parse(r)}var et=function(e){let r=(0,a.useRef)();return(0,a.useEffect)(()=>{r.current=e},[e]),r.current},en=new Map,ea=(0,a.memo)(function({defaultValue:e,defaultLanguage:r,defaultPath:t,value:n,language:s,path:o,theme:l="light",line:i,loading:c="Loading...",options:d={},overrideServices:u={},saveViewState:m=!0,keepCurrentModel:f=!1,width:p="100%",height:h="100%",className:x,wrapperProps:g={},beforeMount:y=J,onMount:v=J,onChange:b,onValidate:j=J}){let[w,N]=(0,a.useState)(!1),[C,k]=(0,a.useState)(!0),R=(0,a.useRef)(null),M=(0,a.useRef)(null),S=(0,a.useRef)(null),E=(0,a.useRef)(v),P=(0,a.useRef)(y),T=(0,a.useRef)(),D=(0,a.useRef)(n),_=et(o),O=(0,a.useRef)(!1),I=(0,a.useRef)(!1);X(()=>{let e=U.init();return e.then(e=>(R.current=e)&&k(!1)).catch(e=>e?.type!=="cancelation"&&console.error("Monaco initialization: error:",e)),()=>M.current?void(T.current?.dispose(),f?m&&en.set(o,M.current.saveViewState()):M.current.getModel()?.dispose(),M.current.dispose()):e.cancel()}),$(()=>{let a=ee(R.current,e||n||"",r||s||"",o||t||"");a!==M.current?.getModel()&&(m&&en.set(_,M.current?.saveViewState()),M.current?.setModel(a),m&&M.current?.restoreViewState(en.get(o)))},[o],w),$(()=>{M.current?.updateOptions(d)},[d],w),$(()=>{M.current&&void 0!==n&&(M.current.getOption(R.current.editor.EditorOption.readOnly)?M.current.setValue(n):n===M.current.getValue()||(I.current=!0,M.current.executeEdits("",[{range:M.current.getModel().getFullModelRange(),text:n,forceMoveMarkers:!0}]),M.current.pushUndoStop(),I.current=!1))},[n],w),$(()=>{let e=M.current?.getModel();e&&s&&R.current?.editor.setModelLanguage(e,s)},[s],w),$(()=>{void 0!==i&&M.current?.revealLine(i)},[i],w),$(()=>{R.current?.editor.setTheme(l)},[l],w);let L=(0,a.useCallback)(()=>{if(!(!S.current||!R.current)&&!O.current){P.current(R.current);let a=o||t,c=ee(R.current,n||e||"",r||s||"",a||"");M.current=R.current?.editor.create(S.current,{model:c,automaticLayout:!0,...d},u),m&&M.current.restoreViewState(en.get(a)),R.current.editor.setTheme(l),void 0!==i&&M.current.revealLine(i),N(!0),O.current=!0}},[e,r,t,n,s,o,d,u,m,l,i]);return(0,a.useEffect)(()=>{w&&E.current(M.current,R.current)},[w]),(0,a.useEffect)(()=>{C||w||L()},[C,w,L]),D.current=n,(0,a.useEffect)(()=>{w&&b&&(T.current?.dispose(),T.current=M.current?.onDidChangeModelContent(e=>{I.current||b(M.current.getValue(),e)}))},[w,b]),(0,a.useEffect)(()=>{if(w){let e=R.current.editor.onDidChangeMarkers(e=>{let r=M.current.getModel()?.uri;if(r&&e.find(e=>e.path===r.path)){let e=R.current.editor.getModelMarkers({resource:r});j?.(e)}});return()=>{e?.dispose()}}return()=>{}},[w,j]),a.createElement(Q,{width:p,height:h,isEditorReady:w,loading:c,_ref:S,className:x,wrapperProps:g})}),es=t(7060);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let eo=(0,c.Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var el=t(91991);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let ei=(0,c.Z)("AlignLeft",[["line",{x1:"21",x2:"3",y1:"6",y2:"6",key:"1fp77t"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}],["line",{x1:"17",x2:"3",y1:"18",y2:"18",key:"1awlsn"}]]),ec=(0,c.Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]);function ed({value:e,onChange:r,language:t,readOnly:i=!1}){let c=(0,a.useRef)(null),d=(0,a.useCallback)((e,r)=>{c.current=e,r.languages.registerCompletionItemProvider("sql",{provideCompletionItems:(e,t)=>({suggestions:[{label:"SELECT",kind:r.languages.CompletionItemKind.Keyword,insertText:"SELECT ",documentation:"SELECT statement"},{label:"FROM",kind:r.languages.CompletionItemKind.Keyword,insertText:"FROM ",documentation:"FROM clause"},{label:"WHERE",kind:r.languages.CompletionItemKind.Keyword,insertText:"WHERE ",documentation:"WHERE clause"},{label:"JOIN",kind:r.languages.CompletionItemKind.Keyword,insertText:"JOIN ",documentation:"JOIN clause"},{label:"GROUP BY",kind:r.languages.CompletionItemKind.Keyword,insertText:"GROUP BY ",documentation:"GROUP BY clause"},{label:"ORDER BY",kind:r.languages.CompletionItemKind.Keyword,insertText:"ORDER BY ",documentation:"ORDER BY clause"}]})}),r.editor.defineTheme("querycraft-theme",{base:"vs",inherit:!0,rules:[{token:"keyword.sql",foreground:"3b82f6",fontStyle:"bold"},{token:"string.sql",foreground:"22c55e"},{token:"comment.sql",foreground:"6b7280",fontStyle:"italic"}],colors:{"editor.background":"#ffffff","editor.foreground":"#1f2937","editor.lineHighlightBackground":"#f9fafb","editor.selectionBackground":"#dbeafe"}}),r.editor.setTheme("querycraft-theme")},[]),{errors:u,warnings:m}=(()=>{let r=[],t=[];return e.trim()&&(e.toLowerCase().includes("select")||e.toLowerCase().includes("insert")||e.toLowerCase().includes("update")||e.toLowerCase().includes("delete")||r.push("Query should contain a valid SQL statement"),e.toLowerCase().includes("select *")&&t.push("Consider specifying column names instead of using SELECT *"),e.toLowerCase().includes("where")&&!e.toLowerCase().includes("limit")&&t.push("Consider adding a LIMIT clause for large datasets")),{errors:r,warnings:t}})();return(0,n.jsxs)("div",{className:"flex flex-col h-full space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[n.jsx("h3",{className:"font-semibold",children:"SQL Editor"}),n.jsx(l.C,{variant:"outline",children:t.toUpperCase()}),0===u.length&&0===m.length&&e.trim()&&(0,n.jsxs)(l.C,{variant:"default",className:"bg-green-100 text-green-800",children:[n.jsx(es.Z,{className:"h-3 w-3 mr-1"}),"Valid"]}),u.length>0&&(0,n.jsxs)(l.C,{variant:"destructive",children:[n.jsx(eo,{className:"h-3 w-3 mr-1"}),u.length," Error",u.length>1?"s":""]}),m.length>0&&(0,n.jsxs)(l.C,{variant:"secondary",className:"bg-yellow-100 text-yellow-800",children:[n.jsx(el.Z,{className:"h-3 w-3 mr-1"}),m.length," Warning",m.length>1?"s":""]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)(o.z,{variant:"outline",size:"sm",onClick:()=>{c.current&&c.current.getAction("editor.action.formatDocument").run()},children:[n.jsx(ei,{className:"h-4 w-4 mr-2"}),"Format"]}),(0,n.jsxs)(o.z,{variant:"outline",size:"sm",children:[n.jsx(ec,{className:"h-4 w-4 mr-2"}),"Optimize"]})]})]}),n.jsx(s.Zb,{className:"flex-1 overflow-hidden sql-editor",children:n.jsx(ea,{height:"100%",language:"sql",value:e,onChange:e=>r(e||""),onMount:d,options:{readOnly:i,minimap:{enabled:!1},fontSize:14,lineNumbers:"on",roundedSelection:!1,scrollBeyondLastLine:!1,automaticLayout:!0,wordWrap:"on",folding:!0,lineDecorationsWidth:10,lineNumbersMinChars:3,glyphMargin:!1,contextmenu:!0,mouseWheelZoom:!0,smoothScrolling:!0,cursorBlinking:"blink",cursorStyle:"line",renderWhitespace:"selection",renderControlCharacters:!1,fontFamily:"JetBrains Mono, Fira Code, Consolas, monospace",fontLigatures:!0,suggest:{showKeywords:!0,showSnippets:!0,showFunctions:!0},quickSuggestions:{other:!0,comments:!1,strings:!1},parameterHints:{enabled:!0},acceptSuggestionOnCommitCharacter:!0,acceptSuggestionOnEnter:"on",accessibilitySupport:"auto"}})}),(u.length>0||m.length>0)&&(0,n.jsxs)("div",{className:"space-y-2",children:[u.map((e,r)=>(0,n.jsxs)("div",{className:"flex items-center gap-2 text-sm text-red-600",children:[n.jsx(eo,{className:"h-4 w-4"}),e]},`error-${r}`)),m.map((e,r)=>(0,n.jsxs)("div",{className:"flex items-center gap-2 text-sm text-yellow-600",children:[n.jsx(el.Z,{className:"h-4 w-4"}),e]},`warning-${r}`))]})]})}var eu=t(99046),em=t(25390),ef=t(97751),ep=t(83389),eh=t(82965);function ex({isConnected:e}){let[r,t]=(0,a.useState)(new Set),i={database:"ecommerce_db",tables:[{name:"customers",rowCount:15420,columns:[{name:"customer_id",type:"INT",isPrimaryKey:!0,isNullable:!1},{name:"name",type:"VARCHAR(255)",isPrimaryKey:!1,isNullable:!1},{name:"email",type:"VARCHAR(255)",isPrimaryKey:!1,isNullable:!1},{name:"phone",type:"VARCHAR(20)",isPrimaryKey:!1,isNullable:!0},{name:"created_at",type:"TIMESTAMP",isPrimaryKey:!1,isNullable:!1}]},{name:"orders",rowCount:45230,columns:[{name:"order_id",type:"INT",isPrimaryKey:!0,isNullable:!1},{name:"customer_id",type:"INT",isPrimaryKey:!1,isNullable:!1},{name:"order_date",type:"TIMESTAMP",isPrimaryKey:!1,isNullable:!1},{name:"total_amount",type:"DECIMAL(10,2)",isPrimaryKey:!1,isNullable:!1},{name:"status",type:"VARCHAR(50)",isPrimaryKey:!1,isNullable:!1}]},{name:"order_items",rowCount:128450,columns:[{name:"item_id",type:"INT",isPrimaryKey:!0,isNullable:!1},{name:"order_id",type:"INT",isPrimaryKey:!1,isNullable:!1},{name:"product_id",type:"INT",isPrimaryKey:!1,isNullable:!1},{name:"quantity",type:"INT",isPrimaryKey:!1,isNullable:!1},{name:"unit_price",type:"DECIMAL(10,2)",isPrimaryKey:!1,isNullable:!1}]},{name:"products",rowCount:2340,columns:[{name:"product_id",type:"INT",isPrimaryKey:!0,isNullable:!1},{name:"name",type:"VARCHAR(255)",isPrimaryKey:!1,isNullable:!1},{name:"description",type:"TEXT",isPrimaryKey:!1,isNullable:!0},{name:"price",type:"DECIMAL(10,2)",isPrimaryKey:!1,isNullable:!1},{name:"category_id",type:"INT",isPrimaryKey:!1,isNullable:!1}]}]},c=e=>{let n=new Set(r);n.has(e)?n.delete(e):n.add(e),t(n)};return e?(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"p-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[n.jsx(eu.Z,{className:"h-4 w-4 text-primary"}),n.jsx("span",{className:"font-medium",children:i.database})]}),(0,n.jsxs)("div",{className:"text-xs text-muted-foreground",children:[i.tables.length," tables"]})]}),n.jsx("div",{className:"space-y-1",children:i.tables.map(e=>(0,n.jsxs)("div",{className:"schema-table",children:[(0,n.jsxs)("button",{onClick:()=>c(e.name),className:"schema-table-header w-full",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[r.has(e.name)?n.jsx(em.Z,{className:"h-4 w-4"}):n.jsx(ef.Z,{className:"h-4 w-4"}),n.jsx(ep.Z,{className:"h-4 w-4 text-primary"}),n.jsx("span",{className:"font-medium",children:e.name})]}),n.jsx("div",{className:"flex items-center gap-2",children:(0,n.jsxs)(l.C,{variant:"secondary",className:"text-xs",children:[e.rowCount.toLocaleString()," rows"]})})]}),r.has(e.name)&&n.jsx("div",{className:"schema-table-content",children:e.columns.map(e=>(0,n.jsxs)("div",{className:"schema-column",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 flex-1",children:[e.isPrimaryKey&&n.jsx(eh.Z,{className:"h-3 w-3 text-yellow-500"}),n.jsx("span",{className:"text-sm font-medium",children:e.name})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[n.jsx("span",{className:"schema-column-type text-xs",children:e.type}),!e.isNullable&&n.jsx(l.C,{variant:"outline",className:"text-xs px-1 py-0",children:"NOT NULL"})]})]},e.name))})]},e.name))}),(0,n.jsxs)(s.Zb,{className:"mx-4",children:[n.jsx(s.Ol,{className:"pb-3",children:(0,n.jsxs)(s.ll,{className:"text-sm flex items-center gap-2",children:[n.jsx(el.Z,{className:"h-4 w-4"}),"Schema Statistics"]})}),(0,n.jsxs)(s.aY,{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex justify-between text-sm",children:[n.jsx("span",{children:"Total Tables:"}),n.jsx("span",{className:"font-medium",children:i.tables.length})]}),(0,n.jsxs)("div",{className:"flex justify-between text-sm",children:[n.jsx("span",{children:"Total Columns:"}),n.jsx("span",{className:"font-medium",children:i.tables.reduce((e,r)=>e+r.columns.length,0)})]}),(0,n.jsxs)("div",{className:"flex justify-between text-sm",children:[n.jsx("span",{children:"Total Rows:"}),n.jsx("span",{className:"font-medium",children:i.tables.reduce((e,r)=>e+r.rowCount,0).toLocaleString()})]})]})]})]}):(0,n.jsxs)("div",{className:"p-4 text-center",children:[n.jsx(eu.Z,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),n.jsx("p",{className:"text-sm text-muted-foreground mb-4",children:"Connect to a database to explore its schema"}),n.jsx(o.z,{variant:"outline",size:"sm",children:"Connect Database"})]})}/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let eg=(0,c.Z)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);var ey=t(96885),ev=t(25545);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let eb=(0,c.Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);function ej(){let[e,r]=(0,a.useState)(!1),t={executionTime:245,rowsAffected:10,columns:[{name:"customer_id",type:"INT"},{name:"name",type:"VARCHAR"},{name:"email",type:"VARCHAR"},{name:"total_orders",type:"INT"},{name:"total_spent",type:"DECIMAL"}],data:[{customer_id:1,name:"John Doe",email:"<EMAIL>",total_orders:15,total_spent:2450.5},{customer_id:2,name:"Jane Smith",email:"<EMAIL>",total_orders:12,total_spent:1890.25},{customer_id:3,name:"Bob Johnson",email:"<EMAIL>",total_orders:8,total_spent:1234.75},{customer_id:4,name:"Alice Brown",email:"<EMAIL>",total_orders:6,total_spent:987.5},{customer_id:5,name:"Charlie Wilson",email:"<EMAIL>",total_orders:4,total_spent:567.25}]};return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[n.jsx("h3",{className:"text-lg font-semibold",children:"Query Results"}),n.jsx("p",{className:"text-sm text-muted-foreground",children:"Last executed: 2 minutes ago"})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)(o.z,{variant:"outline",size:"sm",onClick:()=>{r(!0),setTimeout(()=>r(!1),1e3)},disabled:e,children:[n.jsx(eg,{className:`h-4 w-4 mr-2 ${e?"animate-spin":""}`}),"Refresh"]}),(0,n.jsxs)(o.z,{variant:"outline",size:"sm",onClick:()=>{console.log("Exporting results...")},children:[n.jsx(ey.Z,{className:"h-4 w-4 mr-2"}),"Export"]})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[n.jsx(s.Zb,{children:n.jsx(s.aY,{className:"p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[n.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:n.jsx(ev.Z,{className:"h-4 w-4 text-primary"})}),(0,n.jsxs)("div",{children:[n.jsx("div",{className:"text-sm text-muted-foreground",children:"Execution Time"}),(0,n.jsxs)("div",{className:"text-lg font-semibold",children:[t.executionTime,"ms"]})]})]})})}),n.jsx(s.Zb,{children:n.jsx(s.aY,{className:"p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[n.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:n.jsx(eu.Z,{className:"h-4 w-4 text-green-600"})}),(0,n.jsxs)("div",{children:[n.jsx("div",{className:"text-sm text-muted-foreground",children:"Rows Returned"}),n.jsx("div",{className:"text-lg font-semibold",children:t.rowsAffected})]})]})})}),n.jsx(s.Zb,{children:n.jsx(s.aY,{className:"p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[n.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:n.jsx(eb,{className:"h-4 w-4 text-blue-600"})}),(0,n.jsxs)("div",{children:[n.jsx("div",{className:"text-sm text-muted-foreground",children:"Performance"}),n.jsx("div",{className:"text-lg font-semibold",children:n.jsx(l.C,{variant:"default",className:"bg-green-100 text-green-800",children:"Excellent"})})]})]})})})]}),(0,n.jsxs)(s.Zb,{children:[n.jsx(s.Ol,{children:n.jsx(s.ll,{className:"text-base",children:"Data Results"})}),n.jsx(s.aY,{children:n.jsx("div",{className:"overflow-x-auto",children:(0,n.jsxs)("table",{className:"w-full border-collapse",children:[n.jsx("thead",{children:n.jsx("tr",{className:"border-b border-border",children:t.columns.map(e=>n.jsx("th",{className:"text-left p-3 font-medium text-sm text-muted-foreground",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[e.name,n.jsx(l.C,{variant:"outline",className:"text-xs",children:e.type})]})},e.name))})}),n.jsx("tbody",{children:t.data.map((e,r)=>(0,n.jsxs)("tr",{className:"border-b border-border hover:bg-muted/50",children:[n.jsx("td",{className:"p-3 text-sm",children:e.customer_id}),n.jsx("td",{className:"p-3 text-sm font-medium",children:e.name}),n.jsx("td",{className:"p-3 text-sm text-muted-foreground",children:e.email}),n.jsx("td",{className:"p-3 text-sm",children:e.total_orders}),(0,n.jsxs)("td",{className:"p-3 text-sm font-medium",children:["$",e.total_spent.toFixed(2)]})]},r))})]})})})]}),(0,n.jsxs)(s.Zb,{children:[n.jsx(s.Ol,{children:(0,n.jsxs)(s.ll,{className:"text-base flex items-center gap-2",children:[n.jsx(eb,{className:"h-4 w-4"}),"Query Analysis"]})}),n.jsx(s.aY,{className:"space-y-4",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"font-medium text-sm mb-2",children:"Performance Insights"}),(0,n.jsxs)("ul",{className:"space-y-1 text-sm text-muted-foreground",children:[(0,n.jsxs)("li",{className:"flex items-center gap-2",children:[n.jsx("div",{className:"h-2 w-2 bg-green-500 rounded-full"}),"Query executed efficiently"]}),(0,n.jsxs)("li",{className:"flex items-center gap-2",children:[n.jsx("div",{className:"h-2 w-2 bg-green-500 rounded-full"}),"Proper index usage detected"]}),(0,n.jsxs)("li",{className:"flex items-center gap-2",children:[n.jsx("div",{className:"h-2 w-2 bg-yellow-500 rounded-full"}),"Consider adding LIMIT clause"]})]})]}),(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"font-medium text-sm mb-2",children:"Optimization Suggestions"}),(0,n.jsxs)("ul",{className:"space-y-1 text-sm text-muted-foreground",children:[(0,n.jsxs)("li",{className:"flex items-center gap-2",children:[n.jsx(eo,{className:"h-3 w-3 text-blue-500"}),"Add composite index on (customer_id, order_date)"]}),(0,n.jsxs)("li",{className:"flex items-center gap-2",children:[n.jsx(eo,{className:"h-3 w-3 text-blue-500"}),"Consider partitioning orders table by date"]})]})]})]})})]})]})}var ew=t(62409),eN=t(43234),eC=t(98462),ek=t(31405),eR=t(2256),eM=t(3975),eS=t(16069),eE=t(2633),eP=t(85222),eT="ScrollArea",[eD,e_]=(0,eC.b)(eT),[eO,eI]=eD(eT),eL=a.forwardRef((e,r)=>{let{__scopeScrollArea:t,type:s="hover",dir:o,scrollHideDelay:l=600,...i}=e,[c,d]=a.useState(null),[u,m]=a.useState(null),[f,p]=a.useState(null),[h,x]=a.useState(null),[g,y]=a.useState(null),[v,b]=a.useState(0),[j,w]=a.useState(0),[N,C]=a.useState(!1),[k,R]=a.useState(!1),M=(0,ek.e)(r,e=>d(e)),S=(0,eM.gm)(o);return(0,n.jsx)(eO,{scope:t,type:s,dir:S,scrollHideDelay:l,scrollArea:c,viewport:u,onViewportChange:m,content:f,onContentChange:p,scrollbarX:h,onScrollbarXChange:x,scrollbarXEnabled:N,onScrollbarXEnabledChange:C,scrollbarY:g,onScrollbarYChange:y,scrollbarYEnabled:k,onScrollbarYEnabledChange:R,onCornerWidthChange:b,onCornerHeightChange:w,children:(0,n.jsx)(ew.WV.div,{dir:S,...i,ref:M,style:{position:"relative","--radix-scroll-area-corner-width":v+"px","--radix-scroll-area-corner-height":j+"px",...e.style}})})});eL.displayName=eT;var eA="ScrollAreaViewport",ez=a.forwardRef((e,r)=>{let{__scopeScrollArea:t,children:s,nonce:o,...l}=e,i=eI(eA,t),c=a.useRef(null),d=(0,ek.e)(r,c,i.onViewportChange);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,n.jsx)(ew.WV.div,{"data-radix-scroll-area-viewport":"",...l,ref:d,style:{overflowX:i.scrollbarXEnabled?"scroll":"hidden",overflowY:i.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,n.jsx)("div",{ref:i.onContentChange,style:{minWidth:"100%",display:"table"},children:s})})]})});ez.displayName=eA;var eZ="ScrollAreaScrollbar",eq=a.forwardRef((e,r)=>{let{forceMount:t,...s}=e,o=eI(eZ,e.__scopeScrollArea),{onScrollbarXEnabledChange:l,onScrollbarYEnabledChange:i}=o,c="horizontal"===e.orientation;return a.useEffect(()=>(c?l(!0):i(!0),()=>{c?l(!1):i(!1)}),[c,l,i]),"hover"===o.type?(0,n.jsx)(eF,{...s,ref:r,forceMount:t}):"scroll"===o.type?(0,n.jsx)(eK,{...s,ref:r,forceMount:t}):"auto"===o.type?(0,n.jsx)(eW,{...s,ref:r,forceMount:t}):"always"===o.type?(0,n.jsx)(eV,{...s,ref:r}):null});eq.displayName=eZ;var eF=a.forwardRef((e,r)=>{let{forceMount:t,...s}=e,o=eI(eZ,e.__scopeScrollArea),[l,i]=a.useState(!1);return a.useEffect(()=>{let e=o.scrollArea,r=0;if(e){let t=()=>{window.clearTimeout(r),i(!0)},n=()=>{r=window.setTimeout(()=>i(!1),o.scrollHideDelay)};return e.addEventListener("pointerenter",t),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(r),e.removeEventListener("pointerenter",t),e.removeEventListener("pointerleave",n)}}},[o.scrollArea,o.scrollHideDelay]),(0,n.jsx)(eN.z,{present:t||l,children:(0,n.jsx)(eW,{"data-state":l?"visible":"hidden",...s,ref:r})})}),eK=a.forwardRef((e,r)=>{var t;let{forceMount:s,...o}=e,l=eI(eZ,e.__scopeScrollArea),i="horizontal"===e.orientation,c=e6(()=>u("SCROLL_END"),100),[d,u]=(t={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},a.useReducer((e,r)=>t[e][r]??e,"hidden"));return a.useEffect(()=>{if("idle"===d){let e=window.setTimeout(()=>u("HIDE"),l.scrollHideDelay);return()=>window.clearTimeout(e)}},[d,l.scrollHideDelay,u]),a.useEffect(()=>{let e=l.viewport,r=i?"scrollLeft":"scrollTop";if(e){let t=e[r],n=()=>{let n=e[r];t!==n&&(u("SCROLL"),c()),t=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[l.viewport,i,u,c]),(0,n.jsx)(eN.z,{present:s||"hidden"!==d,children:(0,n.jsx)(eV,{"data-state":"hidden"===d?"hidden":"visible",...o,ref:r,onPointerEnter:(0,eP.M)(e.onPointerEnter,()=>u("POINTER_ENTER")),onPointerLeave:(0,eP.M)(e.onPointerLeave,()=>u("POINTER_LEAVE"))})})}),eW=a.forwardRef((e,r)=>{let t=eI(eZ,e.__scopeScrollArea),{forceMount:s,...o}=e,[l,i]=a.useState(!1),c="horizontal"===e.orientation,d=e6(()=>{if(t.viewport){let e=t.viewport.offsetWidth<t.viewport.scrollWidth,r=t.viewport.offsetHeight<t.viewport.scrollHeight;i(c?e:r)}},10);return e7(t.viewport,d),e7(t.content,d),(0,n.jsx)(eN.z,{present:s||l,children:(0,n.jsx)(eV,{"data-state":l?"visible":"hidden",...o,ref:r})})}),eV=a.forwardRef((e,r)=>{let{orientation:t="vertical",...s}=e,o=eI(eZ,e.__scopeScrollArea),l=a.useRef(null),i=a.useRef(0),[c,d]=a.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=e4(c.viewport,c.content),m={...s,sizes:c,onSizesChange:d,hasThumb:!!(u>0&&u<1),onThumbChange:e=>l.current=e,onThumbPointerUp:()=>i.current=0,onThumbPointerDown:e=>i.current=e};function f(e,r){return function(e,r,t,n="ltr"){let a=e3(t),s=r||a/2,o=t.scrollbar.paddingStart+s,l=t.scrollbar.size-t.scrollbar.paddingEnd-(a-s),i=t.content-t.viewport;return e9([o,l],"ltr"===n?[0,i]:[-1*i,0])(e)}(e,i.current,c,r)}return"horizontal"===t?(0,n.jsx)(eH,{...m,ref:r,onThumbPositionChange:()=>{if(o.viewport&&l.current){let e=e5(o.viewport.scrollLeft,c,o.dir);l.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{o.viewport&&(o.viewport.scrollLeft=e)},onDragScroll:e=>{o.viewport&&(o.viewport.scrollLeft=f(e,o.dir))}}):"vertical"===t?(0,n.jsx)(eU,{...m,ref:r,onThumbPositionChange:()=>{if(o.viewport&&l.current){let e=e5(o.viewport.scrollTop,c);l.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{o.viewport&&(o.viewport.scrollTop=e)},onDragScroll:e=>{o.viewport&&(o.viewport.scrollTop=f(e))}}):null}),eH=a.forwardRef((e,r)=>{let{sizes:t,onSizesChange:s,...o}=e,l=eI(eZ,e.__scopeScrollArea),[i,c]=a.useState(),d=a.useRef(null),u=(0,ek.e)(r,d,l.onScrollbarXChange);return a.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,n.jsx)(eG,{"data-orientation":"horizontal",...o,ref:u,sizes:t,style:{bottom:0,left:"rtl"===l.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===l.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":e3(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.x),onDragScroll:r=>e.onDragScroll(r.x),onWheelScroll:(r,t)=>{if(l.viewport){let n=l.viewport.scrollLeft+r.deltaX;e.onWheelScroll(n),function(e,r){return e>0&&e<r}(n,t)&&r.preventDefault()}},onResize:()=>{d.current&&l.viewport&&i&&s({content:l.viewport.scrollWidth,viewport:l.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:e2(i.paddingLeft),paddingEnd:e2(i.paddingRight)}})}})}),eU=a.forwardRef((e,r)=>{let{sizes:t,onSizesChange:s,...o}=e,l=eI(eZ,e.__scopeScrollArea),[i,c]=a.useState(),d=a.useRef(null),u=(0,ek.e)(r,d,l.onScrollbarYChange);return a.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,n.jsx)(eG,{"data-orientation":"vertical",...o,ref:u,sizes:t,style:{top:0,right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":e3(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.y),onDragScroll:r=>e.onDragScroll(r.y),onWheelScroll:(r,t)=>{if(l.viewport){let n=l.viewport.scrollTop+r.deltaY;e.onWheelScroll(n),function(e,r){return e>0&&e<r}(n,t)&&r.preventDefault()}},onResize:()=>{d.current&&l.viewport&&i&&s({content:l.viewport.scrollHeight,viewport:l.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:e2(i.paddingTop),paddingEnd:e2(i.paddingBottom)}})}})}),[eB,eY]=eD(eZ),eG=a.forwardRef((e,r)=>{let{__scopeScrollArea:t,sizes:s,hasThumb:o,onThumbChange:l,onThumbPointerUp:i,onThumbPointerDown:c,onThumbPositionChange:d,onDragScroll:u,onWheelScroll:m,onResize:f,...p}=e,h=eI(eZ,t),[x,g]=a.useState(null),y=(0,ek.e)(r,e=>g(e)),v=a.useRef(null),b=a.useRef(""),j=h.viewport,w=s.content-s.viewport,N=(0,eR.W)(m),C=(0,eR.W)(d),k=e6(f,10);function R(e){v.current&&u({x:e.clientX-v.current.left,y:e.clientY-v.current.top})}return a.useEffect(()=>{let e=e=>{let r=e.target;x?.contains(r)&&N(e,w)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[j,x,w,N]),a.useEffect(C,[s,C]),e7(x,k),e7(h.content,k),(0,n.jsx)(eB,{scope:t,scrollbar:x,hasThumb:o,onThumbChange:(0,eR.W)(l),onThumbPointerUp:(0,eR.W)(i),onThumbPositionChange:C,onThumbPointerDown:(0,eR.W)(c),children:(0,n.jsx)(ew.WV.div,{...p,ref:y,style:{position:"absolute",...p.style},onPointerDown:(0,eP.M)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),v.current=x.getBoundingClientRect(),b.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",h.viewport&&(h.viewport.style.scrollBehavior="auto"),R(e))}),onPointerMove:(0,eP.M)(e.onPointerMove,R),onPointerUp:(0,eP.M)(e.onPointerUp,e=>{let r=e.target;r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=b.current,h.viewport&&(h.viewport.style.scrollBehavior=""),v.current=null})})})}),eQ="ScrollAreaThumb",eX=a.forwardRef((e,r)=>{let{forceMount:t,...a}=e,s=eY(eQ,e.__scopeScrollArea);return(0,n.jsx)(eN.z,{present:t||s.hasThumb,children:(0,n.jsx)(e$,{ref:r,...a})})}),e$=a.forwardRef((e,r)=>{let{__scopeScrollArea:t,style:s,...o}=e,l=eI(eQ,t),i=eY(eQ,t),{onThumbPositionChange:c}=i,d=(0,ek.e)(r,e=>i.onThumbChange(e)),u=a.useRef(void 0),m=e6(()=>{u.current&&(u.current(),u.current=void 0)},100);return a.useEffect(()=>{let e=l.viewport;if(e){let r=()=>{if(m(),!u.current){let r=e8(e,c);u.current=r,c()}};return c(),e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[l.viewport,m,c]),(0,n.jsx)(ew.WV.div,{"data-state":i.hasThumb?"visible":"hidden",...o,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...s},onPointerDownCapture:(0,eP.M)(e.onPointerDownCapture,e=>{let r=e.target.getBoundingClientRect(),t=e.clientX-r.left,n=e.clientY-r.top;i.onThumbPointerDown({x:t,y:n})}),onPointerUp:(0,eP.M)(e.onPointerUp,i.onThumbPointerUp)})});eX.displayName=eQ;var eJ="ScrollAreaCorner",e0=a.forwardRef((e,r)=>{let t=eI(eJ,e.__scopeScrollArea),a=!!(t.scrollbarX&&t.scrollbarY);return"scroll"!==t.type&&a?(0,n.jsx)(e1,{...e,ref:r}):null});e0.displayName=eJ;var e1=a.forwardRef((e,r)=>{let{__scopeScrollArea:t,...s}=e,o=eI(eJ,t),[l,i]=a.useState(0),[c,d]=a.useState(0),u=!!(l&&c);return e7(o.scrollbarX,()=>{let e=o.scrollbarX?.offsetHeight||0;o.onCornerHeightChange(e),d(e)}),e7(o.scrollbarY,()=>{let e=o.scrollbarY?.offsetWidth||0;o.onCornerWidthChange(e),i(e)}),u?(0,n.jsx)(ew.WV.div,{...s,ref:r,style:{width:l,height:c,position:"absolute",right:"ltr"===o.dir?0:void 0,left:"rtl"===o.dir?0:void 0,bottom:0,...e.style}}):null});function e2(e){return e?parseInt(e,10):0}function e4(e,r){let t=e/r;return isNaN(t)?0:t}function e3(e){let r=e4(e.viewport,e.content),t=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-t)*r,18)}function e5(e,r,t="ltr"){let n=e3(r),a=r.scrollbar.paddingStart+r.scrollbar.paddingEnd,s=r.scrollbar.size-a,o=r.content-r.viewport,l=(0,eE.u)(e,"ltr"===t?[0,o]:[-1*o,0]);return e9([0,o],[0,s-n])(l)}function e9(e,r){return t=>{if(e[0]===e[1]||r[0]===r[1])return r[0];let n=(r[1]-r[0])/(e[1]-e[0]);return r[0]+n*(t-e[0])}}var e8=(e,r=()=>{})=>{let t={left:e.scrollLeft,top:e.scrollTop},n=0;return function a(){let s={left:e.scrollLeft,top:e.scrollTop},o=t.left!==s.left,l=t.top!==s.top;(o||l)&&r(),t=s,n=window.requestAnimationFrame(a)}(),()=>window.cancelAnimationFrame(n)};function e6(e,r){let t=(0,eR.W)(e),n=a.useRef(0);return a.useEffect(()=>()=>window.clearTimeout(n.current),[]),a.useCallback(()=>{window.clearTimeout(n.current),n.current=window.setTimeout(t,r)},[t,r])}function e7(e,r){let t=(0,eR.W)(r);(0,eS.b)(()=>{let r=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(t)});return n.observe(e),()=>{window.cancelAnimationFrame(r),n.unobserve(e)}}},[e,t])}let re=a.forwardRef(({className:e,children:r,...t},a)=>(0,n.jsxs)(eL,{ref:a,className:(0,x.cn)("relative overflow-hidden",e),...t,children:[n.jsx(ez,{className:"h-full w-full rounded-[inherit]",children:r}),n.jsx(rr,{}),n.jsx(e0,{})]}));re.displayName=eL.displayName;let rr=a.forwardRef(({className:e,orientation:r="vertical",...t},a)=>n.jsx(eq,{ref:a,orientation:r,className:(0,x.cn)("flex touch-none select-none transition-colors","vertical"===r&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===r&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...t,children:n.jsx(eX,{className:"relative flex-1 rounded-full bg-border"})}));rr.displayName=eq.displayName;var rt=t(96213),rn=t(57320);function ra({userId:e}){let[r,t]=(0,a.useState)([]),[i,c]=(0,a.useState)(!0),{setCurrentQuery:d,setActiveTab:f,currentUser:p}=(0,g.l)();(0,a.useEffect)(()=>{(async()=>{if(!e&&!p)return;let r=e||p?.id;if(r)try{c(!0);let e=await fetch(`/api/queries?type=history&userId=${r}&limit=50`);if(e.ok){let r=await e.json();t(r)}}catch(e){console.error("Failed to load query history:",e)}finally{c(!1)}})()},[e,p]);let h=e=>{d(e),f("editor")},y=e=>{navigator.clipboard.writeText(e)},v=e=>{switch(e){case"EXECUTED":return"bg-green-100 text-green-800";case"FAILED":return"bg-red-100 text-red-800";case"GENERATED":return"bg-blue-100 text-blue-800";case"OPTIMIZED":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}};return i?(0,n.jsxs)(s.Zb,{children:[n.jsx(s.Ol,{children:(0,n.jsxs)(s.ll,{className:"flex items-center gap-2",children:[n.jsx(rt.Z,{className:"h-5 w-5"}),"Query History"]})}),n.jsx(s.aY,{children:(0,n.jsxs)("div",{className:"flex items-center justify-center py-8",children:[n.jsx("div",{className:"loading-spinner"}),n.jsx("span",{className:"ml-2 text-sm text-muted-foreground",children:"Loading history..."})]})})]}):(0,n.jsxs)(s.Zb,{children:[n.jsx(s.Ol,{children:(0,n.jsxs)(s.ll,{className:"flex items-center gap-2",children:[n.jsx(rt.Z,{className:"h-5 w-5"}),"Query History",(0,n.jsxs)(l.C,{variant:"secondary",className:"ml-auto",children:[r.length," queries"]})]})}),n.jsx(s.aY,{children:n.jsx(re,{className:"h-[600px]",children:0===r.length?(0,n.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[n.jsx(rt.Z,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),n.jsx("p",{children:"No queries in history yet"}),n.jsx("p",{className:"text-sm",children:"Start generating queries to see them here"})]}):n.jsx("div",{className:"space-y-4",children:r.map(e=>n.jsx(s.Zb,{className:"border-l-4 border-l-primary/20",children:n.jsx(s.aY,{className:"p-4",children:(0,n.jsxs)("div",{className:"space-y-3",children:[n.jsx("div",{className:"flex items-start justify-between",children:(0,n.jsxs)("div",{className:"flex-1",children:[n.jsx("p",{className:"text-sm font-medium text-foreground",children:e.userInput}),(0,n.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[n.jsx(l.C,{className:v(e.status),children:e.status}),(0,n.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[n.jsx(eu.Z,{className:"h-3 w-3"}),e.databaseType]}),(0,n.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[n.jsx(ev.Z,{className:"h-3 w-3"}),(0,x.SY)(new Date(e.createdAt))]})]})]})}),n.jsx("div",{className:"bg-muted rounded-lg p-3",children:n.jsx("pre",{className:"text-xs font-mono overflow-x-auto whitespace-pre-wrap",children:n.jsx("code",{children:e.generatedSQL})})}),e.explanation&&(0,n.jsxs)("div",{className:"text-sm text-muted-foreground",children:[n.jsx("strong",{children:"Explanation:"})," ",e.explanation]}),e.executionTime&&(0,n.jsxs)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[(0,n.jsxs)("span",{children:["Execution: ",e.executionTime,"ms"]}),e.rowsAffected&&(0,n.jsxs)("span",{children:["Rows: ",e.rowsAffected]}),e.userFeedback&&(0,n.jsxs)("div",{className:"flex items-center gap-1",children:[n.jsx("span",{children:"Rating:"}),n.jsx("div",{className:"flex",children:[void 0,void 0,void 0,void 0,void 0].map((r,t)=>n.jsx(m,{className:`h-3 w-3 ${t<e.userFeedback?"text-yellow-500 fill-current":"text-gray-300"}`},t))})]})]}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsxs)(o.z,{variant:"outline",size:"sm",onClick:()=>h(e.generatedSQL),children:[n.jsx(rn.Z,{className:"h-3 w-3 mr-1"}),"Use"]}),(0,n.jsxs)(o.z,{variant:"outline",size:"sm",onClick:()=>y(e.generatedSQL),children:[n.jsx(u,{className:"h-3 w-3 mr-1"}),"Copy"]})]})]})})},e.id))})})})]})}/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let rs=(0,c.Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]),ro=(0,c.Z)("Share",[["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["polyline",{points:"16 6 12 2 8 6",key:"m901s6"}],["line",{x1:"12",x2:"12",y1:"2",y2:"15",key:"1p0rca"}]]);function rl(){let{activeTab:e,setActiveTab:r,currentQuery:t,setCurrentQuery:c,currentDatabase:d}=(0,g.l)(),[u,m]=(0,a.useState)(!1),f=[{id:"chat",label:"AI Assistant",icon:i.Z},{id:"editor",label:"SQL Editor",icon:eu.Z},{id:"results",label:"Results",icon:eb},{id:"history",label:"History",icon:rt.Z}];return(0,n.jsxs)("div",{className:"flex h-full",children:[(0,n.jsxs)("div",{className:"w-80 border-r border-border bg-card",children:[(0,n.jsxs)("div",{className:"p-4 border-b border-border",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[n.jsx("h3",{className:"font-semibold",children:"Database Schema"}),n.jsx(l.C,{variant:u?"default":"secondary",children:u?"Connected":"Not Connected"})]}),!u&&(0,n.jsxs)(o.z,{variant:"outline",size:"sm",className:"w-full",onClick:()=>m(!0),children:[n.jsx(eu.Z,{className:"h-4 w-4 mr-2"}),"Connect Database"]})]}),n.jsx("div",{className:"flex-1 overflow-auto",children:n.jsx(ex,{isConnected:u})})]}),(0,n.jsxs)("div",{className:"flex-1 flex flex-col",children:[n.jsx("div",{className:"border-b border-border bg-card",children:(0,n.jsxs)("div",{className:"flex items-center justify-between px-6 py-3",children:[n.jsx("div",{className:"flex space-x-1",children:f.map(t=>{let a=t.icon;return(0,n.jsxs)("button",{onClick:()=>r(t.id),className:`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${e===t.id?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-muted"}`,children:[n.jsx(a,{className:"h-4 w-4"}),t.label]},t.id)})}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)(o.z,{variant:"outline",size:"sm",children:[n.jsx(rt.Z,{className:"h-4 w-4 mr-2"}),"History"]}),(0,n.jsxs)(o.z,{variant:"outline",size:"sm",children:[n.jsx(rs,{className:"h-4 w-4 mr-2"}),"Save"]}),(0,n.jsxs)(o.z,{variant:"outline",size:"sm",children:[n.jsx(ro,{className:"h-4 w-4 mr-2"}),"Share"]}),"editor"===e&&(0,n.jsxs)(o.z,{size:"sm",children:[n.jsx(rn.Z,{className:"h-4 w-4 mr-2"}),"Run Query"]})]})]})}),(0,n.jsxs)("div",{className:"flex-1 overflow-hidden",children:["chat"===e&&n.jsx(y,{onQueryGenerated:e=>{c(e)}}),"editor"===e&&n.jsx("div",{className:"h-full flex flex-col",children:n.jsx("div",{className:"flex-1 p-6",children:n.jsx(ed,{value:t,onChange:c,language:d?.databaseType||"postgresql"})})}),"results"===e&&n.jsx("div",{className:"h-full p-6",children:n.jsx(ej,{})}),"history"===e&&n.jsx("div",{className:"h-full p-6",children:n.jsx(ra,{})})]})]}),(0,n.jsxs)("div",{className:"w-80 border-l border-border bg-card",children:[n.jsx("div",{className:"p-4 border-b border-border",children:(0,n.jsxs)("h3",{className:"font-semibold flex items-center gap-2",children:[n.jsx(ec,{className:"h-4 w-4"}),"AI Insights"]})}),(0,n.jsxs)("div",{className:"p-4 space-y-4",children:[(0,n.jsxs)(s.Zb,{children:[n.jsx(s.Ol,{className:"pb-3",children:n.jsx(s.ll,{className:"text-sm",children:"Query Suggestions"})}),n.jsx(s.aY,{className:"space-y-2",children:n.jsx("div",{className:"text-sm text-muted-foreground",children:"No active query to analyze. Start by describing what you want to query in the AI Assistant."})})]}),(0,n.jsxs)(s.Zb,{children:[n.jsx(s.Ol,{className:"pb-3",children:n.jsx(s.ll,{className:"text-sm",children:"Performance Tips"})}),n.jsx(s.aY,{className:"space-y-2",children:n.jsx("div",{className:"text-sm text-muted-foreground",children:"Connect to a database to get performance recommendations."})})]}),(0,n.jsxs)(s.Zb,{children:[n.jsx(s.Ol,{className:"pb-3",children:n.jsx(s.ll,{className:"text-sm",children:"Recent Activity"})}),n.jsx(s.aY,{className:"space-y-2",children:n.jsx("div",{className:"text-sm text-muted-foreground",children:"No recent queries found."})})]})]})]})]})}},19591:(e,r,t)=>{"use strict";t.d(r,{C:()=>l});var n=t(95344);t(3729);var a=t(49247),s=t(11453);let o=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:r,...t}){return n.jsx("div",{className:(0,s.cn)(o({variant:r}),e),...t})}},5094:(e,r,t)=>{"use strict";t.d(r,{z:()=>c});var n=t(95344),a=t(3729),s=t(32751),o=t(49247),l=t(11453);let i=(0,o.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:r,size:t,asChild:a=!1,...o},c)=>{let d=a?s.g7:"button";return n.jsx(d,{className:(0,l.cn)(i({variant:r,size:t,className:e})),ref:c,...o})});c.displayName="Button"},23673:(e,r,t)=>{"use strict";t.d(r,{Ol:()=>l,Zb:()=>o,aY:()=>c,ll:()=>i});var n=t(95344),a=t(3729),s=t(11453);let o=a.forwardRef(({className:e,...r},t)=>n.jsx("div",{ref:t,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));o.displayName="Card";let l=a.forwardRef(({className:e,...r},t)=>n.jsx("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",e),...r}));l.displayName="CardHeader";let i=a.forwardRef(({className:e,...r},t)=>n.jsx("h3",{ref:t,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));i.displayName="CardTitle",a.forwardRef(({className:e,...r},t)=>n.jsx("p",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",e),...r})).displayName="CardDescription";let c=a.forwardRef(({className:e,...r},t)=>n.jsx("div",{ref:t,className:(0,s.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent",a.forwardRef(({className:e,...r},t)=>n.jsx("div",{ref:t,className:(0,s.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},69194:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p,metadata:()=>f});var n=t(25036),a=t(86843);let s=(0,a.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/dashboard-layout.tsx`),{__esModule:o,$$typeof:l}=s;s.default;let i=(0,a.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/dashboard-layout.tsx#DashboardLayout`),c=(0,a.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx`),{__esModule:d,$$typeof:u}=c;c.default;let m=(0,a.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx#QueryWorkspace`),f={title:"Dashboard - QueryCraft Studio",description:"AI-powered SQL development workspace"};function p(){return n.jsx(i,{children:n.jsx(m,{})})}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[638,599,476,66,162],()=>t(9334));module.exports=n})();