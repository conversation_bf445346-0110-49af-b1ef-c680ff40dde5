(()=>{var e={};e.id=809,e.ids=[809],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},63530:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var a=s(50482),r=s(69108),n=s(62563),l=s.n(n),i=s(68300),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d=["",{children:["dashboard",{children:["query-editor",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,40014)),"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/dashboard/query-editor/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,98890)),"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/dashboard/query-editor/page.tsx"],u="/dashboard/query-editor/page",m={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/query-editor/page",pathname:"/dashboard/query-editor",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},86343:(e,t,s)=>{Promise.resolve().then(s.bind(s,46560))},53148:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},46560:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>tB});var a=s(95344),r=s(3729),n=s(23673),l=s(5094),i=s(11453);let o=r.forwardRef(({className:e,...t},s)=>a.jsx("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...t}));o.displayName="Textarea";var d=s(81202),c=s(2633),u=s(85222),m=s(77411),x=s(31405),h=s(98462),p=s(3975),f=s(44155),g=s(1106),j=s(27386),y=s(99048),v=s(37574),w=s(31179),b=s(62409),N=s(32751),C=s(2256),S=s(33183),k=s(16069);function R(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var T=s(87298),E=s(45904),M=s(71210),Z=[" ","Enter","ArrowUp","ArrowDown"],D=[" ","Enter"],P="Select",[L,I,O]=(0,m.B)(P),[q,F]=(0,h.b)(P,[O,v.D7]),_=(0,v.D7)(),[V,U]=q(P),[z,A]=q(P),H=e=>{let{__scopeSelect:t,children:s,open:n,defaultOpen:l,onOpenChange:i,value:o,defaultValue:d,onValueChange:c,dir:u,name:m,autoComplete:x,disabled:h,required:f,form:g}=e,j=_(t),[w,b]=r.useState(null),[N,C]=r.useState(null),[k,R]=r.useState(!1),T=(0,p.gm)(u),[E,M]=(0,S.T)({prop:n,defaultProp:l??!1,onChange:i,caller:P}),[Z,D]=(0,S.T)({prop:o,defaultProp:d,onChange:c,caller:P}),I=r.useRef(null),O=!w||g||!!w.closest("form"),[q,F]=r.useState(new Set),U=Array.from(q).map(e=>e.props.value).join(";");return(0,a.jsx)(v.fC,{...j,children:(0,a.jsxs)(V,{required:f,scope:t,trigger:w,onTriggerChange:b,valueNode:N,onValueNodeChange:C,valueNodeHasChildren:k,onValueNodeHasChildrenChange:R,contentId:(0,y.M)(),value:Z,onValueChange:D,open:E,onOpenChange:M,dir:T,triggerPointerDownPosRef:I,disabled:h,children:[(0,a.jsx)(L.Provider,{scope:t,children:(0,a.jsx)(z,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{F(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{F(t=>{let s=new Set(t);return s.delete(e),s})},[]),children:s})}),O?(0,a.jsxs)(eE,{"aria-hidden":!0,required:f,tabIndex:-1,name:m,autoComplete:x,value:Z,onChange:e=>D(e.target.value),disabled:h,form:g,children:[void 0===Z?(0,a.jsx)("option",{value:""}):null,Array.from(q)]},U):null]})})};H.displayName=P;var Q="SelectTrigger",W=r.forwardRef((e,t)=>{let{__scopeSelect:s,disabled:n=!1,...l}=e,i=_(s),o=U(Q,s),d=o.disabled||n,c=(0,x.e)(t,o.onTriggerChange),m=I(s),h=r.useRef("touch"),[p,f,g]=eZ(e=>{let t=m().filter(e=>!e.disabled),s=t.find(e=>e.value===o.value),a=eD(t,e,s);void 0!==a&&o.onValueChange(a.value)}),j=e=>{d||(o.onOpenChange(!0),g()),e&&(o.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,a.jsx)(v.ee,{asChild:!0,...i,children:(0,a.jsx)(b.WV.button,{type:"button",role:"combobox","aria-controls":o.contentId,"aria-expanded":o.open,"aria-required":o.required,"aria-autocomplete":"none",dir:o.dir,"data-state":o.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":eM(o.value)?"":void 0,...l,ref:c,onClick:(0,u.M)(l.onClick,e=>{e.currentTarget.focus(),"mouse"!==h.current&&j(e)}),onPointerDown:(0,u.M)(l.onPointerDown,e=>{h.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(j(e),e.preventDefault())}),onKeyDown:(0,u.M)(l.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||f(e.key),(!t||" "!==e.key)&&Z.includes(e.key)&&(j(),e.preventDefault())})})})});W.displayName=Q;var B="SelectValue",Y=r.forwardRef((e,t)=>{let{__scopeSelect:s,className:r,style:n,children:l,placeholder:i="",...o}=e,d=U(B,s),{onValueNodeHasChildrenChange:c}=d,u=void 0!==l,m=(0,x.e)(t,d.onValueNodeChange);return(0,k.b)(()=>{c(u)},[c,u]),(0,a.jsx)(b.WV.span,{...o,ref:m,style:{pointerEvents:"none"},children:eM(d.value)?(0,a.jsx)(a.Fragment,{children:i}):l})});Y.displayName=B;var $=r.forwardRef((e,t)=>{let{__scopeSelect:s,children:r,...n}=e;return(0,a.jsx)(b.WV.span,{"aria-hidden":!0,...n,ref:t,children:r||"▼"})});$.displayName="SelectIcon";var K=e=>(0,a.jsx)(w.h,{asChild:!0,...e});K.displayName="SelectPortal";var J="SelectContent",X=r.forwardRef((e,t)=>{let s=U(J,e.__scopeSelect),[n,l]=r.useState();return((0,k.b)(()=>{l(new DocumentFragment)},[]),s.open)?(0,a.jsx)(es,{...e,ref:t}):n?d.createPortal((0,a.jsx)(G,{scope:e.__scopeSelect,children:(0,a.jsx)(L.Slot,{scope:e.__scopeSelect,children:(0,a.jsx)("div",{children:e.children})})}),n):null});X.displayName=J;var[G,ee]=q(J),et=(0,N.Z8)("SelectContent.RemoveScroll"),es=r.forwardRef((e,t)=>{let{__scopeSelect:s,position:n="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:i,onPointerDownOutside:o,side:d,sideOffset:c,align:m,alignOffset:h,arrowPadding:p,collisionBoundary:y,collisionPadding:v,sticky:w,hideWhenDetached:b,avoidCollisions:N,...C}=e,S=U(J,s),[k,R]=r.useState(null),[T,Z]=r.useState(null),D=(0,x.e)(t,e=>R(e)),[P,L]=r.useState(null),[O,q]=r.useState(null),F=I(s),[_,V]=r.useState(!1),z=r.useRef(!1);r.useEffect(()=>{if(k)return(0,E.Ry)(k)},[k]),(0,g.EW)();let A=r.useCallback(e=>{let[t,...s]=F().map(e=>e.ref.current),[a]=s.slice(-1),r=document.activeElement;for(let s of e)if(s===r||(s?.scrollIntoView({block:"nearest"}),s===t&&T&&(T.scrollTop=0),s===a&&T&&(T.scrollTop=T.scrollHeight),s?.focus(),document.activeElement!==r))return},[F,T]),H=r.useCallback(()=>A([P,k]),[A,P,k]);r.useEffect(()=>{_&&H()},[_,H]);let{onOpenChange:Q,triggerPointerDownPosRef:W}=S;r.useEffect(()=>{if(k){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(W.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(W.current?.y??0))}},s=s=>{e.x<=10&&e.y<=10?s.preventDefault():k.contains(s.target)||Q(!1),document.removeEventListener("pointermove",t),W.current=null};return null!==W.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",s,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",s,{capture:!0})}}},[k,Q,W]),r.useEffect(()=>{let e=()=>Q(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[Q]);let[B,Y]=eZ(e=>{let t=F().filter(e=>!e.disabled),s=t.find(e=>e.ref.current===document.activeElement),a=eD(t,e,s);a&&setTimeout(()=>a.ref.current.focus())}),$=r.useCallback((e,t,s)=>{let a=!z.current&&!s;(void 0!==S.value&&S.value===t||a)&&(L(e),a&&(z.current=!0))},[S.value]),K=r.useCallback(()=>k?.focus(),[k]),X=r.useCallback((e,t,s)=>{let a=!z.current&&!s;(void 0!==S.value&&S.value===t||a)&&q(e)},[S.value]),ee="popper"===n?er:ea,es=ee===er?{side:d,sideOffset:c,align:m,alignOffset:h,arrowPadding:p,collisionBoundary:y,collisionPadding:v,sticky:w,hideWhenDetached:b,avoidCollisions:N}:{};return(0,a.jsx)(G,{scope:s,content:k,viewport:T,onViewportChange:Z,itemRefCallback:$,selectedItem:P,onItemLeave:K,itemTextRefCallback:X,focusSelectedItem:H,selectedItemText:O,position:n,isPositioned:_,searchRef:B,children:(0,a.jsx)(M.Z,{as:et,allowPinchZoom:!0,children:(0,a.jsx)(j.M,{asChild:!0,trapped:S.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,u.M)(l,e=>{S.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,a.jsx)(f.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:o,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>S.onOpenChange(!1),children:(0,a.jsx)(ee,{role:"listbox",id:S.contentId,"data-state":S.open?"open":"closed",dir:S.dir,onContextMenu:e=>e.preventDefault(),...C,...es,onPlaced:()=>V(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,u.M)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=F().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let s=e.target,a=t.indexOf(s);t=t.slice(a+1)}setTimeout(()=>A(t)),e.preventDefault()}})})})})})})});es.displayName="SelectContentImpl";var ea=r.forwardRef((e,t)=>{let{__scopeSelect:s,onPlaced:n,...l}=e,i=U(J,s),o=ee(J,s),[d,u]=r.useState(null),[m,h]=r.useState(null),p=(0,x.e)(t,e=>h(e)),f=I(s),g=r.useRef(!1),j=r.useRef(!0),{viewport:y,selectedItem:v,selectedItemText:w,focusSelectedItem:N}=o,C=r.useCallback(()=>{if(i.trigger&&i.valueNode&&d&&m&&y&&v&&w){let e=i.trigger.getBoundingClientRect(),t=m.getBoundingClientRect(),s=i.valueNode.getBoundingClientRect(),a=w.getBoundingClientRect();if("rtl"!==i.dir){let r=a.left-t.left,n=s.left-r,l=e.left-n,i=e.width+l,o=Math.max(i,t.width),u=window.innerWidth-10,m=(0,c.u)(n,[10,Math.max(10,u-o)]);d.style.minWidth=i+"px",d.style.left=m+"px"}else{let r=t.right-a.right,n=window.innerWidth-s.right-r,l=window.innerWidth-e.right-n,i=e.width+l,o=Math.max(i,t.width),u=window.innerWidth-10,m=(0,c.u)(n,[10,Math.max(10,u-o)]);d.style.minWidth=i+"px",d.style.right=m+"px"}let r=f(),l=window.innerHeight-20,o=y.scrollHeight,u=window.getComputedStyle(m),x=parseInt(u.borderTopWidth,10),h=parseInt(u.paddingTop,10),p=parseInt(u.borderBottomWidth,10),j=x+h+o+parseInt(u.paddingBottom,10)+p,b=Math.min(5*v.offsetHeight,j),N=window.getComputedStyle(y),C=parseInt(N.paddingTop,10),S=parseInt(N.paddingBottom,10),k=e.top+e.height/2-10,R=v.offsetHeight/2,T=x+h+(v.offsetTop+R);if(T<=k){let e=r.length>0&&v===r[r.length-1].ref.current;d.style.bottom="0px";let t=m.clientHeight-y.offsetTop-y.offsetHeight;d.style.height=T+Math.max(l-k,R+(e?S:0)+t+p)+"px"}else{let e=r.length>0&&v===r[0].ref.current;d.style.top="0px";let t=Math.max(k,x+y.offsetTop+(e?C:0)+R);d.style.height=t+(j-T)+"px",y.scrollTop=T-k+y.offsetTop}d.style.margin="10px 0",d.style.minHeight=b+"px",d.style.maxHeight=l+"px",n?.(),requestAnimationFrame(()=>g.current=!0)}},[f,i.trigger,i.valueNode,d,m,y,v,w,i.dir,n]);(0,k.b)(()=>C(),[C]);let[S,R]=r.useState();(0,k.b)(()=>{m&&R(window.getComputedStyle(m).zIndex)},[m]);let T=r.useCallback(e=>{e&&!0===j.current&&(C(),N?.(),j.current=!1)},[C,N]);return(0,a.jsx)(en,{scope:s,contentWrapper:d,shouldExpandOnScrollRef:g,onScrollButtonChange:T,children:(0,a.jsx)("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:S},children:(0,a.jsx)(b.WV.div,{...l,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});ea.displayName="SelectItemAlignedPosition";var er=r.forwardRef((e,t)=>{let{__scopeSelect:s,align:r="start",collisionPadding:n=10,...l}=e,i=_(s);return(0,a.jsx)(v.VY,{...i,...l,ref:t,align:r,collisionPadding:n,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});er.displayName="SelectPopperPosition";var[en,el]=q(J,{}),ei="SelectViewport",eo=r.forwardRef((e,t)=>{let{__scopeSelect:s,nonce:n,...l}=e,i=ee(ei,s),o=el(ei,s),d=(0,x.e)(t,i.onViewportChange),c=r.useRef(0);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:n}),(0,a.jsx)(L.Slot,{scope:s,children:(0,a.jsx)(b.WV.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:(0,u.M)(l.onScroll,e=>{let t=e.currentTarget,{contentWrapper:s,shouldExpandOnScrollRef:a}=o;if(a?.current&&s){let e=Math.abs(c.current-t.scrollTop);if(e>0){let a=window.innerHeight-20,r=Math.max(parseFloat(s.style.minHeight),parseFloat(s.style.height));if(r<a){let n=r+e,l=Math.min(a,n),i=n-l;s.style.height=l+"px","0px"===s.style.bottom&&(t.scrollTop=i>0?i:0,s.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});eo.displayName=ei;var ed="SelectGroup",[ec,eu]=q(ed);r.forwardRef((e,t)=>{let{__scopeSelect:s,...r}=e,n=(0,y.M)();return(0,a.jsx)(ec,{scope:s,id:n,children:(0,a.jsx)(b.WV.div,{role:"group","aria-labelledby":n,...r,ref:t})})}).displayName=ed;var em="SelectLabel",ex=r.forwardRef((e,t)=>{let{__scopeSelect:s,...r}=e,n=eu(em,s);return(0,a.jsx)(b.WV.div,{id:n.id,...r,ref:t})});ex.displayName=em;var eh="SelectItem",[ep,ef]=q(eh),eg=r.forwardRef((e,t)=>{let{__scopeSelect:s,value:n,disabled:l=!1,textValue:i,...o}=e,d=U(eh,s),c=ee(eh,s),m=d.value===n,[h,p]=r.useState(i??""),[f,g]=r.useState(!1),j=(0,x.e)(t,e=>c.itemRefCallback?.(e,n,l)),v=(0,y.M)(),w=r.useRef("touch"),N=()=>{l||(d.onValueChange(n),d.onOpenChange(!1))};if(""===n)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,a.jsx)(ep,{scope:s,value:n,disabled:l,textId:v,isSelected:m,onItemTextChange:r.useCallback(e=>{p(t=>t||(e?.textContent??"").trim())},[]),children:(0,a.jsx)(L.ItemSlot,{scope:s,value:n,disabled:l,textValue:h,children:(0,a.jsx)(b.WV.div,{role:"option","aria-labelledby":v,"data-highlighted":f?"":void 0,"aria-selected":m&&f,"data-state":m?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...o,ref:j,onFocus:(0,u.M)(o.onFocus,()=>g(!0)),onBlur:(0,u.M)(o.onBlur,()=>g(!1)),onClick:(0,u.M)(o.onClick,()=>{"mouse"!==w.current&&N()}),onPointerUp:(0,u.M)(o.onPointerUp,()=>{"mouse"===w.current&&N()}),onPointerDown:(0,u.M)(o.onPointerDown,e=>{w.current=e.pointerType}),onPointerMove:(0,u.M)(o.onPointerMove,e=>{w.current=e.pointerType,l?c.onItemLeave?.():"mouse"===w.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,u.M)(o.onPointerLeave,e=>{e.currentTarget===document.activeElement&&c.onItemLeave?.()}),onKeyDown:(0,u.M)(o.onKeyDown,e=>{c.searchRef?.current!==""&&" "===e.key||(D.includes(e.key)&&N()," "===e.key&&e.preventDefault())})})})})});eg.displayName=eh;var ej="SelectItemText",ey=r.forwardRef((e,t)=>{let{__scopeSelect:s,className:n,style:l,...i}=e,o=U(ej,s),c=ee(ej,s),u=ef(ej,s),m=A(ej,s),[h,p]=r.useState(null),f=(0,x.e)(t,e=>p(e),u.onItemTextChange,e=>c.itemTextRefCallback?.(e,u.value,u.disabled)),g=h?.textContent,j=r.useMemo(()=>(0,a.jsx)("option",{value:u.value,disabled:u.disabled,children:g},u.value),[u.disabled,u.value,g]),{onNativeOptionAdd:y,onNativeOptionRemove:v}=m;return(0,k.b)(()=>(y(j),()=>v(j)),[y,v,j]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.WV.span,{id:u.textId,...i,ref:f}),u.isSelected&&o.valueNode&&!o.valueNodeHasChildren?d.createPortal(i.children,o.valueNode):null]})});ey.displayName=ej;var ev="SelectItemIndicator",ew=r.forwardRef((e,t)=>{let{__scopeSelect:s,...r}=e;return ef(ev,s).isSelected?(0,a.jsx)(b.WV.span,{"aria-hidden":!0,...r,ref:t}):null});ew.displayName=ev;var eb="SelectScrollUpButton",eN=r.forwardRef((e,t)=>{let s=ee(eb,e.__scopeSelect),n=el(eb,e.__scopeSelect),[l,i]=r.useState(!1),o=(0,x.e)(t,n.onScrollButtonChange);return(0,k.b)(()=>{if(s.viewport&&s.isPositioned){let e=function(){i(t.scrollTop>0)},t=s.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[s.viewport,s.isPositioned]),l?(0,a.jsx)(ek,{...e,ref:o,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=s;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eN.displayName=eb;var eC="SelectScrollDownButton",eS=r.forwardRef((e,t)=>{let s=ee(eC,e.__scopeSelect),n=el(eC,e.__scopeSelect),[l,i]=r.useState(!1),o=(0,x.e)(t,n.onScrollButtonChange);return(0,k.b)(()=>{if(s.viewport&&s.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=s.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[s.viewport,s.isPositioned]),l?(0,a.jsx)(ek,{...e,ref:o,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=s;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eS.displayName=eC;var ek=r.forwardRef((e,t)=>{let{__scopeSelect:s,onAutoScroll:n,...l}=e,i=ee("SelectScrollButton",s),o=r.useRef(null),d=I(s),c=r.useCallback(()=>{null!==o.current&&(window.clearInterval(o.current),o.current=null)},[]);return r.useEffect(()=>()=>c(),[c]),(0,k.b)(()=>{let e=d().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[d]),(0,a.jsx)(b.WV.div,{"aria-hidden":!0,...l,ref:t,style:{flexShrink:0,...l.style},onPointerDown:(0,u.M)(l.onPointerDown,()=>{null===o.current&&(o.current=window.setInterval(n,50))}),onPointerMove:(0,u.M)(l.onPointerMove,()=>{i.onItemLeave?.(),null===o.current&&(o.current=window.setInterval(n,50))}),onPointerLeave:(0,u.M)(l.onPointerLeave,()=>{c()})})}),eR=r.forwardRef((e,t)=>{let{__scopeSelect:s,...r}=e;return(0,a.jsx)(b.WV.div,{"aria-hidden":!0,...r,ref:t})});eR.displayName="SelectSeparator";var eT="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:s,...r}=e,n=_(s),l=U(eT,s),i=ee(eT,s);return l.open&&"popper"===i.position?(0,a.jsx)(v.Eh,{...n,...r,ref:t}):null}).displayName=eT;var eE=r.forwardRef(({__scopeSelect:e,value:t,...s},n)=>{let l=r.useRef(null),i=(0,x.e)(n,l),o=R(t);return r.useEffect(()=>{let e=l.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(o!==t&&s){let a=new Event("change",{bubbles:!0});s.call(e,t),e.dispatchEvent(a)}},[o,t]),(0,a.jsx)(b.WV.select,{...s,style:{...T.C2,...s.style},ref:i,defaultValue:t})});function eM(e){return""===e||void 0===e}function eZ(e){let t=(0,C.W)(e),s=r.useRef(""),a=r.useRef(0),n=r.useCallback(e=>{let r=s.current+e;t(r),function e(t){s.current=t,window.clearTimeout(a.current),""!==t&&(a.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),l=r.useCallback(()=>{s.current="",window.clearTimeout(a.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(a.current),[]),[s,n,l]}function eD(e,t,s){var a;let r=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,n=(a=Math.max(s?e.indexOf(s):-1,0),e.map((t,s)=>e[(a+s)%e.length]));1===r.length&&(n=n.filter(e=>e!==s));let l=n.find(e=>e.textValue.toLowerCase().startsWith(r.toLowerCase()));return l!==s?l:void 0}eE.displayName="SelectBubbleInput";var eP=s(25390),eL=s(69224);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let eI=(0,eL.Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var eO=s(62312);let eq=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(W,{ref:r,className:(0,i.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[t,a.jsx($,{asChild:!0,children:a.jsx(eP.Z,{className:"h-4 w-4 opacity-50"})})]}));eq.displayName=W.displayName;let eF=r.forwardRef(({className:e,...t},s)=>a.jsx(eN,{ref:s,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:a.jsx(eI,{className:"h-4 w-4"})}));eF.displayName=eN.displayName;let e_=r.forwardRef(({className:e,...t},s)=>a.jsx(eS,{ref:s,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:a.jsx(eP.Z,{className:"h-4 w-4"})}));e_.displayName=eS.displayName;let eV=r.forwardRef(({className:e,children:t,position:s="popper",...r},n)=>a.jsx(K,{children:(0,a.jsxs)(X,{ref:n,className:(0,i.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...r,children:[a.jsx(eF,{}),a.jsx(eo,{className:(0,i.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),a.jsx(e_,{})]})}));eV.displayName=X.displayName,r.forwardRef(({className:e,...t},s)=>a.jsx(ex,{ref:s,className:(0,i.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=ex.displayName;let eU=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(eg,{ref:r,className:(0,i.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(ew,{children:a.jsx(eO.Z,{className:"h-4 w-4"})})}),a.jsx(ey,{children:t})]}));eU.displayName=eg.displayName,r.forwardRef(({className:e,...t},s)=>a.jsx(eR,{ref:s,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=eR.displayName;var ez=s(19591),eA=s(40874),eH=s(25545),eQ=s(7060);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let eW=(0,eL.Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var eB=s(99046);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let eY=(0,eL.Z)("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);var e$=s(57320),eK=s(91991);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let eJ=(0,eL.Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);function eX({sql:e,connectionId:t,onExecutionComplete:s}){var i;let[o,d]=(0,r.useState)(!1),[c,u]=(0,r.useState)(null),[m,x]=(0,r.useState)(null),[h,p]=(0,r.useState)(!1),f=async()=>{if(t&&e.trim()){d(!0),u(null);try{let a=await fetch("/api/queries/execute",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({connectionId:t,sql:e.trim(),maxRows:1e3,timeout:3e4,saveToHistory:!0})}),r=await a.json();u(r),s?.(r)}catch(a){let e={success:!1,error:a instanceof Error?a.message:"Unknown error occurred",metadata:{connectionId:t,executedAt:new Date().toISOString(),executionTime:0,rowsReturned:0,queryHash:""}};u(e),s?.(e)}finally{d(!1)}}},g=async()=>{if(t&&e.trim()){p(!0),x(null);try{let s=await fetch(`/api/queries/execute/validate?connectionId=${t}&sql=${encodeURIComponent(e.trim())}`);if(s.ok){let e=await s.json();x(e.validation)}}catch(e){console.error("Failed to validate query:",e)}finally{p(!1)}}},j=t&&e.trim()&&!o;return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center gap-2",children:[o?a.jsx(eH.Z,{className:"h-4 w-4 animate-spin"}):c?.success?a.jsx(eQ.Z,{className:"h-4 w-4 text-green-500"}):c&&!c.success?a.jsx(eW,{className:"h-4 w-4 text-red-500"}):a.jsx(eB.Z,{className:"h-4 w-4"}),"Query Execution",c&&a.jsx(ez.C,{variant:c.success?"default":"destructive",children:c.success?"Success":"Failed"})]})}),(0,a.jsxs)(n.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[a.jsx(l.z,{onClick:f,disabled:!j,className:"flex items-center gap-2",children:o?(0,a.jsxs)(a.Fragment,{children:[a.jsx(eY,{className:"h-4 w-4"}),"Executing..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(e$.Z,{className:"h-4 w-4"}),"Execute Query"]})}),a.jsx(l.z,{variant:"outline",onClick:g,disabled:!t||!e.trim()||h,className:"flex items-center gap-2",children:h?(0,a.jsxs)(a.Fragment,{children:[a.jsx(eH.Z,{className:"h-4 w-4 animate-spin"}),"Validating..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(eQ.Z,{className:"h-4 w-4"}),"Validate"]})})]}),!t&&(0,a.jsxs)(eA.bZ,{children:[a.jsx(eK.Z,{className:"h-4 w-4"}),a.jsx(eA.X,{children:"Please select a database connection to execute queries."})]})]})]}),m&&(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center gap-2",children:[a.jsx(eQ.Z,{className:"h-4 w-4"}),"Query Validation",(i=m.estimatedComplexity,(0,a.jsxs)(ez.C,{variant:{LOW:"default",MEDIUM:"secondary",HIGH:"destructive"}[i]||"default",children:[i," Complexity"]}))]})}),(0,a.jsxs)(n.aY,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[a.jsx(ez.C,{variant:"outline",children:m.queryType}),m.isReadOnly&&a.jsx(ez.C,{variant:"secondary",children:"Read-Only"})]}),m.errors.length>0&&(0,a.jsxs)(eA.bZ,{className:"border-red-200 bg-red-50",children:[a.jsx(eW,{className:"h-4 w-4 text-red-500"}),(0,a.jsxs)(eA.X,{children:[a.jsx("div",{className:"font-medium text-red-700 mb-1",children:"Validation Errors:"}),a.jsx("ul",{className:"list-disc list-inside text-red-600 text-sm",children:m.errors.map((e,t)=>a.jsx("li",{children:e},t))})]})]}),m.warnings.length>0&&(0,a.jsxs)(eA.bZ,{className:"border-yellow-200 bg-yellow-50",children:[a.jsx(eJ,{className:"h-4 w-4 text-yellow-500"}),(0,a.jsxs)(eA.X,{children:[a.jsx("div",{className:"font-medium text-yellow-700 mb-1",children:"Warnings:"}),a.jsx("ul",{className:"list-disc list-inside text-yellow-600 text-sm",children:m.warnings.map((e,t)=>a.jsx("li",{children:e},t))})]})]}),m.isValid&&0===m.errors.length&&(0,a.jsxs)(eA.bZ,{className:"border-green-200 bg-green-50",children:[a.jsx(eQ.Z,{className:"h-4 w-4 text-green-500"}),a.jsx(eA.X,{className:"text-green-700",children:"Query validation passed. Ready to execute."})]})]})]}),c&&(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center gap-2",children:[c.success?a.jsx(eQ.Z,{className:"h-4 w-4 text-green-500"}):a.jsx(eW,{className:"h-4 w-4 text-red-500"}),"Execution Results"]})}),(0,a.jsxs)(n.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-muted-foreground",children:"Execution Time"}),(0,a.jsxs)("div",{className:"font-medium",children:[c.metadata.executionTime,"ms"]})]}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-muted-foreground",children:"Rows Returned"}),a.jsx("div",{className:"font-medium",children:c.metadata.rowsReturned})]}),c.data?.affectedRows!==void 0&&(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-muted-foreground",children:"Rows Affected"}),a.jsx("div",{className:"font-medium",children:c.data.affectedRows})]}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-muted-foreground",children:"Executed At"}),a.jsx("div",{className:"font-medium",children:new Date(c.metadata.executedAt).toLocaleTimeString()})]})]}),c.error&&(0,a.jsxs)(eA.bZ,{className:"border-red-200 bg-red-50",children:[a.jsx(eW,{className:"h-4 w-4 text-red-500"}),(0,a.jsxs)(eA.X,{children:[a.jsx("div",{className:"font-medium text-red-700 mb-1",children:"Execution Error:"}),a.jsx("div",{className:"text-red-600 text-sm",children:c.error})]})]}),c.warnings&&c.warnings.length>0&&(0,a.jsxs)(eA.bZ,{className:"border-yellow-200 bg-yellow-50",children:[a.jsx(eJ,{className:"h-4 w-4 text-yellow-500"}),(0,a.jsxs)(eA.X,{children:[a.jsx("div",{className:"font-medium text-yellow-700 mb-1",children:"Warnings:"}),a.jsx("ul",{className:"list-disc list-inside text-yellow-600 text-sm",children:c.warnings.map((e,t)=>a.jsx("li",{children:e},t))})]})]}),c.success&&c.data&&(0,a.jsxs)(eA.bZ,{className:"border-green-200 bg-green-50",children:[a.jsx(eQ.Z,{className:"h-4 w-4 text-green-500"}),(0,a.jsxs)(eA.X,{className:"text-green-700",children:["Query executed successfully!",c.data.rowCount>0&&(0,a.jsxs)("span",{children:[" Returned ",c.data.rowCount," rows."]}),void 0!==c.data.affectedRows&&c.data.affectedRows>0&&(0,a.jsxs)("span",{children:[" Affected ",c.data.affectedRows," rows."]})]})]})]})]})]})}var eG=s(46540);let e0=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{className:"relative w-full overflow-auto",children:a.jsx("table",{ref:s,className:(0,i.cn)("w-full caption-bottom text-sm",e),...t})}));e0.displayName="Table";let e1=r.forwardRef(({className:e,...t},s)=>a.jsx("thead",{ref:s,className:(0,i.cn)("[&_tr]:border-b",e),...t}));e1.displayName="TableHeader";let e2=r.forwardRef(({className:e,...t},s)=>a.jsx("tbody",{ref:s,className:(0,i.cn)("[&_tr:last-child]:border-0",e),...t}));e2.displayName="TableBody",r.forwardRef(({className:e,...t},s)=>a.jsx("tfoot",{ref:s,className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let e4=r.forwardRef(({className:e,...t},s)=>a.jsx("tr",{ref:s,className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));e4.displayName="TableRow";let e5=r.forwardRef(({className:e,...t},s)=>a.jsx("th",{ref:s,className:(0,i.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));e5.displayName="TableHead";let e3=r.forwardRef(({className:e,...t},s)=>a.jsx("td",{ref:s,className:(0,i.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));e3.displayName="TableCell",r.forwardRef(({className:e,...t},s)=>a.jsx("caption",{ref:s,className:(0,i.cn)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption";/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let e6=(0,eL.Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]);var e8=s(96885);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let e9=(0,eL.Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]),e7=(0,eL.Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),te=(0,eL.Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);var tt=s(97751);function ts({data:e,metadata:t}){let[s,i]=(0,r.useState)(""),[o,d]=(0,r.useState)(1),[c]=(0,r.useState)(50),u=(0,r.useMemo)(()=>s?e.rows.filter(e=>Object.values(e).some(e=>String(e).toLowerCase().includes(s.toLowerCase()))):e.rows,[e.rows,s]),m=(0,r.useMemo)(()=>{let e=(o-1)*c;return u.slice(e,e+c)},[u,o,c]),x=Math.ceil(u.length/c),h=(e,t)=>{if(null==e)return a.jsx("span",{className:"text-muted-foreground italic",children:"NULL"});if(t.includes("json")||t.includes("jsonb"))try{return a.jsx("pre",{className:"text-xs",children:JSON.stringify(JSON.parse(e),null,2)})}catch{return String(e)}if(t.includes("bool"))return a.jsx(ez.C,{variant:e?"default":"secondary",children:e?"TRUE":"FALSE"});if(t.includes("date")||t.includes("time"))try{return new Date(e).toLocaleString()}catch{return String(e)}let s=String(e);return s.length>100?(0,a.jsxs)("span",{title:s,children:[s.substring(0,100),"..."]}):s},p=e=>e.includes("int")||e.includes("number")||e.includes("decimal")?"\uD83D\uDD22":e.includes("text")||e.includes("varchar")||e.includes("char")?"\uD83D\uDCDD":e.includes("date")||e.includes("time")?"\uD83D\uDCC5":e.includes("bool")?"✅":e.includes("json")?"\uD83D\uDCCB":"\uD83D\uDCC4";return e.rows.length?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(n.ll,{className:"flex items-center gap-2",children:[a.jsx(e6,{className:"h-5 w-5"}),"Query Results",(0,a.jsxs)(ez.C,{variant:"outline",children:[e.rowCount," rows"]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(l.z,{variant:"outline",size:"sm",onClick:()=>{if(!e.rows.length)return;let s=[e.fields.map(e=>e.name).join(","),...e.rows.map(t=>e.fields.map(e=>{let s=t[e.name];return"string"==typeof s&&(s.includes(",")||s.includes('"'))?`"${s.replace(/"/g,'""')}"`:s??""}).join(","))].join("\n"),a=new Blob([s],{type:"text/csv;charset=utf-8;"}),r=document.createElement("a"),n=URL.createObjectURL(a);r.setAttribute("href",n),r.setAttribute("download",`query_results_${t.queryHash}.csv`),r.style.visibility="hidden",document.body.appendChild(r),r.click(),document.body.removeChild(r)},className:"flex items-center gap-2",children:[a.jsx(e8.Z,{className:"h-4 w-4"}),"CSV"]}),(0,a.jsxs)(l.z,{variant:"outline",size:"sm",onClick:()=>{if(!e.rows.length)return;let s=JSON.stringify(e.rows,null,2),a=new Blob([s],{type:"application/json;charset=utf-8;"}),r=document.createElement("a"),n=URL.createObjectURL(a);r.setAttribute("href",n),r.setAttribute("download",`query_results_${t.queryHash}.json`),r.style.visibility="hidden",document.body.appendChild(r),r.click(),document.body.removeChild(r)},className:"flex items-center gap-2",children:[a.jsx(e9,{className:"h-4 w-4"}),"JSON"]})]})]})}),a.jsx(n.aY,{children:(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[(0,a.jsxs)("span",{children:["Execution time: ",t.executionTime,"ms"]}),a.jsx("span",{children:"•"}),(0,a.jsxs)("span",{children:["Rows returned: ",t.rowsReturned]}),a.jsx("span",{children:"•"}),(0,a.jsxs)("span",{children:["Executed at: ",new Date(t.executedAt).toLocaleString()]})]})})]}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[a.jsx(e7,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),a.jsx(eG.I,{placeholder:"Search in results...",value:s,onChange:e=>{i(e.target.value),d(1)},className:"pl-10"})]}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:[u.length," of ",e.rows.length," rows"]})]})})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-0",children:a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)(e0,{children:[a.jsx(e1,{children:a.jsx(e4,{children:e.fields.map(e=>a.jsx(e5,{className:"whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx("span",{children:p(e.type)}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium",children:e.name}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[e.type,!e.nullable&&a.jsx("span",{className:"ml-1",children:"NOT NULL"})]})]})]})},e.name))})}),a.jsx(e2,{children:m.map((t,s)=>a.jsx(e4,{children:e.fields.map(e=>a.jsx(e3,{className:"max-w-xs",children:h(t[e.name],e.type)},e.name))},s))})]})})})}),x>1&&a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Page ",o," of ",x]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(l.z,{variant:"outline",size:"sm",onClick:()=>d(e=>Math.max(1,e-1)),disabled:1===o,className:"flex items-center gap-2",children:[a.jsx(te,{className:"h-4 w-4"}),"Previous"]}),(0,a.jsxs)(l.z,{variant:"outline",size:"sm",onClick:()=>d(e=>Math.min(x,e+1)),disabled:o===x,className:"flex items-center gap-2",children:["Next",a.jsx(tt.Z,{className:"h-4 w-4"})]})]})]})})})]}):a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"flex items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx(eB.Z,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),a.jsx("p",{className:"text-muted-foreground",children:"No data returned"}),a.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"Query executed successfully but returned no rows"})]})})})}var ta=s(33668),tr=s(63085),tn=s(43234),tl="Checkbox",[ti,to]=(0,h.b)(tl),[td,tc]=ti(tl);function tu(e){let{__scopeCheckbox:t,checked:s,children:n,defaultChecked:l,disabled:i,form:o,name:d,onCheckedChange:c,required:u,value:m="on",internal_do_not_use_render:x}=e,[h,p]=(0,S.T)({prop:s,defaultProp:l??!1,onChange:c,caller:tl}),[f,g]=r.useState(null),[j,y]=r.useState(null),v=r.useRef(!1),w=!f||!!o||!!f.closest("form"),b={checked:h,disabled:i,setChecked:p,control:f,setControl:g,name:d,form:o,value:m,hasConsumerStoppedPropagationRef:v,required:u,defaultChecked:!ty(l)&&l,isFormControl:w,bubbleInput:j,setBubbleInput:y};return(0,a.jsx)(td,{scope:t,...b,children:"function"==typeof x?x(b):n})}var tm="CheckboxTrigger",tx=r.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:s,...n},l)=>{let{control:i,value:o,disabled:d,checked:c,required:m,setControl:h,setChecked:p,hasConsumerStoppedPropagationRef:f,isFormControl:g,bubbleInput:j}=tc(tm,e),y=(0,x.e)(l,h),v=r.useRef(c);return r.useEffect(()=>{let e=i?.form;if(e){let t=()=>p(v.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[i,p]),(0,a.jsx)(b.WV.button,{type:"button",role:"checkbox","aria-checked":ty(c)?"mixed":c,"aria-required":m,"data-state":tv(c),"data-disabled":d?"":void 0,disabled:d,value:o,...n,ref:y,onKeyDown:(0,u.M)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,u.M)(s,e=>{p(e=>!!ty(e)||!e),j&&g&&(f.current=e.isPropagationStopped(),f.current||e.stopPropagation())})})});tx.displayName=tm;var th=r.forwardRef((e,t)=>{let{__scopeCheckbox:s,name:r,checked:n,defaultChecked:l,required:i,disabled:o,value:d,onCheckedChange:c,form:u,...m}=e;return(0,a.jsx)(tu,{__scopeCheckbox:s,checked:n,defaultChecked:l,disabled:o,required:i,onCheckedChange:c,name:r,form:u,value:d,internal_do_not_use_render:({isFormControl:e})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(tx,{...m,ref:t,__scopeCheckbox:s}),e&&(0,a.jsx)(tj,{__scopeCheckbox:s})]})})});th.displayName=tl;var tp="CheckboxIndicator",tf=r.forwardRef((e,t)=>{let{__scopeCheckbox:s,forceMount:r,...n}=e,l=tc(tp,s);return(0,a.jsx)(tn.z,{present:r||ty(l.checked)||!0===l.checked,children:(0,a.jsx)(b.WV.span,{"data-state":tv(l.checked),"data-disabled":l.disabled?"":void 0,...n,ref:t,style:{pointerEvents:"none",...e.style}})})});tf.displayName=tp;var tg="CheckboxBubbleInput",tj=r.forwardRef(({__scopeCheckbox:e,...t},s)=>{let{control:n,hasConsumerStoppedPropagationRef:l,checked:i,defaultChecked:o,required:d,disabled:c,name:u,value:m,form:h,bubbleInput:p,setBubbleInput:f}=tc(tg,e),g=(0,x.e)(s,f),j=R(i),y=(0,tr.t)(n);r.useEffect(()=>{if(!p)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!l.current;if(j!==i&&e){let s=new Event("click",{bubbles:t});p.indeterminate=ty(i),e.call(p,!ty(i)&&i),p.dispatchEvent(s)}},[p,j,i,l]);let v=r.useRef(!ty(i)&&i);return(0,a.jsx)(b.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:o??v.current,required:d,disabled:c,name:u,value:m,form:h,...t,tabIndex:-1,ref:g,style:{...t.style,...y,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function ty(e){return"indeterminate"===e}function tv(e){return ty(e)?"indeterminate":e?"checked":"unchecked"}tj.displayName=tg;let tw=r.forwardRef(({className:e,...t},s)=>a.jsx(th,{ref:s,className:(0,i.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:a.jsx(tf,{className:(0,i.cn)("flex items-center justify-center text-current"),children:a.jsx(eO.Z,{className:"h-4 w-4"})})}));tw.displayName=th.displayName;/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let tb=(0,eL.Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),tN=(0,eL.Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),tC={async getUser(e){let t=await fetch(`/api/users?id=${e}`);if(!t.ok)throw Error("Failed to fetch user");return t.json()},async createUser(e){let t=await fetch("/api/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create user");return t.json()},async updateUser(e,t){let s=await fetch(`/api/users?id=${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!s.ok)throw Error("Failed to update user");return s.json()},async getUserConnections(e){let t=await fetch(`/api/connections?userId=${e}`);if(!t.ok)throw Error("Failed to fetch connections");return t.json()},async createConnection(e){let t=await fetch("/api/connections",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create connection");return t.json()},async updateConnection(e,t){let s=await fetch(`/api/connections?id=${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!s.ok)throw Error("Failed to update connection");return s.json()},async deleteConnection(e){if(!(await fetch(`/api/connections?id=${e}`,{method:"DELETE"})).ok)throw Error("Failed to delete connection")},async getUserSessions(e){let t=await fetch(`/api/queries?type=sessions&userId=${e}`);if(!t.ok)throw Error("Failed to fetch sessions");return t.json()},async getSession(e){let t=await fetch(`/api/queries?sessionId=${e}`);if(!t.ok)throw Error("Failed to fetch session");return t.json()},async createSession(e){let t=await fetch("/api/queries?type=session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create session");return t.json()},async createQuery(e){let t=await fetch("/api/queries?type=query",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create query");return t.json()},async getUserQueryHistory(e,t=20){let s=await fetch(`/api/queries?type=history&userId=${e}&limit=${t}`);if(!s.ok)throw Error("Failed to fetch query history");return s.json()},async testConnection(e,t){let s=await fetch("/api/connections/test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e?{connectionId:e}:t)});if(!s.ok)throw Error((await s.json()).error||"Failed to test connection");return s.json()},async getConnectionSchema(e,t=!1){let s=await fetch(`/api/connections/schema?connectionId=${e}&refresh=${t}`);if(!s.ok)throw Error((await s.json()).error||"Failed to fetch schema");return s.json()},async refreshConnectionSchema(e){let t=await fetch("/api/connections/schema",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({connectionId:e})});if(!t.ok)throw Error((await t.json()).error||"Failed to refresh schema");return t.json()},async executeQuery(e){let t=await fetch("/api/queries/execute",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).error||"Failed to execute query");return t.json()},async validateQuery(e,t){let s=await fetch(`/api/queries/execute/validate?connectionId=${e}&sql=${encodeURIComponent(t)}`);if(!s.ok)throw Error((await s.json()).error||"Failed to validate query");return s.json()},async updateQueryFeedback(e,t){let s=await fetch(`/api/queries?type=query&id=${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({userFeedback:t})});if(!s.ok)throw Error("Failed to update query feedback");return s.json()}},tS=()=>tC;function tk({connectionId:e,onConnectionTested:t}){let[s,i]=(0,r.useState)(!1),[o,d]=(0,r.useState)(null),[c,u]=(0,r.useState)({databaseType:"POSTGRESQL",connectionString:"",host:"",port:5432,database:"",username:"",password:"",ssl:!1}),m=tS(),x=async()=>{i(!0),d(null);try{let s;s=e?await m.testConnection(e):await m.testConnection(void 0,c),d(s),t?.(s)}catch(s){let e={success:!1,connected:!1,error:s instanceof Error?s.message:"Unknown error",timestamp:new Date().toISOString()};d(e),t?.(e)}finally{i(!1)}},h=(e,t)=>{u(s=>({...s,[e]:t})),d(null)};return(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center gap-2",children:[s?a.jsx(tb,{className:"h-4 w-4 animate-spin"}):o?.connected?a.jsx(eQ.Z,{className:"h-4 w-4 text-green-500"}):o&&!o.connected?a.jsx(eW,{className:"h-4 w-4 text-red-500"}):a.jsx(eB.Z,{className:"h-4 w-4"}),e?"Test Connection":"Test Database Connection",s?a.jsx(ez.C,{variant:"secondary",children:"Testing..."}):o?.connected?a.jsx(ez.C,{variant:"default",className:"bg-green-100 text-green-800",children:"Connected"}):o&&!o.connected?a.jsx(ez.C,{variant:"destructive",children:"Failed"}):null]})}),(0,a.jsxs)(n.aY,{className:"space-y-4",children:[!e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(ta._,{htmlFor:"databaseType",children:"Database Type"}),(0,a.jsxs)(H,{value:c.databaseType,onValueChange:e=>h("databaseType",e),children:[a.jsx(eq,{children:a.jsx(Y,{})}),(0,a.jsxs)(eV,{children:[a.jsx(eU,{value:"POSTGRESQL",children:"PostgreSQL"}),a.jsx(eU,{value:"MYSQL",children:"MySQL"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(ta._,{htmlFor:"connectionString",children:"Connection String (Optional)"}),a.jsx(eG.I,{id:"connectionString",type:"text",placeholder:"postgresql://user:password@host:port/database",value:c.connectionString,onChange:e=>h("connectionString",e.target.value)}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Leave empty to use individual connection fields below"})]}),!c.connectionString&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(ta._,{htmlFor:"host",children:"Host"}),a.jsx(eG.I,{id:"host",type:"text",placeholder:"localhost",value:c.host,onChange:e=>h("host",e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(ta._,{htmlFor:"port",children:"Port"}),a.jsx(eG.I,{id:"port",type:"number",placeholder:"MYSQL"===c.databaseType?"3306":"5432",value:c.port,onChange:e=>h("port",parseInt(e.target.value)||5432)})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(ta._,{htmlFor:"database",children:"Database Name"}),a.jsx(eG.I,{id:"database",type:"text",placeholder:"my_database",value:c.database,onChange:e=>h("database",e.target.value)})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(ta._,{htmlFor:"username",children:"Username"}),a.jsx(eG.I,{id:"username",type:"text",placeholder:"username",value:c.username,onChange:e=>h("username",e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(ta._,{htmlFor:"password",children:"Password"}),a.jsx(eG.I,{id:"password",type:"password",placeholder:"password",value:c.password,onChange:e=>h("password",e.target.value)})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(tw,{id:"ssl",checked:c.ssl,onCheckedChange:e=>h("ssl",e)}),a.jsx(ta._,{htmlFor:"ssl",children:"Use SSL"})]})]})]}),a.jsx(l.z,{onClick:x,disabled:s||!e&&!c.connectionString&&(!c.host||!c.database||!c.username),className:"w-full",children:s?(0,a.jsxs)(a.Fragment,{children:[a.jsx(tb,{className:"mr-2 h-4 w-4 animate-spin"}),"Testing Connection..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(tN,{className:"mr-2 h-4 w-4"}),"Test Connection"]})}),o&&a.jsx(eA.bZ,{className:o.connected?"border-green-200 bg-green-50":"border-red-200 bg-red-50",children:(0,a.jsxs)(eA.X,{children:[o.connected?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(eQ.Z,{className:"h-4 w-4 text-green-500"}),a.jsx("span",{className:"text-green-700",children:"Connection successful!"})]}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(eW,{className:"h-4 w-4 text-red-500"}),a.jsx("span",{className:"text-red-700",children:"Connection failed"})]}),o.error&&a.jsx("p",{className:"text-sm text-red-600",children:o.error})]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-2",children:["Tested at ",new Date(o.timestamp).toLocaleString()]})]})})]})]})}var tR="Collapsible",[tT,tE]=(0,h.b)(tR),[tM,tZ]=tT(tR),tD=r.forwardRef((e,t)=>{let{__scopeCollapsible:s,open:n,defaultOpen:l,disabled:i,onOpenChange:o,...d}=e,[c,u]=(0,S.T)({prop:n,defaultProp:l??!1,onChange:o,caller:tR});return(0,a.jsx)(tM,{scope:s,disabled:i,contentId:(0,y.M)(),open:c,onOpenToggle:r.useCallback(()=>u(e=>!e),[u]),children:(0,a.jsx)(b.WV.div,{"data-state":tF(c),"data-disabled":i?"":void 0,...d,ref:t})})});tD.displayName=tR;var tP="CollapsibleTrigger",tL=r.forwardRef((e,t)=>{let{__scopeCollapsible:s,...r}=e,n=tZ(tP,s);return(0,a.jsx)(b.WV.button,{type:"button","aria-controls":n.contentId,"aria-expanded":n.open||!1,"data-state":tF(n.open),"data-disabled":n.disabled?"":void 0,disabled:n.disabled,...r,ref:t,onClick:(0,u.M)(e.onClick,n.onOpenToggle)})});tL.displayName=tP;var tI="CollapsibleContent",tO=r.forwardRef((e,t)=>{let{forceMount:s,...r}=e,n=tZ(tI,e.__scopeCollapsible);return(0,a.jsx)(tn.z,{present:s||n.open,children:({present:e})=>(0,a.jsx)(tq,{...r,ref:t,present:e})})});tO.displayName=tI;var tq=r.forwardRef((e,t)=>{let{__scopeCollapsible:s,present:n,children:l,...i}=e,o=tZ(tI,s),[d,c]=r.useState(n),u=r.useRef(null),m=(0,x.e)(t,u),h=r.useRef(0),p=h.current,f=r.useRef(0),g=f.current,j=o.open||d,y=r.useRef(j),v=r.useRef(void 0);return r.useEffect(()=>{let e=requestAnimationFrame(()=>y.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,k.b)(()=>{let e=u.current;if(e){v.current=v.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();h.current=t.height,f.current=t.width,y.current||(e.style.transitionDuration=v.current.transitionDuration,e.style.animationName=v.current.animationName),c(n)}},[o.open,n]),(0,a.jsx)(b.WV.div,{"data-state":tF(o.open),"data-disabled":o.disabled?"":void 0,id:o.contentId,hidden:!j,...i,ref:m,style:{"--radix-collapsible-content-height":p?`${p}px`:void 0,"--radix-collapsible-content-width":g?`${g}px`:void 0,...e.style},children:j&&l})});function tF(e){return e?"open":"closed"}var t_=s(82965);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let tV=(0,eL.Z)("Columns",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["line",{x1:"12",x2:"12",y1:"3",y2:"21",key:"1efggb"}]]);var tU=s(83389);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let tz=(0,eL.Z)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]]);var tA=s(53148);function tH({connectionId:e}){let[t,s]=(0,r.useState)(null),[i,o]=(0,r.useState)(!1),[d,c]=(0,r.useState)(null),[u,m]=(0,r.useState)(null),[x,h]=(0,r.useState)(!1),[p,f]=(0,r.useState)(new Set),[g,j]=(0,r.useState)(new Set),y=tS(),v=async(t=!1)=>{o(!0),c(null);try{let a=await y.getConnectionSchema(e,t);s(a.schema),m(a.lastUpdated),h(a.cached)}catch(e){c(e instanceof Error?e.message:"Failed to load schema")}finally{o(!1)}},w=async()=>{o(!0),c(null);try{let t=await y.refreshConnectionSchema(e);s(t.schema),m(t.lastUpdated),h(!1)}catch(e){c(e instanceof Error?e.message:"Failed to refresh schema")}finally{o(!1)}};(0,r.useEffect)(()=>{v()},[e]);let b=e=>{let t=new Set(p);t.has(e)?t.delete(e):t.add(e),f(t)},N=e=>{let t=new Set(g);t.has(e)?t.delete(e):t.add(e),j(t)},C=e=>e.isPrimaryKey?a.jsx(t_.Z,{className:"h-3 w-3 text-yellow-500"}):e.isForeignKey?a.jsx(t_.Z,{className:"h-3 w-3 text-blue-500"}):a.jsx(tV,{className:"h-3 w-3 text-gray-400"}),S=e=>{let t=[];return e.isPrimaryKey&&t.push(a.jsx(ez.C,{variant:"secondary",className:"text-xs",children:"PK"},"pk")),e.isForeignKey&&t.push(a.jsx(ez.C,{variant:"outline",className:"text-xs",children:"FK"},"fk")),e.nullable||t.push(a.jsx(ez.C,{variant:"outline",className:"text-xs",children:"NOT NULL"},"nn")),t};return i&&!t?a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"flex items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(tb,{className:"h-4 w-4 animate-spin"}),a.jsx("span",{children:"Loading database schema..."})]})})}):d?a.jsx(n.Zb,{children:(0,a.jsxs)(n.aY,{className:"p-6",children:[a.jsx(eA.bZ,{className:"border-red-200 bg-red-50",children:a.jsx(eA.X,{className:"text-red-700",children:d})}),(0,a.jsxs)(l.z,{onClick:()=>v(),className:"mt-4",children:[a.jsx(tN,{className:"mr-2 h-4 w-4"}),"Retry"]})]})}):t?(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx(n.Zb,{children:(0,a.jsxs)(n.Ol,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(n.ll,{className:"flex items-center gap-2",children:[a.jsx(eB.Z,{className:"h-5 w-5"}),"Database Schema",x&&a.jsx(ez.C,{variant:"secondary",children:"Cached"})]}),(0,a.jsxs)(l.z,{onClick:w,disabled:i,variant:"outline",size:"sm",children:[i?a.jsx(tb,{className:"mr-2 h-4 w-4 animate-spin"}):a.jsx(tN,{className:"mr-2 h-4 w-4"}),"Refresh"]})]}),u&&(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Last updated: ",new Date(u).toLocaleString()]})]})}),t.tables&&t.tables.length>0&&(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center gap-2",children:[a.jsx(tU.Z,{className:"h-4 w-4"}),"Tables (",t.tables.length,")"]})}),a.jsx(n.aY,{className:"space-y-2",children:t.tables.map(e=>(0,a.jsxs)(tD,{open:p.has(e.name),onOpenChange:()=>b(e.name),children:[(0,a.jsxs)(tL,{className:"flex items-center gap-2 w-full p-2 hover:bg-muted rounded",children:[p.has(e.name)?a.jsx(eP.Z,{className:"h-4 w-4"}):a.jsx(tt.Z,{className:"h-4 w-4"}),a.jsx(tU.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"font-medium",children:e.name}),(0,a.jsxs)(ez.C,{variant:"outline",className:"text-xs",children:[e.columns.length," columns"]}),e.indexes.length>0&&(0,a.jsxs)(ez.C,{variant:"outline",className:"text-xs",children:[e.indexes.length," indexes"]})]}),(0,a.jsxs)(tO,{className:"ml-6 mt-2 space-y-2",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[a.jsx("h4",{className:"text-sm font-medium",children:"Columns"}),e.columns.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-muted/50 rounded text-sm",children:[C(e),a.jsx("span",{className:"font-mono",children:e.name}),a.jsx(ez.C,{variant:"outline",className:"text-xs",children:e.type}),a.jsx("div",{className:"flex gap-1",children:S(e)}),e.isForeignKey&&e.referencedTable&&(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:["→ ",e.referencedTable,".",e.referencedColumn]})]},e.name))]}),e.indexes.length>0&&(0,a.jsxs)("div",{className:"space-y-1",children:[a.jsx("h4",{className:"text-sm font-medium",children:"Indexes"}),e.indexes.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-muted/50 rounded text-sm",children:[a.jsx(tz,{className:"h-3 w-3 text-gray-400"}),a.jsx("span",{className:"font-mono",children:e.name}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:["(",e.columns.join(", "),")"]}),e.isUnique&&a.jsx(ez.C,{variant:"outline",className:"text-xs",children:"UNIQUE"}),e.isPrimary&&a.jsx(ez.C,{variant:"secondary",className:"text-xs",children:"PRIMARY"})]},e.name))]})]})]},`${e.schema}.${e.name}`))})]}),t.views&&t.views.length>0&&(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center gap-2",children:[a.jsx(tA.Z,{className:"h-4 w-4"}),"Views (",t.views.length,")"]})}),a.jsx(n.aY,{className:"space-y-2",children:t.views.map(e=>(0,a.jsxs)(tD,{open:g.has(e.name),onOpenChange:()=>N(e.name),children:[(0,a.jsxs)(tL,{className:"flex items-center gap-2 w-full p-2 hover:bg-muted rounded",children:[g.has(e.name)?a.jsx(eP.Z,{className:"h-4 w-4"}):a.jsx(tt.Z,{className:"h-4 w-4"}),a.jsx(tA.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"font-medium",children:e.name})]}),a.jsx(tO,{className:"ml-6 mt-2",children:a.jsx("div",{className:"p-3 bg-muted/50 rounded",children:a.jsx("pre",{className:"text-xs overflow-x-auto whitespace-pre-wrap",children:e.definition})})})]},`${e.schema}.${e.name}`))})]}),(!t.tables||0===t.tables.length)&&(!t.views||0===t.views.length)&&a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"flex items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx(eB.Z,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),a.jsx("p",{className:"text-muted-foreground",children:"No tables or views found in this database"})]})})})]}):a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"flex items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx(eB.Z,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),a.jsx("p",{className:"text-muted-foreground",children:"No schema data available"}),(0,a.jsxs)(l.z,{onClick:()=>v(!0),className:"mt-4",children:[a.jsx(tN,{className:"mr-2 h-4 w-4"}),"Load Schema"]})]})})})}/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let tQ=(0,eL.Z)("TestTube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5h0c-1.4 0-2.5-1.1-2.5-2.5V2",key:"187lwq"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]);var tW=s(13746);function tB(){let[e,t]=(0,r.useState)(`-- Welcome to QueryCraft Studio Query Editor
-- Try executing this sample query:

SELECT 
  customer_id,
  name,
  email,
  COUNT(order_id) as total_orders,
  SUM(order_total) as total_spent
FROM customers c
LEFT JOIN orders o ON c.customer_id = o.customer_id
GROUP BY customer_id, name, email
ORDER BY total_spent DESC
LIMIT 10;`),[s,i]=(0,r.useState)(null),[d,c]=(0,r.useState)("editor"),[u,m]=(0,r.useState)(null),x=[{id:"conn1",name:"Production DB (PostgreSQL)",type:"POSTGRESQL"},{id:"conn2",name:"Analytics DB (MySQL)",type:"MYSQL"},{id:"conn3",name:"Development DB (PostgreSQL)",type:"POSTGRESQL"}],h=[{id:"editor",label:"Query Editor",icon:e9},{id:"results",label:"Results",icon:e$.Z,disabled:!u},{id:"schema",label:"Schema",icon:tA.Z,disabled:!s},{id:"test",label:"Test Connection",icon:tQ}];return(0,a.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold",children:"Query Editor"}),a.jsx("p",{className:"text-muted-foreground",children:"Execute SQL queries against your connected databases"})]}),a.jsx("div",{className:"flex items-center gap-4",children:(0,a.jsxs)(H,{value:s||"",onValueChange:i,children:[a.jsx(eq,{className:"w-64",children:a.jsx(Y,{placeholder:"Select a database connection"})}),a.jsx(eV,{children:x.map(e=>a.jsx(eU,{value:e.id,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(eB.Z,{className:"h-4 w-4"}),a.jsx("span",{children:e.name}),a.jsx(ez.C,{variant:"outline",className:"text-xs",children:e.type})]})},e.id))})]})})]}),s&&a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(eB.Z,{className:"h-4 w-4 text-green-500"}),(0,a.jsxs)("span",{className:"text-sm",children:["Connected to: ",x.find(e=>e.id===s)?.name]}),a.jsx(ez.C,{variant:"default",className:"text-xs",children:x.find(e=>e.id===s)?.type})]})})}),a.jsx("div",{className:"flex space-x-1 bg-muted p-1 rounded-lg",children:h.map(e=>{let t=e.icon;return(0,a.jsxs)(l.z,{variant:d===e.id?"default":"ghost",size:"sm",onClick:()=>c(e.id),disabled:e.disabled,className:"flex items-center gap-2",children:[a.jsx(t,{className:"h-4 w-4"}),e.label]},e.id)})}),(0,a.jsxs)("div",{className:"space-y-6",children:["editor"===d&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center gap-2",children:[a.jsx(e9,{className:"h-5 w-5"}),"SQL Editor"]})}),a.jsx(n.aY,{children:a.jsx(o,{value:e,onChange:e=>t(e.target.value),placeholder:"Enter your SQL query here...",className:"min-h-[400px] font-mono text-sm"})})]}),a.jsx("div",{children:a.jsx(eX,{sql:e,connectionId:s,onExecutionComplete:e=>{m(e),e.success&&c("results")}})})]}),"results"===d&&u&&a.jsx("div",{children:u.success&&u.data?a.jsx(ts,{data:u.data,metadata:u.metadata}):a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-8 text-center",children:a.jsx("div",{className:"text-muted-foreground",children:u.error?(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-red-600 mb-2",children:"Query Failed"}),a.jsx("p",{className:"text-sm",children:u.error})]}):a.jsx("p",{children:"No results to display"})})})})}),"schema"===d&&s&&a.jsx(tH,{connectionId:s}),"test"===d&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[s&&a.jsx(tk,{connectionId:s,onConnectionTested:e=>{console.log("Connection test result:",e)}}),a.jsx(tk,{onConnectionTested:e=>{console.log("New connection test result:",e)}})]})]}),(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center gap-2",children:[a.jsx(tW.Z,{className:"h-5 w-5"}),"Quick Actions"]})}),a.jsx(n.aY,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)(l.z,{variant:"outline",onClick:()=>t("SELECT * FROM users LIMIT 10;"),className:"h-auto p-4 flex flex-col items-center gap-2",children:[a.jsx(eB.Z,{className:"h-6 w-6"}),a.jsx("span",{className:"text-sm",children:"Sample Query"})]}),(0,a.jsxs)(l.z,{variant:"outline",onClick:()=>t(""),className:"h-auto p-4 flex flex-col items-center gap-2",children:[a.jsx(e9,{className:"h-6 w-6"}),a.jsx("span",{className:"text-sm",children:"Clear Editor"})]}),(0,a.jsxs)(l.z,{variant:"outline",onClick:()=>c("schema"),disabled:!s,className:"h-auto p-4 flex flex-col items-center gap-2",children:[a.jsx(tA.Z,{className:"h-6 w-6"}),a.jsx("span",{className:"text-sm",children:"View Schema"})]}),(0,a.jsxs)(l.z,{variant:"outline",onClick:()=>c("test"),className:"h-auto p-4 flex flex-col items-center gap-2",children:[a.jsx(tQ,{className:"h-6 w-6"}),a.jsx("span",{className:"text-sm",children:"Test Connection"})]})]})})]})]})}},40874:(e,t,s)=>{"use strict";s.d(t,{X:()=>d,bZ:()=>o});var a=s(95344),r=s(3729),n=s(49247),l=s(11453);let i=(0,n.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=r.forwardRef(({className:e,variant:t,...s},r)=>a.jsx("div",{ref:r,role:"alert",className:(0,l.cn)(i({variant:t}),e),...s}));o.displayName="Alert",r.forwardRef(({className:e,...t},s)=>a.jsx("h5",{ref:s,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let d=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",e),...t}));d.displayName="AlertDescription"},19591:(e,t,s)=>{"use strict";s.d(t,{C:()=>i});var a=s(95344);s(3729);var r=s(49247),n=s(11453);let l=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...s}){return a.jsx("div",{className:(0,n.cn)(l({variant:t}),e),...s})}},5094:(e,t,s)=>{"use strict";s.d(t,{z:()=>d});var a=s(95344),r=s(3729),n=s(32751),l=s(49247),i=s(11453);let o=(0,l.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef(({className:e,variant:t,size:s,asChild:r=!1,...l},d)=>{let c=r?n.g7:"button";return a.jsx(c,{className:(0,i.cn)(o({variant:t,size:s,className:e})),ref:d,...l})});d.displayName="Button"},23673:(e,t,s)=>{"use strict";s.d(t,{Ol:()=>i,Zb:()=>l,aY:()=>d,ll:()=>o});var a=s(95344),r=s(3729),n=s(11453);let l=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));l.displayName="Card";let i=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let o=r.forwardRef(({className:e,...t},s)=>a.jsx("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));o.displayName="CardTitle",r.forwardRef(({className:e,...t},s)=>a.jsx("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription";let d=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent",r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},46540:(e,t,s)=>{"use strict";s.d(t,{I:()=>l});var a=s(95344),r=s(3729),n=s(11453);let l=r.forwardRef(({className:e,type:t,...s},r)=>a.jsx("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...s}));l.displayName="Input"},33668:(e,t,s)=>{"use strict";s.d(t,{_:()=>c});var a=s(95344),r=s(3729),n=s(62409),l=r.forwardRef((e,t)=>(0,a.jsx)(n.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=s(49247),o=s(11453);let d=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef(({className:e,...t},s)=>a.jsx(l,{ref:s,className:(0,o.cn)(d(),e),...t}));c.displayName=l.displayName},40014:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>r,default:()=>l});let a=(0,s(86843).createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/dashboard/query-editor/page.tsx`),{__esModule:r,$$typeof:n}=a,l=a.default}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[638,599,66,162],()=>s(63530));module.exports=a})();