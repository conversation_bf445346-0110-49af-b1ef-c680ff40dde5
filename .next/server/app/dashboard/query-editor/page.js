(()=>{var e={};e.id=809,e.ids=[809],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14300:e=>{"use strict";e.exports=require("buffer")},32081:e=>{"use strict";e.exports=require("child_process")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},56804:()=>{},68645:()=>{},63530:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>h,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=s(50482),n=s(69108),i=s(62563),o=s.n(i),a=s(68300),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);s.d(t,l);let c=["",{children:["dashboard",{children:["query-editor",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,40014)),"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/dashboard/query-editor/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,98890)),"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/dashboard/query-editor/page.tsx"],h="/dashboard/query-editor/page",u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/dashboard/query-editor/page",pathname:"/dashboard/query-editor",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},86343:(e,t,s)=>{Promise.resolve().then(s.bind(s,21305))},92466:(e,t,s)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let s="color: "+this.color;t.splice(1,0,s,"color: inherit");let r=0,n=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(r++,"%c"===e&&(n=r))}),t.splice(n,0,s)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=s(9687)(t);let{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},9687:(e,t,s)=>{e.exports=function(e){function t(e){let s,n,i;let o=null;function a(...e){if(!a.enabled)return;let r=Number(new Date),n=r-(s||r);a.diff=n,a.prev=s,a.curr=r,s=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(s,r)=>{if("%%"===s)return"%";i++;let n=t.formatters[r];if("function"==typeof n){let t=e[i];s=n.call(a,t),e.splice(i,1),i--}return s}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(n!==t.namespaces&&(n=t.namespaces,i=t.enabled(e)),i),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,s){let r=t(this.namespace+(void 0===s?":":s)+e);return r.log=this.log,r}function n(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(n),...t.skips.map(n).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let s;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let r=("string"==typeof e?e:"").split(/[\s,]+/),n=r.length;for(s=0;s<n;s++)r[s]&&("-"===(e=r[s].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let s,r;if("*"===e[e.length-1])return!0;for(s=0,r=t.skips.length;s<r;s++)if(t.skips[s].test(e))return!1;for(s=0,r=t.names.length;s<r;s++)if(t.names[s].test(e))return!0;return!1},t.humanize=s(58476),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(s=>{t[s]=e[s]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t)|0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}},87978:(e,t,s)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=s(92466):e.exports=s(67854)},67854:(e,t,s)=>{let r=s(76224),n=s(73837);t.init=function(e){e.inspectOpts={};let s=Object.keys(t.inspectOpts);for(let r=0;r<s.length;r++)e.inspectOpts[s[r]]=t.inspectOpts[s[r]]},t.log=function(...e){return process.stderr.write(n.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(s){let{namespace:r,useColors:n}=this;if(n){let t=this.color,n="\x1b[3"+(t<8?t:"8;5;"+t),i=`  ${n};1m${r} \u001B[0m`;s[0]=i+s[0].split("\n").join("\n"+i),s.push(n+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else s[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+r+" "+s[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:r.isatty(process.stderr.fd)},t.destroy=n.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=s(60125);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let s=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),r=process.env[t];return r=!!/^(yes|on|true|enabled)$/i.test(r)||!/^(no|off|false|disabled)$/i.test(r)&&("null"===r?null:Number(r)),e[s]=r,e},{}),e.exports=s(9687)(t);let{formatters:i}=e.exports;i.o=function(e){return this.inspectOpts.colors=this.useColors,n.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},i.O=function(e){return this.inspectOpts.colors=this.useColors,n.inspect(e,this.inspectOpts)}},67980:(e,t,s)=>{"use strict";let{EMPTY_BUFFER:r}=s(81340),n=Buffer[Symbol.species];function i(e,t,s,r,n){for(let i=0;i<n;i++)s[r+i]=e[i]^t[3&i]}function o(e,t){for(let s=0;s<e.length;s++)e[s]^=t[3&s]}if(e.exports={concat:function(e,t){if(0===e.length)return r;if(1===e.length)return e[0];let s=Buffer.allocUnsafe(t),i=0;for(let t=0;t<e.length;t++){let r=e[t];s.set(r,i),i+=r.length}return i<t?new n(s.buffer,s.byteOffset,i):s},mask:i,toArrayBuffer:function(e){return e.length===e.buffer.byteLength?e.buffer:e.buffer.slice(e.byteOffset,e.byteOffset+e.length)},toBuffer:function e(t){let s;return(e.readOnly=!0,Buffer.isBuffer(t))?t:(t instanceof ArrayBuffer?s=new n(t):ArrayBuffer.isView(t)?s=new n(t.buffer,t.byteOffset,t.byteLength):(s=Buffer.from(t),e.readOnly=!1),s)},unmask:o},!process.env.WS_NO_BUFFER_UTIL)try{let t=s(56804);e.exports.mask=function(e,s,r,n,o){o<48?i(e,s,r,n,o):t.mask(e,s,r,n,o)},e.exports.unmask=function(e,s){e.length<32?o(e,s):t.unmask(e,s)}}catch(e){}},81340:e=>{"use strict";e.exports={BINARY_TYPES:["nodebuffer","arraybuffer","fragments"],EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}},18620:(e,t,s)=>{"use strict";let{kForOnEventAttribute:r,kListener:n}=s(81340),i=Symbol("kCode"),o=Symbol("kData"),a=Symbol("kError"),l=Symbol("kMessage"),c=Symbol("kReason"),d=Symbol("kTarget"),h=Symbol("kType"),u=Symbol("kWasClean");class p{constructor(e){this[d]=null,this[h]=e}get target(){return this[d]}get type(){return this[h]}}Object.defineProperty(p.prototype,"target",{enumerable:!0}),Object.defineProperty(p.prototype,"type",{enumerable:!0});class f extends p{constructor(e,t={}){super(e),this[i]=void 0===t.code?0:t.code,this[c]=void 0===t.reason?"":t.reason,this[u]=void 0!==t.wasClean&&t.wasClean}get code(){return this[i]}get reason(){return this[c]}get wasClean(){return this[u]}}Object.defineProperty(f.prototype,"code",{enumerable:!0}),Object.defineProperty(f.prototype,"reason",{enumerable:!0}),Object.defineProperty(f.prototype,"wasClean",{enumerable:!0});class m extends p{constructor(e,t={}){super(e),this[a]=void 0===t.error?null:t.error,this[l]=void 0===t.message?"":t.message}get error(){return this[a]}get message(){return this[l]}}Object.defineProperty(m.prototype,"error",{enumerable:!0}),Object.defineProperty(m.prototype,"message",{enumerable:!0});class g extends p{constructor(e,t={}){super(e),this[o]=void 0===t.data?null:t.data}get data(){return this[o]}}function y(e,t,s){"object"==typeof e&&e.handleEvent?e.handleEvent.call(e,s):e.call(t,s)}Object.defineProperty(g.prototype,"data",{enumerable:!0}),e.exports={CloseEvent:f,ErrorEvent:m,Event:p,EventTarget:{addEventListener(e,t,s={}){let i;for(let i of this.listeners(e))if(!s[r]&&i[n]===t&&!i[r])return;if("message"===e)i=function(e,s){let r=new g("message",{data:s?e:e.toString()});r[d]=this,y(t,this,r)};else if("close"===e)i=function(e,s){let r=new f("close",{code:e,reason:s.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});r[d]=this,y(t,this,r)};else if("error"===e)i=function(e){let s=new m("error",{error:e,message:e.message});s[d]=this,y(t,this,s)};else{if("open"!==e)return;i=function(){let e=new p("open");e[d]=this,y(t,this,e)}}i[r]=!!s[r],i[n]=t,s.once?this.once(e,i):this.on(e,i)},removeEventListener(e,t){for(let s of this.listeners(e))if(s[n]===t&&!s[r]){this.removeListener(e,s);break}}},MessageEvent:g}},39900:(e,t,s)=>{"use strict";let{tokenChars:r}=s(47325);function n(e,t,s){void 0===e[t]?e[t]=[s]:e[t].push(s)}e.exports={format:function(e){return Object.keys(e).map(t=>{let s=e[t];return Array.isArray(s)||(s=[s]),s.map(e=>[t].concat(Object.keys(e).map(t=>{let s=e[t];return Array.isArray(s)||(s=[s]),s.map(e=>!0===e?t:`${t}=${e}`).join("; ")})).join("; ")).join(", ")}).join(", ")},parse:function(e){let t,s;let i=Object.create(null),o=Object.create(null),a=!1,l=!1,c=!1,d=-1,h=-1,u=-1,p=0;for(;p<e.length;p++)if(h=e.charCodeAt(p),void 0===t){if(-1===u&&1===r[h])-1===d&&(d=p);else if(0!==p&&(32===h||9===h))-1===u&&-1!==d&&(u=p);else if(59===h||44===h){if(-1===d)throw SyntaxError(`Unexpected character at index ${p}`);-1===u&&(u=p);let s=e.slice(d,u);44===h?(n(i,s,o),o=Object.create(null)):t=s,d=u=-1}else throw SyntaxError(`Unexpected character at index ${p}`)}else if(void 0===s){if(-1===u&&1===r[h])-1===d&&(d=p);else if(32===h||9===h)-1===u&&-1!==d&&(u=p);else if(59===h||44===h){if(-1===d)throw SyntaxError(`Unexpected character at index ${p}`);-1===u&&(u=p),n(o,e.slice(d,u),!0),44===h&&(n(i,t,o),o=Object.create(null),t=void 0),d=u=-1}else if(61===h&&-1!==d&&-1===u)s=e.slice(d,p),d=u=-1;else throw SyntaxError(`Unexpected character at index ${p}`)}else if(l){if(1!==r[h])throw SyntaxError(`Unexpected character at index ${p}`);-1===d?d=p:a||(a=!0),l=!1}else if(c){if(1===r[h])-1===d&&(d=p);else if(34===h&&-1!==d)c=!1,u=p;else if(92===h)l=!0;else throw SyntaxError(`Unexpected character at index ${p}`)}else if(34===h&&61===e.charCodeAt(p-1))c=!0;else if(-1===u&&1===r[h])-1===d&&(d=p);else if(-1!==d&&(32===h||9===h))-1===u&&(u=p);else if(59===h||44===h){if(-1===d)throw SyntaxError(`Unexpected character at index ${p}`);-1===u&&(u=p);let r=e.slice(d,u);a&&(r=r.replace(/\\/g,""),a=!1),n(o,s,r),44===h&&(n(i,t,o),o=Object.create(null),t=void 0),s=void 0,d=u=-1}else throw SyntaxError(`Unexpected character at index ${p}`);if(-1===d||c||32===h||9===h)throw SyntaxError("Unexpected end of input");-1===u&&(u=p);let f=e.slice(d,u);return void 0===t?n(i,f,o):(void 0===s?n(o,f,!0):a?n(o,s,f.replace(/\\/g,"")):n(o,s,f),n(i,t,o)),i}}},27781:e=>{"use strict";let t=Symbol("kDone"),s=Symbol("kRun");class r{constructor(e){this[t]=()=>{this.pending--,this[s]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[s]()}[s](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[t])}}}e.exports=r},59635:(e,t,s)=>{"use strict";let r;let n=s(59796),i=s(67980),o=s(27781),{kStatusCode:a}=s(81340),l=Buffer[Symbol.species],c=Buffer.from([0,0,255,255]),d=Symbol("permessage-deflate"),h=Symbol("total-length"),u=Symbol("callback"),p=Symbol("buffers"),f=Symbol("error");class m{constructor(e,t,s){this._maxPayload=0|s,this._options=e||{},this._threshold=void 0!==this._options.threshold?this._options.threshold:1024,this._isServer=!!t,this._deflate=null,this._inflate=null,this.params=null,r||(r=new o(void 0!==this._options.concurrencyLimit?this._options.concurrencyLimit:10))}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:null==this._options.clientMaxWindowBits&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[u];this._deflate.close(),this._deflate=null,e&&e(Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let t=this._options,s=e.find(e=>(!1!==t.serverNoContextTakeover||!e.server_no_context_takeover)&&(!e.server_max_window_bits||!1!==t.serverMaxWindowBits&&("number"!=typeof t.serverMaxWindowBits||!(t.serverMaxWindowBits>e.server_max_window_bits)))&&("number"!=typeof t.clientMaxWindowBits||!!e.client_max_window_bits));if(!s)throw Error("None of the extension offers can be accepted");return t.serverNoContextTakeover&&(s.server_no_context_takeover=!0),t.clientNoContextTakeover&&(s.client_no_context_takeover=!0),"number"==typeof t.serverMaxWindowBits&&(s.server_max_window_bits=t.serverMaxWindowBits),"number"==typeof t.clientMaxWindowBits?s.client_max_window_bits=t.clientMaxWindowBits:(!0===s.client_max_window_bits||!1===t.clientMaxWindowBits)&&delete s.client_max_window_bits,s}acceptAsClient(e){let t=e[0];if(!1===this._options.clientNoContextTakeover&&t.client_no_context_takeover)throw Error('Unexpected parameter "client_no_context_takeover"');if(t.client_max_window_bits){if(!1===this._options.clientMaxWindowBits||"number"==typeof this._options.clientMaxWindowBits&&t.client_max_window_bits>this._options.clientMaxWindowBits)throw Error('Unexpected or invalid parameter "client_max_window_bits"')}else"number"==typeof this._options.clientMaxWindowBits&&(t.client_max_window_bits=this._options.clientMaxWindowBits);return t}normalizeParams(e){return e.forEach(e=>{Object.keys(e).forEach(t=>{let s=e[t];if(s.length>1)throw Error(`Parameter "${t}" must have only a single value`);if(s=s[0],"client_max_window_bits"===t){if(!0!==s){let e=+s;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${s}`);s=e}else if(!this._isServer)throw TypeError(`Invalid value for parameter "${t}": ${s}`)}else if("server_max_window_bits"===t){let e=+s;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${s}`);s=e}else if("client_no_context_takeover"===t||"server_no_context_takeover"===t){if(!0!==s)throw TypeError(`Invalid value for parameter "${t}": ${s}`)}else throw Error(`Unknown parameter "${t}"`);e[t]=s})}),e}decompress(e,t,s){r.add(r=>{this._decompress(e,t,(e,t)=>{r(),s(e,t)})})}compress(e,t,s){r.add(r=>{this._compress(e,t,(e,t)=>{r(),s(e,t)})})}_decompress(e,t,s){let r=this._isServer?"client":"server";if(!this._inflate){let e=`${r}_max_window_bits`,t="number"!=typeof this.params[e]?n.Z_DEFAULT_WINDOWBITS:this.params[e];this._inflate=n.createInflateRaw({...this._options.zlibInflateOptions,windowBits:t}),this._inflate[d]=this,this._inflate[h]=0,this._inflate[p]=[],this._inflate.on("error",x),this._inflate.on("data",y)}this._inflate[u]=s,this._inflate.write(e),t&&this._inflate.write(c),this._inflate.flush(()=>{let e=this._inflate[f];if(e){this._inflate.close(),this._inflate=null,s(e);return}let n=i.concat(this._inflate[p],this._inflate[h]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[h]=0,this._inflate[p]=[],t&&this.params[`${r}_no_context_takeover`]&&this._inflate.reset()),s(null,n)})}_compress(e,t,s){let r=this._isServer?"server":"client";if(!this._deflate){let e=`${r}_max_window_bits`,t="number"!=typeof this.params[e]?n.Z_DEFAULT_WINDOWBITS:this.params[e];this._deflate=n.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:t}),this._deflate[h]=0,this._deflate[p]=[],this._deflate.on("data",g)}this._deflate[u]=s,this._deflate.write(e),this._deflate.flush(n.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let e=i.concat(this._deflate[p],this._deflate[h]);t&&(e=new l(e.buffer,e.byteOffset,e.length-4)),this._deflate[u]=null,this._deflate[h]=0,this._deflate[p]=[],t&&this.params[`${r}_no_context_takeover`]&&this._deflate.reset(),s(null,e)})}}function g(e){this[p].push(e),this[h]+=e.length}function y(e){if(this[h]+=e.length,this[d]._maxPayload<1||this[h]<=this[d]._maxPayload){this[p].push(e);return}this[f]=RangeError("Max payload size exceeded"),this[f].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[f][a]=1009,this.removeListener("data",y),this.reset()}function x(e){this[d]._inflate=null,e[a]=1007,this[u](e)}e.exports=m},29573:(e,t,s)=>{"use strict";let{Writable:r}=s(12781),n=s(59635),{BINARY_TYPES:i,EMPTY_BUFFER:o,kStatusCode:a,kWebSocket:l}=s(81340),{concat:c,toArrayBuffer:d,unmask:h}=s(67980),{isValidStatusCode:u,isValidUTF8:p}=s(47325),f=Buffer[Symbol.species];class m extends r{constructor(e={}){super(),this._allowSynchronousEvents=void 0===e.allowSynchronousEvents||e.allowSynchronousEvents,this._binaryType=e.binaryType||i[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=0|e.maxPayload,this._skipUTF8Validation=!!e.skipUTF8Validation,this[l]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=0}_write(e,t,s){if(8===this._opcode&&0==this._state)return s();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(s)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let t=this._buffers[0];return this._buffers[0]=new f(t.buffer,t.byteOffset+e,t.length-e),new f(t.buffer,t.byteOffset,e)}let t=Buffer.allocUnsafe(e);do{let s=this._buffers[0],r=t.length-e;e>=s.length?t.set(this._buffers.shift(),r):(t.set(new Uint8Array(s.buffer,s.byteOffset,e),r),this._buffers[0]=new f(s.buffer,s.byteOffset+e,s.length-e)),e-=s.length}while(e>0);return t}startLoop(e){this._loop=!0;do switch(this._state){case 0:this.getInfo(e);break;case 1:this.getPayloadLength16(e);break;case 2:this.getPayloadLength64(e);break;case 3:this.getMask();break;case 4:this.getData(e);break;case 5:case 6:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let t=this.consume(2);if((48&t[0])!=0){e(this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3"));return}let s=(64&t[0])==64;if(s&&!this._extensions[n.extensionName]){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._fin=(128&t[0])==128,this._opcode=15&t[0],this._payloadLength=127&t[1],0===this._opcode){if(s){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(!this._fragmented){e(this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._opcode=this._fragmented}else if(1===this._opcode||2===this._opcode){if(this._fragmented){e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._compressed=s}else if(this._opcode>7&&this._opcode<11){if(!this._fin){e(this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN"));return}if(s){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._payloadLength>125||8===this._opcode&&1===this._payloadLength){e(this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH"));return}}else{e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}if(this._fin||this._fragmented||(this._fragmented=this._opcode),this._masked=(128&t[1])==128,this._isServer){if(!this._masked){e(this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK"));return}}else if(this._masked){e(this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK"));return}126===this._payloadLength?this._state=1:127===this._payloadLength?this._state=2:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let t=this.consume(8),s=t.readUInt32BE(0);if(s>2097151){e(this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH"));return}this._payloadLength=4294967296*s+t.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0)){e(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._masked?this._state=3:this._state=4}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=4}getData(e){let t=o;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}t=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!=0&&h(t,this._mask)}if(this._opcode>7){this.controlMessage(t,e);return}if(this._compressed){this._state=5,this.decompress(t,e);return}t.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(t)),this.dataMessage(e)}decompress(e,t){this._extensions[n.extensionName].decompress(e,this._fin,(e,s)=>{if(e)return t(e);if(s.length){if(this._messageLength+=s.length,this._messageLength>this._maxPayload&&this._maxPayload>0){t(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._fragments.push(s)}this.dataMessage(t),0===this._state&&this.startLoop(t)})}dataMessage(e){if(!this._fin){this._state=0;return}let t=this._messageLength,s=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],2===this._opcode){let r;r="nodebuffer"===this._binaryType?c(s,t):"arraybuffer"===this._binaryType?d(c(s,t)):s,this._allowSynchronousEvents?(this.emit("message",r,!0),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",r,!0),this._state=0,this.startLoop(e)}))}else{let r=c(s,t);if(!this._skipUTF8Validation&&!p(r)){e(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}5===this._state||this._allowSynchronousEvents?(this.emit("message",r,!1),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",r,!1),this._state=0,this.startLoop(e)}))}}controlMessage(e,t){if(8===this._opcode){if(0===e.length)this._loop=!1,this.emit("conclude",1005,o),this.end();else{let s=e.readUInt16BE(0);if(!u(s)){t(this.createError(RangeError,`invalid status code ${s}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE"));return}let r=new f(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!p(r)){t(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}this._loop=!1,this.emit("conclude",s,r),this.end()}this._state=0;return}this._allowSynchronousEvents?(this.emit(9===this._opcode?"ping":"pong",e),this._state=0):(this._state=6,setImmediate(()=>{this.emit(9===this._opcode?"ping":"pong",e),this._state=0,this.startLoop(t)}))}createError(e,t,s,r,n){this._loop=!1,this._errored=!0;let i=new e(s?`Invalid WebSocket frame: ${t}`:t);return Error.captureStackTrace(i,this.createError),i.code=n,i[a]=r,i}}e.exports=m},14014:(e,t,s)=>{"use strict";let r;let{Duplex:n}=s(12781),{randomFillSync:i}=s(6113),o=s(59635),{EMPTY_BUFFER:a}=s(81340),{isValidStatusCode:l}=s(47325),{mask:c,toBuffer:d}=s(67980),h=Symbol("kByteLength"),u=Buffer.alloc(4),p=8192;class f{constructor(e,t,s){this._extensions=t||{},s&&(this._generateMask=s,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._deflating=!1,this._queue=[]}static frame(e,t){let s,n;let o=!1,a=2,l=!1;t.mask&&(s=t.maskBuffer||u,t.generateMask?t.generateMask(s):(8192===p&&(void 0===r&&(r=Buffer.alloc(8192)),i(r,0,8192),p=0),s[0]=r[p++],s[1]=r[p++],s[2]=r[p++],s[3]=r[p++]),l=(s[0]|s[1]|s[2]|s[3])==0,a=6),"string"==typeof e?n=(!t.mask||l)&&void 0!==t[h]?t[h]:(e=Buffer.from(e)).length:(n=e.length,o=t.mask&&t.readOnly&&!l);let d=n;n>=65536?(a+=8,d=127):n>125&&(a+=2,d=126);let f=Buffer.allocUnsafe(o?n+a:a);return(f[0]=t.fin?128|t.opcode:t.opcode,t.rsv1&&(f[0]|=64),f[1]=d,126===d?f.writeUInt16BE(n,2):127===d&&(f[2]=f[3]=0,f.writeUIntBE(n,4,6)),t.mask)?(f[1]|=128,f[a-4]=s[0],f[a-3]=s[1],f[a-2]=s[2],f[a-1]=s[3],l)?[f,e]:o?(c(e,s,f,a,n),[f]):(c(e,s,e,0,n),[f,e]):[f,e]}close(e,t,s,r){let n;if(void 0===e)n=a;else if("number"==typeof e&&l(e)){if(void 0!==t&&t.length){let s=Buffer.byteLength(t);if(s>123)throw RangeError("The message must not be greater than 123 bytes");(n=Buffer.allocUnsafe(2+s)).writeUInt16BE(e,0),"string"==typeof t?n.write(t,2):n.set(t,2)}else(n=Buffer.allocUnsafe(2)).writeUInt16BE(e,0)}else throw TypeError("First argument must be a valid error code number");let i={[h]:n.length,fin:!0,generateMask:this._generateMask,mask:s,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};this._deflating?this.enqueue([this.dispatch,n,!1,i,r]):this.sendFrame(f.frame(n,i),r)}ping(e,t,s){let r,n;if("string"==typeof e?(r=Buffer.byteLength(e),n=!1):(r=(e=d(e)).length,n=d.readOnly),r>125)throw RangeError("The data size must not be greater than 125 bytes");let i={[h]:r,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:9,readOnly:n,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,i,s]):this.sendFrame(f.frame(e,i),s)}pong(e,t,s){let r,n;if("string"==typeof e?(r=Buffer.byteLength(e),n=!1):(r=(e=d(e)).length,n=d.readOnly),r>125)throw RangeError("The data size must not be greater than 125 bytes");let i={[h]:r,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:10,readOnly:n,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,i,s]):this.sendFrame(f.frame(e,i),s)}send(e,t,s){let r,n;let i=this._extensions[o.extensionName],a=t.binary?2:1,l=t.compress;if("string"==typeof e?(r=Buffer.byteLength(e),n=!1):(r=(e=d(e)).length,n=d.readOnly),this._firstFragment?(this._firstFragment=!1,l&&i&&i.params[i._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(l=r>=i._threshold),this._compress=l):(l=!1,a=0),t.fin&&(this._firstFragment=!0),i){let i={[h]:r,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:a,readOnly:n,rsv1:l};this._deflating?this.enqueue([this.dispatch,e,this._compress,i,s]):this.dispatch(e,this._compress,i,s)}else this.sendFrame(f.frame(e,{[h]:r,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:a,readOnly:n,rsv1:!1}),s)}dispatch(e,t,s,r){if(!t){this.sendFrame(f.frame(e,s),r);return}let n=this._extensions[o.extensionName];this._bufferedBytes+=s[h],this._deflating=!0,n.compress(e,s.fin,(e,t)=>{if(this._socket.destroyed){let e=Error("The socket was closed while data was being compressed");"function"==typeof r&&r(e);for(let t=0;t<this._queue.length;t++){let s=this._queue[t],r=s[s.length-1];"function"==typeof r&&r(e)}return}this._bufferedBytes-=s[h],this._deflating=!1,s.readOnly=!1,this.sendFrame(f.frame(t,s),r),this.dequeue()})}dequeue(){for(;!this._deflating&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][h],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][h],this._queue.push(e)}sendFrame(e,t){2===e.length?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],t),this._socket.uncork()):this._socket.write(e[0],t)}}e.exports=f},67488:(e,t,s)=>{"use strict";let{Duplex:r}=s(12781);function n(e){e.emit("close")}function i(){!this.destroyed&&this._writableState.finished&&this.destroy()}function o(e){this.removeListener("error",o),this.destroy(),0===this.listenerCount("error")&&this.emit("error",e)}e.exports=function(e,t){let s=!0,a=new r({...t,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return e.on("message",function(t,s){let r=!s&&a._readableState.objectMode?t.toString():t;a.push(r)||e.pause()}),e.once("error",function(e){a.destroyed||(s=!1,a.destroy(e))}),e.once("close",function(){a.destroyed||a.push(null)}),a._destroy=function(t,r){if(e.readyState===e.CLOSED){r(t),process.nextTick(n,a);return}let i=!1;e.once("error",function(e){i=!0,r(e)}),e.once("close",function(){i||r(t),process.nextTick(n,a)}),s&&e.terminate()},a._final=function(t){if(e.readyState===e.CONNECTING){e.once("open",function(){a._final(t)});return}null!==e._socket&&(e._socket._writableState.finished?(t(),a._readableState.endEmitted&&a.destroy()):(e._socket.once("finish",function(){t()}),e.close()))},a._read=function(){e.isPaused&&e.resume()},a._write=function(t,s,r){if(e.readyState===e.CONNECTING){e.once("open",function(){a._write(t,s,r)});return}e.send(t,r)},a.on("end",i),a.on("error",o),a}},30405:(e,t,s)=>{"use strict";let{tokenChars:r}=s(47325);e.exports={parse:function(e){let t=new Set,s=-1,n=-1,i=0;for(;i<e.length;i++){let o=e.charCodeAt(i);if(-1===n&&1===r[o])-1===s&&(s=i);else if(0!==i&&(32===o||9===o))-1===n&&-1!==s&&(n=i);else if(44===o){if(-1===s)throw SyntaxError(`Unexpected character at index ${i}`);-1===n&&(n=i);let r=e.slice(s,n);if(t.has(r))throw SyntaxError(`The "${r}" subprotocol is duplicated`);t.add(r),s=n=-1}else throw SyntaxError(`Unexpected character at index ${i}`)}if(-1===s||-1!==n)throw SyntaxError("Unexpected end of input");let o=e.slice(s,i);if(t.has(o))throw SyntaxError(`The "${o}" subprotocol is duplicated`);return t.add(o),t}}},47325:(e,t,s)=>{"use strict";let{isUtf8:r}=s(14300);function n(e){let t=e.length,s=0;for(;s<t;)if((128&e[s])==0)s++;else if((224&e[s])==192){if(s+1===t||(192&e[s+1])!=128||(254&e[s])==192)return!1;s+=2}else if((240&e[s])==224){if(s+2>=t||(192&e[s+1])!=128||(192&e[s+2])!=128||224===e[s]&&(224&e[s+1])==128||237===e[s]&&(224&e[s+1])==160)return!1;s+=3}else{if((248&e[s])!=240||s+3>=t||(192&e[s+1])!=128||(192&e[s+2])!=128||(192&e[s+3])!=128||240===e[s]&&(240&e[s+1])==128||244===e[s]&&e[s+1]>143||e[s]>244)return!1;s+=4}return!0}if(e.exports={isValidStatusCode:function(e){return e>=1e3&&e<=1014&&1004!==e&&1005!==e&&1006!==e||e>=3e3&&e<=4999},isValidUTF8:n,tokenChars:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0]},r)e.exports.isValidUTF8=function(e){return e.length<24?n(e):r(e)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let t=s(68645);e.exports.isValidUTF8=function(e){return e.length<32?n(e):t(e)}}catch(e){}},6616:(e,t,s)=>{"use strict";let r=s(82361),n=s(13685),{Duplex:i}=s(12781),{createHash:o}=s(6113),a=s(39900),l=s(59635),c=s(30405),d=s(48112),{GUID:h,kWebSocket:u}=s(81340),p=/^[+/0-9A-Za-z]{22}==$/;class f extends r{constructor(e,t){if(super(),null==(e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:d,...e}).port&&!e.server&&!e.noServer||null!=e.port&&(e.server||e.noServer)||e.server&&e.noServer)throw TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(null!=e.port?(this._server=n.createServer((e,t)=>{let s=n.STATUS_CODES[426];t.writeHead(426,{"Content-Length":s.length,"Content-Type":"text/plain"}),t.end(s)}),this._server.listen(e.port,e.host,e.backlog,t)):e.server&&(this._server=e.server),this._server){let e=this.emit.bind(this,"connection");this._removeListeners=function(e,t){for(let s of Object.keys(t))e.on(s,t[s]);return function(){for(let s of Object.keys(t))e.removeListener(s,t[s])}}(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(t,s,r)=>{this.handleUpgrade(t,s,r,e)}})}!0===e.perMessageDeflate&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=0}address(){if(this.options.noServer)throw Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(2===this._state){e&&this.once("close",()=>{e(Error("The server is not running"))}),process.nextTick(m,this);return}if(e&&this.once("close",e),1!==this._state){if(this._state=1,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients&&this.clients.size?this._shouldEmitClose=!0:process.nextTick(m,this);else{let e=this._server;this._removeListeners(),this._removeListeners=this._server=null,e.close(()=>{m(this)})}}}shouldHandle(e){if(this.options.path){let t=e.url.indexOf("?");if((-1!==t?e.url.slice(0,t):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,t,s,r){t.on("error",g);let n=e.headers["sec-websocket-key"],i=e.headers.upgrade,o=+e.headers["sec-websocket-version"];if("GET"!==e.method){x(this,e,t,405,"Invalid HTTP method");return}if(void 0===i||"websocket"!==i.toLowerCase()){x(this,e,t,400,"Invalid Upgrade header");return}if(void 0===n||!p.test(n)){x(this,e,t,400,"Missing or invalid Sec-WebSocket-Key header");return}if(8!==o&&13!==o){x(this,e,t,400,"Missing or invalid Sec-WebSocket-Version header");return}if(!this.shouldHandle(e)){y(t,400);return}let d=e.headers["sec-websocket-protocol"],h=new Set;if(void 0!==d)try{h=c.parse(d)}catch(s){x(this,e,t,400,"Invalid Sec-WebSocket-Protocol header");return}let u=e.headers["sec-websocket-extensions"],f={};if(this.options.perMessageDeflate&&void 0!==u){let s=new l(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let e=a.parse(u);e[l.extensionName]&&(s.accept(e[l.extensionName]),f[l.extensionName]=s)}catch(s){x(this,e,t,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let i={origin:e.headers[`${8===o?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(2===this.options.verifyClient.length){this.options.verifyClient(i,(i,o,a,l)=>{if(!i)return y(t,o||401,a,l);this.completeUpgrade(f,n,h,e,t,s,r)});return}if(!this.options.verifyClient(i))return y(t,401)}this.completeUpgrade(f,n,h,e,t,s,r)}completeUpgrade(e,t,s,r,n,i,c){if(!n.readable||!n.writable)return n.destroy();if(n[u])throw Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>0)return y(n,503);let d=o("sha1").update(t+h).digest("base64"),p=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${d}`],f=new this.options.WebSocket(null,void 0,this.options);if(s.size){let e=this.options.handleProtocols?this.options.handleProtocols(s,r):s.values().next().value;e&&(p.push(`Sec-WebSocket-Protocol: ${e}`),f._protocol=e)}if(e[l.extensionName]){let t=e[l.extensionName].params,s=a.format({[l.extensionName]:[t]});p.push(`Sec-WebSocket-Extensions: ${s}`),f._extensions=e}this.emit("headers",p,r),n.write(p.concat("\r\n").join("\r\n")),n.removeListener("error",g),f.setSocket(n,i,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(f),f.on("close",()=>{this.clients.delete(f),this._shouldEmitClose&&!this.clients.size&&process.nextTick(m,this)})),c(f,r)}}function m(e){e._state=2,e.emit("close")}function g(){this.destroy()}function y(e,t,s,r){s=s||n.STATUS_CODES[t],r={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(s),...r},e.once("finish",e.destroy),e.end(`HTTP/1.1 ${t} ${n.STATUS_CODES[t]}\r
`+Object.keys(r).map(e=>`${e}: ${r[e]}`).join("\r\n")+"\r\n\r\n"+s)}function x(e,t,s,r,n){if(e.listenerCount("wsClientError")){let r=Error(n);Error.captureStackTrace(r,x),e.emit("wsClientError",r,s,t)}else y(s,r,n)}e.exports=f},48112:(e,t,s)=>{"use strict";let r=s(82361),n=s(95687),i=s(13685),o=s(41808),a=s(24404),{randomBytes:l,createHash:c}=s(6113),{Duplex:d,Readable:h}=s(12781),{URL:u}=s(57310),p=s(59635),f=s(29573),m=s(14014),{BINARY_TYPES:g,EMPTY_BUFFER:y,GUID:x,kForOnEventAttribute:v,kListener:b,kStatusCode:_,kWebSocket:C,NOOP:w}=s(81340),{EventTarget:{addEventListener:j,removeEventListener:k}}=s(18620),{format:S,parse:N}=s(39900),{toBuffer:E}=s(67980),O=Symbol("kAborted"),T=[8,13],R=["CONNECTING","OPEN","CLOSING","CLOSED"],F=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;class L extends r{constructor(e,t,s){super(),this._binaryType=g[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=y,this._closeTimer=null,this._extensions={},this._paused=!1,this._protocol="",this._readyState=L.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,null!==e?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,void 0===t?t=[]:Array.isArray(t)||("object"==typeof t&&null!==t?(s=t,t=[]):t=[t]),function e(t,s,r,o){let a,d,h,f;let m={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:T[1],maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...o,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(t._autoPong=m.autoPong,!T.includes(m.protocolVersion))throw RangeError(`Unsupported protocol version: ${m.protocolVersion} (supported versions: ${T.join(", ")})`);if(s instanceof u)a=s;else try{a=new u(s)}catch(e){throw SyntaxError(`Invalid URL: ${s}`)}"http:"===a.protocol?a.protocol="ws:":"https:"===a.protocol&&(a.protocol="wss:"),t._url=a.href;let g="wss:"===a.protocol,y="ws+unix:"===a.protocol;if("ws:"===a.protocol||g||y?y&&!a.pathname?d="The URL's pathname is empty":a.hash&&(d="The URL contains a fragment identifier"):d='The URL\'s protocol must be one of "ws:", "wss:", "http:", "https", or "ws+unix:"',d){let e=SyntaxError(d);if(0===t._redirects)throw e;I(t,e);return}let v=g?443:80,b=l(16).toString("base64"),_=g?n.request:i.request,C=new Set;if(m.createConnection=m.createConnection||(g?P:A),m.defaultPort=m.defaultPort||v,m.port=a.port||v,m.host=a.hostname.startsWith("[")?a.hostname.slice(1,-1):a.hostname,m.headers={...m.headers,"Sec-WebSocket-Version":m.protocolVersion,"Sec-WebSocket-Key":b,Connection:"Upgrade",Upgrade:"websocket"},m.path=a.pathname+a.search,m.timeout=m.handshakeTimeout,m.perMessageDeflate&&(h=new p(!0!==m.perMessageDeflate?m.perMessageDeflate:{},!1,m.maxPayload),m.headers["Sec-WebSocket-Extensions"]=S({[p.extensionName]:h.offer()})),r.length){for(let e of r){if("string"!=typeof e||!F.test(e)||C.has(e))throw SyntaxError("An invalid or duplicated subprotocol was specified");C.add(e)}m.headers["Sec-WebSocket-Protocol"]=r.join(",")}if(m.origin&&(m.protocolVersion<13?m.headers["Sec-WebSocket-Origin"]=m.origin:m.headers.Origin=m.origin),(a.username||a.password)&&(m.auth=`${a.username}:${a.password}`),y){let e=m.path.split(":");m.socketPath=e[0],m.path=e[1]}if(m.followRedirects){if(0===t._redirects){t._originalIpc=y,t._originalSecure=g,t._originalHostOrSocketPath=y?m.socketPath:a.host;let e=o&&o.headers;if(o={...o,headers:{}},e)for(let[t,s]of Object.entries(e))o.headers[t.toLowerCase()]=s}else if(0===t.listenerCount("redirect")){let e=y?!!t._originalIpc&&m.socketPath===t._originalHostOrSocketPath:!t._originalIpc&&a.host===t._originalHostOrSocketPath;e&&(!t._originalSecure||g)||(delete m.headers.authorization,delete m.headers.cookie,e||delete m.headers.host,m.auth=void 0)}m.auth&&!o.headers.authorization&&(o.headers.authorization="Basic "+Buffer.from(m.auth).toString("base64")),f=t._req=_(m),t._redirects&&t.emit("redirect",t.url,f)}else f=t._req=_(m);m.timeout&&f.on("timeout",()=>{D(t,f,"Opening handshake has timed out")}),f.on("error",e=>{null===f||f[O]||(f=t._req=null,I(t,e))}),f.on("response",n=>{let i=n.headers.location,a=n.statusCode;if(i&&m.followRedirects&&a>=300&&a<400){let n;if(++t._redirects>m.maxRedirects){D(t,f,"Maximum redirects exceeded");return}f.abort();try{n=new u(i,s)}catch(e){I(t,SyntaxError(`Invalid URL: ${i}`));return}e(t,n,r,o)}else t.emit("unexpected-response",f,n)||D(t,f,`Unexpected server response: ${n.statusCode}`)}),f.on("upgrade",(e,s,r)=>{let n;if(t.emit("upgrade",e),t.readyState!==L.CONNECTING)return;f=t._req=null;let i=e.headers.upgrade;if(void 0===i||"websocket"!==i.toLowerCase()){D(t,s,"Invalid Upgrade header");return}let o=c("sha1").update(b+x).digest("base64");if(e.headers["sec-websocket-accept"]!==o){D(t,s,"Invalid Sec-WebSocket-Accept header");return}let a=e.headers["sec-websocket-protocol"];if(void 0!==a?C.size?C.has(a)||(n="Server sent an invalid subprotocol"):n="Server sent a subprotocol but none was requested":C.size&&(n="Server sent no subprotocol"),n){D(t,s,n);return}a&&(t._protocol=a);let l=e.headers["sec-websocket-extensions"];if(void 0!==l){let e;if(!h){D(t,s,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}try{e=N(l)}catch(e){D(t,s,"Invalid Sec-WebSocket-Extensions header");return}let r=Object.keys(e);if(1!==r.length||r[0]!==p.extensionName){D(t,s,"Server indicated an extension that was not requested");return}try{h.accept(e[p.extensionName])}catch(e){D(t,s,"Invalid Sec-WebSocket-Extensions header");return}t._extensions[p.extensionName]=h}t.setSocket(s,r,{allowSynchronousEvents:m.allowSynchronousEvents,generateMask:m.generateMask,maxPayload:m.maxPayload,skipUTF8Validation:m.skipUTF8Validation})}),m.finishRequest?m.finishRequest(f,t):f.end()}(this,e,t,s)):(this._autoPong=s.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){g.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,t,s){let r=new f({allowSynchronousEvents:s.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:s.maxPayload,skipUTF8Validation:s.skipUTF8Validation});this._sender=new m(e,this._extensions,s.generateMask),this._receiver=r,this._socket=e,r[C]=this,e[C]=this,r.on("conclude",B),r.on("drain",U),r.on("error",q),r.on("message",$),r.on("ping",W),r.on("pong",z),e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),t.length>0&&e.unshift(t),e.on("close",H),e.on("data",G),e.on("end",Y),e.on("error",Q),this._readyState=L.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=L.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[p.extensionName]&&this._extensions[p.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=L.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,t){if(this.readyState!==L.CLOSED){if(this.readyState===L.CONNECTING){D(this,this._req,"WebSocket was closed before the connection was established");return}if(this.readyState===L.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=L.CLOSING,this._sender.close(e,t,!this._isServer,e=>{!e&&(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),this._closeTimer=setTimeout(this._socket.destroy.bind(this._socket),3e4)}}pause(){this.readyState!==L.CONNECTING&&this.readyState!==L.CLOSED&&(this._paused=!0,this._socket.pause())}ping(e,t,s){if(this.readyState===L.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(s=e,e=t=void 0):"function"==typeof t&&(s=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==L.OPEN){M(this,e,s);return}void 0===t&&(t=!this._isServer),this._sender.ping(e||y,t,s)}pong(e,t,s){if(this.readyState===L.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(s=e,e=t=void 0):"function"==typeof t&&(s=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==L.OPEN){M(this,e,s);return}void 0===t&&(t=!this._isServer),this._sender.pong(e||y,t,s)}resume(){this.readyState!==L.CONNECTING&&this.readyState!==L.CLOSED&&(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,t,s){if(this.readyState===L.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof t&&(s=t,t={}),"number"==typeof e&&(e=e.toString()),this.readyState!==L.OPEN){M(this,e,s);return}let r={binary:"string"!=typeof e,mask:!this._isServer,compress:!0,fin:!0,...t};this._extensions[p.extensionName]||(r.compress=!1),this._sender.send(e||y,r,s)}terminate(){if(this.readyState!==L.CLOSED){if(this.readyState===L.CONNECTING){D(this,this._req,"WebSocket was closed before the connection was established");return}this._socket&&(this._readyState=L.CLOSING,this._socket.destroy())}}}function I(e,t){e._readyState=L.CLOSING,e.emit("error",t),e.emitClose()}function A(e){return e.path=e.socketPath,o.connect(e)}function P(e){return e.path=void 0,e.servername||""===e.servername||(e.servername=o.isIP(e.host)?"":e.host),a.connect(e)}function D(e,t,s){e._readyState=L.CLOSING;let r=Error(s);Error.captureStackTrace(r,D),t.setHeader?(t[O]=!0,t.abort(),t.socket&&!t.socket.destroyed&&t.socket.destroy(),process.nextTick(I,e,r)):(t.destroy(r),t.once("error",e.emit.bind(e,"error")),t.once("close",e.emitClose.bind(e)))}function M(e,t,s){if(t){let s=E(t).length;e._socket?e._sender._bufferedBytes+=s:e._bufferedAmount+=s}if(s){let t=Error(`WebSocket is not open: readyState ${e.readyState} (${R[e.readyState]})`);process.nextTick(s,t)}}function B(e,t){let s=this[C];s._closeFrameReceived=!0,s._closeMessage=t,s._closeCode=e,void 0!==s._socket[C]&&(s._socket.removeListener("data",G),process.nextTick(Z,s._socket),1005===e?s.close():s.close(e,t))}function U(){let e=this[C];e.isPaused||e._socket.resume()}function q(e){let t=this[C];void 0!==t._socket[C]&&(t._socket.removeListener("data",G),process.nextTick(Z,t._socket),t.close(e[_])),t.emit("error",e)}function V(){this[C].emitClose()}function $(e,t){this[C].emit("message",e,t)}function W(e){let t=this[C];t._autoPong&&t.pong(e,!this._isServer,w),t.emit("ping",e)}function z(e){this[C].emit("pong",e)}function Z(e){e.resume()}function H(){let e;let t=this[C];this.removeListener("close",H),this.removeListener("data",G),this.removeListener("end",Y),t._readyState=L.CLOSING,this._readableState.endEmitted||t._closeFrameReceived||t._receiver._writableState.errorEmitted||null===(e=t._socket.read())||t._receiver.write(e),t._receiver.end(),this[C]=void 0,clearTimeout(t._closeTimer),t._receiver._writableState.finished||t._receiver._writableState.errorEmitted?t.emitClose():(t._receiver.on("error",V),t._receiver.on("finish",V))}function G(e){this[C]._receiver.write(e)||this.pause()}function Y(){let e=this[C];e._readyState=L.CLOSING,e._receiver.end(),this.end()}function Q(){let e=this[C];this.removeListener("error",Q),this.on("error",w),e&&(e._readyState=L.CLOSING,this.destroy())}Object.defineProperty(L,"CONNECTING",{enumerable:!0,value:R.indexOf("CONNECTING")}),Object.defineProperty(L.prototype,"CONNECTING",{enumerable:!0,value:R.indexOf("CONNECTING")}),Object.defineProperty(L,"OPEN",{enumerable:!0,value:R.indexOf("OPEN")}),Object.defineProperty(L.prototype,"OPEN",{enumerable:!0,value:R.indexOf("OPEN")}),Object.defineProperty(L,"CLOSING",{enumerable:!0,value:R.indexOf("CLOSING")}),Object.defineProperty(L.prototype,"CLOSING",{enumerable:!0,value:R.indexOf("CLOSING")}),Object.defineProperty(L,"CLOSED",{enumerable:!0,value:R.indexOf("CLOSED")}),Object.defineProperty(L.prototype,"CLOSED",{enumerable:!0,value:R.indexOf("CLOSED")}),["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(e=>{Object.defineProperty(L.prototype,e,{enumerable:!0})}),["open","error","close","message"].forEach(e=>{Object.defineProperty(L.prototype,`on${e}`,{enumerable:!0,get(){for(let t of this.listeners(e))if(t[v])return t[b];return null},set(t){for(let t of this.listeners(e))if(t[v]){this.removeListener(e,t);break}"function"==typeof t&&this.addEventListener(e,t,{[v]:!0})}})}),L.prototype.addEventListener=j,L.prototype.removeEventListener=k,e.exports=L},70814:e=>{"use strict";e.exports=(e,t=process.argv)=>{let s=e.startsWith("-")?"":1===e.length?"-":"--",r=t.indexOf(s+e),n=t.indexOf("--");return -1!==r&&(-1===n||r<n)}},53148:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},58476:e=>{function t(e,t,s,r){return Math.round(e/s)+" "+r+(t>=1.5*s?"s":"")}e.exports=function(e,s){s=s||{};var r,n,i=typeof e;if("string"===i&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var s=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*s;case"weeks":case"week":case"w":return 6048e5*s;case"days":case"day":case"d":return 864e5*s;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*s;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*s;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*s;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return s;default:return}}}}(e);if("number"===i&&isFinite(e))return s.long?(r=Math.abs(e))>=864e5?t(e,r,864e5,"day"):r>=36e5?t(e,r,36e5,"hour"):r>=6e4?t(e,r,6e4,"minute"):r>=1e3?t(e,r,1e3,"second"):e+" ms":(n=Math.abs(e))>=864e5?Math.round(e/864e5)+"d":n>=36e5?Math.round(e/36e5)+"h":n>=6e4?Math.round(e/6e4)+"m":n>=1e3?Math.round(e/1e3)+"s":e+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},29408:()=>{},51938:(e,t,s)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let s="color: "+this.color;t.splice(1,0,s,"color: inherit");let r=0,n=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(r++,"%c"===e&&(n=r))}),t.splice(n,0,s)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=s(24889)(t);let{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},24889:(e,t,s)=>{e.exports=function(e){function t(e){let s,n,i;let o=null;function a(...e){if(!a.enabled)return;let r=Number(new Date),n=r-(s||r);a.diff=n,a.prev=s,a.curr=r,s=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(s,r)=>{if("%%"===s)return"%";i++;let n=t.formatters[r];if("function"==typeof n){let t=e[i];s=n.call(a,t),e.splice(i,1),i--}return s}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(n!==t.namespaces&&(n=t.namespaces,i=t.enabled(e)),i),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,s){let r=t(this.namespace+(void 0===s?":":s)+e);return r.log=this.log,r}function n(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(n),...t.skips.map(n).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let s;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let r=("string"==typeof e?e:"").split(/[\s,]+/),n=r.length;for(s=0;s<n;s++)r[s]&&("-"===(e=r[s].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let s,r;if("*"===e[e.length-1])return!0;for(s=0,r=t.skips.length;s<r;s++)if(t.skips[s].test(e))return!1;for(s=0,r=t.names.length;s<r;s++)if(t.names[s].test(e))return!0;return!1},t.humanize=s(58476),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(s=>{t[s]=e[s]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t)|0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}},94974:(e,t,s)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=s(51938):e.exports=s(50575)},50575:(e,t,s)=>{let r=s(76224),n=s(73837);t.init=function(e){e.inspectOpts={};let s=Object.keys(t.inspectOpts);for(let r=0;r<s.length;r++)e.inspectOpts[s[r]]=t.inspectOpts[s[r]]},t.log=function(...e){return process.stderr.write(n.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(s){let{namespace:r,useColors:n}=this;if(n){let t=this.color,n="\x1b[3"+(t<8?t:"8;5;"+t),i=`  ${n};1m${r} \u001B[0m`;s[0]=i+s[0].split("\n").join("\n"+i),s.push(n+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else s[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+r+" "+s[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:r.isatty(process.stderr.fd)},t.destroy=n.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=s(60125);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let s=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),r=process.env[t];return r=!!/^(yes|on|true|enabled)$/i.test(r)||!/^(no|off|false|disabled)$/i.test(r)&&("null"===r?null:Number(r)),e[s]=r,e},{}),e.exports=s(24889)(t);let{formatters:i}=e.exports;i.o=function(e){return this.inspectOpts.colors=this.useColors,n.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},i.O=function(e){return this.inspectOpts.colors=this.useColors,n.inspect(e,this.inspectOpts)}},71393:(e,t,s)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let s="color: "+this.color;t.splice(1,0,s,"color: inherit");let r=0,n=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(r++,"%c"===e&&(n=r))}),t.splice(n,0,s)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=s(66793)(t);let{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},66793:(e,t,s)=>{e.exports=function(e){function t(e){let s,n,i;let o=null;function a(...e){if(!a.enabled)return;let r=Number(new Date),n=r-(s||r);a.diff=n,a.prev=s,a.curr=r,s=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(s,r)=>{if("%%"===s)return"%";i++;let n=t.formatters[r];if("function"==typeof n){let t=e[i];s=n.call(a,t),e.splice(i,1),i--}return s}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(n!==t.namespaces&&(n=t.namespaces,i=t.enabled(e)),i),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,s){let r=t(this.namespace+(void 0===s?":":s)+e);return r.log=this.log,r}function n(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(n),...t.skips.map(n).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let s;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let r=("string"==typeof e?e:"").split(/[\s,]+/),n=r.length;for(s=0;s<n;s++)r[s]&&("-"===(e=r[s].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let s,r;if("*"===e[e.length-1])return!0;for(s=0,r=t.skips.length;s<r;s++)if(t.skips[s].test(e))return!1;for(s=0,r=t.names.length;s<r;s++)if(t.names[s].test(e))return!0;return!1},t.humanize=s(58476),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(s=>{t[s]=e[s]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t)|0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}},32428:(e,t,s)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=s(71393):e.exports=s(5919)},5919:(e,t,s)=>{let r=s(76224),n=s(73837);t.init=function(e){e.inspectOpts={};let s=Object.keys(t.inspectOpts);for(let r=0;r<s.length;r++)e.inspectOpts[s[r]]=t.inspectOpts[s[r]]},t.log=function(...e){return process.stderr.write(n.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(s){let{namespace:r,useColors:n}=this;if(n){let t=this.color,n="\x1b[3"+(t<8?t:"8;5;"+t),i=`  ${n};1m${r} \u001B[0m`;s[0]=i+s[0].split("\n").join("\n"+i),s.push(n+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else s[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+r+" "+s[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:r.isatty(process.stderr.fd)},t.destroy=n.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=s(60125);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let s=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),r=process.env[t];return r=!!/^(yes|on|true|enabled)$/i.test(r)||!/^(no|off|false|disabled)$/i.test(r)&&("null"===r?null:Number(r)),e[s]=r,e},{}),e.exports=s(66793)(t);let{formatters:i}=e.exports;i.o=function(e){return this.inspectOpts.colors=this.useColors,n.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},i.O=function(e){return this.inspectOpts.colors=this.useColors,n.inspect(e,this.inspectOpts)}},18630:(e,t,s)=>{s(29408);var r=s(3729),n=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(r);function i(e,t){for(var s=0;s<t.length;s++){var r=t[s];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var o="undefined"!=typeof process&&process.env&&!0,a=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,s=t.name,r=void 0===s?"stylesheet":s,n=t.optimizeForSpeed,i=void 0===n?o:n;c(a(r),"`name` must be a string"),this._name=r,this._deletedRulePlaceholder="#"+r+"-deleted-rule____{}",c("boolean"==typeof i,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=i,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var t,s=e.prototype;return s.setOptimizeForSpeed=function(e){c("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),c(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},s.isOptimizeForSpeed=function(){return this._optimizeForSpeed},s.inject=function(){var e=this;c(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(t,s){return"number"==typeof s?e._serverSheet.cssRules[s]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),s},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},s.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},s.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},s.insertRule=function(e,t){return c(a(e),"`insertRule` accepts only strings"),"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++},s.replaceRule=function(e,t){this._optimizeForSpeed;var s=this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!s.cssRules[e])return e;s.deleteRule(e);try{s.insertRule(t,e)}catch(r){o||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),s.insertRule(this._deletedRulePlaceholder,e)}return e},s.deleteRule=function(e){this._serverSheet.deleteRule(e)},s.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},s.cssRules=function(){return this._serverSheet.cssRules},s.makeStyleTag=function(e,t,s){t&&c(a(t),"makeStyleTag accepts only strings as second parameter");var r=document.createElement("style");this._nonce&&r.setAttribute("nonce",this._nonce),r.type="text/css",r.setAttribute("data-"+e,""),t&&r.appendChild(document.createTextNode(t));var n=document.head||document.getElementsByTagName("head")[0];return s?n.insertBefore(r,s):n.appendChild(r),r},i(e.prototype,[{key:"length",get:function(){return this._rulesCount}}]),t&&i(e,t),e}();function c(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,s=e.length;s;)t=33*t^e.charCodeAt(--s);return t>>>0},h={};function u(e,t){if(!t)return"jsx-"+e;var s=String(t),r=e+s;return h[r]||(h[r]="jsx-"+d(e+"-"+s)),h[r]}function p(e,t){var s=e+(t=t.replace(/\/style/gi,"\\/style"));return h[s]||(h[s]=t.replace(/__jsx-style-dynamic-selector/g,e)),h[s]}var f=r.createContext(null);f.displayName="StyleSheetContext",n.default.useInsertionEffect||n.default.useLayoutEffect;var m=void 0;function g(e){var t=m||r.useContext(f);return t&&t.add(e),null}g.dynamic=function(e){return e.map(function(e){return u(e[0],e[1])}).join(" ")},t.style=g},47983:(e,t,s)=>{e.exports=s(18630).style},60125:(e,t,s)=>{"use strict";let r;let n=s(22037),i=s(76224),o=s(70814),{env:a}=process;function l(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function c(e,t){if(0===r)return 0;if(o("color=16m")||o("color=full")||o("color=truecolor"))return 3;if(o("color=256"))return 2;if(e&&!t&&void 0===r)return 0;let s=r||0;if("dumb"===a.TERM)return s;if("win32"===process.platform){let e=n.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in a)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in a)||"codeship"===a.CI_NAME?1:s;if("TEAMCITY_VERSION"in a)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(a.TEAMCITY_VERSION)?1:0;if("truecolor"===a.COLORTERM)return 3;if("TERM_PROGRAM"in a){let e=parseInt((a.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(a.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(a.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(a.TERM)||"COLORTERM"in a?1:s}o("no-color")||o("no-colors")||o("color=false")||o("color=never")?r=0:(o("color")||o("colors")||o("color=true")||o("color=always"))&&(r=1),"FORCE_COLOR"in a&&(r="true"===a.FORCE_COLOR?1:"false"===a.FORCE_COLOR?0:0===a.FORCE_COLOR.length?1:Math.min(parseInt(a.FORCE_COLOR,10),3)),e.exports={supportsColor:function(e){return l(c(e,e&&e.isTTY))},stdout:l(c(!0,i.isatty(1))),stderr:l(c(!0,i.isatty(2)))}},75574:(e,t,s)=>{/**
 * Wrapper for built-in http.js to emulate the browser XMLHttpRequest object.
 *
 * This can be used with JS designed for browsers to improve reuse of code and
 * allow the use of existing libraries.
 *
 * Usage: include("XMLHttpRequest.js") and use XMLHttpRequest per W3C specs.
 *
 * <AUTHOR> DeFelippi <<EMAIL>>
 * @contributor David Ellis <<EMAIL>>
 * @license MIT
 */var r=s(57147),n=s(57310),i=s(32081).spawn;function o(e){"use strict";e=e||{};var t,o,a=this,l=s(13685),c=s(95687),d={},h=!1,u={"User-Agent":"node-XMLHttpRequest",Accept:"*/*"},p=Object.assign({},u),f=["accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","content-transfer-encoding","cookie","cookie2","date","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","via"],m=["TRACE","TRACK","CONNECT"],g=!1,y=!1,x=!1,v={};this.UNSENT=0,this.OPENED=1,this.HEADERS_RECEIVED=2,this.LOADING=3,this.DONE=4,this.readyState=this.UNSENT,this.onreadystatechange=null,this.responseText="",this.responseXML="",this.response=Buffer.alloc(0),this.status=null,this.statusText=null,this.open=function(e,t,s,r,n){if(this.abort(),y=!1,x=!1,!(e&&-1===m.indexOf(e)))throw Error("SecurityError: Request method not allowed");d={method:e,url:t.toString(),async:"boolean"!=typeof s||s,user:r||null,password:n||null},b(this.OPENED)},this.setDisableHeaderCheck=function(e){h=e},this.setRequestHeader=function(e,t){if(this.readyState!=this.OPENED)throw Error("INVALID_STATE_ERR: setRequestHeader can only be called when state is OPEN");if(!h&&(!e||-1!==f.indexOf(e.toLowerCase())))return console.warn('Refused to set unsafe header "'+e+'"'),!1;if(g)throw Error("INVALID_STATE_ERR: send flag is true");return p[e]=t,!0},this.getResponseHeader=function(e){return"string"==typeof e&&this.readyState>this.OPENED&&o.headers[e.toLowerCase()]&&!y?o.headers[e.toLowerCase()]:null},this.getAllResponseHeaders=function(){if(this.readyState<this.HEADERS_RECEIVED||y)return"";var e="";for(var t in o.headers)"set-cookie"!==t&&"set-cookie2"!==t&&(e+=t+": "+o.headers[t]+"\r\n");return e.substr(0,e.length-2)},this.getRequestHeader=function(e){return"string"==typeof e&&p[e]?p[e]:""},this.send=function(s){if(this.readyState!=this.OPENED)throw Error("INVALID_STATE_ERR: connection must be opened before send() is called");if(g)throw Error("INVALID_STATE_ERR: send has already been called");var h,u=!1,f=!1,m=n.parse(d.url);switch(m.protocol){case"https:":u=!0;case"http:":h=m.hostname;break;case"file:":f=!0;break;case void 0:case"":h="localhost";break;default:throw Error("Protocol not supported.")}if(f){if("GET"!==d.method)throw Error("XMLHttpRequest: Only GET method is supported");if(d.async)r.readFile(unescape(m.pathname),function(e,t){e?a.handleError(e,e.errno||-1):(a.status=200,a.responseText=t.toString("utf8"),a.response=t,b(a.DONE))});else try{this.response=r.readFileSync(unescape(m.pathname)),this.responseText=this.response.toString("utf8"),this.status=200,b(a.DONE)}catch(e){this.handleError(e,e.errno||-1)}return}var x=m.port||(u?443:80),v=m.pathname+(m.search?m.search:"");if(p.Host=h,u&&443===x||80===x||(p.Host+=":"+m.port),d.user){void 0===d.password&&(d.password="");var _=new Buffer(d.user+":"+d.password);p.Authorization="Basic "+_.toString("base64")}"GET"===d.method||"HEAD"===d.method?s=null:s?(p["Content-Length"]=Buffer.isBuffer(s)?s.length:Buffer.byteLength(s),Object.keys(p).some(function(e){return"content-type"===e.toLowerCase()})||(p["Content-Type"]="text/plain;charset=UTF-8")):"POST"===d.method&&(p["Content-Length"]=0);var C=e.agent||!1,w={host:h,port:x,path:v,method:d.method,headers:p,agent:C};if(u&&(w.pfx=e.pfx,w.key=e.key,w.passphrase=e.passphrase,w.cert=e.cert,w.ca=e.ca,w.ciphers=e.ciphers,w.rejectUnauthorized=!1!==e.rejectUnauthorized),y=!1,d.async){var j=u?c.request:l.request;g=!0,a.dispatchEvent("readystatechange");var k=function(s){if(302===(o=s).statusCode||303===o.statusCode||307===o.statusCode){d.url=o.headers.location;var r=n.parse(d.url);h=r.hostname;var i={hostname:r.hostname,port:r.port,path:r.path,method:303===o.statusCode?"GET":d.method,headers:p};u&&(i.pfx=e.pfx,i.key=e.key,i.passphrase=e.passphrase,i.cert=e.cert,i.ca=e.ca,i.ciphers=e.ciphers,i.rejectUnauthorized=!1!==e.rejectUnauthorized),(t=j(i,k).on("error",S)).end();return}b(a.HEADERS_RECEIVED),a.status=o.statusCode,o.on("data",function(e){if(e){var t=Buffer.from(e);a.response=Buffer.concat([a.response,t])}g&&b(a.LOADING)}),o.on("end",function(){g&&(g=!1,b(a.DONE),a.responseText=a.response.toString("utf8"))}),o.on("error",function(e){a.handleError(e)})},S=function(e){if(t.reusedSocket&&"ECONNRESET"===e.code)return j(w,k).on("error",S);a.handleError(e)};t=j(w,k).on("error",S),e.autoUnref&&t.on("socket",e=>{e.unref()}),s&&t.write(s),t.end(),a.dispatchEvent("loadstart")}else{var N=".node-xmlhttprequest-content-"+process.pid,E=".node-xmlhttprequest-sync-"+process.pid;r.writeFileSync(E,"","utf8");for(var O="var http = require('http'), https = require('https'), fs = require('fs');var doRequest = http"+(u?"s":"")+".request;var options = "+JSON.stringify(w)+";var responseText = '';var responseData = Buffer.alloc(0);var req = doRequest(options, function(response) {response.on('data', function(chunk) {  var data = Buffer.from(chunk);  responseText += data.toString('utf8');  responseData = Buffer.concat([responseData, data]);});response.on('end', function() {fs.writeFileSync('"+N+"', JSON.stringify({err: null, data: {statusCode: response.statusCode, headers: response.headers, text: responseText, data: responseData.toString('base64')}}), 'utf8');fs.unlinkSync('"+E+"');});response.on('error', function(error) {fs.writeFileSync('"+N+"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');fs.unlinkSync('"+E+"');});}).on('error', function(error) {fs.writeFileSync('"+N+"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');fs.unlinkSync('"+E+"');});"+(s?"req.write('"+JSON.stringify(s).slice(1,-1).replace(/'/g,"\\'")+"');":"")+"req.end();",T=i(process.argv[0],["-e",O]);r.existsSync(E););if(a.responseText=r.readFileSync(N,"utf8"),T.stdin.end(),r.unlinkSync(N),a.responseText.match(/^NODE-XMLHTTPREQUEST-ERROR:/)){var R=JSON.parse(a.responseText.replace(/^NODE-XMLHTTPREQUEST-ERROR:/,""));a.handleError(R,503)}else{a.status=a.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:([0-9]*),.*/,"$1");var F=JSON.parse(a.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:[0-9]*,(.*)/,"$1"));o={statusCode:a.status,headers:F.data.headers},a.responseText=F.data.text,a.response=Buffer.from(F.data.data,"base64"),b(a.DONE,!0)}}},this.handleError=function(e,t){this.status=t||0,this.statusText=e,this.responseText=e.stack,y=!0,b(this.DONE)},this.abort=function(){t&&(t.abort(),t=null),p=Object.assign({},u),this.responseText="",this.responseXML="",this.response=Buffer.alloc(0),y=x=!0,this.readyState!==this.UNSENT&&(this.readyState!==this.OPENED||g)&&this.readyState!==this.DONE&&(g=!1,b(this.DONE)),this.readyState=this.UNSENT},this.addEventListener=function(e,t){e in v||(v[e]=[]),v[e].push(t)},this.removeEventListener=function(e,t){e in v&&(v[e]=v[e].filter(function(e){return e!==t}))},this.dispatchEvent=function(e){if("function"==typeof a["on"+e]&&(this.readyState===this.DONE&&d.async?setTimeout(function(){a["on"+e]()},0):a["on"+e]()),e in v)for(let t=0,s=v[e].length;t<s;t++)this.readyState===this.DONE?setTimeout(function(){v[e][t].call(a)},0):v[e][t].call(a)};var b=function(e){if(a.readyState!==e&&(a.readyState!==a.UNSENT||!x)&&(a.readyState=e,(d.async||a.readyState<a.OPENED||a.readyState===a.DONE)&&a.dispatchEvent("readystatechange"),a.readyState===a.DONE)){let e;e=x?"abort":y?"error":"load",a.dispatchEvent(e),a.dispatchEvent("loadend")}}}e.exports=o,o.XMLHttpRequest=o},21305:(e,t,s)=>{"use strict";let r,n;s.r(t),s.d(t,{default:()=>rj});var i,o={};s.r(o),s.d(o,{Decoder:()=>s9,Encoder:()=>s4,PacketType:()=>i,protocol:()=>s3});var a=s(95344),l=s(3729),c=s.n(l),d=s(23673),h=s(5094),u=s(11453);let p=l.forwardRef(({className:e,...t},s)=>a.jsx("textarea",{className:(0,u.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...t}));p.displayName="Textarea";var f=s(81202),m=s(2633),g=s(85222),y=s(77411),x=s(31405),v=s(98462),b=s(3975),_=s(44155),C=s(1106),w=s(27386),j=s(99048),k=s(37574),S=s(31179),N=s(62409),E=s(32751),O=s(2256),T=s(33183),R=s(16069);function F(e){let t=l.useRef({value:e,previous:e});return l.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var L=s(87298),I=s(45904),A=s(71210),P=[" ","Enter","ArrowUp","ArrowDown"],D=[" ","Enter"],M="Select",[B,U,q]=(0,y.B)(M),[V,$]=(0,v.b)(M,[q,k.D7]),W=(0,k.D7)(),[z,Z]=V(M),[H,G]=V(M),Y=e=>{let{__scopeSelect:t,children:s,open:r,defaultOpen:n,onOpenChange:i,value:o,defaultValue:c,onValueChange:d,dir:h,name:u,autoComplete:p,disabled:f,required:m,form:g}=e,y=W(t),[x,v]=l.useState(null),[_,C]=l.useState(null),[w,S]=l.useState(!1),N=(0,b.gm)(h),[E,O]=(0,T.T)({prop:r,defaultProp:n??!1,onChange:i,caller:M}),[R,F]=(0,T.T)({prop:o,defaultProp:c,onChange:d,caller:M}),L=l.useRef(null),I=!x||g||!!x.closest("form"),[A,P]=l.useState(new Set),D=Array.from(A).map(e=>e.props.value).join(";");return(0,a.jsx)(k.fC,{...y,children:(0,a.jsxs)(z,{required:m,scope:t,trigger:x,onTriggerChange:v,valueNode:_,onValueNodeChange:C,valueNodeHasChildren:w,onValueNodeHasChildrenChange:S,contentId:(0,j.M)(),value:R,onValueChange:F,open:E,onOpenChange:O,dir:N,triggerPointerDownPosRef:L,disabled:f,children:[(0,a.jsx)(B.Provider,{scope:t,children:(0,a.jsx)(H,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(e=>{P(t=>new Set(t).add(e))},[]),onNativeOptionRemove:l.useCallback(e=>{P(t=>{let s=new Set(t);return s.delete(e),s})},[]),children:s})}),I?(0,a.jsxs)(eI,{"aria-hidden":!0,required:m,tabIndex:-1,name:u,autoComplete:p,value:R,onChange:e=>F(e.target.value),disabled:f,form:g,children:[void 0===R?(0,a.jsx)("option",{value:""}):null,Array.from(A)]},D):null]})})};Y.displayName=M;var Q="SelectTrigger",J=l.forwardRef((e,t)=>{let{__scopeSelect:s,disabled:r=!1,...n}=e,i=W(s),o=Z(Q,s),c=o.disabled||r,d=(0,x.e)(t,o.onTriggerChange),h=U(s),u=l.useRef("touch"),[p,f,m]=eP(e=>{let t=h().filter(e=>!e.disabled),s=t.find(e=>e.value===o.value),r=eD(t,e,s);void 0!==r&&o.onValueChange(r.value)}),y=e=>{c||(o.onOpenChange(!0),m()),e&&(o.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,a.jsx)(k.ee,{asChild:!0,...i,children:(0,a.jsx)(N.WV.button,{type:"button",role:"combobox","aria-controls":o.contentId,"aria-expanded":o.open,"aria-required":o.required,"aria-autocomplete":"none",dir:o.dir,"data-state":o.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":eA(o.value)?"":void 0,...n,ref:d,onClick:(0,g.M)(n.onClick,e=>{e.currentTarget.focus(),"mouse"!==u.current&&y(e)}),onPointerDown:(0,g.M)(n.onPointerDown,e=>{u.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,g.M)(n.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||f(e.key),(!t||" "!==e.key)&&P.includes(e.key)&&(y(),e.preventDefault())})})})});J.displayName=Q;var K="SelectValue",X=l.forwardRef((e,t)=>{let{__scopeSelect:s,className:r,style:n,children:i,placeholder:o="",...l}=e,c=Z(K,s),{onValueNodeHasChildrenChange:d}=c,h=void 0!==i,u=(0,x.e)(t,c.onValueNodeChange);return(0,R.b)(()=>{d(h)},[d,h]),(0,a.jsx)(N.WV.span,{...l,ref:u,style:{pointerEvents:"none"},children:eA(c.value)?(0,a.jsx)(a.Fragment,{children:o}):i})});X.displayName=K;var ee=l.forwardRef((e,t)=>{let{__scopeSelect:s,children:r,...n}=e;return(0,a.jsx)(N.WV.span,{"aria-hidden":!0,...n,ref:t,children:r||"▼"})});ee.displayName="SelectIcon";var et=e=>(0,a.jsx)(S.h,{asChild:!0,...e});et.displayName="SelectPortal";var es="SelectContent",er=l.forwardRef((e,t)=>{let s=Z(es,e.__scopeSelect),[r,n]=l.useState();return((0,R.b)(()=>{n(new DocumentFragment)},[]),s.open)?(0,a.jsx)(ea,{...e,ref:t}):r?f.createPortal((0,a.jsx)(en,{scope:e.__scopeSelect,children:(0,a.jsx)(B.Slot,{scope:e.__scopeSelect,children:(0,a.jsx)("div",{children:e.children})})}),r):null});er.displayName=es;var[en,ei]=V(es),eo=(0,E.Z8)("SelectContent.RemoveScroll"),ea=l.forwardRef((e,t)=>{let{__scopeSelect:s,position:r="item-aligned",onCloseAutoFocus:n,onEscapeKeyDown:i,onPointerDownOutside:o,side:c,sideOffset:d,align:h,alignOffset:u,arrowPadding:p,collisionBoundary:f,collisionPadding:m,sticky:y,hideWhenDetached:v,avoidCollisions:b,...j}=e,k=Z(es,s),[S,N]=l.useState(null),[E,O]=l.useState(null),T=(0,x.e)(t,e=>N(e)),[R,F]=l.useState(null),[L,P]=l.useState(null),D=U(s),[M,B]=l.useState(!1),q=l.useRef(!1);l.useEffect(()=>{if(S)return(0,I.Ry)(S)},[S]),(0,C.EW)();let V=l.useCallback(e=>{let[t,...s]=D().map(e=>e.ref.current),[r]=s.slice(-1),n=document.activeElement;for(let s of e)if(s===n||(s?.scrollIntoView({block:"nearest"}),s===t&&E&&(E.scrollTop=0),s===r&&E&&(E.scrollTop=E.scrollHeight),s?.focus(),document.activeElement!==n))return},[D,E]),$=l.useCallback(()=>V([R,S]),[V,R,S]);l.useEffect(()=>{M&&$()},[M,$]);let{onOpenChange:W,triggerPointerDownPosRef:z}=k;l.useEffect(()=>{if(S){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(z.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(z.current?.y??0))}},s=s=>{e.x<=10&&e.y<=10?s.preventDefault():S.contains(s.target)||W(!1),document.removeEventListener("pointermove",t),z.current=null};return null!==z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",s,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",s,{capture:!0})}}},[S,W,z]),l.useEffect(()=>{let e=()=>W(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[W]);let[H,G]=eP(e=>{let t=D().filter(e=>!e.disabled),s=t.find(e=>e.ref.current===document.activeElement),r=eD(t,e,s);r&&setTimeout(()=>r.ref.current.focus())}),Y=l.useCallback((e,t,s)=>{let r=!q.current&&!s;(void 0!==k.value&&k.value===t||r)&&(F(e),r&&(q.current=!0))},[k.value]),Q=l.useCallback(()=>S?.focus(),[S]),J=l.useCallback((e,t,s)=>{let r=!q.current&&!s;(void 0!==k.value&&k.value===t||r)&&P(e)},[k.value]),K="popper"===r?ec:el,X=K===ec?{side:c,sideOffset:d,align:h,alignOffset:u,arrowPadding:p,collisionBoundary:f,collisionPadding:m,sticky:y,hideWhenDetached:v,avoidCollisions:b}:{};return(0,a.jsx)(en,{scope:s,content:S,viewport:E,onViewportChange:O,itemRefCallback:Y,selectedItem:R,onItemLeave:Q,itemTextRefCallback:J,focusSelectedItem:$,selectedItemText:L,position:r,isPositioned:M,searchRef:H,children:(0,a.jsx)(A.Z,{as:eo,allowPinchZoom:!0,children:(0,a.jsx)(w.M,{asChild:!0,trapped:k.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,g.M)(n,e=>{k.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,a.jsx)(_.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:o,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>k.onOpenChange(!1),children:(0,a.jsx)(K,{role:"listbox",id:k.contentId,"data-state":k.open?"open":"closed",dir:k.dir,onContextMenu:e=>e.preventDefault(),...j,...X,onPlaced:()=>B(!0),ref:T,style:{display:"flex",flexDirection:"column",outline:"none",...j.style},onKeyDown:(0,g.M)(j.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||G(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=D().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let s=e.target,r=t.indexOf(s);t=t.slice(r+1)}setTimeout(()=>V(t)),e.preventDefault()}})})})})})})});ea.displayName="SelectContentImpl";var el=l.forwardRef((e,t)=>{let{__scopeSelect:s,onPlaced:r,...n}=e,i=Z(es,s),o=ei(es,s),[c,d]=l.useState(null),[h,u]=l.useState(null),p=(0,x.e)(t,e=>u(e)),f=U(s),g=l.useRef(!1),y=l.useRef(!0),{viewport:v,selectedItem:b,selectedItemText:_,focusSelectedItem:C}=o,w=l.useCallback(()=>{if(i.trigger&&i.valueNode&&c&&h&&v&&b&&_){let e=i.trigger.getBoundingClientRect(),t=h.getBoundingClientRect(),s=i.valueNode.getBoundingClientRect(),n=_.getBoundingClientRect();if("rtl"!==i.dir){let r=n.left-t.left,i=s.left-r,o=e.left-i,a=e.width+o,l=Math.max(a,t.width),d=window.innerWidth-10,h=(0,m.u)(i,[10,Math.max(10,d-l)]);c.style.minWidth=a+"px",c.style.left=h+"px"}else{let r=t.right-n.right,i=window.innerWidth-s.right-r,o=window.innerWidth-e.right-i,a=e.width+o,l=Math.max(a,t.width),d=window.innerWidth-10,h=(0,m.u)(i,[10,Math.max(10,d-l)]);c.style.minWidth=a+"px",c.style.right=h+"px"}let o=f(),a=window.innerHeight-20,l=v.scrollHeight,d=window.getComputedStyle(h),u=parseInt(d.borderTopWidth,10),p=parseInt(d.paddingTop,10),y=parseInt(d.borderBottomWidth,10),x=u+p+l+parseInt(d.paddingBottom,10)+y,C=Math.min(5*b.offsetHeight,x),w=window.getComputedStyle(v),j=parseInt(w.paddingTop,10),k=parseInt(w.paddingBottom,10),S=e.top+e.height/2-10,N=b.offsetHeight/2,E=u+p+(b.offsetTop+N);if(E<=S){let e=o.length>0&&b===o[o.length-1].ref.current;c.style.bottom="0px";let t=h.clientHeight-v.offsetTop-v.offsetHeight;c.style.height=E+Math.max(a-S,N+(e?k:0)+t+y)+"px"}else{let e=o.length>0&&b===o[0].ref.current;c.style.top="0px";let t=Math.max(S,u+v.offsetTop+(e?j:0)+N);c.style.height=t+(x-E)+"px",v.scrollTop=E-S+v.offsetTop}c.style.margin="10px 0",c.style.minHeight=C+"px",c.style.maxHeight=a+"px",r?.(),requestAnimationFrame(()=>g.current=!0)}},[f,i.trigger,i.valueNode,c,h,v,b,_,i.dir,r]);(0,R.b)(()=>w(),[w]);let[j,k]=l.useState();(0,R.b)(()=>{h&&k(window.getComputedStyle(h).zIndex)},[h]);let S=l.useCallback(e=>{e&&!0===y.current&&(w(),C?.(),y.current=!1)},[w,C]);return(0,a.jsx)(ed,{scope:s,contentWrapper:c,shouldExpandOnScrollRef:g,onScrollButtonChange:S,children:(0,a.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:j},children:(0,a.jsx)(N.WV.div,{...n,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...n.style}})})})});el.displayName="SelectItemAlignedPosition";var ec=l.forwardRef((e,t)=>{let{__scopeSelect:s,align:r="start",collisionPadding:n=10,...i}=e,o=W(s);return(0,a.jsx)(k.VY,{...o,...i,ref:t,align:r,collisionPadding:n,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ec.displayName="SelectPopperPosition";var[ed,eh]=V(es,{}),eu="SelectViewport",ep=l.forwardRef((e,t)=>{let{__scopeSelect:s,nonce:r,...n}=e,i=ei(eu,s),o=eh(eu,s),c=(0,x.e)(t,i.onViewportChange),d=l.useRef(0);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,a.jsx)(B.Slot,{scope:s,children:(0,a.jsx)(N.WV.div,{"data-radix-select-viewport":"",role:"presentation",...n,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...n.style},onScroll:(0,g.M)(n.onScroll,e=>{let t=e.currentTarget,{contentWrapper:s,shouldExpandOnScrollRef:r}=o;if(r?.current&&s){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,n=Math.max(parseFloat(s.style.minHeight),parseFloat(s.style.height));if(n<r){let i=n+e,o=Math.min(r,i),a=i-o;s.style.height=o+"px","0px"===s.style.bottom&&(t.scrollTop=a>0?a:0,s.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});ep.displayName=eu;var ef="SelectGroup",[em,eg]=V(ef);l.forwardRef((e,t)=>{let{__scopeSelect:s,...r}=e,n=(0,j.M)();return(0,a.jsx)(em,{scope:s,id:n,children:(0,a.jsx)(N.WV.div,{role:"group","aria-labelledby":n,...r,ref:t})})}).displayName=ef;var ey="SelectLabel",ex=l.forwardRef((e,t)=>{let{__scopeSelect:s,...r}=e,n=eg(ey,s);return(0,a.jsx)(N.WV.div,{id:n.id,...r,ref:t})});ex.displayName=ey;var ev="SelectItem",[eb,e_]=V(ev),eC=l.forwardRef((e,t)=>{let{__scopeSelect:s,value:r,disabled:n=!1,textValue:i,...o}=e,c=Z(ev,s),d=ei(ev,s),h=c.value===r,[u,p]=l.useState(i??""),[f,m]=l.useState(!1),y=(0,x.e)(t,e=>d.itemRefCallback?.(e,r,n)),v=(0,j.M)(),b=l.useRef("touch"),_=()=>{n||(c.onValueChange(r),c.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,a.jsx)(eb,{scope:s,value:r,disabled:n,textId:v,isSelected:h,onItemTextChange:l.useCallback(e=>{p(t=>t||(e?.textContent??"").trim())},[]),children:(0,a.jsx)(B.ItemSlot,{scope:s,value:r,disabled:n,textValue:u,children:(0,a.jsx)(N.WV.div,{role:"option","aria-labelledby":v,"data-highlighted":f?"":void 0,"aria-selected":h&&f,"data-state":h?"checked":"unchecked","aria-disabled":n||void 0,"data-disabled":n?"":void 0,tabIndex:n?void 0:-1,...o,ref:y,onFocus:(0,g.M)(o.onFocus,()=>m(!0)),onBlur:(0,g.M)(o.onBlur,()=>m(!1)),onClick:(0,g.M)(o.onClick,()=>{"mouse"!==b.current&&_()}),onPointerUp:(0,g.M)(o.onPointerUp,()=>{"mouse"===b.current&&_()}),onPointerDown:(0,g.M)(o.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,g.M)(o.onPointerMove,e=>{b.current=e.pointerType,n?d.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,g.M)(o.onPointerLeave,e=>{e.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:(0,g.M)(o.onKeyDown,e=>{d.searchRef?.current!==""&&" "===e.key||(D.includes(e.key)&&_()," "===e.key&&e.preventDefault())})})})})});eC.displayName=ev;var ew="SelectItemText",ej=l.forwardRef((e,t)=>{let{__scopeSelect:s,className:r,style:n,...i}=e,o=Z(ew,s),c=ei(ew,s),d=e_(ew,s),h=G(ew,s),[u,p]=l.useState(null),m=(0,x.e)(t,e=>p(e),d.onItemTextChange,e=>c.itemTextRefCallback?.(e,d.value,d.disabled)),g=u?.textContent,y=l.useMemo(()=>(0,a.jsx)("option",{value:d.value,disabled:d.disabled,children:g},d.value),[d.disabled,d.value,g]),{onNativeOptionAdd:v,onNativeOptionRemove:b}=h;return(0,R.b)(()=>(v(y),()=>b(y)),[v,b,y]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(N.WV.span,{id:d.textId,...i,ref:m}),d.isSelected&&o.valueNode&&!o.valueNodeHasChildren?f.createPortal(i.children,o.valueNode):null]})});ej.displayName=ew;var ek="SelectItemIndicator",eS=l.forwardRef((e,t)=>{let{__scopeSelect:s,...r}=e;return e_(ek,s).isSelected?(0,a.jsx)(N.WV.span,{"aria-hidden":!0,...r,ref:t}):null});eS.displayName=ek;var eN="SelectScrollUpButton",eE=l.forwardRef((e,t)=>{let s=ei(eN,e.__scopeSelect),r=eh(eN,e.__scopeSelect),[n,i]=l.useState(!1),o=(0,x.e)(t,r.onScrollButtonChange);return(0,R.b)(()=>{if(s.viewport&&s.isPositioned){let e=function(){i(t.scrollTop>0)},t=s.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[s.viewport,s.isPositioned]),n?(0,a.jsx)(eR,{...e,ref:o,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=s;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eE.displayName=eN;var eO="SelectScrollDownButton",eT=l.forwardRef((e,t)=>{let s=ei(eO,e.__scopeSelect),r=eh(eO,e.__scopeSelect),[n,i]=l.useState(!1),o=(0,x.e)(t,r.onScrollButtonChange);return(0,R.b)(()=>{if(s.viewport&&s.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=s.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[s.viewport,s.isPositioned]),n?(0,a.jsx)(eR,{...e,ref:o,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=s;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eT.displayName=eO;var eR=l.forwardRef((e,t)=>{let{__scopeSelect:s,onAutoScroll:r,...n}=e,i=ei("SelectScrollButton",s),o=l.useRef(null),c=U(s),d=l.useCallback(()=>{null!==o.current&&(window.clearInterval(o.current),o.current=null)},[]);return l.useEffect(()=>()=>d(),[d]),(0,R.b)(()=>{let e=c().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[c]),(0,a.jsx)(N.WV.div,{"aria-hidden":!0,...n,ref:t,style:{flexShrink:0,...n.style},onPointerDown:(0,g.M)(n.onPointerDown,()=>{null===o.current&&(o.current=window.setInterval(r,50))}),onPointerMove:(0,g.M)(n.onPointerMove,()=>{i.onItemLeave?.(),null===o.current&&(o.current=window.setInterval(r,50))}),onPointerLeave:(0,g.M)(n.onPointerLeave,()=>{d()})})}),eF=l.forwardRef((e,t)=>{let{__scopeSelect:s,...r}=e;return(0,a.jsx)(N.WV.div,{"aria-hidden":!0,...r,ref:t})});eF.displayName="SelectSeparator";var eL="SelectArrow";l.forwardRef((e,t)=>{let{__scopeSelect:s,...r}=e,n=W(s),i=Z(eL,s),o=ei(eL,s);return i.open&&"popper"===o.position?(0,a.jsx)(k.Eh,{...n,...r,ref:t}):null}).displayName=eL;var eI=l.forwardRef(({__scopeSelect:e,value:t,...s},r)=>{let n=l.useRef(null),i=(0,x.e)(r,n),o=F(t);return l.useEffect(()=>{let e=n.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(o!==t&&s){let r=new Event("change",{bubbles:!0});s.call(e,t),e.dispatchEvent(r)}},[o,t]),(0,a.jsx)(N.WV.select,{...s,style:{...L.C2,...s.style},ref:i,defaultValue:t})});function eA(e){return""===e||void 0===e}function eP(e){let t=(0,O.W)(e),s=l.useRef(""),r=l.useRef(0),n=l.useCallback(e=>{let n=s.current+e;t(n),function e(t){s.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),i=l.useCallback(()=>{s.current="",window.clearTimeout(r.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(r.current),[]),[s,n,i]}function eD(e,t,s){var r;let n=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(r=Math.max(s?e.indexOf(s):-1,0),e.map((t,s)=>e[(r+s)%e.length]));1===n.length&&(i=i.filter(e=>e!==s));let o=i.find(e=>e.textValue.toLowerCase().startsWith(n.toLowerCase()));return o!==s?o:void 0}eI.displayName="SelectBubbleInput";var eM=s(25390),eB=s(69224);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let eU=(0,eB.Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var eq=s(62312);let eV=l.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(J,{ref:r,className:(0,u.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[t,a.jsx(ee,{asChild:!0,children:a.jsx(eM.Z,{className:"h-4 w-4 opacity-50"})})]}));eV.displayName=J.displayName;let e$=l.forwardRef(({className:e,...t},s)=>a.jsx(eE,{ref:s,className:(0,u.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:a.jsx(eU,{className:"h-4 w-4"})}));e$.displayName=eE.displayName;let eW=l.forwardRef(({className:e,...t},s)=>a.jsx(eT,{ref:s,className:(0,u.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:a.jsx(eM.Z,{className:"h-4 w-4"})}));eW.displayName=eT.displayName;let ez=l.forwardRef(({className:e,children:t,position:s="popper",...r},n)=>a.jsx(et,{children:(0,a.jsxs)(er,{ref:n,className:(0,u.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...r,children:[a.jsx(e$,{}),a.jsx(ep,{className:(0,u.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),a.jsx(eW,{})]})}));ez.displayName=er.displayName,l.forwardRef(({className:e,...t},s)=>a.jsx(ex,{ref:s,className:(0,u.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=ex.displayName;let eZ=l.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(eC,{ref:r,className:(0,u.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(eS,{children:a.jsx(eq.Z,{className:"h-4 w-4"})})}),a.jsx(ej,{children:t})]}));eZ.displayName=eC.displayName,l.forwardRef(({className:e,...t},s)=>a.jsx(eF,{ref:s,className:(0,u.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=eF.displayName;var eH=s(19591),eG=s(40874),eY=s(25545),eQ=s(7060);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let eJ=(0,eB.Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var eK=s(99046);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let eX=(0,eB.Z)("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);var e0=s(57320),e1=s(91991);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let e2=(0,eB.Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);function e3({sql:e,connectionId:t,onExecutionComplete:s}){var r;let[n,i]=(0,l.useState)(!1),[o,c]=(0,l.useState)(null),[u,p]=(0,l.useState)(null),[f,m]=(0,l.useState)(!1),g=async()=>{if(t&&e.trim()){i(!0),c(null);try{let r=await fetch("/api/queries/execute",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({connectionId:t,sql:e.trim(),maxRows:1e3,timeout:3e4,saveToHistory:!0})}),n=await r.json();c(n),s?.(n)}catch(r){let e={success:!1,error:r instanceof Error?r.message:"Unknown error occurred",metadata:{connectionId:t,executedAt:new Date().toISOString(),executionTime:0,rowsReturned:0,queryHash:""}};c(e),s?.(e)}finally{i(!1)}}},y=async()=>{if(t&&e.trim()){m(!0),p(null);try{let s=await fetch(`/api/queries/execute/validate?connectionId=${t}&sql=${encodeURIComponent(e.trim())}`);if(s.ok){let e=await s.json();p(e.validation)}}catch(e){console.error("Failed to validate query:",e)}finally{m(!1)}}},x=t&&e.trim()&&!n;return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(d.Zb,{children:[a.jsx(d.Ol,{children:(0,a.jsxs)(d.ll,{className:"flex items-center gap-2",children:[n?a.jsx(eY.Z,{className:"h-4 w-4 animate-spin"}):o?.success?a.jsx(eQ.Z,{className:"h-4 w-4 text-green-500"}):o&&!o.success?a.jsx(eJ,{className:"h-4 w-4 text-red-500"}):a.jsx(eK.Z,{className:"h-4 w-4"}),"Query Execution",o&&a.jsx(eH.C,{variant:o.success?"default":"destructive",children:o.success?"Success":"Failed"})]})}),(0,a.jsxs)(d.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[a.jsx(h.z,{onClick:g,disabled:!x,className:"flex items-center gap-2",children:n?(0,a.jsxs)(a.Fragment,{children:[a.jsx(eX,{className:"h-4 w-4"}),"Executing..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(e0.Z,{className:"h-4 w-4"}),"Execute Query"]})}),a.jsx(h.z,{variant:"outline",onClick:y,disabled:!t||!e.trim()||f,className:"flex items-center gap-2",children:f?(0,a.jsxs)(a.Fragment,{children:[a.jsx(eY.Z,{className:"h-4 w-4 animate-spin"}),"Validating..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(eQ.Z,{className:"h-4 w-4"}),"Validate"]})})]}),!t&&(0,a.jsxs)(eG.bZ,{children:[a.jsx(e1.Z,{className:"h-4 w-4"}),a.jsx(eG.X,{children:"Please select a database connection to execute queries."})]})]})]}),u&&(0,a.jsxs)(d.Zb,{children:[a.jsx(d.Ol,{children:(0,a.jsxs)(d.ll,{className:"flex items-center gap-2",children:[a.jsx(eQ.Z,{className:"h-4 w-4"}),"Query Validation",(r=u.estimatedComplexity,(0,a.jsxs)(eH.C,{variant:{LOW:"default",MEDIUM:"secondary",HIGH:"destructive"}[r]||"default",children:[r," Complexity"]}))]})}),(0,a.jsxs)(d.aY,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[a.jsx(eH.C,{variant:"outline",children:u.queryType}),u.isReadOnly&&a.jsx(eH.C,{variant:"secondary",children:"Read-Only"})]}),u.errors.length>0&&(0,a.jsxs)(eG.bZ,{className:"border-red-200 bg-red-50",children:[a.jsx(eJ,{className:"h-4 w-4 text-red-500"}),(0,a.jsxs)(eG.X,{children:[a.jsx("div",{className:"font-medium text-red-700 mb-1",children:"Validation Errors:"}),a.jsx("ul",{className:"list-disc list-inside text-red-600 text-sm",children:u.errors.map((e,t)=>a.jsx("li",{children:e},t))})]})]}),u.warnings.length>0&&(0,a.jsxs)(eG.bZ,{className:"border-yellow-200 bg-yellow-50",children:[a.jsx(e2,{className:"h-4 w-4 text-yellow-500"}),(0,a.jsxs)(eG.X,{children:[a.jsx("div",{className:"font-medium text-yellow-700 mb-1",children:"Warnings:"}),a.jsx("ul",{className:"list-disc list-inside text-yellow-600 text-sm",children:u.warnings.map((e,t)=>a.jsx("li",{children:e},t))})]})]}),u.isValid&&0===u.errors.length&&(0,a.jsxs)(eG.bZ,{className:"border-green-200 bg-green-50",children:[a.jsx(eQ.Z,{className:"h-4 w-4 text-green-500"}),a.jsx(eG.X,{className:"text-green-700",children:"Query validation passed. Ready to execute."})]})]})]}),o&&(0,a.jsxs)(d.Zb,{children:[a.jsx(d.Ol,{children:(0,a.jsxs)(d.ll,{className:"flex items-center gap-2",children:[o.success?a.jsx(eQ.Z,{className:"h-4 w-4 text-green-500"}):a.jsx(eJ,{className:"h-4 w-4 text-red-500"}),"Execution Results"]})}),(0,a.jsxs)(d.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-muted-foreground",children:"Execution Time"}),(0,a.jsxs)("div",{className:"font-medium",children:[o.metadata.executionTime,"ms"]})]}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-muted-foreground",children:"Rows Returned"}),a.jsx("div",{className:"font-medium",children:o.metadata.rowsReturned})]}),o.data?.affectedRows!==void 0&&(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-muted-foreground",children:"Rows Affected"}),a.jsx("div",{className:"font-medium",children:o.data.affectedRows})]}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-muted-foreground",children:"Executed At"}),a.jsx("div",{className:"font-medium",children:new Date(o.metadata.executedAt).toLocaleTimeString()})]})]}),o.error&&(0,a.jsxs)(eG.bZ,{className:"border-red-200 bg-red-50",children:[a.jsx(eJ,{className:"h-4 w-4 text-red-500"}),(0,a.jsxs)(eG.X,{children:[a.jsx("div",{className:"font-medium text-red-700 mb-1",children:"Execution Error:"}),a.jsx("div",{className:"text-red-600 text-sm",children:o.error})]})]}),o.warnings&&o.warnings.length>0&&(0,a.jsxs)(eG.bZ,{className:"border-yellow-200 bg-yellow-50",children:[a.jsx(e2,{className:"h-4 w-4 text-yellow-500"}),(0,a.jsxs)(eG.X,{children:[a.jsx("div",{className:"font-medium text-yellow-700 mb-1",children:"Warnings:"}),a.jsx("ul",{className:"list-disc list-inside text-yellow-600 text-sm",children:o.warnings.map((e,t)=>a.jsx("li",{children:e},t))})]})]}),o.success&&o.data&&(0,a.jsxs)(eG.bZ,{className:"border-green-200 bg-green-50",children:[a.jsx(eQ.Z,{className:"h-4 w-4 text-green-500"}),(0,a.jsxs)(eG.X,{className:"text-green-700",children:["Query executed successfully!",o.data.rowCount>0&&(0,a.jsxs)("span",{children:[" Returned ",o.data.rowCount," rows."]}),void 0!==o.data.affectedRows&&o.data.affectedRows>0&&(0,a.jsxs)("span",{children:[" Affected ",o.data.affectedRows," rows."]})]})]})]})]})]})}var e4=s(46540);let e6=l.forwardRef(({className:e,...t},s)=>a.jsx("div",{className:"relative w-full overflow-auto",children:a.jsx("table",{ref:s,className:(0,u.cn)("w-full caption-bottom text-sm",e),...t})}));e6.displayName="Table";let e9=l.forwardRef(({className:e,...t},s)=>a.jsx("thead",{ref:s,className:(0,u.cn)("[&_tr]:border-b",e),...t}));e9.displayName="TableHeader";let e5=l.forwardRef(({className:e,...t},s)=>a.jsx("tbody",{ref:s,className:(0,u.cn)("[&_tr:last-child]:border-0",e),...t}));e5.displayName="TableBody",l.forwardRef(({className:e,...t},s)=>a.jsx("tfoot",{ref:s,className:(0,u.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let e8=l.forwardRef(({className:e,...t},s)=>a.jsx("tr",{ref:s,className:(0,u.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));e8.displayName="TableRow";let e7=l.forwardRef(({className:e,...t},s)=>a.jsx("th",{ref:s,className:(0,u.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));e7.displayName="TableHead";let te=l.forwardRef(({className:e,...t},s)=>a.jsx("td",{ref:s,className:(0,u.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));te.displayName="TableCell",l.forwardRef(({className:e,...t},s)=>a.jsx("caption",{ref:s,className:(0,u.cn)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption";/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let tt=(0,eB.Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]);var ts=s(96885);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let tr=(0,eB.Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]),tn=(0,eB.Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),ti=(0,eB.Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);var to=s(97751);function ta({data:e,metadata:t}){let[s,r]=(0,l.useState)(""),[n,i]=(0,l.useState)(1),[o]=(0,l.useState)(50),c=(0,l.useMemo)(()=>s?e.rows.filter(e=>Object.values(e).some(e=>String(e).toLowerCase().includes(s.toLowerCase()))):e.rows,[e.rows,s]),u=(0,l.useMemo)(()=>{let e=(n-1)*o;return c.slice(e,e+o)},[c,n,o]),p=Math.ceil(c.length/o),f=(e,t)=>{if(null==e)return a.jsx("span",{className:"text-muted-foreground italic",children:"NULL"});if(t.includes("json")||t.includes("jsonb"))try{return a.jsx("pre",{className:"text-xs",children:JSON.stringify(JSON.parse(e),null,2)})}catch{return String(e)}if(t.includes("bool"))return a.jsx(eH.C,{variant:e?"default":"secondary",children:e?"TRUE":"FALSE"});if(t.includes("date")||t.includes("time"))try{return new Date(e).toLocaleString()}catch{return String(e)}let s=String(e);return s.length>100?(0,a.jsxs)("span",{title:s,children:[s.substring(0,100),"..."]}):s},m=e=>e.includes("int")||e.includes("number")||e.includes("decimal")?"\uD83D\uDD22":e.includes("text")||e.includes("varchar")||e.includes("char")?"\uD83D\uDCDD":e.includes("date")||e.includes("time")?"\uD83D\uDCC5":e.includes("bool")?"✅":e.includes("json")?"\uD83D\uDCCB":"\uD83D\uDCC4";return e.rows.length?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(d.Zb,{children:[a.jsx(d.Ol,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(d.ll,{className:"flex items-center gap-2",children:[a.jsx(tt,{className:"h-5 w-5"}),"Query Results",(0,a.jsxs)(eH.C,{variant:"outline",children:[e.rowCount," rows"]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(h.z,{variant:"outline",size:"sm",onClick:()=>{if(!e.rows.length)return;let s=[e.fields.map(e=>e.name).join(","),...e.rows.map(t=>e.fields.map(e=>{let s=t[e.name];return"string"==typeof s&&(s.includes(",")||s.includes('"'))?`"${s.replace(/"/g,'""')}"`:s??""}).join(","))].join("\n"),r=new Blob([s],{type:"text/csv;charset=utf-8;"}),n=document.createElement("a"),i=URL.createObjectURL(r);n.setAttribute("href",i),n.setAttribute("download",`query_results_${t.queryHash}.csv`),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n)},className:"flex items-center gap-2",children:[a.jsx(ts.Z,{className:"h-4 w-4"}),"CSV"]}),(0,a.jsxs)(h.z,{variant:"outline",size:"sm",onClick:()=>{if(!e.rows.length)return;let s=JSON.stringify(e.rows,null,2),r=new Blob([s],{type:"application/json;charset=utf-8;"}),n=document.createElement("a"),i=URL.createObjectURL(r);n.setAttribute("href",i),n.setAttribute("download",`query_results_${t.queryHash}.json`),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n)},className:"flex items-center gap-2",children:[a.jsx(tr,{className:"h-4 w-4"}),"JSON"]})]})]})}),a.jsx(d.aY,{children:(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[(0,a.jsxs)("span",{children:["Execution time: ",t.executionTime,"ms"]}),a.jsx("span",{children:"•"}),(0,a.jsxs)("span",{children:["Rows returned: ",t.rowsReturned]}),a.jsx("span",{children:"•"}),(0,a.jsxs)("span",{children:["Executed at: ",new Date(t.executedAt).toLocaleString()]})]})})]}),a.jsx(d.Zb,{children:a.jsx(d.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[a.jsx(tn,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),a.jsx(e4.I,{placeholder:"Search in results...",value:s,onChange:e=>{r(e.target.value),i(1)},className:"pl-10"})]}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:[c.length," of ",e.rows.length," rows"]})]})})}),a.jsx(d.Zb,{children:a.jsx(d.aY,{className:"p-0",children:a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)(e6,{children:[a.jsx(e9,{children:a.jsx(e8,{children:e.fields.map(e=>a.jsx(e7,{className:"whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx("span",{children:m(e.type)}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium",children:e.name}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[e.type,!e.nullable&&a.jsx("span",{className:"ml-1",children:"NOT NULL"})]})]})]})},e.name))})}),a.jsx(e5,{children:u.map((t,s)=>a.jsx(e8,{children:e.fields.map(e=>a.jsx(te,{className:"max-w-xs",children:f(t[e.name],e.type)},e.name))},s))})]})})})}),p>1&&a.jsx(d.Zb,{children:a.jsx(d.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Page ",n," of ",p]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(h.z,{variant:"outline",size:"sm",onClick:()=>i(e=>Math.max(1,e-1)),disabled:1===n,className:"flex items-center gap-2",children:[a.jsx(ti,{className:"h-4 w-4"}),"Previous"]}),(0,a.jsxs)(h.z,{variant:"outline",size:"sm",onClick:()=>i(e=>Math.min(p,e+1)),disabled:n===p,className:"flex items-center gap-2",children:["Next",a.jsx(to.Z,{className:"h-4 w-4"})]})]})]})})})]}):a.jsx(d.Zb,{children:a.jsx(d.aY,{className:"flex items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx(eK.Z,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),a.jsx("p",{className:"text-muted-foreground",children:"No data returned"}),a.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"Query executed successfully but returned no rows"})]})})})}var tl=s(33668),tc=s(63085),td=s(43234),th="Checkbox",[tu,tp]=(0,v.b)(th),[tf,tm]=tu(th);function tg(e){let{__scopeCheckbox:t,checked:s,children:r,defaultChecked:n,disabled:i,form:o,name:c,onCheckedChange:d,required:h,value:u="on",internal_do_not_use_render:p}=e,[f,m]=(0,T.T)({prop:s,defaultProp:n??!1,onChange:d,caller:th}),[g,y]=l.useState(null),[x,v]=l.useState(null),b=l.useRef(!1),_=!g||!!o||!!g.closest("form"),C={checked:f,disabled:i,setChecked:m,control:g,setControl:y,name:c,form:o,value:u,hasConsumerStoppedPropagationRef:b,required:h,defaultChecked:!tj(n)&&n,isFormControl:_,bubbleInput:x,setBubbleInput:v};return(0,a.jsx)(tf,{scope:t,...C,children:"function"==typeof p?p(C):r})}var ty="CheckboxTrigger",tx=l.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:s,...r},n)=>{let{control:i,value:o,disabled:c,checked:d,required:h,setControl:u,setChecked:p,hasConsumerStoppedPropagationRef:f,isFormControl:m,bubbleInput:y}=tm(ty,e),v=(0,x.e)(n,u),b=l.useRef(d);return l.useEffect(()=>{let e=i?.form;if(e){let t=()=>p(b.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[i,p]),(0,a.jsx)(N.WV.button,{type:"button",role:"checkbox","aria-checked":tj(d)?"mixed":d,"aria-required":h,"data-state":tk(d),"data-disabled":c?"":void 0,disabled:c,value:o,...r,ref:v,onKeyDown:(0,g.M)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,g.M)(s,e=>{p(e=>!!tj(e)||!e),y&&m&&(f.current=e.isPropagationStopped(),f.current||e.stopPropagation())})})});tx.displayName=ty;var tv=l.forwardRef((e,t)=>{let{__scopeCheckbox:s,name:r,checked:n,defaultChecked:i,required:o,disabled:l,value:c,onCheckedChange:d,form:h,...u}=e;return(0,a.jsx)(tg,{__scopeCheckbox:s,checked:n,defaultChecked:i,disabled:l,required:o,onCheckedChange:d,name:r,form:h,value:c,internal_do_not_use_render:({isFormControl:e})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(tx,{...u,ref:t,__scopeCheckbox:s}),e&&(0,a.jsx)(tw,{__scopeCheckbox:s})]})})});tv.displayName=th;var tb="CheckboxIndicator",t_=l.forwardRef((e,t)=>{let{__scopeCheckbox:s,forceMount:r,...n}=e,i=tm(tb,s);return(0,a.jsx)(td.z,{present:r||tj(i.checked)||!0===i.checked,children:(0,a.jsx)(N.WV.span,{"data-state":tk(i.checked),"data-disabled":i.disabled?"":void 0,...n,ref:t,style:{pointerEvents:"none",...e.style}})})});t_.displayName=tb;var tC="CheckboxBubbleInput",tw=l.forwardRef(({__scopeCheckbox:e,...t},s)=>{let{control:r,hasConsumerStoppedPropagationRef:n,checked:i,defaultChecked:o,required:c,disabled:d,name:h,value:u,form:p,bubbleInput:f,setBubbleInput:m}=tm(tC,e),g=(0,x.e)(s,m),y=F(i),v=(0,tc.t)(r);l.useEffect(()=>{if(!f)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!n.current;if(y!==i&&e){let s=new Event("click",{bubbles:t});f.indeterminate=tj(i),e.call(f,!tj(i)&&i),f.dispatchEvent(s)}},[f,y,i,n]);let b=l.useRef(!tj(i)&&i);return(0,a.jsx)(N.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:o??b.current,required:c,disabled:d,name:h,value:u,form:p,...t,tabIndex:-1,ref:g,style:{...t.style,...v,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function tj(e){return"indeterminate"===e}function tk(e){return tj(e)?"indeterminate":e?"checked":"unchecked"}tw.displayName=tC;let tS=l.forwardRef(({className:e,...t},s)=>a.jsx(tv,{ref:s,className:(0,u.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:a.jsx(t_,{className:(0,u.cn)("flex items-center justify-center text-current"),children:a.jsx(eq.Z,{className:"h-4 w-4"})})}));tS.displayName=tv.displayName;/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let tN=(0,eB.Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),tE=(0,eB.Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),tO={async getUser(e){let t=await fetch(`/api/users?id=${e}`);if(!t.ok)throw Error("Failed to fetch user");return t.json()},async createUser(e){let t=await fetch("/api/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create user");return t.json()},async updateUser(e,t){let s=await fetch(`/api/users?id=${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!s.ok)throw Error("Failed to update user");return s.json()},async getUserConnections(e){let t=await fetch(`/api/connections?userId=${e}`);if(!t.ok)throw Error("Failed to fetch connections");return t.json()},async createConnection(e){let t=await fetch("/api/connections",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create connection");return t.json()},async updateConnection(e,t){let s=await fetch(`/api/connections?id=${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!s.ok)throw Error("Failed to update connection");return s.json()},async deleteConnection(e){if(!(await fetch(`/api/connections?id=${e}`,{method:"DELETE"})).ok)throw Error("Failed to delete connection")},async getUserSessions(e){let t=await fetch(`/api/queries?type=sessions&userId=${e}`);if(!t.ok)throw Error("Failed to fetch sessions");return t.json()},async getSession(e){let t=await fetch(`/api/queries?sessionId=${e}`);if(!t.ok)throw Error("Failed to fetch session");return t.json()},async createSession(e){let t=await fetch("/api/queries?type=session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create session");return t.json()},async createQuery(e){let t=await fetch("/api/queries?type=query",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create query");return t.json()},async getUserQueryHistory(e,t=20){let s=await fetch(`/api/queries?type=history&userId=${e}&limit=${t}`);if(!s.ok)throw Error("Failed to fetch query history");return s.json()},async testConnection(e,t){let s=await fetch("/api/connections/test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e?{connectionId:e}:t)});if(!s.ok)throw Error((await s.json()).error||"Failed to test connection");return s.json()},async getConnectionSchema(e,t=!1){let s=await fetch(`/api/connections/schema?connectionId=${e}&refresh=${t}`);if(!s.ok)throw Error((await s.json()).error||"Failed to fetch schema");return s.json()},async refreshConnectionSchema(e){let t=await fetch("/api/connections/schema",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({connectionId:e})});if(!t.ok)throw Error((await t.json()).error||"Failed to refresh schema");return t.json()},async executeQuery(e){let t=await fetch("/api/queries/execute",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).error||"Failed to execute query");return t.json()},async validateQuery(e,t){let s=await fetch(`/api/queries/execute/validate?connectionId=${e}&sql=${encodeURIComponent(t)}`);if(!s.ok)throw Error((await s.json()).error||"Failed to validate query");return s.json()},async updateQueryFeedback(e,t){let s=await fetch(`/api/queries?type=query&id=${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({userFeedback:t})});if(!s.ok)throw Error("Failed to update query feedback");return s.json()}},tT=()=>tO;function tR({connectionId:e,onConnectionTested:t}){let[s,r]=(0,l.useState)(!1),[n,i]=(0,l.useState)(null),[o,c]=(0,l.useState)({databaseType:"POSTGRESQL",connectionString:"",host:"",port:5432,database:"",username:"",password:"",ssl:!1}),u=tT(),p=async()=>{r(!0),i(null);try{let s;s=e?await u.testConnection(e):await u.testConnection(void 0,o),i(s),t?.(s)}catch(s){let e={success:!1,connected:!1,error:s instanceof Error?s.message:"Unknown error",timestamp:new Date().toISOString()};i(e),t?.(e)}finally{r(!1)}},f=(e,t)=>{c(s=>({...s,[e]:t})),i(null)};return(0,a.jsxs)(d.Zb,{children:[a.jsx(d.Ol,{children:(0,a.jsxs)(d.ll,{className:"flex items-center gap-2",children:[s?a.jsx(tN,{className:"h-4 w-4 animate-spin"}):n?.connected?a.jsx(eQ.Z,{className:"h-4 w-4 text-green-500"}):n&&!n.connected?a.jsx(eJ,{className:"h-4 w-4 text-red-500"}):a.jsx(eK.Z,{className:"h-4 w-4"}),e?"Test Connection":"Test Database Connection",s?a.jsx(eH.C,{variant:"secondary",children:"Testing..."}):n?.connected?a.jsx(eH.C,{variant:"default",className:"bg-green-100 text-green-800",children:"Connected"}):n&&!n.connected?a.jsx(eH.C,{variant:"destructive",children:"Failed"}):null]})}),(0,a.jsxs)(d.aY,{className:"space-y-4",children:[!e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(tl._,{htmlFor:"databaseType",children:"Database Type"}),(0,a.jsxs)(Y,{value:o.databaseType,onValueChange:e=>f("databaseType",e),children:[a.jsx(eV,{children:a.jsx(X,{})}),(0,a.jsxs)(ez,{children:[a.jsx(eZ,{value:"POSTGRESQL",children:"PostgreSQL"}),a.jsx(eZ,{value:"MYSQL",children:"MySQL"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(tl._,{htmlFor:"connectionString",children:"Connection String (Optional)"}),a.jsx(e4.I,{id:"connectionString",type:"text",placeholder:"postgresql://user:password@host:port/database",value:o.connectionString,onChange:e=>f("connectionString",e.target.value)}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Leave empty to use individual connection fields below"})]}),!o.connectionString&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(tl._,{htmlFor:"host",children:"Host"}),a.jsx(e4.I,{id:"host",type:"text",placeholder:"localhost",value:o.host,onChange:e=>f("host",e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(tl._,{htmlFor:"port",children:"Port"}),a.jsx(e4.I,{id:"port",type:"number",placeholder:"MYSQL"===o.databaseType?"3306":"5432",value:o.port,onChange:e=>f("port",parseInt(e.target.value)||5432)})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(tl._,{htmlFor:"database",children:"Database Name"}),a.jsx(e4.I,{id:"database",type:"text",placeholder:"my_database",value:o.database,onChange:e=>f("database",e.target.value)})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(tl._,{htmlFor:"username",children:"Username"}),a.jsx(e4.I,{id:"username",type:"text",placeholder:"username",value:o.username,onChange:e=>f("username",e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(tl._,{htmlFor:"password",children:"Password"}),a.jsx(e4.I,{id:"password",type:"password",placeholder:"password",value:o.password,onChange:e=>f("password",e.target.value)})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(tS,{id:"ssl",checked:o.ssl,onCheckedChange:e=>f("ssl",e)}),a.jsx(tl._,{htmlFor:"ssl",children:"Use SSL"})]})]})]}),a.jsx(h.z,{onClick:p,disabled:s||!e&&!o.connectionString&&(!o.host||!o.database||!o.username),className:"w-full",children:s?(0,a.jsxs)(a.Fragment,{children:[a.jsx(tN,{className:"mr-2 h-4 w-4 animate-spin"}),"Testing Connection..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(tE,{className:"mr-2 h-4 w-4"}),"Test Connection"]})}),n&&a.jsx(eG.bZ,{className:n.connected?"border-green-200 bg-green-50":"border-red-200 bg-red-50",children:(0,a.jsxs)(eG.X,{children:[n.connected?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(eQ.Z,{className:"h-4 w-4 text-green-500"}),a.jsx("span",{className:"text-green-700",children:"Connection successful!"})]}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(eJ,{className:"h-4 w-4 text-red-500"}),a.jsx("span",{className:"text-red-700",children:"Connection failed"})]}),n.error&&a.jsx("p",{className:"text-sm text-red-600",children:n.error})]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-2",children:["Tested at ",new Date(n.timestamp).toLocaleString()]})]})})]})]})}var tF="Collapsible",[tL,tI]=(0,v.b)(tF),[tA,tP]=tL(tF),tD=l.forwardRef((e,t)=>{let{__scopeCollapsible:s,open:r,defaultOpen:n,disabled:i,onOpenChange:o,...c}=e,[d,h]=(0,T.T)({prop:r,defaultProp:n??!1,onChange:o,caller:tF});return(0,a.jsx)(tA,{scope:s,disabled:i,contentId:(0,j.M)(),open:d,onOpenToggle:l.useCallback(()=>h(e=>!e),[h]),children:(0,a.jsx)(N.WV.div,{"data-state":t$(d),"data-disabled":i?"":void 0,...c,ref:t})})});tD.displayName=tF;var tM="CollapsibleTrigger",tB=l.forwardRef((e,t)=>{let{__scopeCollapsible:s,...r}=e,n=tP(tM,s);return(0,a.jsx)(N.WV.button,{type:"button","aria-controls":n.contentId,"aria-expanded":n.open||!1,"data-state":t$(n.open),"data-disabled":n.disabled?"":void 0,disabled:n.disabled,...r,ref:t,onClick:(0,g.M)(e.onClick,n.onOpenToggle)})});tB.displayName=tM;var tU="CollapsibleContent",tq=l.forwardRef((e,t)=>{let{forceMount:s,...r}=e,n=tP(tU,e.__scopeCollapsible);return(0,a.jsx)(td.z,{present:s||n.open,children:({present:e})=>(0,a.jsx)(tV,{...r,ref:t,present:e})})});tq.displayName=tU;var tV=l.forwardRef((e,t)=>{let{__scopeCollapsible:s,present:r,children:n,...i}=e,o=tP(tU,s),[c,d]=l.useState(r),h=l.useRef(null),u=(0,x.e)(t,h),p=l.useRef(0),f=p.current,m=l.useRef(0),g=m.current,y=o.open||c,v=l.useRef(y),b=l.useRef(void 0);return l.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,R.b)(()=>{let e=h.current;if(e){b.current=b.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();p.current=t.height,m.current=t.width,v.current||(e.style.transitionDuration=b.current.transitionDuration,e.style.animationName=b.current.animationName),d(r)}},[o.open,r]),(0,a.jsx)(N.WV.div,{"data-state":t$(o.open),"data-disabled":o.disabled?"":void 0,id:o.contentId,hidden:!y,...i,ref:u,style:{"--radix-collapsible-content-height":f?`${f}px`:void 0,"--radix-collapsible-content-width":g?`${g}px`:void 0,...e.style},children:y&&n})});function t$(e){return e?"open":"closed"}var tW=s(82965);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let tz=(0,eB.Z)("Columns",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["line",{x1:"12",x2:"12",y1:"3",y2:"21",key:"1efggb"}]]);var tZ=s(83389);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let tH=(0,eB.Z)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]]);var tG=s(53148);function tY({connectionId:e}){let[t,s]=(0,l.useState)(null),[r,n]=(0,l.useState)(!1),[i,o]=(0,l.useState)(null),[c,u]=(0,l.useState)(null),[p,f]=(0,l.useState)(!1),[m,g]=(0,l.useState)(new Set),[y,x]=(0,l.useState)(new Set),v=tT(),b=async(t=!1)=>{n(!0),o(null);try{let r=await v.getConnectionSchema(e,t);s(r.schema),u(r.lastUpdated),f(r.cached)}catch(e){o(e instanceof Error?e.message:"Failed to load schema")}finally{n(!1)}},_=async()=>{n(!0),o(null);try{let t=await v.refreshConnectionSchema(e);s(t.schema),u(t.lastUpdated),f(!1)}catch(e){o(e instanceof Error?e.message:"Failed to refresh schema")}finally{n(!1)}};(0,l.useEffect)(()=>{b()},[e]);let C=e=>{let t=new Set(m);t.has(e)?t.delete(e):t.add(e),g(t)},w=e=>{let t=new Set(y);t.has(e)?t.delete(e):t.add(e),x(t)},j=e=>e.isPrimaryKey?a.jsx(tW.Z,{className:"h-3 w-3 text-yellow-500"}):e.isForeignKey?a.jsx(tW.Z,{className:"h-3 w-3 text-blue-500"}):a.jsx(tz,{className:"h-3 w-3 text-gray-400"}),k=e=>{let t=[];return e.isPrimaryKey&&t.push(a.jsx(eH.C,{variant:"secondary",className:"text-xs",children:"PK"},"pk")),e.isForeignKey&&t.push(a.jsx(eH.C,{variant:"outline",className:"text-xs",children:"FK"},"fk")),e.nullable||t.push(a.jsx(eH.C,{variant:"outline",className:"text-xs",children:"NOT NULL"},"nn")),t};return r&&!t?a.jsx(d.Zb,{children:a.jsx(d.aY,{className:"flex items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(tN,{className:"h-4 w-4 animate-spin"}),a.jsx("span",{children:"Loading database schema..."})]})})}):i?a.jsx(d.Zb,{children:(0,a.jsxs)(d.aY,{className:"p-6",children:[a.jsx(eG.bZ,{className:"border-red-200 bg-red-50",children:a.jsx(eG.X,{className:"text-red-700",children:i})}),(0,a.jsxs)(h.z,{onClick:()=>b(),className:"mt-4",children:[a.jsx(tE,{className:"mr-2 h-4 w-4"}),"Retry"]})]})}):t?(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx(d.Zb,{children:(0,a.jsxs)(d.Ol,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(d.ll,{className:"flex items-center gap-2",children:[a.jsx(eK.Z,{className:"h-5 w-5"}),"Database Schema",p&&a.jsx(eH.C,{variant:"secondary",children:"Cached"})]}),(0,a.jsxs)(h.z,{onClick:_,disabled:r,variant:"outline",size:"sm",children:[r?a.jsx(tN,{className:"mr-2 h-4 w-4 animate-spin"}):a.jsx(tE,{className:"mr-2 h-4 w-4"}),"Refresh"]})]}),c&&(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Last updated: ",new Date(c).toLocaleString()]})]})}),t.tables&&t.tables.length>0&&(0,a.jsxs)(d.Zb,{children:[a.jsx(d.Ol,{children:(0,a.jsxs)(d.ll,{className:"flex items-center gap-2",children:[a.jsx(tZ.Z,{className:"h-4 w-4"}),"Tables (",t.tables.length,")"]})}),a.jsx(d.aY,{className:"space-y-2",children:t.tables.map(e=>(0,a.jsxs)(tD,{open:m.has(e.name),onOpenChange:()=>C(e.name),children:[(0,a.jsxs)(tB,{className:"flex items-center gap-2 w-full p-2 hover:bg-muted rounded",children:[m.has(e.name)?a.jsx(eM.Z,{className:"h-4 w-4"}):a.jsx(to.Z,{className:"h-4 w-4"}),a.jsx(tZ.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"font-medium",children:e.name}),(0,a.jsxs)(eH.C,{variant:"outline",className:"text-xs",children:[e.columns.length," columns"]}),e.indexes.length>0&&(0,a.jsxs)(eH.C,{variant:"outline",className:"text-xs",children:[e.indexes.length," indexes"]})]}),(0,a.jsxs)(tq,{className:"ml-6 mt-2 space-y-2",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[a.jsx("h4",{className:"text-sm font-medium",children:"Columns"}),e.columns.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-muted/50 rounded text-sm",children:[j(e),a.jsx("span",{className:"font-mono",children:e.name}),a.jsx(eH.C,{variant:"outline",className:"text-xs",children:e.type}),a.jsx("div",{className:"flex gap-1",children:k(e)}),e.isForeignKey&&e.referencedTable&&(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:["→ ",e.referencedTable,".",e.referencedColumn]})]},e.name))]}),e.indexes.length>0&&(0,a.jsxs)("div",{className:"space-y-1",children:[a.jsx("h4",{className:"text-sm font-medium",children:"Indexes"}),e.indexes.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-muted/50 rounded text-sm",children:[a.jsx(tH,{className:"h-3 w-3 text-gray-400"}),a.jsx("span",{className:"font-mono",children:e.name}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:["(",e.columns.join(", "),")"]}),e.isUnique&&a.jsx(eH.C,{variant:"outline",className:"text-xs",children:"UNIQUE"}),e.isPrimary&&a.jsx(eH.C,{variant:"secondary",className:"text-xs",children:"PRIMARY"})]},e.name))]})]})]},`${e.schema}.${e.name}`))})]}),t.views&&t.views.length>0&&(0,a.jsxs)(d.Zb,{children:[a.jsx(d.Ol,{children:(0,a.jsxs)(d.ll,{className:"flex items-center gap-2",children:[a.jsx(tG.Z,{className:"h-4 w-4"}),"Views (",t.views.length,")"]})}),a.jsx(d.aY,{className:"space-y-2",children:t.views.map(e=>(0,a.jsxs)(tD,{open:y.has(e.name),onOpenChange:()=>w(e.name),children:[(0,a.jsxs)(tB,{className:"flex items-center gap-2 w-full p-2 hover:bg-muted rounded",children:[y.has(e.name)?a.jsx(eM.Z,{className:"h-4 w-4"}):a.jsx(to.Z,{className:"h-4 w-4"}),a.jsx(tG.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"font-medium",children:e.name})]}),a.jsx(tq,{className:"ml-6 mt-2",children:a.jsx("div",{className:"p-3 bg-muted/50 rounded",children:a.jsx("pre",{className:"text-xs overflow-x-auto whitespace-pre-wrap",children:e.definition})})})]},`${e.schema}.${e.name}`))})]}),(!t.tables||0===t.tables.length)&&(!t.views||0===t.views.length)&&a.jsx(d.Zb,{children:a.jsx(d.aY,{className:"flex items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx(eK.Z,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),a.jsx("p",{className:"text-muted-foreground",children:"No tables or views found in this database"})]})})})]}):a.jsx(d.Zb,{children:a.jsx(d.aY,{className:"flex items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx(eK.Z,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),a.jsx("p",{className:"text-muted-foreground",children:"No schema data available"}),(0,a.jsxs)(h.z,{onClick:()=>b(!0),className:"mt-4",children:[a.jsx(tE,{className:"mr-2 h-4 w-4"}),"Load Schema"]})]})})})}/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let tQ=(0,eB.Z)("Wifi",[["path",{d:"M5 13a10 10 0 0 1 14 0",key:"6v8j51"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]),tJ=(0,eB.Z)("WifiOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 4.17-2.65",key:"11utq1"}],["path",{d:"M10.66 5c4.01-.36 8.14.9 11.34 3.76",key:"hxefdu"}],["path",{d:"M16.85 11.25a10 10 0 0 1 2.22 1.68",key:"q734kn"}],["path",{d:"M5 13a10 10 0 0 1 5.24-2.76",key:"piq4yl"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]);var tK=s(89895),tX=s(1960);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t0=(0,eB.Z)("UserMinus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var t1=s(11542);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t2=(0,eB.Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var t3=s(75574),t4=s.t(t3,2);let t6=Object.create(null);t6.open="0",t6.close="1",t6.ping="2",t6.pong="3",t6.message="4",t6.upgrade="5",t6.noop="6";let t9=Object.create(null);Object.keys(t6).forEach(e=>{t9[t6[e]]=e});let t5={type:"error",data:"parser error"},t8=({type:e,data:t},s,r)=>r(t instanceof ArrayBuffer||ArrayBuffer.isView(t)?s?t:"b"+t7(t,!0).toString("base64"):t6[e]+(t||"")),t7=(e,t)=>Buffer.isBuffer(e)||e instanceof Uint8Array&&!t?e:e instanceof ArrayBuffer?Buffer.from(e):Buffer.from(e.buffer,e.byteOffset,e.byteLength),se=(e,t)=>{if("string"!=typeof e)return{type:"message",data:st(e,t)};let s=e.charAt(0);return"b"===s?{type:"message",data:st(Buffer.from(e.substring(1),"base64"),t)}:t9[s]?e.length>1?{type:t9[s],data:e.substring(1)}:{type:t9[s]}:t5},st=(e,t)=>"arraybuffer"===t?e instanceof ArrayBuffer?e:Buffer.isBuffer(e)?e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength):e.buffer:Buffer.isBuffer(e)?e:Buffer.from(e),ss=(e,t)=>{let s=e.length,r=Array(s),n=0;e.forEach((e,i)=>{t8(e,!1,e=>{r[i]=e,++n===s&&t(r.join("\x1e"))})})},sr=(e,t)=>{let s=e.split("\x1e"),r=[];for(let e=0;e<s.length;e++){let n=se(s[e],t);if(r.push(n),"error"===n.type)break}return r};function sn(e){return e.reduce((e,t)=>e+t.length,0)}function si(e,t){if(e[0].length===t)return e.shift();let s=new Uint8Array(t),r=0;for(let n=0;n<t;n++)s[n]=e[0][r++],r===e[0].length&&(e.shift(),r=0);return e.length&&r<e[0].length&&(e[0]=e[0].slice(r)),s}function so(e){if(e)return function(e){for(var t in so.prototype)e[t]=so.prototype[t];return e}(e)}so.prototype.on=so.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},so.prototype.once=function(e,t){function s(){this.off(e,s),t.apply(this,arguments)}return s.fn=t,this.on(e,s),this},so.prototype.off=so.prototype.removeListener=so.prototype.removeAllListeners=so.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var s,r=this._callbacks["$"+e];if(!r)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var n=0;n<r.length;n++)if((s=r[n])===t||s.fn===t){r.splice(n,1);break}return 0===r.length&&delete this._callbacks["$"+e],this},so.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=Array(arguments.length-1),s=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(s){s=s.slice(0);for(var r=0,n=s.length;r<n;++r)s[r].apply(this,t)}return this},so.prototype.emitReserved=so.prototype.emit,so.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},so.prototype.hasListeners=function(e){return!!this.listeners(e).length};let sa=process.nextTick,sl=global;class sc{constructor(){this._cookies=new Map}parseCookies(e){e&&e.forEach(e=>{let t=function(e){let t=e.split("; "),s=t[0].indexOf("=");if(-1===s)return;let r=t[0].substring(0,s).trim();if(!r.length)return;let n=t[0].substring(s+1).trim();34===n.charCodeAt(0)&&(n=n.slice(1,-1));let i={name:r,value:n};for(let e=1;e<t.length;e++){let s=t[e].split("=");if(2!==s.length)continue;let r=s[0].trim(),n=s[1].trim();switch(r){case"Expires":i.expires=new Date(n);break;case"Max-Age":let o=new Date;o.setUTCSeconds(o.getUTCSeconds()+parseInt(n,10)),i.expires=o}}return i}(e);t&&this._cookies.set(t.name,t)})}get cookies(){let e=Date.now();return this._cookies.forEach((t,s)=>{var r;(null===(r=t.expires)||void 0===r?void 0:r.getTime())<e&&this._cookies.delete(s)}),this._cookies.entries()}addCookies(e){let t=[];for(let[e,s]of this.cookies)t.push(`${e}=${s.value}`);t.length&&(e.setDisableHeaderCheck(!0),e.setRequestHeader("cookie",t.join("; ")))}appendCookies(e){for(let[t,s]of this.cookies)e.append("cookie",`${t}=${s.value}`)}}function sd(e,...t){return t.reduce((t,s)=>(e.hasOwnProperty(s)&&(t[s]=e[s]),t),{})}let sh=sl.setTimeout,su=sl.clearTimeout;function sp(e,t){t.useNativeTimers?(e.setTimeoutFn=sh.bind(sl),e.clearTimeoutFn=su.bind(sl)):(e.setTimeoutFn=sl.setTimeout.bind(sl),e.clearTimeoutFn=sl.clearTimeout.bind(sl))}function sf(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}var sm=s(87978);let sg=sm("engine.io-client:transport");class sy extends Error{constructor(e,t,s){super(e),this.description=t,this.context=s,this.type="TransportError"}}class sx extends so{constructor(e){super(),this.writable=!1,sp(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64}onError(e,t,s){return super.emitReserved("error",new sy(e,t,s)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return("opening"===this.readyState||"open"===this.readyState)&&(this.doClose(),this.onClose()),this}send(e){"open"===this.readyState?this.write(e):sg("transport is not open, discarding packets")}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){let t=se(e,this.socket.binaryType);this.onPacket(t)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e,t={}){return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(t)}_hostname(){let e=this.opts.hostname;return -1===e.indexOf(":")?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(e){let t=function(e){let t="";for(let s in e)e.hasOwnProperty(s)&&(t.length&&(t+="&"),t+=encodeURIComponent(s)+"="+encodeURIComponent(e[s]));return t}(e);return t.length?"?"+t:""}}let sv=sm("engine.io-client:polling");class sb extends sx{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(e){this.readyState="pausing";let t=()=>{sv("paused"),this.readyState="paused",e()};if(this._polling||!this.writable){let e=0;this._polling&&(sv("we are currently polling - waiting to pause"),e++,this.once("pollComplete",function(){sv("pre-pause polling complete"),--e||t()})),this.writable||(sv("we are currently writing - waiting to pause"),e++,this.once("drain",function(){sv("pre-pause writing complete"),--e||t()}))}else t()}_poll(){sv("polling"),this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){sv("polling got data %s",e),sr(e,this.socket.binaryType).forEach(e=>{if("opening"===this.readyState&&"open"===e.type&&this.onOpen(),"close"===e.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(e)}),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState?this._poll():sv('ignoring poll - transport state "%s"',this.readyState))}doClose(){let e=()=>{sv("writing close packet"),this.write([{type:"close"}])};"open"===this.readyState?(sv("transport open - closing"),e()):(sv("transport not open - deferring close"),this.once("open",e))}write(e){this.writable=!1,ss(e,e=>{this.doWrite(e,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){let e=this.opts.secure?"https":"http",t=this.query||{};return!1!==this.opts.timestampRequests&&(t[this.opts.timestampParam]=sf()),this.supportsBinary||t.sid||(t.b64=1),this.createUri(e,t)}}let s_=!1;try{s_="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(e){}let sC=s_,sw=sm("engine.io-client:polling");function sj(){}class sk extends sb{constructor(e){if(super(e),"undefined"!=typeof location){let t="https:"===location.protocol,s=location.port;s||(s=t?"443":"80"),this.xd="undefined"!=typeof location&&e.hostname!==location.hostname||s!==e.port}}doWrite(e,t){let s=this.request({method:"POST",data:e});s.on("success",t),s.on("error",(e,t)=>{this.onError("xhr post error",e,t)})}doPoll(){sw("xhr poll");let e=this.request();e.on("data",this.onData.bind(this)),e.on("error",(e,t)=>{this.onError("xhr poll error",e,t)}),this.pollXhr=e}}class sS extends so{constructor(e,t,s){super(),this.createRequest=e,sp(this,s),this._opts=s,this._method=s.method||"GET",this._uri=t,this._data=void 0!==s.data?s.data:null,this._create()}_create(){var e;let t=sd(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");t.xdomain=!!this._opts.xd;let s=this._xhr=this.createRequest(t);try{sw("xhr open %s: %s",this._method,this._uri),s.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders)for(let e in s.setDisableHeaderCheck&&s.setDisableHeaderCheck(!0),this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(e)&&s.setRequestHeader(e,this._opts.extraHeaders[e])}catch(e){}if("POST"===this._method)try{s.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(e){}try{s.setRequestHeader("Accept","*/*")}catch(e){}null===(e=this._opts.cookieJar)||void 0===e||e.addCookies(s),"withCredentials"in s&&(s.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(s.timeout=this._opts.requestTimeout),s.onreadystatechange=()=>{var e;3===s.readyState&&(null===(e=this._opts.cookieJar)||void 0===e||e.parseCookies(s.getResponseHeader("set-cookie"))),4===s.readyState&&(200===s.status||1223===s.status?this._onLoad():this.setTimeoutFn(()=>{this._onError("number"==typeof s.status?s.status:0)},0))},sw("xhr data %s",this._data),s.send(this._data)}catch(e){this.setTimeoutFn(()=>{this._onError(e)},0);return}"undefined"!=typeof document&&(this._index=sS.requestsCount++,sS.requests[this._index]=this)}_onError(e){this.emitReserved("error",e,this._xhr),this._cleanup(!0)}_cleanup(e){if(void 0!==this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=sj,e)try{this._xhr.abort()}catch(e){}"undefined"!=typeof document&&delete sS.requests[this._index],this._xhr=null}}_onLoad(){let e=this._xhr.responseText;null!==e&&(this.emitReserved("data",e),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}if(sS.requestsCount=0,sS.requests={},"undefined"!=typeof document){if("function"==typeof attachEvent)attachEvent("onunload",sN);else if("function"==typeof addEventListener){let e="onpagehide"in sl?"pagehide":"unload";addEventListener(e,sN,!1)}}function sN(){for(let e in sS.requests)sS.requests.hasOwnProperty(e)&&sS.requests[e].abort()}!function(){let e=function(e){let t=e.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!t||sC))return new XMLHttpRequest}catch(e){}if(!t)try{return new sl[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch(e){}}({xdomain:!1});e&&e.responseType}();let sE=t3||t4;class sO extends sk{request(e={}){var t;return Object.assign(e,{xd:this.xd,cookieJar:null===(t=this.socket)||void 0===t?void 0:t._cookieJar},this.opts),new sS(e=>new sE(e),this.uri(),e)}}s(67488),s(29573),s(14014);var sT=s(48112);s(6616);let sR=sm("engine.io-client:websocket"),sF="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class sL extends sx{get name(){return"websocket"}doOpen(){let e=this.uri(),t=this.opts.protocols,s=sF?{}:sd(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(s.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,t,s)}catch(e){return this.emitReserved("error",e)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let t=0;t<e.length;t++){let s=e[t],r=t===e.length-1;t8(s,this.supportsBinary,e=>{try{this.doWrite(s,e)}catch(e){sR("websocket closed before onclose event")}r&&sa(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){void 0!==this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){let e=this.opts.secure?"wss":"ws",t=this.query||{};return this.opts.timestampRequests&&(t[this.opts.timestampParam]=sf()),this.supportsBinary||(t.b64=1),this.createUri(e,t)}}sl.WebSocket||sl.MozWebSocket;class sI extends sL{createSocket(e,t,s){var r;if(null===(r=this.socket)||void 0===r?void 0:r._cookieJar)for(let[e,t]of(s.headers=s.headers||{},s.headers.cookie="string"==typeof s.headers.cookie?[s.headers.cookie]:s.headers.cookie||[],this.socket._cookieJar.cookies))s.headers.cookie.push(`${e}=${t.value}`);return new sT(e,t,s)}doWrite(e,t){let s={};e.options&&(s.compress=e.options.compress),this.opts.perMessageDeflate&&("string"==typeof t?Buffer.byteLength(t):t.length)<this.opts.perMessageDeflate.threshold&&(s.compress=!1),this.ws.send(t,s)}}let sA=sm("engine.io-client:webtransport");class sP extends sx{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(e){return this.emitReserved("error",e)}this._transport.closed.then(()=>{sA("transport closed gracefully"),this.onClose()}).catch(e=>{sA("transport closed due to %s",e),this.onError("webtransport error",e)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(e=>{let t=function(e,t){n||(n=new TextDecoder);let s=[],r=0,i=-1,o=!1;return new TransformStream({transform(a,l){for(s.push(a);;){if(0===r){if(1>sn(s))break;let e=si(s,1);o=(128&e[0])==128,r=(i=127&e[0])<126?3:126===i?1:2}else if(1===r){if(2>sn(s))break;let e=si(s,2);i=new DataView(e.buffer,e.byteOffset,e.length).getUint16(0),r=3}else if(2===r){if(8>sn(s))break;let e=si(s,8),t=new DataView(e.buffer,e.byteOffset,e.length),n=t.getUint32(0);if(n>2097151){l.enqueue(t5);break}i=4294967296*n+t.getUint32(4),r=3}else{if(sn(s)<i)break;let e=si(s,i);l.enqueue(se(o?e:n.decode(e),t)),r=0}if(0===i||i>e){l.enqueue(t5);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),s=e.readable.pipeThrough(t).getReader(),i=new TransformStream({transform(e,t){!function(e,t){if(e.data instanceof ArrayBuffer||ArrayBuffer.isView(e.data))return t(t7(e.data,!1));t8(e,!0,e=>{r||(r=new TextEncoder),t(r.encode(e))})}(e,s=>{let r;let n=s.length;if(n<126)r=new Uint8Array(1),new DataView(r.buffer).setUint8(0,n);else if(n<65536){r=new Uint8Array(3);let e=new DataView(r.buffer);e.setUint8(0,126),e.setUint16(1,n)}else{r=new Uint8Array(9);let e=new DataView(r.buffer);e.setUint8(0,127),e.setBigUint64(1,BigInt(n))}e.data&&"string"!=typeof e.data&&(r[0]|=128),t.enqueue(r),t.enqueue(s)})}});i.readable.pipeTo(e.writable),this._writer=i.writable.getWriter();let o=()=>{s.read().then(({done:e,value:t})=>{if(e){sA("session is closed");return}sA("received chunk: %o",t),this.onPacket(t),o()}).catch(e=>{sA("an error occurred while reading: %s",e)})};o();let a={type:"open"};this.query.sid&&(a.data=`{"sid":"${this.query.sid}"}`),this._writer.write(a).then(()=>this.onOpen())})})}write(e){this.writable=!1;for(let t=0;t<e.length;t++){let s=e[t],r=t===e.length-1;this._writer.write(s).then(()=>{r&&sa(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var e;null===(e=this._transport)||void 0===e||e.close()}}let sD={websocket:sI,webtransport:sP,polling:sO},sM=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,sB=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function sU(e){if(e.length>8e3)throw"URI too long";let t=e,s=e.indexOf("["),r=e.indexOf("]");-1!=s&&-1!=r&&(e=e.substring(0,s)+e.substring(s,r).replace(/:/g,";")+e.substring(r,e.length));let n=sM.exec(e||""),i={},o=14;for(;o--;)i[sB[o]]=n[o]||"";return -1!=s&&-1!=r&&(i.source=t,i.host=i.host.substring(1,i.host.length-1).replace(/;/g,":"),i.authority=i.authority.replace("[","").replace("]","").replace(/;/g,":"),i.ipv6uri=!0),i.pathNames=function(e,t){let s=t.replace(/\/{2,9}/g,"/").split("/");return("/"==t.slice(0,1)||0===t.length)&&s.splice(0,1),"/"==t.slice(-1)&&s.splice(s.length-1,1),s}(0,i.path),i.queryKey=function(e,t){let s={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(e,t,r){t&&(s[t]=r)}),s}(0,i.query),i}let sq=sm("engine.io-client:socket"),sV="function"==typeof addEventListener&&"function"==typeof removeEventListener,s$=[];sV&&addEventListener("offline",()=>{sq("closing %d connection(s) because the network was lost",s$.length),s$.forEach(e=>e())},!1);class sW extends so{constructor(e,t){if(super(),this.binaryType="nodebuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&"object"==typeof e&&(t=e,e=null),e){let s=sU(e);t.hostname=s.host,t.secure="https"===s.protocol||"wss"===s.protocol,t.port=s.port,s.query&&(t.query=s.query)}else t.host&&(t.hostname=sU(t.host).host);sp(this,t),this.secure=null!=t.secure?t.secure:"undefined"!=typeof location&&"https:"===location.protocol,t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.hostname=t.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=t.port||("undefined"!=typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},t.transports.forEach(e=>{let t=e.prototype.name;this.transports.push(t),this._transportsByName[t]=e}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},t),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"==typeof this.opts.query&&(this.opts.query=function(e){let t={},s=e.split("&");for(let e=0,r=s.length;e<r;e++){let r=s[e].split("=");t[decodeURIComponent(r[0])]=decodeURIComponent(r[1])}return t}(this.opts.query)),sV&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(sq("adding listener for the 'offline' event"),this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},s$.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=new sc),this._open()}createTransport(e){sq('creating transport "%s"',e);let t=Object.assign({},this.opts.query);t.EIO=4,t.transport=e,this.id&&(t.sid=this.id);let s=Object.assign({},this.opts,{query:t,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return sq("options: %j",s),new this._transportsByName[e](s)}_open(){if(0===this.transports.length){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}let e=this.opts.rememberUpgrade&&sW.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";let t=this.createTransport(e);t.open(),this.setTransport(t)}setTransport(e){sq("setting transport %s",e.name),this.transport&&(sq("clearing existing transport %s",this.transport.name),this.transport.removeAllListeners()),this.transport=e,e.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",e=>this._onClose("transport close",e))}onOpen(){sq("socket open"),this.readyState="open",sW.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(sq('socket receive: type "%s", data "%s"',e.type,e.data),this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":let t=Error("server error");t.code=e.data,this._onError(t);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data)}else sq('packet received with socket readyState "%s"',this.readyState)}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);let e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},e),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){let e=this._getWritablePackets();sq("flushing %d packets in socket",e.length),this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let e=1;for(let t=0;t<this.writeBuffer.length;t++){let s=this.writeBuffer[t].data;if(s&&(e+="string"==typeof s?function(e){let t=0,s=0;for(let r=0,n=e.length;r<n;r++)(t=e.charCodeAt(r))<128?s+=1:t<2048?s+=2:t<55296||t>=57344?s+=3:(r++,s+=4);return s}(s):Math.ceil(1.33*(s.byteLength||s.size))),t>0&&e>this._maxPayload)return sq("only send %d out of %d packets",t,this.writeBuffer.length),this.writeBuffer.slice(0,t);e+=2}return sq("payload size is %d (max: %d)",e,this._maxPayload),this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;let e=Date.now()>this._pingTimeoutTime;return e&&(sq("throttled timer detected, scheduling connection close"),this._pingTimeoutTime=0,sa(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),e}write(e,t,s){return this._sendPacket("message",e,t,s),this}send(e,t,s){return this._sendPacket("message",e,t,s),this}_sendPacket(e,t,s,r){if("function"==typeof t&&(r=t,t=void 0),"function"==typeof s&&(r=s,s=null),"closing"===this.readyState||"closed"===this.readyState)return;(s=s||{}).compress=!1!==s.compress;let n={type:e,data:t,options:s};this.emitReserved("packetCreate",n),this.writeBuffer.push(n),r&&this.once("flush",r),this.flush()}close(){let e=()=>{this._onClose("forced close"),sq("socket closing - telling transport to close"),this.transport.close()},t=()=>{this.off("upgrade",t),this.off("upgradeError",t),e()},s=()=>{this.once("upgrade",t),this.once("upgradeError",t)};return("opening"===this.readyState||"open"===this.readyState)&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?s():e()}):this.upgrading?s():e()),this}_onError(e){if(sq("socket error %j",e),sW.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return sq("trying next transport"),this.transports.shift(),this._open();this.emitReserved("error",e),this._onClose("transport error",e)}_onClose(e,t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(sq('socket close with reason: "%s"',e),this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),sV&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){let e=s$.indexOf(this._offlineEventListener);-1!==e&&(sq("removing listener for the 'offline' event"),s$.splice(e,1))}this.readyState="closed",this.id=null,this.emitReserved("close",e,t),this.writeBuffer=[],this._prevBufferLen=0}}}sW.protocol=4;class sz extends sW{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade){sq("starting upgrade probes");for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e])}}_probe(e){sq('probing transport "%s"',e);let t=this.createTransport(e),s=!1;sW.priorWebsocketSuccess=!1;let r=()=>{s||(sq('probe transport "%s" opened',e),t.send([{type:"ping",data:"probe"}]),t.once("packet",r=>{if(!s){if("pong"===r.type&&"probe"===r.data)sq('probe transport "%s" pong',e),this.upgrading=!0,this.emitReserved("upgrading",t),t&&(sW.priorWebsocketSuccess="websocket"===t.name,sq('pausing current transport "%s"',this.transport.name),this.transport.pause(()=>{s||"closed"===this.readyState||(sq("changing transport and sending upgrade packet"),c(),this.setTransport(t),t.send([{type:"upgrade"}]),this.emitReserved("upgrade",t),t=null,this.upgrading=!1,this.flush())}));else{sq('probe transport "%s" failed',e);let s=Error("probe error");s.transport=t.name,this.emitReserved("upgradeError",s)}}}))};function n(){s||(s=!0,c(),t.close(),t=null)}let i=s=>{let r=Error("probe error: "+s);r.transport=t.name,n(),sq('probe transport "%s" failed because of error: %s',e,s),this.emitReserved("upgradeError",r)};function o(){i("transport closed")}function a(){i("socket closed")}function l(e){t&&e.name!==t.name&&(sq('"%s" works - aborting "%s"',e.name,t.name),n())}let c=()=>{t.removeListener("open",r),t.removeListener("error",i),t.removeListener("close",o),this.off("close",a),this.off("upgrading",l)};t.once("open",r),t.once("error",i),t.once("close",o),this.once("close",a),this.once("upgrading",l),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==e?this.setTimeoutFn(()=>{s||t.open()},200):t.open()}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e)}_filterUpgrades(e){let t=[];for(let s=0;s<e.length;s++)~this.transports.indexOf(e[s])&&t.push(e[s]);return t}}class sZ extends sz{constructor(e,t={}){let s="object"==typeof e?e:t;(!s.transports||s.transports&&"string"==typeof s.transports[0])&&(s.transports=(s.transports||["polling","websocket","webtransport"]).map(e=>sD[e]).filter(e=>!!e)),super(e,s)}}sZ.protocol;var sH=s(94974);let sG=sH("socket.io-client:url"),sY="function"==typeof ArrayBuffer,sQ=e=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer,sJ=Object.prototype.toString,sK="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===sJ.call(Blob),sX="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===sJ.call(File);function s0(e){return sY&&(e instanceof ArrayBuffer||sQ(e))||sK&&e instanceof Blob||sX&&e instanceof File}let s1=s(32428)("socket.io-parser"),s2=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],s3=5;!function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"}(i||(i={}));class s4{constructor(e){this.replacer=e}encode(e){return(s1("encoding packet %j",e),(e.type===i.EVENT||e.type===i.ACK)&&function e(t,s){if(!t||"object"!=typeof t)return!1;if(Array.isArray(t)){for(let s=0,r=t.length;s<r;s++)if(e(t[s]))return!0;return!1}if(s0(t))return!0;if(t.toJSON&&"function"==typeof t.toJSON&&1==arguments.length)return e(t.toJSON(),!0);for(let s in t)if(Object.prototype.hasOwnProperty.call(t,s)&&e(t[s]))return!0;return!1}(e))?this.encodeAsBinary({type:e.type===i.EVENT?i.BINARY_EVENT:i.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id}):[this.encodeAsString(e)]}encodeAsString(e){let t=""+e.type;return(e.type===i.BINARY_EVENT||e.type===i.BINARY_ACK)&&(t+=e.attachments+"-"),e.nsp&&"/"!==e.nsp&&(t+=e.nsp+","),null!=e.id&&(t+=e.id),null!=e.data&&(t+=JSON.stringify(e.data,this.replacer)),s1("encoded %j as %s",e,t),t}encodeAsBinary(e){let t=function(e){let t=[],s=e.data;return e.data=function e(t,s){if(!t)return t;if(s0(t)){let e={_placeholder:!0,num:s.length};return s.push(t),e}if(Array.isArray(t)){let r=Array(t.length);for(let n=0;n<t.length;n++)r[n]=e(t[n],s);return r}if("object"==typeof t&&!(t instanceof Date)){let r={};for(let n in t)Object.prototype.hasOwnProperty.call(t,n)&&(r[n]=e(t[n],s));return r}return t}(s,t),e.attachments=t.length,{packet:e,buffers:t}}(e),s=this.encodeAsString(t.packet),r=t.buffers;return r.unshift(s),r}}function s6(e){return"[object Object]"===Object.prototype.toString.call(e)}class s9 extends so{constructor(e){super(),this.reviver=e}add(e){let t;if("string"==typeof e){if(this.reconstructor)throw Error("got plaintext data when reconstructing a packet");let s=(t=this.decodeString(e)).type===i.BINARY_EVENT;s||t.type===i.BINARY_ACK?(t.type=s?i.EVENT:i.ACK,this.reconstructor=new s5(t),0===t.attachments&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else if(s0(e)||e.base64){if(this.reconstructor)(t=this.reconstructor.takeBinaryData(e))&&(this.reconstructor=null,super.emitReserved("decoded",t));else throw Error("got binary data when not reconstructing a packet")}else throw Error("Unknown type: "+e)}decodeString(e){let t=0,s={type:Number(e.charAt(0))};if(void 0===i[s.type])throw Error("unknown packet type "+s.type);if(s.type===i.BINARY_EVENT||s.type===i.BINARY_ACK){let r=t+1;for(;"-"!==e.charAt(++t)&&t!=e.length;);let n=e.substring(r,t);if(n!=Number(n)||"-"!==e.charAt(t))throw Error("Illegal attachments");s.attachments=Number(n)}if("/"===e.charAt(t+1)){let r=t+1;for(;++t&&","!==e.charAt(t)&&t!==e.length;);s.nsp=e.substring(r,t)}else s.nsp="/";let r=e.charAt(t+1);if(""!==r&&Number(r)==r){let r=t+1;for(;++t;){let s=e.charAt(t);if(null==s||Number(s)!=s){--t;break}if(t===e.length)break}s.id=Number(e.substring(r,t+1))}if(e.charAt(++t)){let r=this.tryParse(e.substr(t));if(s9.isPayloadValid(s.type,r))s.data=r;else throw Error("invalid payload")}return s1("decoded %s as %j",e,s),s}tryParse(e){try{return JSON.parse(e,this.reviver)}catch(e){return!1}}static isPayloadValid(e,t){switch(e){case i.CONNECT:return s6(t);case i.DISCONNECT:return void 0===t;case i.CONNECT_ERROR:return"string"==typeof t||s6(t);case i.EVENT:case i.BINARY_EVENT:return Array.isArray(t)&&("number"==typeof t[0]||"string"==typeof t[0]&&-1===s2.indexOf(t[0]));case i.ACK:case i.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class s5{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){var t,s;let e=(t=this.reconPack,s=this.buffers,t.data=function e(t,s){if(!t)return t;if(t&&!0===t._placeholder){if("number"==typeof t.num&&t.num>=0&&t.num<s.length)return s[t.num];throw Error("illegal attachments")}if(Array.isArray(t))for(let r=0;r<t.length;r++)t[r]=e(t[r],s);else if("object"==typeof t)for(let r in t)Object.prototype.hasOwnProperty.call(t,r)&&(t[r]=e(t[r],s));return t}(t.data,s),delete t.attachments,t);return this.finishedReconstruction(),e}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function s8(e,t,s){return e.on(t,s),function(){e.off(t,s)}}let s7=sH("socket.io-client:socket"),re=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class rt extends so{constructor(e,t,s){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=t,s&&s.auth&&(this.auth=s.auth),this._opts=Object.assign({},s),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;let e=this.io;this.subs=[s8(e,"open",this.onopen.bind(this)),s8(e,"packet",this.onpacket.bind(this)),s8(e,"error",this.onerror.bind(this)),s8(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(...e){return e.unshift("message"),this.emit.apply(this,e),this}emit(e,...t){var s,r,n;if(re.hasOwnProperty(e))throw Error('"'+e.toString()+'" is a reserved event name');if(t.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(t),this;let o={type:i.EVENT,data:t};if(o.options={},o.options.compress=!1!==this.flags.compress,"function"==typeof t[t.length-1]){let e=this.ids++;s7("emitting packet with ack id %d",e);let s=t.pop();this._registerAckCallback(e,s),o.id=e}let a=null===(r=null===(s=this.io.engine)||void 0===s?void 0:s.transport)||void 0===r?void 0:r.writable,l=this.connected&&!(null===(n=this.io.engine)||void 0===n?void 0:n._hasPingExpired());return this.flags.volatile&&!a?s7("discard packet as the transport is not currently writable"):l?(this.notifyOutgoingListeners(o),this.packet(o)):this.sendBuffer.push(o),this.flags={},this}_registerAckCallback(e,t){var s;let r=null!==(s=this.flags.timeout)&&void 0!==s?s:this._opts.ackTimeout;if(void 0===r){this.acks[e]=t;return}let n=this.io.setTimeoutFn(()=>{delete this.acks[e];for(let t=0;t<this.sendBuffer.length;t++)this.sendBuffer[t].id===e&&(s7("removing packet with ack id %d from the buffer",e),this.sendBuffer.splice(t,1));s7("event with ack id %d has timed out after %d ms",e,r),t.call(this,Error("operation has timed out"))},r),i=(...e)=>{this.io.clearTimeoutFn(n),t.apply(this,e)};i.withError=!0,this.acks[e]=i}emitWithAck(e,...t){return new Promise((s,r)=>{let n=(e,t)=>e?r(e):s(t);n.withError=!0,t.push(n),this.emit(e,...t)})}_addToQueue(e){let t;"function"==typeof e[e.length-1]&&(t=e.pop());let s={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push((e,...r)=>{if(s===this._queue[0])return null!==e?s.tryCount>this._opts.retries&&(s7("packet [%d] is discarded after %d tries",s.id,s.tryCount),this._queue.shift(),t&&t(e)):(s7("packet [%d] was successfully sent",s.id),this._queue.shift(),t&&t(null,...r)),s.pending=!1,this._drainQueue()}),this._queue.push(s),this._drainQueue()}_drainQueue(e=!1){if(s7("draining queue"),!this.connected||0===this._queue.length)return;let t=this._queue[0];if(t.pending&&!e){s7("packet [%d] has already been sent and is waiting for an ack",t.id);return}t.pending=!0,t.tryCount++,s7("sending packet [%d] (try n\xb0%d)",t.id,t.tryCount),this.flags=t.flags,this.emit.apply(this,t.args)}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){s7("transport is open - connecting"),"function"==typeof this.auth?this.auth(e=>{this._sendConnectPacket(e)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:i.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,t){s7("close (%s)",e),this.connected=!1,delete this.id,this.emitReserved("disconnect",e,t),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(e=>{if(!this.sendBuffer.some(t=>String(t.id)===e)){let t=this.acks[e];delete this.acks[e],t.withError&&t.call(this,Error("socket has been disconnected"))}})}onpacket(e){if(!(e.nsp!==this.nsp))switch(e.type){case i.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case i.EVENT:case i.BINARY_EVENT:this.onevent(e);break;case i.ACK:case i.BINARY_ACK:this.onack(e);break;case i.DISCONNECT:this.ondisconnect();break;case i.CONNECT_ERROR:this.destroy();let t=Error(e.data.message);t.data=e.data.data,this.emitReserved("connect_error",t)}}onevent(e){let t=e.data||[];s7("emitting event %j",t),null!=e.id&&(s7("attaching ack callback to event"),t.push(this.ack(e.id))),this.connected?this.emitEvent(t):this.receiveBuffer.push(Object.freeze(t))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length)for(let t of this._anyListeners.slice())t.apply(this,e);super.emit.apply(this,e),this._pid&&e.length&&"string"==typeof e[e.length-1]&&(this._lastOffset=e[e.length-1])}ack(e){let t=this,s=!1;return function(...r){s||(s=!0,s7("sending ack %j",r),t.packet({type:i.ACK,id:e,data:r}))}}onack(e){let t=this.acks[e.id];if("function"!=typeof t){s7("bad ack %s",e.id);return}delete this.acks[e.id],s7("calling ack %s with %j",e.id,e.data),t.withError&&e.data.unshift(null),t.apply(this,e.data)}onconnect(e,t){s7("socket connected with id %s",e),this.id=e,this.recovered=t&&this._pid===t,this._pid=t,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(e=>this.emitEvent(e)),this.receiveBuffer=[],this.sendBuffer.forEach(e=>{this.notifyOutgoingListeners(e),this.packet(e)}),this.sendBuffer=[]}ondisconnect(){s7("server disconnect (%s)",this.nsp),this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(e=>e()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&(s7("performing disconnect (%s)",this.nsp),this.packet({type:i.DISCONNECT})),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){let t=this._anyListeners;for(let s=0;s<t.length;s++)if(e===t[s]){t.splice(s,1);break}}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){let t=this._anyOutgoingListeners;for(let s=0;s<t.length;s++)if(e===t[s]){t.splice(s,1);break}}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length)for(let t of this._anyOutgoingListeners.slice())t.apply(this,e.data)}}function rs(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}rs.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),s=Math.floor(t*this.jitter*e);e=(1&Math.floor(10*t))==0?e-s:e+s}return 0|Math.min(e,this.max)},rs.prototype.reset=function(){this.attempts=0},rs.prototype.setMin=function(e){this.ms=e},rs.prototype.setMax=function(e){this.max=e},rs.prototype.setJitter=function(e){this.jitter=e};let rr=sH("socket.io-client:manager");class rn extends so{constructor(e,t){var s;super(),this.nsps={},this.subs=[],e&&"object"==typeof e&&(t=e,e=void 0),(t=t||{}).path=t.path||"/socket.io",this.opts=t,sp(this,t),this.reconnection(!1!==t.reconnection),this.reconnectionAttempts(t.reconnectionAttempts||1/0),this.reconnectionDelay(t.reconnectionDelay||1e3),this.reconnectionDelayMax(t.reconnectionDelayMax||5e3),this.randomizationFactor(null!==(s=t.randomizationFactor)&&void 0!==s?s:.5),this.backoff=new rs({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==t.timeout?2e4:t.timeout),this._readyState="closed",this.uri=e;let r=t.parser||o;this.encoder=new r.Encoder,this.decoder=new r.Decoder,this._autoConnect=!1!==t.autoConnect,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(e){return void 0===e?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var t;return void 0===e?this._reconnectionDelay:(this._reconnectionDelay=e,null===(t=this.backoff)||void 0===t||t.setMin(e),this)}randomizationFactor(e){var t;return void 0===e?this._randomizationFactor:(this._randomizationFactor=e,null===(t=this.backoff)||void 0===t||t.setJitter(e),this)}reconnectionDelayMax(e){var t;return void 0===e?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,null===(t=this.backoff)||void 0===t||t.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(e){if(rr("readyState %s",this._readyState),~this._readyState.indexOf("open"))return this;rr("opening %s",this.uri),this.engine=new sZ(this.uri,this.opts);let t=this.engine,s=this;this._readyState="opening",this.skipReconnect=!1;let r=s8(t,"open",function(){s.onopen(),e&&e()}),n=t=>{rr("error"),this.cleanup(),this._readyState="closed",this.emitReserved("error",t),e?e(t):this.maybeReconnectOnOpen()},i=s8(t,"error",n);if(!1!==this._timeout){let e=this._timeout;rr("connect attempt will timeout after %d",e);let s=this.setTimeoutFn(()=>{rr("connect attempt timed out after %d",e),r(),n(Error("timeout")),t.close()},e);this.opts.autoUnref&&s.unref(),this.subs.push(()=>{this.clearTimeoutFn(s)})}return this.subs.push(r),this.subs.push(i),this}connect(e){return this.open(e)}onopen(){rr("open"),this.cleanup(),this._readyState="open",this.emitReserved("open");let e=this.engine;this.subs.push(s8(e,"ping",this.onping.bind(this)),s8(e,"data",this.ondata.bind(this)),s8(e,"error",this.onerror.bind(this)),s8(e,"close",this.onclose.bind(this)),s8(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(e){this.onclose("parse error",e)}}ondecoded(e){sa(()=>{this.emitReserved("packet",e)},this.setTimeoutFn)}onerror(e){rr("error",e),this.emitReserved("error",e)}socket(e,t){let s=this.nsps[e];return s?this._autoConnect&&!s.active&&s.connect():(s=new rt(this,e,t),this.nsps[e]=s),s}_destroy(e){for(let e of Object.keys(this.nsps))if(this.nsps[e].active){rr("socket %s is still active, skipping close",e);return}this._close()}_packet(e){rr("writing packet %j",e);let t=this.encoder.encode(e);for(let s=0;s<t.length;s++)this.engine.write(t[s],e.options)}cleanup(){rr("cleanup"),this.subs.forEach(e=>e()),this.subs.length=0,this.decoder.destroy()}_close(){rr("disconnect"),this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(e,t){var s;rr("closed due to %s",e),this.cleanup(),null===(s=this.engine)||void 0===s||s.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,t),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;let e=this;if(this.backoff.attempts>=this._reconnectionAttempts)rr("reconnect failed"),this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{let t=this.backoff.duration();rr("will wait %dms before reconnect attempt",t),this._reconnecting=!0;let s=this.setTimeoutFn(()=>{!e.skipReconnect&&(rr("attempting reconnect"),this.emitReserved("reconnect_attempt",e.backoff.attempts),e.skipReconnect||e.open(t=>{t?(rr("reconnect attempt error"),e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",t)):(rr("reconnect success"),e.onreconnect())}))},t);this.opts.autoUnref&&s.unref(),this.subs.push(()=>{this.clearTimeoutFn(s)})}}onreconnect(){let e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}}let ri=sH("socket.io-client"),ro={};function ra(e,t){let s;"object"==typeof e&&(t=e,e=void 0);let r=function(e,t="",s){let r=e;s=s||"undefined"!=typeof location&&location,null==e&&(e=s.protocol+"//"+s.host),"string"==typeof e&&("/"===e.charAt(0)&&(e="/"===e.charAt(1)?s.protocol+e:s.host+e),/^(https?|wss?):\/\//.test(e)||(sG("protocol-less url %s",e),e=void 0!==s?s.protocol+"//"+e:"https://"+e),sG("parse %s",e),r=sU(e)),!r.port&&(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";let n=-1!==r.host.indexOf(":")?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+n+":"+r.port+t,r.href=r.protocol+"://"+n+(s&&s.port===r.port?"":":"+r.port),r}(e,(t=t||{}).path||"/socket.io"),n=r.source,i=r.id,o=r.path,a=ro[i]&&o in ro[i].nsps;return t.forceNew||t["force new connection"]||!1===t.multiplex||a?(ri("ignoring socket cache for %s",n),s=new rn(n,t)):(ro[i]||(ri("new io instance for %s",n),ro[i]=new rn(n,t)),s=ro[i]),r.query&&!t.query&&(t.query=r.queryKey),s.socket(r.path,t)}Object.assign(ra,{Manager:rn,Socket:rt,io:ra,connect:ra});class rl{constructor(){this.socket=null,this.eventHandlers={},this.isConnected=!1,this.currentSessionId=null,this.currentUser=null,this.connect()}connect(){this.socket=ra({path:"/api/socket",autoConnect:!1}),this.setupEventHandlers()}setupEventHandlers(){this.socket&&(this.socket.on("connect",()=>{console.log("Connected to collaboration server"),this.isConnected=!0}),this.socket.on("disconnect",()=>{console.log("Disconnected from collaboration server"),this.isConnected=!1,this.currentSessionId=null,this.currentUser=null}),Object.keys(this.eventHandlers).forEach(e=>{this.socket.on(e,t=>{let s=this.eventHandlers[e];s&&s(t)})}))}async authenticate(e){return new Promise(t=>{if(!this.socket){t(!1);return}this.socket.connect(),this.socket.once("authenticated",e=>{e.success&&e.user?(this.currentUser=e.user,t(!0)):(console.error("Authentication failed:",e.error),t(!1))}),this.socket.emit("authenticate",e)})}joinSession(e,t,s){if(!this.socket||!this.isConnected){console.error("Not connected to collaboration server");return}this.currentSessionId=e,this.socket.emit("join_session",{sessionId:e,connectionId:t,sessionName:s})}leaveSession(){this.socket&&this.currentSessionId&&(this.socket.emit("leave_session",this.currentSessionId),this.currentSessionId=null)}updateSqlContent(e,t){this.socket&&this.currentSessionId&&this.socket.emit("sql_change",{sessionId:this.currentSessionId,content:e,operation:t})}updateCursorPosition(e){this.socket&&this.currentSessionId&&this.socket.emit("cursor_update",{sessionId:this.currentSessionId,position:e})}sendChatMessage(e){this.socket&&this.currentSessionId&&this.socket.emit("chat_message",{sessionId:this.currentSessionId,message:e})}notifyQueryExecutionStart(e){this.socket&&this.currentSessionId&&this.socket.emit("query_execution_start",{sessionId:this.currentSessionId,sql:e})}notifyQueryExecutionComplete(e){this.socket&&this.currentSessionId&&this.socket.emit("query_execution_complete",{sessionId:this.currentSessionId,result:e})}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null),this.isConnected=!1,this.currentSessionId=null,this.currentUser=null}on(e,t){this.eventHandlers[e]=t,this.socket&&this.socket.on(e,t)}off(e){delete this.eventHandlers[e],this.socket&&this.socket.off(e)}get connected(){return this.isConnected}get sessionId(){return this.currentSessionId}get user(){return this.currentUser}}let rc=null;function rd(e={}){let t=(0,l.useRef)(null),[s,r]=(0,l.useState)({isConnected:!1,isAuthenticated:!1,currentUser:null,participants:[],cursors:new Map,chatMessages:[],sqlContent:"",isInSession:!1});(0,l.useEffect)(()=>{t.current||(t.current=(rc||(rc=new rl),rc));let e=t.current;e.on("authenticated",e=>{r(t=>({...t,isAuthenticated:e.success,currentUser:e.user||null}))}),e.on("session_joined",e=>{r(t=>({...t,isInSession:!0,participants:e.participants,currentUser:e.user}))}),e.on("user_joined",e=>{r(t=>({...t,participants:e.participants}))}),e.on("user_left",e=>{r(t=>{let s=new Map(t.cursors);return s.delete(e.userId),{...t,participants:e.participants,cursors:s}})}),e.on("sql_sync",e=>{r(t=>({...t,sqlContent:e.content}))}),e.on("sql_change",e=>{r(t=>({...t,sqlContent:e.content}))}),e.on("cursor_update",e=>{r(t=>{let s=new Map(t.cursors);return s.set(e.user.id,{...e.position,user:e.user}),{...t,cursors:s}})}),e.on("chat_message",e=>{r(t=>({...t,chatMessages:[...t.chatMessages,e]}))}),e.on("query_execution_start",e=>{console.log(`${e.userName} started executing a query`)}),e.on("query_execution_complete",e=>{console.log(`${e.userName} completed query execution`)}),e.on("error",e=>{console.error("Collaboration error:",e.message)});let s=setInterval(()=>{r(t=>({...t,isConnected:e.connected}))},1e3);return()=>{clearInterval(s)}},[]),(0,l.useEffect)(()=>{e.autoConnect&&t.current&&!s.isAuthenticated&&n()},[e.autoConnect]),(0,l.useEffect)(()=>{e.sessionId&&e.connectionId&&s.isAuthenticated&&!s.isInSession&&i(e.sessionId,e.connectionId,e.sessionName)},[e.sessionId,e.connectionId,s.isAuthenticated,s.isInSession]);let n=(0,l.useCallback)(async()=>{if(!t.current)return!1;try{return await t.current.authenticate("demo-token")}catch(e){return console.error("Authentication failed:",e),!1}},[]),i=(0,l.useCallback)((e,s,r)=>{t.current&&t.current.joinSession(e,s,r)},[]),o=(0,l.useCallback)(()=>{t.current&&(t.current.leaveSession(),r(e=>({...e,isInSession:!1,participants:[],cursors:new Map,chatMessages:[],sqlContent:""})))},[]),a=(0,l.useCallback)((e,s)=>{t.current&&t.current.updateSqlContent(e,s)},[]),c=(0,l.useCallback)(e=>{t.current&&t.current.updateCursorPosition(e)},[]),d=(0,l.useCallback)(e=>{t.current&&t.current.sendChatMessage(e)},[]),h=(0,l.useCallback)(e=>{t.current&&t.current.notifyQueryExecutionStart(e)},[]),u=(0,l.useCallback)(e=>{t.current&&t.current.notifyQueryExecutionComplete(e)},[]),p=(0,l.useCallback)(()=>{r(e=>({...e,chatMessages:[]}))},[]),f=(0,l.useCallback)(()=>{t.current&&(t.current.disconnect(),r({isConnected:!1,isAuthenticated:!1,currentUser:null,participants:[],cursors:new Map,chatMessages:[],sqlContent:"",isInSession:!1}))},[]);return{...s,authenticate:n,joinSession:i,leaveSession:o,updateSqlContent:a,updateCursorPosition:c,sendChatMessage:d,notifyQueryExecutionStart:h,notifyQueryExecutionComplete:u,clearChatMessages:p,disconnect:f,participantCount:s.participants.length,otherParticipants:s.participants.filter(e=>e.id!==s.currentUser?.id),canCollaborate:s.isConnected&&s.isAuthenticated&&s.isInSession,sessionId:t.current?.sessionId||null}}function rh({connectionId:e,onSessionJoined:t,onSessionLeft:s}){let[r,n]=(0,l.useState)(""),[i,o]=(0,l.useState)(""),[c,u]=(0,l.useState)(!1),[p,f]=(0,l.useState)(""),m=rd({autoConnect:!0});(0,l.useEffect)(()=>{m.isInSession&&m.currentUser?(f(`${window.location.origin}/dashboard/query-editor?session=${m.sessionId}&connection=${e}`),t?.(m.sessionId||"")):(f(""),s?.())},[m.isInSession,m.sessionId,e,t,s]);let g=async()=>{m.isAuthenticated||await m.authenticate(),u(!0);try{let t=`session_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,s=i.trim()||"Collaboration Session";m.joinSession(t,e,s),o("")}catch(e){console.error("Failed to create session:",e)}finally{u(!1)}},y=async()=>{if(r.trim()){m.isAuthenticated||await m.authenticate();try{m.joinSession(r.trim(),e),n("")}catch(e){console.error("Failed to join session:",e)}}},x=async()=>{if(p)try{await navigator.clipboard.writeText(p)}catch(e){console.error("Failed to copy URL:",e)}};return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(d.Zb,{children:[a.jsx(d.Ol,{children:(0,a.jsxs)(d.ll,{className:"flex items-center gap-2",children:[m.isConnected?a.jsx(tQ,{className:"h-4 w-4 text-green-500"}):a.jsx(tJ,{className:"h-4 w-4 text-red-500"}),"Collaboration Status",a.jsx(eH.C,{variant:m.isConnected?"default":"destructive",children:m.isConnected?m.isAuthenticated?m.isInSession?"In Session":"Connected":"Connecting...":"Disconnected"})]})}),(0,a.jsxs)(d.aY,{children:[!m.isConnected&&(0,a.jsxs)(eG.bZ,{children:[a.jsx(tJ,{className:"h-4 w-4"}),a.jsx(eG.X,{children:"Not connected to collaboration server. Real-time features are unavailable."})]}),m.isConnected&&!m.isAuthenticated&&a.jsx(eG.bZ,{children:a.jsx(eG.X,{children:"Authenticating with collaboration server..."})})]})]}),m.isInSession&&(0,a.jsxs)(d.Zb,{children:[a.jsx(d.Ol,{children:(0,a.jsxs)(d.ll,{className:"flex items-center gap-2",children:[a.jsx(tK.Z,{className:"h-5 w-5"}),"Active Session",(0,a.jsxs)(eH.C,{variant:"default",children:[m.participantCount," participants"]})]})}),(0,a.jsxs)(d.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("h4",{className:"text-sm font-medium",children:"Participants"}),a.jsx("div",{className:"space-y-1",children:m.participants.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-muted rounded",children:[a.jsx("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color}}),a.jsx("span",{className:"text-sm",children:e.name}),e.id===m.currentUser?.id&&a.jsx(eH.C,{variant:"secondary",className:"text-xs",children:"You"})]},e.id))})]}),p&&(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("h4",{className:"text-sm font-medium",children:"Share Session"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[a.jsx(e4.I,{value:p,readOnly:!0,className:"text-xs"}),(0,a.jsxs)(h.z,{variant:"outline",size:"sm",onClick:x,className:"flex items-center gap-2",children:[a.jsx(tX.Z,{className:"h-4 w-4"}),"Copy"]})]})]}),(0,a.jsxs)(h.z,{variant:"outline",onClick:()=>{m.leaveSession()},className:"w-full flex items-center gap-2",children:[a.jsx(t0,{className:"h-4 w-4"}),"Leave Session"]})]})]}),m.isAuthenticated&&!m.isInSession&&(0,a.jsxs)(d.Zb,{children:[a.jsx(d.Ol,{children:(0,a.jsxs)(d.ll,{className:"flex items-center gap-2",children:[a.jsx(t1.Z,{className:"h-5 w-5"}),"Start Collaboration"]})}),(0,a.jsxs)(d.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("h4",{className:"text-sm font-medium",children:"Create New Session"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[a.jsx(e4.I,{placeholder:"Session name (optional)",value:i,onChange:e=>o(e.target.value)}),(0,a.jsxs)(h.z,{onClick:g,disabled:c,className:"flex items-center gap-2",children:[a.jsx(t2,{className:"h-4 w-4"}),"Create"]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("h4",{className:"text-sm font-medium",children:"Join Existing Session"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[a.jsx(e4.I,{placeholder:"Enter session ID",value:r,onChange:e=>n(e.target.value)}),(0,a.jsxs)(h.z,{onClick:y,disabled:!r.trim(),variant:"outline",className:"flex items-center gap-2",children:[a.jsx(tK.Z,{className:"h-4 w-4"}),"Join"]})]})]})]})]}),m.isConnected&&!m.isAuthenticated&&a.jsx(d.Zb,{children:a.jsx(d.aY,{className:"pt-6",children:a.jsx("div",{className:"text-center",children:(0,a.jsxs)(h.z,{onClick:m.authenticate,className:"flex items-center gap-2",children:[a.jsx(tK.Z,{className:"h-4 w-4"}),"Connect to Collaboration"]})})})})]})}var ru=s(47983),rp=s.n(ru);function rf({editorRef:e,onCursorUpdate:t}){let s=rd(),[r,n]=(0,l.useState)([]);return(0,l.useEffect)(()=>{let e=[];s.cursors.forEach((t,r)=>{r!==s.currentUser?.id&&e.push({id:r,user:t.user,position:{line:t.line,column:t.column,selection:t.selection}})}),n(e)},[s.cursors,s.currentUser?.id]),(0,l.useEffect)(()=>{if(!e?.current)return;let r=e.current,n=()=>{let e=r.selectionStart,n=r.selectionEnd,i=r.value.substring(0,e).split("\n"),o=i.length-1,a=i[i.length-1]?.length||0;s.canCollaborate&&s.updateCursorPosition({line:o,column:a,selection:e!==n?{startLine:o,startColumn:a,endLine:o,endColumn:a+(n-e)}:void 0}),t?.(o,a)};return r.addEventListener("selectionchange",n),r.addEventListener("keyup",n),r.addEventListener("mouseup",n),()=>{r.removeEventListener("selectionchange",n),r.removeEventListener("keyup",n),r.removeEventListener("mouseup",n)}},[e,s,t]),(0,a.jsxs)("div",{className:"relative",children:[e?.current&&0!==r.length?r.map(t=>a.jsx(rm,{cursor:t,editorRef:e},t.id)):null,s.canCollaborate&&s.otherParticipants.length>0&&(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[a.jsx("span",{className:"text-xs text-muted-foreground",children:"Active collaborators:"}),s.otherParticipants.map(e=>(0,a.jsxs)(eH.C,{variant:"outline",className:"text-xs flex items-center gap-1",style:{borderColor:e.color},children:[a.jsx("div",{className:"w-2 h-2 rounded-full",style:{backgroundColor:e.color}}),e.name]},e.id))]})]})}function rm({cursor:e,editorRef:t}){let[s,r]=(0,l.useState)(null);return((0,l.useEffect)(()=>{if(!t?.current)return;let s=t.current,n=s.value,i=n.split("\n"),o=0;for(let t=0;t<e.position.line&&t<i.length;t++)o+=(i[t]?.length||0)+1;o+=Math.min(e.position.column,i[e.position.line]?.length||0);let a=document.createElement("textarea");a.style.position="absolute",a.style.visibility="hidden",a.style.whiteSpace="pre",a.style.font=window.getComputedStyle(s).font,a.style.fontSize=window.getComputedStyle(s).fontSize,a.style.fontFamily=window.getComputedStyle(s).fontFamily,a.style.lineHeight=window.getComputedStyle(s).lineHeight,a.style.padding=window.getComputedStyle(s).padding,a.style.border=window.getComputedStyle(s).border,a.style.width=s.offsetWidth+"px",a.value=n.substring(0,o),document.body.appendChild(a);let l=s.scrollTop,c=s.scrollLeft,d=parseInt(window.getComputedStyle(s).lineHeight)||20,h=s.getBoundingClientRect();r({top:h.top+e.position.line*d-l,left:h.left+8*e.position.column-c}),document.body.removeChild(a)},[e.position,t]),s)?(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{style:{top:s.top,left:s.left,backgroundColor:e.user.color,animation:"blink 1s infinite"},className:"jsx-a58dbfef7be8da39 absolute w-0.5 h-5 z-10 pointer-events-none"}),a.jsx("div",{style:{top:s.top-25,left:s.left,transform:"translateX(-50%)"},className:"jsx-a58dbfef7be8da39 absolute z-20 pointer-events-none",children:a.jsx(eH.C,{className:"text-xs px-2 py-1 text-white",style:{backgroundColor:e.user.color},children:e.user.name})}),e.position.selection&&a.jsx("div",{style:{top:s.top,left:s.left,width:(e.position.selection.endColumn-e.position.selection.startColumn)*8,height:20,backgroundColor:e.user.color,opacity:.2},className:"jsx-a58dbfef7be8da39 absolute pointer-events-none z-5"}),a.jsx(rp(),{id:"a58dbfef7be8da39",children:"@-webkit-keyframes blink{0%,50%{opacity:1}51%,100%{opacity:0}}@-moz-keyframes blink{0%,50%{opacity:1}51%,100%{opacity:0}}@-o-keyframes blink{0%,50%{opacity:1}51%,100%{opacity:0}}@keyframes blink{0%,50%{opacity:1}51%,100%{opacity:0}}"})]}):null}var rg=s(74218);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let ry=(0,eB.Z)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);var rx=s(18822);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let rv=(0,eB.Z)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]]);var rb=s(36135);function r_({className:e,maxHeight:t="400px"}){let[s,r]=(0,l.useState)(""),[n,i]=(0,l.useState)(!1),o=(0,l.useRef)(null),c=rd();(0,l.useEffect)(()=>{o.current?.scrollIntoView({behavior:"smooth"})},[c.chatMessages]);let u=()=>{s.trim()&&c.canCollaborate&&(c.sendChatMessage(s.trim()),r(""))},p=e=>new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),f=e=>{switch(e){case"system":return a.jsx(ry,{className:"h-3 w-3"});case"query_execution":return a.jsx(e0.Z,{className:"h-3 w-3"});default:return a.jsx(rx.Z,{className:"h-3 w-3"})}},m=e=>{let t=e.userId===c.currentUser?.id;return"system"===e.type||"query_execution"===e.type?{container:"bg-muted/50 border-l-2 border-muted-foreground/20",text:"text-muted-foreground text-sm italic"}:t?{container:"bg-primary/10 border-l-2 border-primary/30 ml-4",text:"text-foreground"}:{container:"bg-background border-l-2 border-muted/30",text:"text-foreground"}};return c.canCollaborate?(0,a.jsxs)(d.Zb,{className:e,children:[a.jsx(d.Ol,{className:"cursor-pointer",onClick:()=>i(!n),children:(0,a.jsxs)(d.ll,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(rv,{className:"h-5 w-5"}),"Chat",c.chatMessages.length>0&&a.jsx(eH.C,{variant:"secondary",className:"text-xs",children:c.chatMessages.length})]}),a.jsx("div",{className:"flex items-center gap-2",children:c.otherParticipants.length>0&&(0,a.jsxs)(eH.C,{variant:"outline",className:"text-xs",children:[c.participantCount," online"]})})]})}),n&&(0,a.jsxs)(d.aY,{className:"space-y-4",children:[a.jsx(rg.x,{style:{height:t},children:(0,a.jsxs)("div",{className:"space-y-3 pr-4",children:[0===c.chatMessages.length?(0,a.jsxs)("div",{className:"text-center text-muted-foreground text-sm py-8",children:[a.jsx(rv,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),a.jsx("p",{children:"No messages yet. Start the conversation!"})]}):c.chatMessages.map(e=>{let t=m(e),s=e.userId===c.currentUser?.id;return a.jsx("div",{className:`p-3 rounded-lg ${t.container}`,children:(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[a.jsx("div",{className:"flex-shrink-0 mt-0.5",children:f(e.type)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[a.jsx("span",{className:"text-sm font-medium",children:s?"You":e.userName}),a.jsx("span",{className:"text-xs text-muted-foreground",children:p(e.timestamp)}),"message"!==e.type&&a.jsx(eH.C,{variant:"outline",className:"text-xs",children:"system"===e.type?"System":"Query"})]}),a.jsx("p",{className:`text-sm ${t.text} break-words`,children:e.message})]})]})},e.id)}),a.jsx("div",{ref:o})]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[a.jsx(e4.I,{value:s,onChange:e=>r(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),u())},placeholder:"Type a message...",className:"flex-1",maxLength:500}),a.jsx(h.z,{onClick:u,disabled:!s.trim(),size:"sm",className:"flex items-center gap-2",children:a.jsx(rb.Z,{className:"h-4 w-4"})})]})]})]}):a.jsx(d.Zb,{className:e,children:(0,a.jsxs)(d.aY,{className:"p-6 text-center",children:[a.jsx(rv,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),a.jsx("p",{className:"text-muted-foreground text-sm",children:"Join a collaboration session to start chatting"})]})})}/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let rC=(0,eB.Z)("TestTube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5h0c-1.4 0-2.5-1.1-2.5-2.5V2",key:"187lwq"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]);var rw=s(13746);function rj(){let[e,t]=(0,l.useState)(`-- Welcome to QueryCraft Studio Query Editor
-- Try executing this sample query:

SELECT
  customer_id,
  name,
  email,
  COUNT(order_id) as total_orders,
  SUM(order_total) as total_spent
FROM customers c
LEFT JOIN orders o ON c.customer_id = o.customer_id
GROUP BY customer_id, name, email
ORDER BY total_spent DESC
LIMIT 10;`),[s,r]=(0,l.useState)(null),[n,i]=(0,l.useState)("editor"),[o,u]=(0,l.useState)(null),[f]=(0,l.useState)(c().createRef()),m=rd({connectionId:s||void 0,autoConnect:!0}),g=[{id:"conn1",name:"Production DB (PostgreSQL)",type:"POSTGRESQL"},{id:"conn2",name:"Analytics DB (MySQL)",type:"MYSQL"},{id:"conn3",name:"Development DB (PostgreSQL)",type:"POSTGRESQL"}],y=e=>{t(e),m.canCollaborate&&e!==m.sqlContent&&m.updateSqlContent(e)};c().useEffect(()=>{m.sqlContent&&m.sqlContent!==e&&t(m.sqlContent)},[m.sqlContent]);let x=[{id:"editor",label:"Query Editor",icon:tr},{id:"results",label:"Results",icon:e0.Z,disabled:!o},{id:"schema",label:"Schema",icon:tG.Z,disabled:!s},{id:"test",label:"Test Connection",icon:rC},{id:"collaborate",label:"Collaborate",icon:tK.Z,badge:m.participantCount>1?m.participantCount:void 0}];return(0,a.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold",children:"Query Editor"}),a.jsx("p",{className:"text-muted-foreground",children:"Execute SQL queries against your connected databases"})]}),a.jsx("div",{className:"flex items-center gap-4",children:(0,a.jsxs)(Y,{value:s||"",onValueChange:r,children:[a.jsx(eV,{className:"w-64",children:a.jsx(X,{placeholder:"Select a database connection"})}),a.jsx(ez,{children:g.map(e=>a.jsx(eZ,{value:e.id,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(eK.Z,{className:"h-4 w-4"}),a.jsx("span",{children:e.name}),a.jsx(eH.C,{variant:"outline",className:"text-xs",children:e.type})]})},e.id))})]})})]}),s&&a.jsx(d.Zb,{children:a.jsx(d.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(eK.Z,{className:"h-4 w-4 text-green-500"}),(0,a.jsxs)("span",{className:"text-sm",children:["Connected to: ",g.find(e=>e.id===s)?.name]}),a.jsx(eH.C,{variant:"default",className:"text-xs",children:g.find(e=>e.id===s)?.type})]})})}),a.jsx("div",{className:"flex space-x-1 bg-muted p-1 rounded-lg",children:x.map(e=>{let t=e.icon;return(0,a.jsxs)(h.z,{variant:n===e.id?"default":"ghost",size:"sm",onClick:()=>i(e.id),disabled:e.disabled,className:"flex items-center gap-2",children:[a.jsx(t,{className:"h-4 w-4"}),e.label,e.badge&&a.jsx(eH.C,{variant:"secondary",className:"text-xs",children:e.badge})]},e.id)})}),(0,a.jsxs)("div",{className:"space-y-6",children:["editor"===n&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[(0,a.jsxs)(d.Zb,{children:[a.jsx(d.Ol,{children:(0,a.jsxs)(d.ll,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(tr,{className:"h-5 w-5"}),"SQL Editor",m.canCollaborate&&a.jsx(eH.C,{variant:"outline",className:"text-xs",children:"Live"})]}),m.participantCount>1&&(0,a.jsxs)(eH.C,{variant:"secondary",className:"text-xs",children:[m.participantCount," collaborators"]})]})}),(0,a.jsxs)(d.aY,{className:"relative",children:[a.jsx(rf,{editorRef:f,onCursorUpdate:(e,t)=>{}}),a.jsx(p,{ref:f,value:e,onChange:e=>y(e.target.value),placeholder:"Enter your SQL query here...",className:"min-h-[400px] font-mono text-sm"})]})]}),a.jsx("div",{className:"mt-6",children:a.jsx(e3,{sql:e,connectionId:s,onExecutionComplete:e=>{u(e),e.success&&i("results"),m.canCollaborate&&m.notifyQueryExecutionComplete(e)}})})]}),a.jsx("div",{className:"space-y-6",children:a.jsx(r_,{maxHeight:"300px"})})]}),"results"===n&&o&&a.jsx("div",{children:o.success&&o.data?a.jsx(ta,{data:o.data,metadata:o.metadata}):a.jsx(d.Zb,{children:a.jsx(d.aY,{className:"p-8 text-center",children:a.jsx("div",{className:"text-muted-foreground",children:o.error?(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-red-600 mb-2",children:"Query Failed"}),a.jsx("p",{className:"text-sm",children:o.error})]}):a.jsx("p",{children:"No results to display"})})})})}),"schema"===n&&s&&a.jsx(tY,{connectionId:s}),"test"===n&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[s&&a.jsx(tR,{connectionId:s,onConnectionTested:e=>{console.log("Connection test result:",e)}}),a.jsx(tR,{onConnectionTested:e=>{console.log("New connection test result:",e)}})]}),"collaborate"===n&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[a.jsx("div",{className:"space-y-6",children:a.jsx(rh,{connectionId:s||"",onSessionJoined:e=>{console.log("Joined session:",e)},onSessionLeft:()=>{console.log("Left session")}})}),a.jsx("div",{children:a.jsx(r_,{maxHeight:"500px"})})]})]}),(0,a.jsxs)(d.Zb,{children:[a.jsx(d.Ol,{children:(0,a.jsxs)(d.ll,{className:"flex items-center gap-2",children:[a.jsx(rw.Z,{className:"h-5 w-5"}),"Quick Actions"]})}),a.jsx(d.aY,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)(h.z,{variant:"outline",onClick:()=>t("SELECT * FROM users LIMIT 10;"),className:"h-auto p-4 flex flex-col items-center gap-2",children:[a.jsx(eK.Z,{className:"h-6 w-6"}),a.jsx("span",{className:"text-sm",children:"Sample Query"})]}),(0,a.jsxs)(h.z,{variant:"outline",onClick:()=>t(""),className:"h-auto p-4 flex flex-col items-center gap-2",children:[a.jsx(tr,{className:"h-6 w-6"}),a.jsx("span",{className:"text-sm",children:"Clear Editor"})]}),(0,a.jsxs)(h.z,{variant:"outline",onClick:()=>i("schema"),disabled:!s,className:"h-auto p-4 flex flex-col items-center gap-2",children:[a.jsx(tG.Z,{className:"h-6 w-6"}),a.jsx("span",{className:"text-sm",children:"View Schema"})]}),(0,a.jsxs)(h.z,{variant:"outline",onClick:()=>i("test"),className:"h-auto p-4 flex flex-col items-center gap-2",children:[a.jsx(rC,{className:"h-6 w-6"}),a.jsx("span",{className:"text-sm",children:"Test Connection"})]})]})})]})]})}},40874:(e,t,s)=>{"use strict";s.d(t,{X:()=>c,bZ:()=>l});var r=s(95344),n=s(3729),i=s(49247),o=s(11453);let a=(0,i.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),l=n.forwardRef(({className:e,variant:t,...s},n)=>r.jsx("div",{ref:n,role:"alert",className:(0,o.cn)(a({variant:t}),e),...s}));l.displayName="Alert",n.forwardRef(({className:e,...t},s)=>r.jsx("h5",{ref:s,className:(0,o.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let c=n.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,o.cn)("text-sm [&_p]:leading-relaxed",e),...t}));c.displayName="AlertDescription"},19591:(e,t,s)=>{"use strict";s.d(t,{C:()=>a});var r=s(95344);s(3729);var n=s(49247),i=s(11453);let o=(0,n.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function a({className:e,variant:t,...s}){return r.jsx("div",{className:(0,i.cn)(o({variant:t}),e),...s})}},5094:(e,t,s)=>{"use strict";s.d(t,{z:()=>c});var r=s(95344),n=s(3729),i=s(32751),o=s(49247),a=s(11453);let l=(0,o.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=n.forwardRef(({className:e,variant:t,size:s,asChild:n=!1,...o},c)=>{let d=n?i.g7:"button";return r.jsx(d,{className:(0,a.cn)(l({variant:t,size:s,className:e})),ref:c,...o})});c.displayName="Button"},23673:(e,t,s)=>{"use strict";s.d(t,{Ol:()=>a,Zb:()=>o,aY:()=>c,ll:()=>l});var r=s(95344),n=s(3729),i=s(11453);let o=n.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));o.displayName="Card";let a=n.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));a.displayName="CardHeader";let l=n.forwardRef(({className:e,...t},s)=>r.jsx("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle",n.forwardRef(({className:e,...t},s)=>r.jsx("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription";let c=n.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent",n.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},46540:(e,t,s)=>{"use strict";s.d(t,{I:()=>o});var r=s(95344),n=s(3729),i=s(11453);let o=n.forwardRef(({className:e,type:t,...s},n)=>r.jsx("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...s}));o.displayName="Input"},33668:(e,t,s)=>{"use strict";s.d(t,{_:()=>d});var r=s(95344),n=s(3729),i=s(62409),o=n.forwardRef((e,t)=>(0,r.jsx)(i.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var a=s(49247),l=s(11453);let c=(0,a.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=n.forwardRef(({className:e,...t},s)=>r.jsx(o,{ref:s,className:(0,l.cn)(c(),e),...t}));d.displayName=o.displayName},74218:(e,t,s)=>{"use strict";s.d(t,{x:()=>a});var r=s(95344),n=s(3729),i=s(49027),o=s(11453);let a=n.forwardRef(({className:e,children:t,...s},n)=>(0,r.jsxs)(i.fC,{ref:n,className:(0,o.cn)("relative overflow-hidden",e),...s,children:[r.jsx(i.l_,{className:"h-full w-full rounded-[inherit]",children:t}),r.jsx(l,{}),r.jsx(i.Ns,{})]}));a.displayName=i.fC.displayName;let l=n.forwardRef(({className:e,orientation:t="vertical",...s},n)=>r.jsx(i.gb,{ref:n,orientation:t,className:(0,o.cn)("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...s,children:r.jsx(i.q4,{className:"relative flex-1 rounded-full bg-border"})}));l.displayName=i.gb.displayName},40014:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>n,default:()=>o});let r=(0,s(86843).createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/dashboard/query-editor/page.tsx`),{__esModule:n,$$typeof:i}=r,o=r.default}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,599,305,162],()=>s(63530));module.exports=r})();