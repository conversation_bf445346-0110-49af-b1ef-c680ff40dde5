"use strict";exports.id=305,exports.ids=[305],exports.modules={45904:(e,t,r)=>{r.d(t,{Ry:()=>u});var n=new WeakMap,o=new WeakMap,i={},l=0,a=function(e){return e&&(e.host||a(e.parentNode))},c=function(e,t,r,c){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=a(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[r]||(i[r]=new WeakMap);var s=i[r],f=[],d=new Set,p=new Set(u),h=function(e){!e||d.has(e)||(d.add(e),h(e.parentNode))};u.forEach(h);var v=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(d.has(e))v(e);else try{var t=e.getAttribute(c),i=null!==t&&"false"!==t,l=(n.get(e)||0)+1,a=(s.get(e)||0)+1;n.set(e,l),s.set(e,a),f.push(e),1===l&&i&&o.set(e,!0),1===a&&e.setAttribute(r,"true"),i||e.setAttribute(c,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return v(t),d.clear(),l++,function(){f.forEach(function(e){var t=n.get(e)-1,i=s.get(e)-1;n.set(e,t),s.set(e,i),t||(o.has(e)||e.removeAttribute(c),o.delete(e)),i||e.removeAttribute(r)}),--l||(n=new WeakMap,n=new WeakMap,o=new WeakMap,i={})}},u=function(e,t,r){void 0===r&&(r="data-aria-hidden");var n,o=Array.from(Array.isArray(e)?e:[e]),i=t||(n=e,"undefined"==typeof document?null:(Array.isArray(n)?n[0]:n).ownerDocument.body);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),c(o,i,r,"aria-hidden")):function(){return null}}},7060:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},62312:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},25390:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},97751:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},25545:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},1960:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},99046:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},96885:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},91991:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},82965:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]])},57320:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},36135:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},13746:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},11542:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Share",[["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["polyline",{points:"16 6 12 2 8 6",key:"m901s6"}],["line",{x1:"12",x2:"12",y1:"2",y2:"15",key:"1p0rca"}]])},83389:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]])},18822:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},89895:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},71210:(e,t,r)=>{r.d(t,{Z:()=>Z});var n,o=function(){return(o=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}Object.create,Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,r(3729)),a="right-scroll-bar-position",c="width-before-scroll-bar";function u(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var s=l.useEffect,f=new WeakMap;function d(e){return e}var p=function(e){void 0===e&&(e={});var t,r,n,i=(void 0===t&&(t=d),r=[],n=!1,{read:function(){if(n)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var o=t(e,n);return r.push(o),function(){r=r.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(n=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){n=!0;var t=[];if(r.length){var o=r;r=[],o.forEach(e),t=r}var i=function(){var r=t;t=[],r.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),r={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),r}}}});return i.options=o({async:!0,ssr:!1},e),i}(),h=function(){},v=l.forwardRef(function(e,t){var r,n,a,c,d=l.useRef(null),v=l.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),m=v[0],g=v[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,S=e.shards,R=e.sideCar,C=e.noRelative,A=e.noIsolation,k=e.inert,T=e.allowPinchZoom,L=e.as,P=e.gapMode,M=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),O=(r=[d,t],n=function(e){return r.forEach(function(t){return u(t,e)})},(a=(0,l.useState)(function(){return{value:null,callback:n,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=n,c=a.facade,s(function(){var e=f.get(c);if(e){var t=new Set(e),n=new Set(r),o=c.current;t.forEach(function(e){n.has(e)||u(e,null)}),n.forEach(function(e){t.has(e)||u(e,o)})}f.set(c,r)},[r]),c),W=o(o({},M),m);return l.createElement(l.Fragment,null,E&&l.createElement(R,{sideCar:p,removeScrollBar:x,shards:S,noRelative:C,noIsolation:A,inert:k,setCallbacks:g,allowPinchZoom:!!T,lockRef:d,gapMode:P}),y?l.cloneElement(l.Children.only(w),o(o({},W),{ref:O})):l.createElement(void 0===L?"div":L,o({},W,{className:b,ref:O}),w))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:c,zeroRight:a};var m=function(e){var t=e.sideCar,r=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return l.createElement(n,o({},r))};m.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=n||r.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,r){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},w=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},x=w(),E="data-scroll-locked",S=function(e,t,r,n){var o=e.left,i=e.top,l=e.right,u=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(u,"px ").concat(n,";\n  }\n  body[").concat(E,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(u,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a," {\n    right: ").concat(u,"px ").concat(n,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(u,"px ").concat(n,";\n  }\n  \n  .").concat(a," .").concat(a," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(E,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},R=function(){var e=parseInt(document.body.getAttribute(E)||"0",10);return isFinite(e)?e:0},C=function(){l.useEffect(function(){return document.body.setAttribute(E,(R()+1).toString()),function(){var e=R()-1;e<=0?document.body.removeAttribute(E):document.body.setAttribute(E,e.toString())}},[])},A=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;C();var i=l.useMemo(function(){return b},[o]);return l.createElement(x,{styles:S(i,!t,o,r?"":"!important")})},k=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&!(r.overflowY===r.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===r[t])},T=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),L(e,n)){var o=P(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},L=function(e,t){return"v"===e?k(t,"overflowY"):k(t,"overflowX")},P=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},M=function(e,t,r,n,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*n,c=r.target,u=t.contains(c),s=!1,f=a>0,d=0,p=0;do{if(!c)break;var h=P(e,c),v=h[0],m=h[1]-h[2]-l*v;(v||m)&&L(e,c)&&(d+=m,p+=v);var g=c.parentNode;c=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!u&&c!==document.body||u&&(t.contains(c)||t===c));return f&&(o&&1>Math.abs(d)||!o&&a>d)?s=!0:!f&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},O=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},W=function(e){return[e.deltaX,e.deltaY]},D=function(e){return e&&"current"in e?e.current:e},j=0,N=[];let z=(p.useMedium(function(e){var t=l.useRef([]),r=l.useRef([0,0]),n=l.useRef(),o=l.useState(j++)[0],i=l.useState(w)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(D),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=O(e),l=r.current,c="deltaX"in e?e.deltaX:l[0]-i[0],u="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,f=Math.abs(c)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=T(f,s);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=T(f,s)),!d)return!1;if(!n.current&&"changedTouches"in e&&(c||u)&&(n.current=o),!o)return!0;var p=n.current||o;return M(p,t,e,"h"===p?c:u,!0)},[]),u=l.useCallback(function(e){if(N.length&&N[N.length-1]===i){var r="deltaY"in e?W(e):O(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta)[0]===r[0]&&n[1]===r[1]})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(a.current.shards||[]).map(D).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?c(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=l.useCallback(function(e,r,n,o){var i={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),f=l.useCallback(function(e){r.current=O(e),n.current=void 0},[]),d=l.useCallback(function(t){s(t.type,W(t),t.target,c(t,e.lockRef.current))},[]),p=l.useCallback(function(t){s(t.type,O(t),t.target,c(t,e.lockRef.current))},[]);l.useEffect(function(){return N.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",u,!1),document.addEventListener("touchmove",u,!1),document.addEventListener("touchstart",f,!1),function(){N=N.filter(function(e){return e!==i}),document.removeEventListener("wheel",u,!1),document.removeEventListener("touchmove",u,!1),document.removeEventListener("touchstart",f,!1)}},[]);var h=e.removeScrollBar,v=e.inert;return l.createElement(l.Fragment,null,v?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?l.createElement(A,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),m);var H=l.forwardRef(function(e,t){return l.createElement(v,o({},e,{ref:t,sideCar:z}))});H.classNames=v.classNames;let Z=H},2633:(e,t,r)=>{r.d(t,{u:()=>n});function n(e,[t,r]){return Math.min(r,Math.max(t,e))}},3975:(e,t,r)=>{r.d(t,{gm:()=>i});var n=r(3729);r(95344);var o=n.createContext(void 0);function i(e){let t=n.useContext(o);return e||t||"ltr"}},1106:(e,t,r)=>{r.d(t,{EW:()=>i});var n=r(3729),o=0;function i(){n.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??l()),document.body.insertAdjacentElement("beforeend",e[1]??l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},27386:(e,t,r)=>{r.d(t,{M:()=>f});var n=r(3729),o=r(31405),i=r(62409),l=r(2256),a=r(95344),c="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},f=n.forwardRef((e,t)=>{let{loop:r=!1,trapped:f=!1,onMountAutoFocus:m,onUnmountAutoFocus:g,...y}=e,[w,b]=n.useState(null),x=(0,l.W)(m),E=(0,l.W)(g),S=n.useRef(null),R=(0,o.e)(t,e=>b(e)),C=n.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;n.useEffect(()=>{if(f){let e=function(e){if(C.paused||!w)return;let t=e.target;w.contains(t)?S.current=t:h(S.current,{select:!0})},t=function(e){if(C.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||h(S.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&r.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[f,w,C.paused]),n.useEffect(()=>{if(w){v.add(C);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(c,s);w.addEventListener(c,x),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(h(n,{select:t}),document.activeElement!==r)return}(d(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(c,x),setTimeout(()=>{let t=new CustomEvent(u,s);w.addEventListener(u,E),w.dispatchEvent(t),t.defaultPrevented||h(e??document.body,{select:!0}),w.removeEventListener(u,E),v.remove(C)},0)}}},[w,x,E,C]);let A=n.useCallback(e=>{if(!r&&!f||C.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[o,i]=function(e){let t=d(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||n!==i?e.shiftKey&&n===o&&(e.preventDefault(),r&&h(i,{select:!0})):(e.preventDefault(),r&&h(o,{select:!0})):n===t&&e.preventDefault()}},[r,f,C.paused]);return(0,a.jsx)(i.WV.div,{tabIndex:-1,...y,ref:R,onKeyDown:A})});function d(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function p(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function h(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}f.displayName="FocusScope";var v=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=m(e,t)).unshift(t)},remove(t){e=m(e,t),e[0]?.resume()}}}();function m(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},99048:(e,t,r)=>{r.d(t,{M:()=>c});var n,o=r(3729),i=r(16069),l=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function c(e){let[t,r]=o.useState(l());return(0,i.b)(()=>{e||r(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},37574:(e,t,r)=>{r.d(t,{ee:()=>e_,Eh:()=>eF,VY:()=>eI,fC:()=>eZ,D7:()=>eS});var n=r(3729);let o=["top","right","bottom","left"],i=Math.min,l=Math.max,a=Math.round,c=Math.floor,u=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function v(e){return"x"===e?"y":"x"}function m(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>f[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function b(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function E(e,t,r){let n,{reference:o,floating:i}=e,l=g(t),a=v(g(t)),c=m(a),u=p(t),s="y"===l,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,y=o[c]/2-i[c]/2;switch(u){case"top":n={x:f,y:o.y-i.height};break;case"bottom":n={x:f,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:d};break;case"left":n={x:o.x-i.width,y:d};break;default:n={x:o.x,y:o.y}}switch(h(t)){case"start":n[a]-=y*(r&&s?-1:1);break;case"end":n[a]+=y*(r&&s?-1:1)}return n}let S=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:i=[],platform:l}=r,a=i.filter(Boolean),c=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:f}=E(u,n,c),d=n,p={},h=0;for(let r=0;r<a.length;r++){let{name:i,fn:v}=a[r],{x:m,y:g,data:y,reset:w}=await v({x:s,y:f,initialPlacement:n,placement:d,strategy:o,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});s=null!=m?m:s,f=null!=g?g:f,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(u=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:f}=E(u,d,c)),r=-1)}return{x:s,y:f,placement:d,strategy:o,middlewareData:p}};async function R(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:i,rects:l,elements:a,strategy:c}=e,{boundary:u="clippingAncestors",rootBoundary:s="viewport",elementContext:f="floating",altBoundary:p=!1,padding:h=0}=d(t,e),v=b(h),m=a[p?"floating"===f?"reference":"floating":f],g=x(await i.getClippingRect({element:null==(r=await (null==i.isElement?void 0:i.isElement(m)))||r?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:s,strategy:c})),y="floating"===f?{x:n,y:o,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),E=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},S=x(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:w,strategy:c}):y);return{top:(g.top-S.top+v.top)/E.y,bottom:(S.bottom-g.bottom+v.bottom)/E.y,left:(g.left-S.left+v.left)/E.x,right:(S.right-g.right+v.right)/E.x}}function C(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function A(e){return o.some(t=>e[t]>=0)}async function k(e,t){let{placement:r,platform:n,elements:o}=e,i=await (null==n.isRTL?void 0:n.isRTL(o.floating)),l=p(r),a=h(r),c="y"===g(r),u=["left","top"].includes(l)?-1:1,s=i&&c?-1:1,f=d(t,e),{mainAxis:v,crossAxis:m,alignmentAxis:y}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof y&&(m="end"===a?-1*y:y),c?{x:m*s,y:v*u}:{x:v*u,y:m*s}}function T(e){var t;return t=0,"#document"}function L(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function P(e){var t,r;return null==(t=(r=0,e.document||window.document))?void 0:t.documentElement}function M(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=j(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(o)}function O(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function W(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function D(e){return["html","body","#document"].includes(T(e))}function j(e){return L(e).getComputedStyle(e)}function N(e){return{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function z(e){return"html"===T(e)?e:e.assignedSlot||e.parentNode||P(e)}function H(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=z(t);return D(r)?t.ownerDocument?t.ownerDocument.body:t.body:e(r)}(e),i=o===(null==(n=e.ownerDocument)?void 0:n.body),l=L(o);if(i){let e=Z(l);return t.concat(l,l.visualViewport||[],M(o)?o:[],e&&r?H(e):[])}return t.concat(o,H(o,[],r))}function Z(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function _(e){return e.contextElement}function I(e){return _(e),u(1)}let F=u(0);function V(e){let t=L(e);return W()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:F}function B(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let i=e.getBoundingClientRect(),l=_(e),a=u(1);t&&(n||(a=I(e)));let c=(void 0===(o=r)&&(o=!1),n&&(!o||n===L(l))&&o)?V(l):u(0),s=(i.left+c.x)/a.x,f=(i.top+c.y)/a.y,d=i.width/a.x,p=i.height/a.y;if(l){let e=L(l),t=Z(e);for(;t&&n&&n!==e;){let r=I(t),n=t.getBoundingClientRect(),o=j(t),i=n.left+(t.clientLeft+parseFloat(o.paddingLeft))*r.x,l=n.top+(t.clientTop+parseFloat(o.paddingTop))*r.y;s*=r.x,f*=r.y,d*=r.x,p*=r.y,s+=i,f+=l,t=Z(e=L(t))}}return x({width:d,height:p,x:s,y:f})}function Y(e,t){let r=N(e).scrollLeft;return t?t.left+r:B(P(e)).left+r}function X(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:Y(e,n)),y:n.top+t.scrollTop}}function $(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=L(e),n=P(e),o=r.visualViewport,i=n.clientWidth,l=n.clientHeight,a=0,c=0;if(o){i=o.width,l=o.height;let e=W();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,c=o.offsetTop)}return{width:i,height:l,x:a,y:c}}(e,r);else if("document"===t)n=function(e){let t=P(e),r=N(e),n=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),i=l(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),a=-r.scrollLeft+Y(e),c=-r.scrollTop;return"rtl"===j(n).direction&&(a+=l(t.clientWidth,n.clientWidth)-o),{width:o,height:i,x:a,y:c}}(P(e));else{let r=V(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return x(n)}function q(e,t){let r=L(e);if(O(e))return r;{let t=z(e);for(;t&&!D(t);)t=z(t);return r}}let U=async function(e){let t=this.getOffsetParent||q,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=P(t),o="fixed"===r,i=B(e,!0,o,t),l={scrollLeft:0,scrollTop:0},a=u(0);if(!o){("body"!==T(t)||M(n))&&(l=N(t));n&&(a.x=Y(n))}o&&n&&(a.x=Y(n));let c=!n||o?u(0):X(n,l);return{x:i.left+l.scrollLeft-a.x-c.x,y:i.top+l.scrollTop-a.y-c.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},K={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,i="fixed"===o,l=P(n),a=!!t&&O(t.floating);if(n===l||a&&i)return r;let c={scrollLeft:0,scrollTop:0},s=u(1),f=u(0);i||("body"!==T(n)||M(l))&&(c=N(n));let d=!l||i?u(0):X(l,c,!0);return{width:r.width*s.x,height:r.height*s.y,x:r.x*s.x-c.scrollLeft*s.x+f.x+d.x,y:r.y*s.y-c.scrollTop*s.y+f.y+d.y}},getDocumentElement:P,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,a=[..."clippingAncestors"===r?O(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=H(e,[],!1).filter(e=>!1);return"fixed"===j(e).position&&z(e),t.set(e,n),n}(t,this._c):[].concat(r),n],c=a[0],u=a.reduce((e,r)=>{let n=$(t,r,o);return e.top=l(n.top,e.top),e.right=i(n.right,e.right),e.bottom=i(n.bottom,e.bottom),e.left=l(n.left,e.left),e},$(t,c,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:q,getElementRects:U,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=function(e){let t=j(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=r,i=n,l=a(r)!==o||a(n)!==i;return l&&(r=o,n=i),{width:r,height:n,$:l}}(e);return{width:t,height:r}},getScale:I,isElement:function(e){return!1},isRTL:function(e){return"rtl"===j(e).direction}};function G(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let J=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:o,rects:a,platform:c,elements:u,middlewareData:s}=t,{element:f,padding:p=0}=d(e,t)||{};if(null==f)return{};let y=b(p),w={x:r,y:n},x=v(g(o)),E=m(x),S=await c.getDimensions(f),R="y"===x,C=R?"clientHeight":"clientWidth",A=a.reference[E]+a.reference[x]-w[x]-a.floating[E],k=w[x]-a.reference[x],T=await (null==c.getOffsetParent?void 0:c.getOffsetParent(f)),L=T?T[C]:0;L&&await (null==c.isElement?void 0:c.isElement(T))||(L=u.floating[C]||a.floating[E]);let P=L/2-S[E]/2-1,M=i(y[R?"top":"left"],P),O=i(y[R?"bottom":"right"],P),W=L-S[E]-O,D=L/2-S[E]/2+(A/2-k/2),j=l(M,i(D,W)),N=!s.arrow&&null!=h(o)&&D!==j&&a.reference[E]/2-(D<M?M:O)-S[E]/2<0,z=N?D<M?D-M:D-W:0;return{[x]:w[x]+z,data:{[x]:j,centerOffset:D-j-z,...N&&{alignmentOffset:z}},reset:N}}}),Q=(e,t,r)=>{let n=new Map,o={platform:K,...r},i={...o.platform,_c:n};return S(e,t,{...o,platform:i})};var ee=r(81202),et="undefined"!=typeof document?n.useLayoutEffect:function(){};function er(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!er(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!er(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function en(e,t){return Math.round(1*t)/1}function eo(e){let t=n.useRef(e);return et(()=>{t.current=e}),t}let ei=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?J({element:r.current,padding:n}).fn(t):{}:r?J({element:r,padding:n}).fn(t):{}}}),el=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:o,y:i,placement:l,middlewareData:a}=t,c=await k(t,e);return l===(null==(r=a.offset)?void 0:r.placement)&&null!=(n=a.arrow)&&n.alignmentOffset?{}:{x:o+c.x,y:i+c.y,data:{...c,placement:l}}}}}(e),options:[e,t]}),ea=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:o}=t,{mainAxis:a=!0,crossAxis:c=!1,limiter:u={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...s}=d(e,t),f={x:r,y:n},h=await R(t,s),m=g(p(o)),y=v(m),w=f[y],b=f[m];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",r=w+h[e],n=w-h[t];w=l(r,i(w,n))}if(c){let e="y"===m?"top":"left",t="y"===m?"bottom":"right",r=b+h[e],n=b-h[t];b=l(r,i(b,n))}let x=u.fn({...t,[y]:w,[m]:b});return{...x,data:{x:x.x-r,y:x.y-n,enabled:{[y]:a,[m]:c}}}}}}(e),options:[e,t]}),ec=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:c=!0,crossAxis:u=!0}=d(e,t),s={x:r,y:n},f=g(o),h=v(f),m=s[h],y=s[f],w=d(a,t),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(c){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+b.mainAxis,r=i.reference[h]+i.reference[e]-b.mainAxis;m<t?m=t:m>r&&(m=r)}if(u){var x,E;let e="y"===h?"width":"height",t=["top","left"].includes(p(o)),r=i.reference[f]-i.floating[e]+(t&&(null==(x=l.offset)?void 0:x[f])||0)+(t?0:b.crossAxis),n=i.reference[f]+i.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[f])||0)-(t?b.crossAxis:0);y<r?y=r:y>n&&(y=n)}return{[h]:m,[f]:y}}}}(e),options:[e,t]}),eu=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,o,i,l;let{placement:a,middlewareData:c,rects:u,initialPlacement:s,platform:f,elements:b}=t,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:S,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:A="none",flipAlignment:k=!0,...T}=d(e,t);if(null!=(r=c.arrow)&&r.alignmentOffset)return{};let L=p(a),P=g(s),M=p(s)===s,O=await (null==f.isRTL?void 0:f.isRTL(b.floating)),W=S||(M||!k?[w(s)]:function(e){let t=w(e);return[y(e),t,y(t)]}(s)),D="none"!==A;!S&&D&&W.push(...function(e,t,r,n){let o=h(e),i=function(e,t,r){let n=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(r)return t?o:n;return t?n:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===r,n);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(y)))),i}(s,k,A,O));let j=[s,...W],N=await R(t,T),z=[],H=(null==(n=c.flip)?void 0:n.overflows)||[];if(x&&z.push(N[L]),E){let e=function(e,t,r){void 0===r&&(r=!1);let n=h(e),o=v(g(e)),i=m(o),l="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=w(l)),[l,w(l)]}(a,u,O);z.push(N[e[0]],N[e[1]])}if(H=[...H,{placement:a,overflows:z}],!z.every(e=>e<=0)){let e=((null==(o=c.flip)?void 0:o.index)||0)+1,t=j[e];if(t&&(!("alignment"===E&&P!==g(t))||H.every(e=>e.overflows[0]>0&&g(e.placement)===P)))return{data:{index:e,overflows:H},reset:{placement:t}};let r=null==(i=H.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!r)switch(C){case"bestFit":{let e=null==(l=H.filter(e=>{if(D){let t=g(e.placement);return t===P||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(r=e);break}case"initialPlacement":r=s}if(a!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),es=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let o,a;let{placement:c,rects:u,platform:s,elements:f}=t,{apply:v=()=>{},...m}=d(e,t),y=await R(t,m),w=p(c),b=h(c),x="y"===g(c),{width:E,height:S}=u.floating;"top"===w||"bottom"===w?(o=w,a=b===(await (null==s.isRTL?void 0:s.isRTL(f.floating))?"start":"end")?"left":"right"):(a=w,o="end"===b?"top":"bottom");let C=S-y.top-y.bottom,A=E-y.left-y.right,k=i(S-y[o],C),T=i(E-y[a],A),L=!t.middlewareData.shift,P=k,M=T;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(M=A),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(P=C),L&&!b){let e=l(y.left,0),t=l(y.right,0),r=l(y.top,0),n=l(y.bottom,0);x?M=E-2*(0!==e||0!==t?e+t:l(y.left,y.right)):P=S-2*(0!==r||0!==n?r+n:l(y.top,y.bottom))}await v({...t,availableWidth:M,availableHeight:P});let O=await s.getDimensions(f.floating);return E!==O.width||S!==O.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ef=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...o}=d(e,t);switch(n){case"referenceHidden":{let e=C(await R(t,{...o,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:A(e)}}}case"escaped":{let e=C(await R(t,{...o,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:A(e)}}}default:return{}}}}}(e),options:[e,t]}),ed=(e,t)=>({...ei(e),options:[e,t]});var ep=r(62409),eh=r(95344),ev=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,eh.jsx)(ep.WV.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,eh.jsx)("polygon",{points:"0,0 30,0 15,10"})})});ev.displayName="Arrow";var em=r(31405),eg=r(98462),ey=r(2256),ew=r(16069),eb=r(63085),ex="Popper",[eE,eS]=(0,eg.b)(ex),[eR,eC]=eE(ex),eA=e=>{let{__scopePopper:t,children:r}=e,[o,i]=n.useState(null);return(0,eh.jsx)(eR,{scope:t,anchor:o,onAnchorChange:i,children:r})};eA.displayName=ex;var ek="PopperAnchor",eT=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:o,...i}=e,l=eC(ek,r),a=n.useRef(null),c=(0,em.e)(t,a);return n.useEffect(()=>{l.onAnchorChange(o?.current||a.current)}),o?null:(0,eh.jsx)(ep.WV.div,{...i,ref:c})});eT.displayName=ek;var eL="PopperContent",[eP,eM]=eE(eL),eO=n.forwardRef((e,t)=>{let{__scopePopper:r,side:o="bottom",sideOffset:a=0,align:u="center",alignOffset:s=0,arrowPadding:f=0,avoidCollisions:d=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:v="partial",hideWhenDetached:m=!1,updatePositionStrategy:g="optimized",onPlaced:y,...w}=e,b=eC(eL,r),[x,E]=n.useState(null),S=(0,em.e)(t,e=>E(e)),[R,C]=n.useState(null),A=(0,eb.t)(R),k=A?.width??0,T=A?.height??0,L="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},M=Array.isArray(p)?p:[p],O=M.length>0,W={padding:L,boundary:M.filter(eN),altBoundary:O},{refs:D,floatingStyles:j,placement:N,isPositioned:z,middlewareData:Z}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:o=[],platform:i,elements:{reference:l,floating:a}={},transform:c=!0,whileElementsMounted:u,open:s}=e,[f,d]=n.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=n.useState(o);er(p,o)||h(o);let[v,m]=n.useState(null),[g,y]=n.useState(null),w=n.useCallback(e=>{e!==S.current&&(S.current=e,m(e))},[]),b=n.useCallback(e=>{e!==R.current&&(R.current=e,y(e))},[]),x=l||v,E=a||g,S=n.useRef(null),R=n.useRef(null),C=n.useRef(f),A=null!=u,k=eo(u),T=eo(i),L=eo(s),P=n.useCallback(()=>{if(!S.current||!R.current)return;let e={placement:t,strategy:r,middleware:p};T.current&&(e.platform=T.current),Q(S.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==L.current};M.current&&!er(C.current,t)&&(C.current=t,ee.flushSync(()=>{d(t)}))})},[p,t,r,T,L]);et(()=>{!1===s&&C.current.isPositioned&&(C.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);let M=n.useRef(!1);et(()=>(M.current=!0,()=>{M.current=!1}),[]),et(()=>{if(x&&(S.current=x),E&&(R.current=E),x&&E){if(k.current)return k.current(x,E,P);P()}},[x,E,P,k,A]);let O=n.useMemo(()=>({reference:S,floating:R,setReference:w,setFloating:b}),[w,b]),W=n.useMemo(()=>({reference:x,floating:E}),[x,E]),D=n.useMemo(()=>{let e={position:r,left:0,top:0};if(!W.floating)return e;let t=en(W.floating,f.x),n=en(W.floating,f.y);return c?{...e,transform:"translate("+t+"px, "+n+"px)",...(W.floating,!1)}:{position:r,left:t,top:n}},[r,c,W.floating,f.x,f.y]);return n.useMemo(()=>({...f,update:P,refs:O,elements:W,floatingStyles:D}),[f,P,O,W,D])}({strategy:"fixed",placement:o+("center"!==u?"-"+u:""),whileElementsMounted:(...e)=>(function(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=n,p=_(e),h=a||u?[...p?H(p):[],...H(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",r,{passive:!0}),u&&e.addEventListener("resize",r)});let v=p&&f?function(e,t){let r,n=null,o=P(e);function a(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return function u(s,f){void 0===s&&(s=!1),void 0===f&&(f=1),a();let d=e.getBoundingClientRect(),{left:p,top:h,width:v,height:m}=d;if(s||t(),!v||!m)return;let g=c(h),y=c(o.clientWidth-(p+v)),w={rootMargin:-g+"px "+-y+"px "+-c(o.clientHeight-(h+m))+"px "+-c(p)+"px",threshold:l(0,i(1,f))||1},b=!0;function x(t){let n=t[0].intersectionRatio;if(n!==f){if(!b)return u();n?u(!1,n):r=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==n||G(d,e.getBoundingClientRect())||u(),b=!1}try{n=new IntersectionObserver(x,{...w,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(x,w)}n.observe(e)}(!0),a}(p,r):null,m=-1,g=null;s&&(g=new ResizeObserver(e=>{let[n]=e;n&&n.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),r()}),p&&!d&&g.observe(p),g.observe(t));let y=d?B(e):null;return d&&function t(){let n=B(e);y&&!G(y,n)&&r(),y=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",r),u&&e.removeEventListener("resize",r)}),null==v||v(),null==(e=g)||e.disconnect(),g=null,d&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===g}),elements:{reference:b.anchor},middleware:[el({mainAxis:a+T,alignmentAxis:s}),d&&ea({mainAxis:!0,crossAxis:!1,limiter:"partial"===v?ec():void 0,...W}),d&&eu({...W}),es({...W,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${r}px`),l.setProperty("--radix-popper-available-height",`${n}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),R&&ed({element:R,padding:f}),ez({arrowWidth:k,arrowHeight:T}),m&&ef({strategy:"referenceHidden",...W})]}),[I,F]=eH(N),V=(0,ey.W)(y);(0,ew.b)(()=>{z&&V?.()},[z,V]);let Y=Z.arrow?.x,X=Z.arrow?.y,$=Z.arrow?.centerOffset!==0,[q,U]=n.useState();return(0,ew.b)(()=>{x&&U(window.getComputedStyle(x).zIndex)},[x]),(0,eh.jsx)("div",{ref:D.setFloating,"data-radix-popper-content-wrapper":"",style:{...j,transform:z?j.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:q,"--radix-popper-transform-origin":[Z.transformOrigin?.x,Z.transformOrigin?.y].join(" "),...Z.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eh.jsx)(eP,{scope:r,placedSide:I,onArrowChange:C,arrowX:Y,arrowY:X,shouldHideArrow:$,children:(0,eh.jsx)(ep.WV.div,{"data-side":I,"data-align":F,...w,ref:S,style:{...w.style,animation:z?void 0:"none"}})})})});eO.displayName=eL;var eW="PopperArrow",eD={top:"bottom",right:"left",bottom:"top",left:"right"},ej=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=eM(eW,r),i=eD[o.placedSide];return(0,eh.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eh.jsx)(ev,{...n,ref:t,style:{...n.style,display:"block"}})})});function eN(e){return null!==e}ej.displayName=eW;var ez=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[c,u]=eH(r),s={start:"0%",center:"50%",end:"100%"}[u],f=(o.arrow?.x??0)+l/2,d=(o.arrow?.y??0)+a/2,p="",h="";return"bottom"===c?(p=i?s:`${f}px`,h=`${-a}px`):"top"===c?(p=i?s:`${f}px`,h=`${n.floating.height+a}px`):"right"===c?(p=`${-a}px`,h=i?s:`${d}px`):"left"===c&&(p=`${n.floating.width+a}px`,h=i?s:`${d}px`),{data:{x:p,y:h}}}});function eH(e){let[t,r="center"]=e.split("-");return[t,r]}var eZ=eA,e_=eT,eI=eO,eF=ej},49027:(e,t,r)=>{r.d(t,{Ns:()=>U,fC:()=>$,gb:()=>S,l_:()=>q,q4:()=>D});var n=r(3729),o=r(62409),i=r(43234),l=r(98462),a=r(31405),c=r(2256),u=r(3975),s=r(16069),f=r(2633),d=r(85222),p=r(95344),h="ScrollArea",[v,m]=(0,l.b)(h),[g,y]=v(h),w=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:i="hover",dir:l,scrollHideDelay:c=600,...s}=e,[f,d]=n.useState(null),[h,v]=n.useState(null),[m,y]=n.useState(null),[w,b]=n.useState(null),[x,E]=n.useState(null),[S,R]=n.useState(0),[C,A]=n.useState(0),[k,T]=n.useState(!1),[L,P]=n.useState(!1),M=(0,a.e)(t,e=>d(e)),O=(0,u.gm)(l);return(0,p.jsx)(g,{scope:r,type:i,dir:O,scrollHideDelay:c,scrollArea:f,viewport:h,onViewportChange:v,content:m,onContentChange:y,scrollbarX:w,onScrollbarXChange:b,scrollbarXEnabled:k,onScrollbarXEnabledChange:T,scrollbarY:x,onScrollbarYChange:E,scrollbarYEnabled:L,onScrollbarYEnabledChange:P,onCornerWidthChange:R,onCornerHeightChange:A,children:(0,p.jsx)(o.WV.div,{dir:O,...s,ref:M,style:{position:"relative","--radix-scroll-area-corner-width":S+"px","--radix-scroll-area-corner-height":C+"px",...e.style}})})});w.displayName=h;var b="ScrollAreaViewport",x=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:i,nonce:l,...c}=e,u=y(b,r),s=n.useRef(null),f=(0,a.e)(t,s,u.onViewportChange);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,p.jsx)(o.WV.div,{"data-radix-scroll-area-viewport":"",...c,ref:f,style:{overflowX:u.scrollbarXEnabled?"scroll":"hidden",overflowY:u.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,p.jsx)("div",{ref:u.onContentChange,style:{minWidth:"100%",display:"table"},children:i})})]})});x.displayName=b;var E="ScrollAreaScrollbar",S=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,i=y(E,e.__scopeScrollArea),{onScrollbarXEnabledChange:l,onScrollbarYEnabledChange:a}=i,c="horizontal"===e.orientation;return n.useEffect(()=>(c?l(!0):a(!0),()=>{c?l(!1):a(!1)}),[c,l,a]),"hover"===i.type?(0,p.jsx)(R,{...o,ref:t,forceMount:r}):"scroll"===i.type?(0,p.jsx)(C,{...o,ref:t,forceMount:r}):"auto"===i.type?(0,p.jsx)(A,{...o,ref:t,forceMount:r}):"always"===i.type?(0,p.jsx)(k,{...o,ref:t}):null});S.displayName=E;var R=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=y(E,e.__scopeScrollArea),[a,c]=n.useState(!1);return n.useEffect(()=>{let e=l.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),c(!0)},n=()=>{t=window.setTimeout(()=>c(!1),l.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[l.scrollArea,l.scrollHideDelay]),(0,p.jsx)(i.z,{present:r||a,children:(0,p.jsx)(A,{"data-state":a?"visible":"hidden",...o,ref:t})})}),C=n.forwardRef((e,t)=>{var r;let{forceMount:o,...l}=e,a=y(E,e.__scopeScrollArea),c="horizontal"===e.orientation,u=Y(()=>f("SCROLL_END"),100),[s,f]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>r[e][t]??e,"hidden"));return n.useEffect(()=>{if("idle"===s){let e=window.setTimeout(()=>f("HIDE"),a.scrollHideDelay);return()=>window.clearTimeout(e)}},[s,a.scrollHideDelay,f]),n.useEffect(()=>{let e=a.viewport,t=c?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(f("SCROLL"),u()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[a.viewport,c,f,u]),(0,p.jsx)(i.z,{present:o||"hidden"!==s,children:(0,p.jsx)(k,{"data-state":"hidden"===s?"hidden":"visible",...l,ref:t,onPointerEnter:(0,d.M)(e.onPointerEnter,()=>f("POINTER_ENTER")),onPointerLeave:(0,d.M)(e.onPointerLeave,()=>f("POINTER_LEAVE"))})})}),A=n.forwardRef((e,t)=>{let r=y(E,e.__scopeScrollArea),{forceMount:o,...l}=e,[a,c]=n.useState(!1),u="horizontal"===e.orientation,s=Y(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;c(u?e:t)}},10);return X(r.viewport,s),X(r.content,s),(0,p.jsx)(i.z,{present:o||a,children:(0,p.jsx)(k,{"data-state":a?"visible":"hidden",...l,ref:t})})}),k=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,i=y(E,e.__scopeScrollArea),l=n.useRef(null),a=n.useRef(0),[c,u]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),s=_(c.viewport,c.content),f={...o,sizes:c,onSizesChange:u,hasThumb:!!(s>0&&s<1),onThumbChange:e=>l.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function d(e,t){return function(e,t,r,n="ltr"){let o=I(r),i=t||o/2,l=r.scrollbar.paddingStart+i,a=r.scrollbar.size-r.scrollbar.paddingEnd-(o-i),c=r.content-r.viewport;return V([l,a],"ltr"===n?[0,c]:[-1*c,0])(e)}(e,a.current,c,t)}return"horizontal"===r?(0,p.jsx)(T,{...f,ref:t,onThumbPositionChange:()=>{if(i.viewport&&l.current){let e=F(i.viewport.scrollLeft,c,i.dir);l.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollLeft=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollLeft=d(e,i.dir))}}):"vertical"===r?(0,p.jsx)(L,{...f,ref:t,onThumbPositionChange:()=>{if(i.viewport&&l.current){let e=F(i.viewport.scrollTop,c);l.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollTop=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollTop=d(e))}}):null}),T=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...i}=e,l=y(E,e.__scopeScrollArea),[c,u]=n.useState(),s=n.useRef(null),f=(0,a.e)(t,s,l.onScrollbarXChange);return n.useEffect(()=>{s.current&&u(getComputedStyle(s.current))},[s]),(0,p.jsx)(O,{"data-orientation":"horizontal",...i,ref:f,sizes:r,style:{bottom:0,left:"rtl"===l.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===l.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":I(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(l.viewport){let n=l.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{s.current&&l.viewport&&c&&o({content:l.viewport.scrollWidth,viewport:l.viewport.offsetWidth,scrollbar:{size:s.current.clientWidth,paddingStart:Z(c.paddingLeft),paddingEnd:Z(c.paddingRight)}})}})}),L=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...i}=e,l=y(E,e.__scopeScrollArea),[c,u]=n.useState(),s=n.useRef(null),f=(0,a.e)(t,s,l.onScrollbarYChange);return n.useEffect(()=>{s.current&&u(getComputedStyle(s.current))},[s]),(0,p.jsx)(O,{"data-orientation":"vertical",...i,ref:f,sizes:r,style:{top:0,right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":I(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(l.viewport){let n=l.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{s.current&&l.viewport&&c&&o({content:l.viewport.scrollHeight,viewport:l.viewport.offsetHeight,scrollbar:{size:s.current.clientHeight,paddingStart:Z(c.paddingTop),paddingEnd:Z(c.paddingBottom)}})}})}),[P,M]=v(E),O=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:i,hasThumb:l,onThumbChange:u,onThumbPointerUp:s,onThumbPointerDown:f,onThumbPositionChange:h,onDragScroll:v,onWheelScroll:m,onResize:g,...w}=e,b=y(E,r),[x,S]=n.useState(null),R=(0,a.e)(t,e=>S(e)),C=n.useRef(null),A=n.useRef(""),k=b.viewport,T=i.content-i.viewport,L=(0,c.W)(m),M=(0,c.W)(h),O=Y(g,10);function W(e){C.current&&v({x:e.clientX-C.current.left,y:e.clientY-C.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;x?.contains(t)&&L(e,T)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[k,x,T,L]),n.useEffect(M,[i,M]),X(x,O),X(b.content,O),(0,p.jsx)(P,{scope:r,scrollbar:x,hasThumb:l,onThumbChange:(0,c.W)(u),onThumbPointerUp:(0,c.W)(s),onThumbPositionChange:M,onThumbPointerDown:(0,c.W)(f),children:(0,p.jsx)(o.WV.div,{...w,ref:R,style:{position:"absolute",...w.style},onPointerDown:(0,d.M)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),C.current=x.getBoundingClientRect(),A.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",b.viewport&&(b.viewport.style.scrollBehavior="auto"),W(e))}),onPointerMove:(0,d.M)(e.onPointerMove,W),onPointerUp:(0,d.M)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=A.current,b.viewport&&(b.viewport.style.scrollBehavior=""),C.current=null})})})}),W="ScrollAreaThumb",D=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=M(W,e.__scopeScrollArea);return(0,p.jsx)(i.z,{present:r||o.hasThumb,children:(0,p.jsx)(j,{ref:t,...n})})}),j=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:i,...l}=e,c=y(W,r),u=M(W,r),{onThumbPositionChange:s}=u,f=(0,a.e)(t,e=>u.onThumbChange(e)),h=n.useRef(void 0),v=Y(()=>{h.current&&(h.current(),h.current=void 0)},100);return n.useEffect(()=>{let e=c.viewport;if(e){let t=()=>{if(v(),!h.current){let t=B(e,s);h.current=t,s()}};return s(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[c.viewport,v,s]),(0,p.jsx)(o.WV.div,{"data-state":u.hasThumb?"visible":"hidden",...l,ref:f,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:(0,d.M)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;u.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,d.M)(e.onPointerUp,u.onThumbPointerUp)})});D.displayName=W;var N="ScrollAreaCorner",z=n.forwardRef((e,t)=>{let r=y(N,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,p.jsx)(H,{...e,ref:t}):null});z.displayName=N;var H=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...i}=e,l=y(N,r),[a,c]=n.useState(0),[u,s]=n.useState(0),f=!!(a&&u);return X(l.scrollbarX,()=>{let e=l.scrollbarX?.offsetHeight||0;l.onCornerHeightChange(e),s(e)}),X(l.scrollbarY,()=>{let e=l.scrollbarY?.offsetWidth||0;l.onCornerWidthChange(e),c(e)}),f?(0,p.jsx)(o.WV.div,{...i,ref:t,style:{width:a,height:u,position:"absolute",right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:0,...e.style}}):null});function Z(e){return e?parseInt(e,10):0}function _(e,t){let r=e/t;return isNaN(r)?0:r}function I(e){let t=_(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function F(e,t,r="ltr"){let n=I(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,i=t.scrollbar.size-o,l=t.content-t.viewport,a=(0,f.u)(e,"ltr"===r?[0,l]:[-1*l,0]);return V([0,l],[0,i-n])(a)}function V(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var B=(e,t=()=>{})=>{let r={left:e.scrollLeft,top:e.scrollTop},n=0;return function o(){let i={left:e.scrollLeft,top:e.scrollTop},l=r.left!==i.left,a=r.top!==i.top;(l||a)&&t(),r=i,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function Y(e,t){let r=(0,c.W)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function X(e,t){let r=(0,c.W)(t);(0,s.b)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var $=w,q=x,U=z},63085:(e,t,r)=>{r.d(t,{t:()=>i});var n=r(3729),o=r(16069);function i(e){let[t,r]=n.useState(void 0);return(0,o.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}}};