"use strict";exports.id=66,exports.ids=[66],exports.modules={45904:(e,t,n)=>{n.d(t,{Ry:()=>u});var r=new WeakMap,o=new WeakMap,i={},a=0,l=function(e){return e&&(e.host||l(e.parentNode))},c=function(e,t,n,c){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=l(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var s=i[n],f=[],d=new Set,p=new Set(u),h=function(e){!e||d.has(e)||(d.add(e),h(e.parentNode))};u.forEach(h);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(d.has(e))m(e);else try{var t=e.getAttribute(c),i=null!==t&&"false"!==t,a=(r.get(e)||0)+1,l=(s.get(e)||0)+1;r.set(e,a),s.set(e,l),f.push(e),1===a&&i&&o.set(e,!0),1===l&&e.setAttribute(n,"true"),i||e.setAttribute(c,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),d.clear(),a++,function(){f.forEach(function(e){var t=r.get(e)-1,i=s.get(e)-1;r.set(e,t),s.set(e,i),t||(o.has(e)||e.removeAttribute(c),o.delete(e)),i||e.removeAttribute(n)}),--a||(r=new WeakMap,r=new WeakMap,o=new WeakMap,i={})}},u=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r,o=Array.from(Array.isArray(e)?e:[e]),i=t||(r=e,"undefined"==typeof document?null:(Array.isArray(r)?r[0]:r).ownerDocument.body);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),c(o,i,n,"aria-hidden")):function(){return null}}},7060:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},62312:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},25390:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},97751:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},25545:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},99046:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},96885:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},91991:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},82965:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]])},57320:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},13746:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},83389:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]])},71210:(e,t,n)=>{n.d(t,{Z:()=>I});var r,o=function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create,Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,n(3729)),l="right-scroll-bar-position",c="width-before-scroll-bar";function u(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var s=a.useEffect,f=new WeakMap;function d(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,i=(void 0===t&&(t=d),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),n={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),n}}}});return i.options=o({async:!0,ssr:!1},e),i}(),h=function(){},m=a.forwardRef(function(e,t){var n,r,l,c,d=a.useRef(null),m=a.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),v=m[0],g=m[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,A=e.shards,S=e.sideCar,R=e.noRelative,k=e.noIsolation,C=e.inert,M=e.allowPinchZoom,T=e.as,L=e.gapMode,O=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),P=(n=[d,t],r=function(e){return n.forEach(function(t){return u(t,e)})},(l=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return l.value},set current(value){var e=l.value;e!==value&&(l.value=value,l.callback(value,e))}}}})[0]).callback=r,c=l.facade,s(function(){var e=f.get(c);if(e){var t=new Set(e),r=new Set(n),o=c.current;t.forEach(function(e){r.has(e)||u(e,null)}),r.forEach(function(e){t.has(e)||u(e,o)})}f.set(c,n)},[n]),c),N=o(o({},O),v);return a.createElement(a.Fragment,null,E&&a.createElement(S,{sideCar:p,removeScrollBar:x,shards:A,noRelative:R,noIsolation:k,inert:C,setCallbacks:g,allowPinchZoom:!!M,lockRef:d,gapMode:L}),y?a.cloneElement(a.Children.only(w),o(o({},N),{ref:P})):a.createElement(void 0===T?"div":T,o({},N,{className:b,ref:P}),w))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:c,zeroRight:l};var v=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,o({},n))};v.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},w=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},x=w(),E="data-scroll-locked",A=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(E,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(E,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},S=function(){var e=parseInt(document.body.getAttribute(E)||"0",10);return isFinite(e)?e:0},R=function(){a.useEffect(function(){return document.body.setAttribute(E,(S()+1).toString()),function(){var e=S()-1;e<=0?document.body.removeAttribute(E):document.body.setAttribute(E,e.toString())}},[])},k=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;R();var i=a.useMemo(function(){return b},[o]);return a.createElement(x,{styles:A(i,!t,o,n?"":"!important")})},C=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},M=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),T(e,r)){var o=L(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},T=function(e,t){return"v"===e?C(t,"overflowY"):C(t,"overflowX")},L=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},O=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,c=n.target,u=t.contains(c),s=!1,f=l>0,d=0,p=0;do{if(!c)break;var h=L(e,c),m=h[0],v=h[1]-h[2]-a*m;(m||v)&&T(e,c)&&(d+=v,p+=m);var g=c.parentNode;c=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!u&&c!==document.body||u&&(t.contains(c)||t===c));return f&&(o&&1>Math.abs(d)||!o&&l>d)?s=!0:!f&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},P=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},N=function(e){return[e.deltaX,e.deltaY]},W=function(e){return e&&"current"in e?e.current:e},D=0,j=[];let Z=(p.useMedium(function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(D++)[0],i=a.useState(w)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(W),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=P(e),a=n.current,c="deltaX"in e?e.deltaX:a[0]-i[0],u="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,f=Math.abs(c)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=M(f,s);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=M(f,s)),!d)return!1;if(!r.current&&"changedTouches"in e&&(c||u)&&(r.current=o),!o)return!0;var p=r.current||o;return O(p,t,e,"h"===p?c:u,!0)},[]),u=a.useCallback(function(e){if(j.length&&j[j.length-1]===i){var n="deltaY"in e?N(e):P(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(W).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?c(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),f=a.useCallback(function(e){n.current=P(e),r.current=void 0},[]),d=a.useCallback(function(t){s(t.type,N(t),t.target,c(t,e.lockRef.current))},[]),p=a.useCallback(function(t){s(t.type,P(t),t.target,c(t,e.lockRef.current))},[]);a.useEffect(function(){return j.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",u,!1),document.addEventListener("touchmove",u,!1),document.addEventListener("touchstart",f,!1),function(){j=j.filter(function(e){return e!==i}),document.removeEventListener("wheel",u,!1),document.removeEventListener("touchmove",u,!1),document.removeEventListener("touchstart",f,!1)}},[]);var h=e.removeScrollBar,m=e.inert;return a.createElement(a.Fragment,null,m?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?a.createElement(k,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),v);var F=a.forwardRef(function(e,t){return a.createElement(m,o({},e,{ref:t,sideCar:Z}))});F.classNames=m.classNames;let I=F},2633:(e,t,n)=>{n.d(t,{u:()=>r});function r(e,[t,n]){return Math.min(n,Math.max(t,e))}},3975:(e,t,n)=>{n.d(t,{gm:()=>i});var r=n(3729);n(95344);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},1106:(e,t,n)=>{n.d(t,{EW:()=>i});var r=n(3729),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},27386:(e,t,n)=>{n.d(t,{M:()=>f});var r=n(3729),o=n(31405),i=n(62409),a=n(2256),l=n(95344),c="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},f=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:f=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,b]=r.useState(null),x=(0,a.W)(v),E=(0,a.W)(g),A=r.useRef(null),S=(0,o.e)(t,e=>b(e)),R=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(f){let e=function(e){if(R.paused||!w)return;let t=e.target;w.contains(t)?A.current=t:h(A.current,{select:!0})},t=function(e){if(R.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||h(A.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[f,w,R.paused]),r.useEffect(()=>{if(w){m.add(R);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(c,s);w.addEventListener(c,x),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(d(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(c,x),setTimeout(()=>{let t=new CustomEvent(u,s);w.addEventListener(u,E),w.dispatchEvent(t),t.defaultPrevented||h(e??document.body,{select:!0}),w.removeEventListener(u,E),m.remove(R)},0)}}},[w,x,E,R]);let k=r.useCallback(e=>{if(!n&&!f||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=d(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,f,R.paused]);return(0,l.jsx)(i.WV.div,{tabIndex:-1,...y,ref:S,onKeyDown:k})});function d(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}f.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},99048:(e,t,n)=>{n.d(t,{M:()=>c});var r,o=n(3729),i=n(16069),a=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function c(e){let[t,n]=o.useState(a());return(0,i.b)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},37574:(e,t,n)=>{n.d(t,{ee:()=>eH,Eh:()=>ez,VY:()=>eB,fC:()=>eI,D7:()=>eA});var r=n(3729);let o=["top","right","bottom","left"],i=Math.min,a=Math.max,l=Math.round,c=Math.floor,u=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>f[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function b(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function E(e,t,n){let r,{reference:o,floating:i}=e,a=g(t),l=m(g(t)),c=v(l),u=p(t),s="y"===a,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,y=o[c]/2-i[c]/2;switch(u){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[l]-=y*(n&&s?-1:1);break;case"end":r[l]+=y*(n&&s?-1:1)}return r}let A=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),c=await (null==a.isRTL?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:f}=E(u,r,c),d=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:m}=l[n],{x:v,y:g,data:y,reset:w}=await m({x:s,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:u,platform:a,elements:{reference:e,floating:t}});s=null!=v?v:s,f=null!=g?g:f,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(u=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:f}=E(u,d,c)),n=-1)}return{x:s,y:f,placement:d,strategy:o,middlewareData:p}};async function S(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:c}=e,{boundary:u="clippingAncestors",rootBoundary:s="viewport",elementContext:f="floating",altBoundary:p=!1,padding:h=0}=d(t,e),m=b(h),v=l[p?"floating"===f?"reference":"floating":f],g=x(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:u,rootBoundary:s,strategy:c})),y="floating"===f?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),E=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},A=x(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:w,strategy:c}):y);return{top:(g.top-A.top+m.top)/E.y,bottom:(A.bottom-g.bottom+m.bottom)/E.y,left:(g.left-A.left+m.left)/E.x,right:(A.right-g.right+m.right)/E.x}}function R(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function k(e){return o.some(t=>e[t]>=0)}async function C(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=p(n),l=h(n),c="y"===g(n),u=["left","top"].includes(a)?-1:1,s=i&&c?-1:1,f=d(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:y}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return l&&"number"==typeof y&&(v="end"===l?-1*y:y),c?{x:v*s,y:m*u}:{x:m*u,y:v*s}}function M(e){var t;return t=0,"#document"}function T(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function L(e){var t,n;return null==(t=(n=0,e.document||window.document))?void 0:t.documentElement}function O(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=D(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function P(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function N(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function W(e){return["html","body","#document"].includes(M(e))}function D(e){return T(e).getComputedStyle(e)}function j(e){return{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Z(e){return"html"===M(e)?e:e.assignedSlot||e.parentNode||L(e)}function F(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=Z(t);return W(n)?t.ownerDocument?t.ownerDocument.body:t.body:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=T(o);if(i){let e=I(a);return t.concat(a,a.visualViewport||[],O(o)?o:[],e&&n?F(e):[])}return t.concat(o,F(o,[],n))}function I(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function H(e){return e.contextElement}function B(e){return H(e),u(1)}let z=u(0);function V(e){let t=T(e);return N()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:z}function $(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=H(e),l=u(1);t&&(r||(l=B(e)));let c=(void 0===(o=n)&&(o=!1),r&&(!o||r===T(a))&&o)?V(a):u(0),s=(i.left+c.x)/l.x,f=(i.top+c.y)/l.y,d=i.width/l.x,p=i.height/l.y;if(a){let e=T(a),t=I(e);for(;t&&r&&r!==e;){let n=B(t),r=t.getBoundingClientRect(),o=D(t),i=r.left+(t.clientLeft+parseFloat(o.paddingLeft))*n.x,a=r.top+(t.clientTop+parseFloat(o.paddingTop))*n.y;s*=n.x,f*=n.y,d*=n.x,p*=n.y,s+=i,f+=a,t=I(e=T(t))}}return x({width:d,height:p,x:s,y:f})}function Y(e,t){let n=j(e).scrollLeft;return t?t.left+n:$(L(e)).left+n}function q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:Y(e,r)),y:r.top+t.scrollTop}}function X(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=T(e),r=L(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,c=0;if(o){i=o.width,a=o.height;let e=N();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,c=o.offsetTop)}return{width:i,height:a,x:l,y:c}}(e,n);else if("document"===t)r=function(e){let t=L(e),n=j(e),r=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+Y(e),c=-n.scrollTop;return"rtl"===D(r).direction&&(l+=a(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:c}}(L(e));else{let n=V(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return x(r)}function K(e,t){let n=T(e);if(P(e))return n;{let t=Z(e);for(;t&&!W(t);)t=Z(t);return n}}let _=async function(e){let t=this.getOffsetParent||K,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=L(t),o="fixed"===n,i=$(e,!0,o,t),a={scrollLeft:0,scrollTop:0},l=u(0);if(!o){("body"!==M(t)||O(r))&&(a=j(t));r&&(l.x=Y(r))}o&&r&&(l.x=Y(r));let c=!r||o?u(0):q(r,a);return{x:i.left+a.scrollLeft-l.x-c.x,y:i.top+a.scrollTop-l.y-c.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},U={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=L(r),l=!!t&&P(t.floating);if(r===a||l&&i)return n;let c={scrollLeft:0,scrollTop:0},s=u(1),f=u(0);i||("body"!==M(r)||O(a))&&(c=j(r));let d=!a||i?u(0):q(a,c,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-c.scrollLeft*s.x+f.x+d.x,y:n.y*s.y-c.scrollTop*s.y+f.y+d.y}},getDocumentElement:L,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?P(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=F(e,[],!1).filter(e=>!1);return"fixed"===D(e).position&&Z(e),t.set(e,r),r}(t,this._c):[].concat(n),r],c=l[0],u=l.reduce((e,n)=>{let r=X(t,n,o);return e.top=a(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=a(r.left,e.left),e},X(t,c,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:K,getElementRects:_,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=function(e){let t=D(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=n,i=r,a=l(n)!==o||l(r)!==i;return a&&(n=o,r=i),{width:n,height:r,$:a}}(e);return{width:t,height:n}},getScale:B,isElement:function(e){return!1},isRTL:function(e){return"rtl"===D(e).direction}};function G(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let J=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:c,elements:u,middlewareData:s}=t,{element:f,padding:p=0}=d(e,t)||{};if(null==f)return{};let y=b(p),w={x:n,y:r},x=m(g(o)),E=v(x),A=await c.getDimensions(f),S="y"===x,R=S?"clientHeight":"clientWidth",k=l.reference[E]+l.reference[x]-w[x]-l.floating[E],C=w[x]-l.reference[x],M=await (null==c.getOffsetParent?void 0:c.getOffsetParent(f)),T=M?M[R]:0;T&&await (null==c.isElement?void 0:c.isElement(M))||(T=u.floating[R]||l.floating[E]);let L=T/2-A[E]/2-1,O=i(y[S?"top":"left"],L),P=i(y[S?"bottom":"right"],L),N=T-A[E]-P,W=T/2-A[E]/2+(k/2-C/2),D=a(O,i(W,N)),j=!s.arrow&&null!=h(o)&&W!==D&&l.reference[E]/2-(W<O?O:P)-A[E]/2<0,Z=j?W<O?W-O:W-N:0;return{[x]:w[x]+Z,data:{[x]:D,centerOffset:W-D-Z,...j&&{alignmentOffset:Z}},reset:j}}}),Q=(e,t,n)=>{let r=new Map,o={platform:U,...n},i={...o.platform,_c:r};return A(e,t,{...o,platform:i})};var ee=n(81202),et="undefined"!=typeof document?r.useLayoutEffect:function(){};function en(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!en(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!en(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function er(e,t){return Math.round(1*t)/1}function eo(e){let t=r.useRef(e);return et(()=>{t.current=e}),t}let ei=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?J({element:n.current,padding:r}).fn(t):{}:n?J({element:n,padding:r}).fn(t):{}}}),ea=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,c=await C(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+c.x,y:i+c.y,data:{...c,placement:a}}}}}(e),options:[e,t]}),el=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:c=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=d(e,t),f={x:n,y:r},h=await S(t,s),v=g(p(o)),y=m(v),w=f[y],b=f[v];if(l){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+h[e],r=w-h[t];w=a(n,i(w,r))}if(c){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=b+h[e],r=b-h[t];b=a(n,i(b,r))}let x=u.fn({...t,[y]:w,[v]:b});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[y]:l,[v]:c}}}}}}(e),options:[e,t]}),ec=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:c=!0,crossAxis:u=!0}=d(e,t),s={x:n,y:r},f=g(o),h=m(f),v=s[h],y=s[f],w=d(l,t),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(c){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+b.mainAxis,n=i.reference[h]+i.reference[e]-b.mainAxis;v<t?v=t:v>n&&(v=n)}if(u){var x,E;let e="y"===h?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(x=a.offset)?void 0:x[f])||0)+(t?0:b.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(E=a.offset)?void 0:E[f])||0)-(t?b.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:v,[f]:y}}}}(e),options:[e,t]}),eu=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:c,rects:u,initialPlacement:s,platform:f,elements:b}=t,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:A,fallbackStrategy:R="bestFit",fallbackAxisSideDirection:k="none",flipAlignment:C=!0,...M}=d(e,t);if(null!=(n=c.arrow)&&n.alignmentOffset)return{};let T=p(l),L=g(s),O=p(s)===s,P=await (null==f.isRTL?void 0:f.isRTL(b.floating)),N=A||(O||!C?[w(s)]:function(e){let t=w(e);return[y(e),t,y(t)]}(s)),W="none"!==k;!A&&W&&N.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(y)))),i}(s,C,k,P));let D=[s,...N],j=await S(t,M),Z=[],F=(null==(r=c.flip)?void 0:r.overflows)||[];if(x&&Z.push(j[T]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=m(g(e)),i=v(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=w(a)),[a,w(a)]}(l,u,P);Z.push(j[e[0]],j[e[1]])}if(F=[...F,{placement:l,overflows:Z}],!Z.every(e=>e<=0)){let e=((null==(o=c.flip)?void 0:o.index)||0)+1,t=D[e];if(t&&(!("alignment"===E&&L!==g(t))||F.every(e=>e.overflows[0]>0&&g(e.placement)===L)))return{data:{index:e,overflows:F},reset:{placement:t}};let n=null==(i=F.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(R){case"bestFit":{let e=null==(a=F.filter(e=>{if(W){let t=g(e.placement);return t===L||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=s}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),es=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l;let{placement:c,rects:u,platform:s,elements:f}=t,{apply:m=()=>{},...v}=d(e,t),y=await S(t,v),w=p(c),b=h(c),x="y"===g(c),{width:E,height:A}=u.floating;"top"===w||"bottom"===w?(o=w,l=b===(await (null==s.isRTL?void 0:s.isRTL(f.floating))?"start":"end")?"left":"right"):(l=w,o="end"===b?"top":"bottom");let R=A-y.top-y.bottom,k=E-y.left-y.right,C=i(A-y[o],R),M=i(E-y[l],k),T=!t.middlewareData.shift,L=C,O=M;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(O=k),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(L=R),T&&!b){let e=a(y.left,0),t=a(y.right,0),n=a(y.top,0),r=a(y.bottom,0);x?O=E-2*(0!==e||0!==t?e+t:a(y.left,y.right)):L=A-2*(0!==n||0!==r?n+r:a(y.top,y.bottom))}await m({...t,availableWidth:O,availableHeight:L});let P=await s.getDimensions(f.floating);return E!==P.width||A!==P.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ef=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=d(e,t);switch(r){case"referenceHidden":{let e=R(await S(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:k(e)}}}case"escaped":{let e=R(await S(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:k(e)}}}default:return{}}}}}(e),options:[e,t]}),ed=(e,t)=>({...ei(e),options:[e,t]});var ep=n(62409),eh=n(95344),em=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eh.jsx)(ep.WV.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eh.jsx)("polygon",{points:"0,0 30,0 15,10"})})});em.displayName="Arrow";var ev=n(31405),eg=n(98462),ey=n(2256),ew=n(16069),eb=n(63085),ex="Popper",[eE,eA]=(0,eg.b)(ex),[eS,eR]=eE(ex),ek=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eh.jsx)(eS,{scope:t,anchor:o,onAnchorChange:i,children:n})};ek.displayName=ex;var eC="PopperAnchor",eM=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=eR(eC,n),l=r.useRef(null),c=(0,ev.e)(t,l);return r.useEffect(()=>{a.onAnchorChange(o?.current||l.current)}),o?null:(0,eh.jsx)(ep.WV.div,{...i,ref:c})});eM.displayName=eC;var eT="PopperContent",[eL,eO]=eE(eT),eP=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:l=0,align:u="center",alignOffset:s=0,arrowPadding:f=0,avoidCollisions:d=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:y,...w}=e,b=eR(eT,n),[x,E]=r.useState(null),A=(0,ev.e)(t,e=>E(e)),[S,R]=r.useState(null),k=(0,eb.t)(S),C=k?.width??0,M=k?.height??0,T="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},O=Array.isArray(p)?p:[p],P=O.length>0,N={padding:T,boundary:O.filter(ej),altBoundary:P},{refs:W,floatingStyles:D,placement:j,isPositioned:Z,middlewareData:I}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:l}={},transform:c=!0,whileElementsMounted:u,open:s}=e,[f,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);en(p,o)||h(o);let[m,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==A.current&&(A.current=e,v(e))},[]),b=r.useCallback(e=>{e!==S.current&&(S.current=e,y(e))},[]),x=a||m,E=l||g,A=r.useRef(null),S=r.useRef(null),R=r.useRef(f),k=null!=u,C=eo(u),M=eo(i),T=eo(s),L=r.useCallback(()=>{if(!A.current||!S.current)return;let e={placement:t,strategy:n,middleware:p};M.current&&(e.platform=M.current),Q(A.current,S.current,e).then(e=>{let t={...e,isPositioned:!1!==T.current};O.current&&!en(R.current,t)&&(R.current=t,ee.flushSync(()=>{d(t)}))})},[p,t,n,M,T]);et(()=>{!1===s&&R.current.isPositioned&&(R.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);let O=r.useRef(!1);et(()=>(O.current=!0,()=>{O.current=!1}),[]),et(()=>{if(x&&(A.current=x),E&&(S.current=E),x&&E){if(C.current)return C.current(x,E,L);L()}},[x,E,L,C,k]);let P=r.useMemo(()=>({reference:A,floating:S,setReference:w,setFloating:b}),[w,b]),N=r.useMemo(()=>({reference:x,floating:E}),[x,E]),W=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!N.floating)return e;let t=er(N.floating,f.x),r=er(N.floating,f.y);return c?{...e,transform:"translate("+t+"px, "+r+"px)",...(N.floating,!1)}:{position:n,left:t,top:r}},[n,c,N.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:L,refs:P,elements:N,floatingStyles:W}),[f,L,P,N,W])}({strategy:"fixed",placement:o+("center"!==u?"-"+u:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:u=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=H(e),h=l||u?[...p?F(p):[],...F(t)]:[];h.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=p&&f?function(e,t){let n,r=null,o=L(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function u(s,f){void 0===s&&(s=!1),void 0===f&&(f=1),l();let d=e.getBoundingClientRect(),{left:p,top:h,width:m,height:v}=d;if(s||t(),!m||!v)return;let g=c(h),y=c(o.clientWidth-(p+m)),w={rootMargin:-g+"px "+-y+"px "+-c(o.clientHeight-(h+v))+"px "+-c(p)+"px",threshold:a(0,i(1,f))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==f){if(!b)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||G(d,e.getBoundingClientRect())||u(),b=!1}try{r=new IntersectionObserver(x,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),l}(p,n):null,v=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!d&&g.observe(p),g.observe(t));let y=d?$(e):null;return d&&function t(){let r=$(e);y&&!G(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{l&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,d&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===g}),elements:{reference:b.anchor},middleware:[ea({mainAxis:l+M,alignmentAxis:s}),d&&el({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?ec():void 0,...N}),d&&eu({...N}),es({...N,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),S&&ed({element:S,padding:f}),eZ({arrowWidth:C,arrowHeight:M}),v&&ef({strategy:"referenceHidden",...N})]}),[B,z]=eF(j),V=(0,ey.W)(y);(0,ew.b)(()=>{Z&&V?.()},[Z,V]);let Y=I.arrow?.x,q=I.arrow?.y,X=I.arrow?.centerOffset!==0,[K,_]=r.useState();return(0,ew.b)(()=>{x&&_(window.getComputedStyle(x).zIndex)},[x]),(0,eh.jsx)("div",{ref:W.setFloating,"data-radix-popper-content-wrapper":"",style:{...D,transform:Z?D.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:K,"--radix-popper-transform-origin":[I.transformOrigin?.x,I.transformOrigin?.y].join(" "),...I.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eh.jsx)(eL,{scope:n,placedSide:B,onArrowChange:R,arrowX:Y,arrowY:q,shouldHideArrow:X,children:(0,eh.jsx)(ep.WV.div,{"data-side":B,"data-align":z,...w,ref:A,style:{...w.style,animation:Z?void 0:"none"}})})})});eP.displayName=eT;var eN="PopperArrow",eW={top:"bottom",right:"left",bottom:"top",left:"right"},eD=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eO(eN,n),i=eW[o.placedSide];return(0,eh.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eh.jsx)(em,{...r,ref:t,style:{...r.style,display:"block"}})})});function ej(e){return null!==e}eD.displayName=eN;var eZ=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[c,u]=eF(n),s={start:"0%",center:"50%",end:"100%"}[u],f=(o.arrow?.x??0)+a/2,d=(o.arrow?.y??0)+l/2,p="",h="";return"bottom"===c?(p=i?s:`${f}px`,h=`${-l}px`):"top"===c?(p=i?s:`${f}px`,h=`${r.floating.height+l}px`):"right"===c?(p=`${-l}px`,h=i?s:`${d}px`):"left"===c&&(p=`${r.floating.width+l}px`,h=i?s:`${d}px`),{data:{x:p,y:h}}}});function eF(e){let[t,n="center"]=e.split("-");return[t,n]}var eI=ek,eH=eM,eB=eP,ez=eD},63085:(e,t,n)=>{n.d(t,{t:()=>i});var r=n(3729),o=n(16069);function i(e){let[t,n]=r.useState(void 0);return(0,o.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}}};