"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[150],{5859:function(e,t,n){n.d(t,{Ry:function(){return u}});var r=new WeakMap,o=new WeakMap,i={},a=0,l=function(e){return e&&(e.host||l(e.parentNode))},c=function(e,t,n,c){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=l(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var f=i[n],s=[],d=new Set,p=new Set(u),h=function(e){!e||d.has(e)||(d.add(e),h(e.parentNode))};u.forEach(h);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(d.has(e))m(e);else try{var t=e.getAttribute(c),i=null!==t&&"false"!==t,a=(r.get(e)||0)+1,l=(f.get(e)||0)+1;r.set(e,a),f.set(e,l),s.push(e),1===a&&i&&o.set(e,!0),1===l&&e.setAttribute(n,"true"),i||e.setAttribute(c,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),d.clear(),a++,function(){s.forEach(function(e){var t=r.get(e)-1,i=f.get(e)-1;r.set(e,t),f.set(e,i),t||(o.has(e)||e.removeAttribute(c),o.delete(e)),i||e.removeAttribute(n)}),--a||(r=new WeakMap,r=new WeakMap,o=new WeakMap,i={})}},u=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),c(r,o,n,"aria-hidden")):function(){return null}}},3008:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2442:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},3523:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},7158:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},6141:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},7332:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},5817:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},4056:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},774:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]])},4900:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},9409:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3223:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]])},3386:function(e,t,n){n.d(t,{Z:function(){return K}});var r,o,i,a,l,c,u=function(){return(u=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function f(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}"function"==typeof SuppressedError&&SuppressedError;var s=n(2265),d="right-scroll-bar-position",p="width-before-scroll-bar";function h(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var m="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,v=new WeakMap,g=(void 0===o&&(o={}),(void 0===i&&(i=function(e){return e}),a=[],l=!1,c={read:function(){if(l)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return a.length?a[a.length-1]:null},useMedium:function(e){var t=i(e,l);return a.push(t),function(){a=a.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(l=!0;a.length;){var t=a;a=[],t.forEach(e)}a={push:function(t){return e(t)},filter:function(){return a}}},assignMedium:function(e){l=!0;var t=[];if(a.length){var n=a;a=[],n.forEach(e),t=a}var r=function(){var n=t;t=[],n.forEach(e)},o=function(){return Promise.resolve().then(r)};o(),a={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),a}}}}).options=u({async:!0,ssr:!1},o),c),y=function(){},w=s.forwardRef(function(e,t){var n,r,o,i,a=s.useRef(null),l=s.useState({onScrollCapture:y,onWheelCapture:y,onTouchMoveCapture:y}),c=l[0],d=l[1],p=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,A=e.shards,R=e.sideCar,k=e.noRelative,S=e.noIsolation,C=e.inert,L=e.allowPinchZoom,T=e.as,M=e.gapMode,P=f(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),O=(n=[a,t],r=function(e){return n.forEach(function(t){return h(t,e)})},(o=(0,s.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,m(function(){var e=v.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||h(e,null)}),r.forEach(function(e){t.has(e)||h(e,o)})}v.set(i,n)},[n]),i),N=u(u({},P),c);return s.createElement(s.Fragment,null,E&&s.createElement(R,{sideCar:g,removeScrollBar:x,shards:A,noRelative:k,noIsolation:S,inert:C,setCallbacks:d,allowPinchZoom:!!L,lockRef:a,gapMode:M}),p?s.cloneElement(s.Children.only(w),u(u({},N),{ref:O})):s.createElement(void 0===T?"div":T,u({},N,{className:b,ref:O}),w))});w.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},w.classNames={fullWidth:p,zeroRight:d};var b=function(e){var t=e.sideCar,n=f(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return s.createElement(r,u({},n))};b.isSideCarExport=!0;var x=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},E=function(){var e=x();return function(t,n){s.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},A=function(){var e=E();return function(t){return e(t.styles,t.dynamic),null}},R={left:0,top:0,right:0,gap:0},k=function(e){return parseInt(e||"",10)||0},S=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[k(n),k(r),k(o)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return R;var t=S(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},L=A(),T="data-scroll-locked",M=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(T,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(d," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(p," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(d," .").concat(d," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(p," .").concat(p," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(T,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},P=function(){var e=parseInt(document.body.getAttribute(T)||"0",10);return isFinite(e)?e:0},O=function(){s.useEffect(function(){return document.body.setAttribute(T,(P()+1).toString()),function(){var e=P()-1;e<=0?document.body.removeAttribute(T):document.body.setAttribute(T,e.toString())}},[])},N=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;O();var i=s.useMemo(function(){return C(o)},[o]);return s.createElement(L,{styles:M(i,!t,o,n?"":"!important")})},W=!1;if("undefined"!=typeof window)try{var D=Object.defineProperty({},"passive",{get:function(){return W=!0,!0}});window.addEventListener("test",D,D),window.removeEventListener("test",D,D)}catch(e){W=!1}var F=!!W&&{passive:!1},Z=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},j=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),H(e,r)){var o=I(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},H=function(e,t){return"v"===e?Z(t,"overflowY"):Z(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},B=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,c=n.target,u=t.contains(c),f=!1,s=l>0,d=0,p=0;do{if(!c)break;var h=I(e,c),m=h[0],v=h[1]-h[2]-a*m;(m||v)&&H(e,c)&&(d+=v,p+=m);var g=c.parentNode;c=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!u&&c!==document.body||u&&(t.contains(c)||t===c));return s&&(o&&1>Math.abs(d)||!o&&l>d)?f=!0:!s&&(o&&1>Math.abs(p)||!o&&-l>p)&&(f=!0),f},V=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},z=function(e){return[e.deltaX,e.deltaY]},$=function(e){return e&&"current"in e?e.current:e},Y=0,_=[],q=(g.useMedium(function(e){var t=s.useRef([]),n=s.useRef([0,0]),r=s.useRef(),o=s.useState(Y++)[0],i=s.useState(A)[0],a=s.useRef(e);s.useEffect(function(){a.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map($),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=s.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=V(e),l=n.current,c="deltaX"in e?e.deltaX:l[0]-i[0],u="deltaY"in e?e.deltaY:l[1]-i[1],f=e.target,s=Math.abs(c)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===s&&"range"===f.type)return!1;var d=j(s,f);if(!d)return!0;if(d?o=s:(o="v"===s?"h":"v",d=j(s,f)),!d)return!1;if(!r.current&&"changedTouches"in e&&(c||u)&&(r.current=o),!o)return!0;var p=r.current||o;return B(p,t,e,"h"===p?c:u,!0)},[]),c=s.useCallback(function(e){if(_.length&&_[_.length-1]===i){var n="deltaY"in e?z(e):V(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map($).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),u=s.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),f=s.useCallback(function(e){n.current=V(e),r.current=void 0},[]),d=s.useCallback(function(t){u(t.type,z(t),t.target,l(t,e.lockRef.current))},[]),p=s.useCallback(function(t){u(t.type,V(t),t.target,l(t,e.lockRef.current))},[]);s.useEffect(function(){return _.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",c,F),document.addEventListener("touchmove",c,F),document.addEventListener("touchstart",f,F),function(){_=_.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,F),document.removeEventListener("touchmove",c,F),document.removeEventListener("touchstart",f,F)}},[]);var h=e.removeScrollBar,m=e.inert;return s.createElement(s.Fragment,null,m?s.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?s.createElement(N,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),b),X=s.forwardRef(function(e,t){return s.createElement(w,u({},e,{ref:t,sideCar:q}))});X.classNames=w.classNames;var K=X},760:function(e,t,n){n.d(t,{u:function(){return r}});function r(e,[t,n]){return Math.min(n,Math.max(t,e))}},5400:function(e,t,n){n.d(t,{gm:function(){return i}});var r=n(2265);n(7437);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},1244:function(e,t,n){n.d(t,{EW:function(){return i}});var r=n(2265),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2759:function(e,t,n){let r;n.d(t,{M:function(){return d}});var o=n(2265),i=n(2210),a=n(9381),l=n(6459),c=n(7437),u="focusScope.autoFocusOnMount",f="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:d,onUnmountAutoFocus:g,...y}=e,[w,b]=o.useState(null),x=(0,l.W)(d),E=(0,l.W)(g),A=o.useRef(null),R=(0,i.e)(t,e=>b(e)),k=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(k.paused||!w)return;let t=e.target;w.contains(t)?A.current=t:m(A.current,{select:!0})},t=function(e){if(k.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||m(A.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,w,k.paused]),o.useEffect(()=>{if(w){v.add(k);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,x),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(m(r,{select:t}),document.activeElement!==n)return}(p(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(w))}return()=>{w.removeEventListener(u,x),setTimeout(()=>{let t=new CustomEvent(f,s);w.addEventListener(f,E),w.dispatchEvent(t),t.defaultPrevented||m(e??document.body,{select:!0}),w.removeEventListener(f,E),v.remove(k)},0)}}},[w,x,E,k]);let S=o.useCallback(e=>{if(!n&&!r||k.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=p(e);return[h(t,e),h(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&m(i,{select:!0})):(e.preventDefault(),n&&m(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,k.paused]);return(0,c.jsx)(a.WV.div,{tabIndex:-1,...y,ref:R,onKeyDown:S})});function p(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function h(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function m(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var v=(r=[],{add(e){let t=r[0];e!==t&&t?.pause(),(r=g(r,e)).unshift(e)},remove(e){r=g(r,e),r[0]?.resume()}});function g(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},966:function(e,t,n){n.d(t,{M:function(){return c}});var r,o=n(2265),i=n(5655),a=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function c(e){let[t,n]=o.useState(a());return(0,i.b)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},3995:function(e,t,n){n.d(t,{ee:function(){return eU},Eh:function(){return eJ},VY:function(){return eG},fC:function(){return eK},D7:function(){return eN}});var r=n(2265);let o=["top","right","bottom","left"],i=Math.min,a=Math.max,l=Math.round,c=Math.floor,u=e=>({x:e,y:e}),f={left:"right",right:"left",bottom:"top",top:"bottom"},s={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>s[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>f[e])}function b(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function E(e,t,n){let r,{reference:o,floating:i}=e,a=g(t),l=m(g(t)),c=v(l),u=p(t),f="y"===a,s=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,y=o[c]/2-i[c]/2;switch(u){case"top":r={x:s,y:o.y-i.height};break;case"bottom":r={x:s,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[l]-=y*(n&&f?-1:1);break;case"end":r[l]+=y*(n&&f?-1:1)}return r}let A=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),c=await (null==a.isRTL?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:f,y:s}=E(u,r,c),d=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:m}=l[n],{x:v,y:g,data:y,reset:w}=await m({x:f,y:s,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:u,platform:a,elements:{reference:e,floating:t}});f=null!=v?v:f,s=null!=g?g:s,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(u=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:f,y:s}=E(u,d,c)),n=-1)}return{x:f,y:s,placement:d,strategy:o,middlewareData:p}};async function R(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:c}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:s="floating",altBoundary:p=!1,padding:h=0}=d(t,e),m=b(h),v=l[p?"floating"===s?"reference":"floating":s],g=x(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:u,rootBoundary:f,strategy:c})),y="floating"===s?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),E=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},A=x(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:w,strategy:c}):y);return{top:(g.top-A.top+m.top)/E.y,bottom:(A.bottom-g.bottom+m.bottom)/E.y,left:(g.left-A.left+m.left)/E.x,right:(A.right-g.right+m.right)/E.x}}function k(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function S(e){return o.some(t=>e[t]>=0)}async function C(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=p(n),l=h(n),c="y"===g(n),u=["left","top"].includes(a)?-1:1,f=i&&c?-1:1,s=d(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:y}="number"==typeof s?{mainAxis:s,crossAxis:0,alignmentAxis:null}:{mainAxis:s.mainAxis||0,crossAxis:s.crossAxis||0,alignmentAxis:s.alignmentAxis};return l&&"number"==typeof y&&(v="end"===l?-1*y:y),c?{x:v*f,y:m*u}:{x:m*u,y:v*f}}function L(){return"undefined"!=typeof window}function T(e){return O(e)?(e.nodeName||"").toLowerCase():"#document"}function M(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function P(e){var t;return null==(t=(O(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function O(e){return!!L()&&(e instanceof Node||e instanceof M(e).Node)}function N(e){return!!L()&&(e instanceof Element||e instanceof M(e).Element)}function W(e){return!!L()&&(e instanceof HTMLElement||e instanceof M(e).HTMLElement)}function D(e){return!!L()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof M(e).ShadowRoot)}function F(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=B(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function Z(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function j(e){let t=H(),n=N(e)?B(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function H(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function I(e){return["html","body","#document"].includes(T(e))}function B(e){return M(e).getComputedStyle(e)}function V(e){return N(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function z(e){if("html"===T(e))return e;let t=e.assignedSlot||e.parentNode||D(e)&&e.host||P(e);return D(t)?t.host:t}function $(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=z(t);return I(n)?t.ownerDocument?t.ownerDocument.body:t.body:W(n)&&F(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=M(o);if(i){let e=Y(a);return t.concat(a,a.visualViewport||[],F(o)?o:[],e&&n?$(e):[])}return t.concat(o,$(o,[],n))}function Y(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function _(e){let t=B(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=W(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,c=l(n)!==i||l(r)!==a;return c&&(n=i,r=a),{width:n,height:r,$:c}}function q(e){return N(e)?e:e.contextElement}function X(e){let t=q(e);if(!W(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=_(t),a=(i?l(n.width):n.width)/r,c=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),c&&Number.isFinite(c)||(c=1),{x:a,y:c}}let K=u(0);function U(e){let t=M(e);return H()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:K}function G(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=q(e),l=u(1);t&&(r?N(r)&&(l=X(r)):l=X(e));let c=(void 0===(o=n)&&(o=!1),r&&(!o||r===M(a))&&o)?U(a):u(0),f=(i.left+c.x)/l.x,s=(i.top+c.y)/l.y,d=i.width/l.x,p=i.height/l.y;if(a){let e=M(a),t=r&&N(r)?M(r):r,n=e,o=Y(n);for(;o&&r&&t!==n;){let e=X(o),t=o.getBoundingClientRect(),r=B(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;f*=e.x,s*=e.y,d*=e.x,p*=e.y,f+=i,s+=a,o=Y(n=M(o))}}return x({width:d,height:p,x:f,y:s})}function J(e,t){let n=V(e).scrollLeft;return t?t.left+n:G(P(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=M(e),r=P(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,c=0;if(o){i=o.width,a=o.height;let e=H();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,c=o.offsetTop)}return{width:i,height:a,x:l,y:c}}(e,n);else if("document"===t)r=function(e){let t=P(e),n=V(e),r=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+J(e),c=-n.scrollTop;return"rtl"===B(r).direction&&(l+=a(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:c}}(P(e));else if(N(t))r=function(e,t){let n=G(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=W(e)?X(e):u(1),a=e.clientWidth*i.x;return{width:a,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{let n=U(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return x(r)}function et(e){return"static"===B(e).position}function en(e,t){if(!W(e)||"fixed"===B(e).position)return null;if(t)return t(e);let n=e.offsetParent;return P(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=M(e);if(Z(e))return n;if(!W(e)){let t=z(e);for(;t&&!I(t);){if(N(t)&&!et(t))return t;t=z(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(T(r))&&et(r);)r=en(r,t);return r&&I(r)&&et(r)&&!j(r)?n:r||function(e){let t=z(e);for(;W(t)&&!I(t);){if(j(t))return t;if(Z(t))break;t=z(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=W(t),o=P(t),i="fixed"===n,a=G(e,!0,i,t),l={scrollLeft:0,scrollTop:0},c=u(0);if(r||!r&&!i){if(("body"!==T(t)||F(o))&&(l=V(t)),r){let e=G(t,!0,i,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else o&&(c.x=J(o))}i&&!r&&o&&(c.x=J(o));let f=!o||r||i?u(0):Q(o,l);return{x:a.left+l.scrollLeft-c.x-f.x,y:a.top+l.scrollTop-c.y-f.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=P(r),l=!!t&&Z(t.floating);if(r===a||l&&i)return n;let c={scrollLeft:0,scrollTop:0},f=u(1),s=u(0),d=W(r);if((d||!d&&!i)&&(("body"!==T(r)||F(a))&&(c=V(r)),W(r))){let e=G(r);f=X(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let p=!a||d||i?u(0):Q(a,c,!0);return{width:n.width*f.x,height:n.height*f.y,x:n.x*f.x-c.scrollLeft*f.x+s.x+p.x,y:n.y*f.y-c.scrollTop*f.y+s.y+p.y}},getDocumentElement:P,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?Z(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=$(e,[],!1).filter(e=>N(e)&&"body"!==T(e)),o=null,i="fixed"===B(e).position,a=i?z(e):e;for(;N(a)&&!I(a);){let t=B(a),n=j(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||F(a)&&!n&&function e(t,n){let r=z(t);return!(r===n||!N(r)||I(r))&&("fixed"===B(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=z(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],c=l[0],u=l.reduce((e,n)=>{let r=ee(t,n,o);return e.top=a(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=a(r.left,e.left),e},ee(t,c,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=_(e);return{width:t,height:n}},getScale:X,isElement:N,isRTL:function(e){return"rtl"===B(e).direction}};function ea(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let el=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:c,elements:u,middlewareData:f}=t,{element:s,padding:p=0}=d(e,t)||{};if(null==s)return{};let y=b(p),w={x:n,y:r},x=m(g(o)),E=v(x),A=await c.getDimensions(s),R="y"===x,k=R?"clientHeight":"clientWidth",S=l.reference[E]+l.reference[x]-w[x]-l.floating[E],C=w[x]-l.reference[x],L=await (null==c.getOffsetParent?void 0:c.getOffsetParent(s)),T=L?L[k]:0;T&&await (null==c.isElement?void 0:c.isElement(L))||(T=u.floating[k]||l.floating[E]);let M=T/2-A[E]/2-1,P=i(y[R?"top":"left"],M),O=i(y[R?"bottom":"right"],M),N=T-A[E]-O,W=T/2-A[E]/2+(S/2-C/2),D=a(P,i(W,N)),F=!f.arrow&&null!=h(o)&&W!==D&&l.reference[E]/2-(W<P?P:O)-A[E]/2<0,Z=F?W<P?W-P:W-N:0;return{[x]:w[x]+Z,data:{[x]:D,centerOffset:W-D-Z,...F&&{alignmentOffset:Z}},reset:F}}}),ec=(e,t,n)=>{let r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return A(e,t,{...o,platform:i})};var eu=n(4887),ef="undefined"!=typeof document?r.useLayoutEffect:function(){};function es(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!es(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!es(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ed(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return ef(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?el({element:n.current,padding:r}).fn(t):{}:n?el({element:n,padding:r}).fn(t):{}}}),ev=(e,t)=>{var n;return{...(void 0===(n=e)&&(n=0),{name:"offset",options:n,async fn(e){var t,r;let{x:o,y:i,placement:a,middlewareData:l}=e,c=await C(e,n);return a===(null==(t=l.offset)?void 0:t.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+c.x,y:i+c.y,data:{...c,placement:a}}}}),options:[e,t]}},eg=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"shift",options:n,async fn(e){let{x:t,y:r,placement:o}=e,{mainAxis:l=!0,crossAxis:c=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...f}=d(n,e),s={x:t,y:r},h=await R(e,f),v=g(p(o)),y=m(v),w=s[y],b=s[v];if(l){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+h[e],r=w-h[t];w=a(n,i(w,r))}if(c){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=b+h[e],r=b-h[t];b=a(n,i(b,r))}let x=u.fn({...e,[y]:w,[v]:b});return{...x,data:{x:x.x-t,y:x.y-r,enabled:{[y]:l,[v]:c}}}}}),options:[e,t]}},ey=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{options:n,fn(e){let{x:t,y:r,placement:o,rects:i,middlewareData:a}=e,{offset:l=0,mainAxis:c=!0,crossAxis:u=!0}=d(n,e),f={x:t,y:r},s=g(o),h=m(s),v=f[h],y=f[s],w=d(l,e),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(c){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+b.mainAxis,n=i.reference[h]+i.reference[e]-b.mainAxis;v<t?v=t:v>n&&(v=n)}if(u){var x,E;let e="y"===h?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[s]-i.floating[e]+(t&&(null==(x=a.offset)?void 0:x[s])||0)+(t?0:b.crossAxis),r=i.reference[s]+i.reference[e]+(t?0:(null==(E=a.offset)?void 0:E[s])||0)-(t?b.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:v,[s]:y}}}),options:[e,t]}},ew=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"flip",options:n,async fn(e){var t,r,o,i,a;let{placement:l,middlewareData:c,rects:u,initialPlacement:f,platform:s,elements:b}=e,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:A,fallbackStrategy:k="bestFit",fallbackAxisSideDirection:S="none",flipAlignment:C=!0,...L}=d(n,e);if(null!=(t=c.arrow)&&t.alignmentOffset)return{};let T=p(l),M=g(f),P=p(f)===f,O=await (null==s.isRTL?void 0:s.isRTL(b.floating)),N=A||(P||!C?[w(f)]:function(e){let t=w(e);return[y(e),t,y(t)]}(f)),W="none"!==S;!A&&W&&N.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(y)))),i}(f,C,S,O));let D=[f,...N],F=await R(e,L),Z=[],j=(null==(r=c.flip)?void 0:r.overflows)||[];if(x&&Z.push(F[T]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=m(g(e)),i=v(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=w(a)),[a,w(a)]}(l,u,O);Z.push(F[e[0]],F[e[1]])}if(j=[...j,{placement:l,overflows:Z}],!Z.every(e=>e<=0)){let e=((null==(o=c.flip)?void 0:o.index)||0)+1,t=D[e];if(t&&(!("alignment"===E&&M!==g(t))||j.every(e=>e.overflows[0]>0&&g(e.placement)===M)))return{data:{index:e,overflows:j},reset:{placement:t}};let n=null==(i=j.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(k){case"bestFit":{let e=null==(a=j.filter(e=>{if(W){let t=g(e.placement);return t===M||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=f}if(l!==n)return{reset:{placement:n}}}return{}}}),options:[e,t]}},eb=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"size",options:n,async fn(e){var t,r;let o,l;let{placement:c,rects:u,platform:f,elements:s}=e,{apply:m=()=>{},...v}=d(n,e),y=await R(e,v),w=p(c),b=h(c),x="y"===g(c),{width:E,height:A}=u.floating;"top"===w||"bottom"===w?(o=w,l=b===(await (null==f.isRTL?void 0:f.isRTL(s.floating))?"start":"end")?"left":"right"):(l=w,o="end"===b?"top":"bottom");let k=A-y.top-y.bottom,S=E-y.left-y.right,C=i(A-y[o],k),L=i(E-y[l],S),T=!e.middlewareData.shift,M=C,P=L;if(null!=(t=e.middlewareData.shift)&&t.enabled.x&&(P=S),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(M=k),T&&!b){let e=a(y.left,0),t=a(y.right,0),n=a(y.top,0),r=a(y.bottom,0);x?P=E-2*(0!==e||0!==t?e+t:a(y.left,y.right)):M=A-2*(0!==n||0!==r?n+r:a(y.top,y.bottom))}await m({...e,availableWidth:P,availableHeight:M});let O=await f.getDimensions(s.floating);return E!==O.width||A!==O.height?{reset:{rects:!0}}:{}}}),options:[e,t]}},ex=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"hide",options:n,async fn(e){let{rects:t}=e,{strategy:r="referenceHidden",...o}=d(n,e);switch(r){case"referenceHidden":{let n=k(await R(e,{...o,elementContext:"reference"}),t.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:S(n)}}}case"escaped":{let n=k(await R(e,{...o,altBoundary:!0}),t.floating);return{data:{escapedOffsets:n,escaped:S(n)}}}default:return{}}}}),options:[e,t]}},eE=(e,t)=>({...em(e),options:[e,t]});var eA=n(9381),eR=n(7437),ek=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eR.jsx)(eA.WV.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eR.jsx)("polygon",{points:"0,0 30,0 15,10"})})});ek.displayName="Arrow";var eS=n(2210),eC=n(6989),eL=n(6459),eT=n(5655),eM=n(4977),eP="Popper",[eO,eN]=(0,eC.b)(eP),[eW,eD]=eO(eP),eF=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eR.jsx)(eW,{scope:t,anchor:o,onAnchorChange:i,children:n})};eF.displayName=eP;var eZ="PopperAnchor",ej=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=eD(eZ,n),l=r.useRef(null),c=(0,eS.e)(t,l);return r.useEffect(()=>{a.onAnchorChange(o?.current||l.current)}),o?null:(0,eR.jsx)(eA.WV.div,{...i,ref:c})});ej.displayName=eZ;var eH="PopperContent",[eI,eB]=eO(eH),eV=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:l=0,align:u="center",alignOffset:f=0,arrowPadding:s=0,avoidCollisions:d=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:y,...w}=e,b=eD(eH,n),[x,E]=r.useState(null),A=(0,eS.e)(t,e=>E(e)),[R,k]=r.useState(null),S=(0,eM.t)(R),C=S?.width??0,L=S?.height??0,T="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},M=Array.isArray(p)?p:[p],O=M.length>0,N={padding:T,boundary:M.filter(e_),altBoundary:O},{refs:W,floatingStyles:D,placement:F,isPositioned:Z,middlewareData:j}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:l}={},transform:c=!0,whileElementsMounted:u,open:f}=e,[s,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);es(p,o)||h(o);let[m,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==A.current&&(A.current=e,v(e))},[]),b=r.useCallback(e=>{e!==R.current&&(R.current=e,y(e))},[]),x=a||m,E=l||g,A=r.useRef(null),R=r.useRef(null),k=r.useRef(s),S=null!=u,C=eh(u),L=eh(i),T=eh(f),M=r.useCallback(()=>{if(!A.current||!R.current)return;let e={placement:t,strategy:n,middleware:p};L.current&&(e.platform=L.current),ec(A.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==T.current};P.current&&!es(k.current,t)&&(k.current=t,eu.flushSync(()=>{d(t)}))})},[p,t,n,L,T]);ef(()=>{!1===f&&k.current.isPositioned&&(k.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[f]);let P=r.useRef(!1);ef(()=>(P.current=!0,()=>{P.current=!1}),[]),ef(()=>{if(x&&(A.current=x),E&&(R.current=E),x&&E){if(C.current)return C.current(x,E,M);M()}},[x,E,M,C,S]);let O=r.useMemo(()=>({reference:A,floating:R,setReference:w,setFloating:b}),[w,b]),N=r.useMemo(()=>({reference:x,floating:E}),[x,E]),W=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!N.floating)return e;let t=ep(N.floating,s.x),r=ep(N.floating,s.y);return c?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(N.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,c,N.floating,s.x,s.y]);return r.useMemo(()=>({...s,update:M,refs:O,elements:N,floatingStyles:W}),[s,M,O,N,W])}({strategy:"fixed",placement:o+("center"!==u?"-"+u:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:u=!0,elementResize:f="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=q(e),h=l||u?[...p?$(p):[],...$(t)]:[];h.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=p&&s?function(e,t){let n,r=null,o=P(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function u(f,s){void 0===f&&(f=!1),void 0===s&&(s=1),l();let d=e.getBoundingClientRect(),{left:p,top:h,width:m,height:v}=d;if(f||t(),!m||!v)return;let g=c(h),y=c(o.clientWidth-(p+m)),w={rootMargin:-g+"px "+-y+"px "+-c(o.clientHeight-(h+v))+"px "+-c(p)+"px",threshold:a(0,i(1,s))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==s){if(!b)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||ea(d,e.getBoundingClientRect())||u(),b=!1}try{r=new IntersectionObserver(x,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),l}(p,n):null,v=-1,g=null;f&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!d&&g.observe(p),g.observe(t));let y=d?G(e):null;return d&&function t(){let r=G(e);y&&!ea(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{l&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,d&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===g}),elements:{reference:b.anchor},middleware:[ev({mainAxis:l+L,alignmentAxis:f}),d&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?ey():void 0,...N}),d&&ew({...N}),eb({...N,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),R&&eE({element:R,padding:s}),eq({arrowWidth:C,arrowHeight:L}),v&&ex({strategy:"referenceHidden",...N})]}),[H,I]=eX(F),B=(0,eL.W)(y);(0,eT.b)(()=>{Z&&B?.()},[Z,B]);let V=j.arrow?.x,z=j.arrow?.y,Y=j.arrow?.centerOffset!==0,[_,X]=r.useState();return(0,eT.b)(()=>{x&&X(window.getComputedStyle(x).zIndex)},[x]),(0,eR.jsx)("div",{ref:W.setFloating,"data-radix-popper-content-wrapper":"",style:{...D,transform:Z?D.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:_,"--radix-popper-transform-origin":[j.transformOrigin?.x,j.transformOrigin?.y].join(" "),...j.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eR.jsx)(eI,{scope:n,placedSide:H,onArrowChange:k,arrowX:V,arrowY:z,shouldHideArrow:Y,children:(0,eR.jsx)(eA.WV.div,{"data-side":H,"data-align":I,...w,ref:A,style:{...w.style,animation:Z?void 0:"none"}})})})});eV.displayName=eH;var ez="PopperArrow",e$={top:"bottom",right:"left",bottom:"top",left:"right"},eY=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eB(ez,n),i=e$[o.placedSide];return(0,eR.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eR.jsx)(ek,{...r,ref:t,style:{...r.style,display:"block"}})})});function e_(e){return null!==e}eY.displayName=ez;var eq=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[c,u]=eX(n),f={start:"0%",center:"50%",end:"100%"}[u],s=(o.arrow?.x??0)+a/2,d=(o.arrow?.y??0)+l/2,p="",h="";return"bottom"===c?(p=i?f:`${s}px`,h=`${-l}px`):"top"===c?(p=i?f:`${s}px`,h=`${r.floating.height+l}px`):"right"===c?(p=`${-l}px`,h=i?f:`${d}px`):"left"===c&&(p=`${r.floating.width+l}px`,h=i?f:`${d}px`),{data:{x:p,y:h}}}});function eX(e){let[t,n="center"]=e.split("-");return[t,n]}var eK=eF,eU=ej,eG=eV,eJ=eY},4977:function(e,t,n){n.d(t,{t:function(){return i}});var r=n(2265),o=n(5655);function i(e){let[t,n]=r.useState(void 0);return(0,o.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}}}]);