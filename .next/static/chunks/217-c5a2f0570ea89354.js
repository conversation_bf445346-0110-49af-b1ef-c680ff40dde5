(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[217],{1981:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},8940:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("AlignLeft",[["line",{x1:"21",x2:"3",y1:"6",y2:"6",key:"1fp77t"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}],["line",{x1:"17",x2:"3",y1:"18",y2:"18",key:"1awlsn"}]])},9865:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},4907:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("BrainCircuit",[["path",{d:"M12 4.5a2.5 2.5 0 0 0-4.96-.46 2.5 2.5 0 0 0-1.98 3 2.5 2.5 0 0 0-1.32 4.24 3 3 0 0 0 .34 5.58 2.5 2.5 0 0 0 2.96 3.08 2.5 2.5 0 0 0 4.91.05L12 20V4.5Z",key:"ixwj2a"}],["path",{d:"M16 8V5c0-1.1.9-2 2-2",key:"13dx7u"}],["path",{d:"M12 13h4",key:"1ku699"}],["path",{d:"M12 18h6a2 2 0 0 1 2 2v1",key:"105ag5"}],["path",{d:"M12 8h8",key:"1lhi5i"}],["path",{d:"M20.5 8a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"1s25gz"}],["path",{d:"M16.5 13a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"127460"}],["path",{d:"M20.5 21a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"fys062"}],["path",{d:"M18.5 3a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"1vib61"}]])},3008:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2442:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},3523:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},7158:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},6369:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},6141:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},6224:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},1738:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},7332:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},5817:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},5479:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},8244:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},4056:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},774:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]])},1097:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]])},5883:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},8004:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},4900:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},9883:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5432:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},6245:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},6020:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},9409:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1274:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Share",[["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["polyline",{points:"16 6 12 2 8 6",key:"m901s6"}],["line",{x1:"12",x2:"12",y1:"2",y2:"15",key:"1p0rca"}]])},2851:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},3223:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]])},4658:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("ThumbsDown",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22h0a3.13 3.13 0 0 1-3-3.88Z",key:"s6e0r"}]])},9868:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z",key:"y3tblf"}]])},5790:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},7972:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},5750:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},2369:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},1396:function(e,t,n){e.exports=n(5250)},837:function(e,t,n){"use strict";function r(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function o(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?r(Object(n),!0).forEach(function(t){var r;r=n[t],t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(Object(n),!0).forEach(function(t){var r;r=n[t],t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function u(e){return function t(){for(var n=this,r=arguments.length,o=Array(r),i=0;i<r;i++)o[i]=arguments[i];return o.length>=e.length?e.apply(this,o):function(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return t.apply(n,[].concat(o,r))}}}function c(e){return({}).toString.call(e).includes("Object")}function s(e){return"function"==typeof e}n.d(t,{ML:function(){return K}});var d,f,p=u(function(e,t){throw Error(e[t]||e.default)})({initialIsRequired:"initial state is required",initialType:"initial state should be an object",initialContent:"initial state shouldn't be an empty object",handlerType:"handler should be an object or a function",handlersType:"all handlers should be a functions",selectorType:"selector should be a function",changeType:"provided value of changes should be an object",changeField:'it seams you want to change a field in the state which is not specified in the "initial" state',default:"an unknown error accured in `state-local` package"}),h={changes:function(e,t){return c(t)||p("changeType"),Object.keys(t).some(function(t){return!Object.prototype.hasOwnProperty.call(e,t)})&&p("changeField"),t},selector:function(e){s(e)||p("selectorType")},handler:function(e){s(e)||c(e)||p("handlerType"),c(e)&&Object.values(e).some(function(e){return!s(e)})&&p("handlersType")},initial:function(e){e||p("initialIsRequired"),c(e)||p("initialType"),Object.keys(e).length||p("initialContent")}};function g(e,t){return s(t)?t(e.current):t}function v(e,t){return e.current=l(l({},e.current),t),t}function m(e,t,n){return s(t)?t(e.current):Object.keys(n).forEach(function(n){var r;return null===(r=t[n])||void 0===r?void 0:r.call(t,e.current[n])}),n}var y={configIsRequired:"the configuration object is required",configType:"the configuration object should be an object",default:"an unknown error accured in `@monaco-editor/loader` package",deprecation:"Deprecation warning!\n    You are using deprecated way of configuration.\n\n    Instead of using\n      monaco.config({ urls: { monacoBase: '...' } })\n    use\n      monaco.config({ paths: { vs: '...' } })\n\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\n  "},w=(d=function(e,t){throw Error(e[t]||e.default)},function e(){for(var t=this,n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];return r.length>=d.length?d.apply(this,r):function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return e.apply(t,[].concat(r,o))}})(y),b={config:function(e){return e||w("configIsRequired"),({}).toString.call(e).includes("Object")||w("configType"),e.urls?(console.warn(y.deprecation),{paths:{vs:e.urls.monacoBase}}):e}},x=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return t.reduceRight(function(e,t){return t(e)},e)}},M={type:"cancelation",msg:"operation is manually canceled"},E=function(e){var t=!1,n=new Promise(function(n,r){e.then(function(e){return t?r(M):n(e)}),e.catch(r)});return n.cancel=function(){return t=!0},n},k=function(e){if(Array.isArray(e))return e}(f=({create:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};h.initial(e),h.handler(t);var n={current:e},r=u(m)(n,t),o=u(v)(n),i=u(h.changes)(e),a=u(g)(n);return[function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(e){return e};return h.selector(e),e(n.current)},function(e){(function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return t.reduceRight(function(e,t){return t(e)},e)}})(r,o,i,a)(e)}]}}).create({config:{paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}},isInitialized:!1,resolve:null,reject:null,monaco:null}))||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var n=[],r=!0,o=!1,i=void 0;try{for(var a,l=e[Symbol.iterator]();!(r=(a=l.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(e){o=!0,i=e}finally{try{r||null==l.return||l.return()}finally{if(o)throw i}}return n}}(f,2)||function(e,t){if(e){if("string"==typeof e)return i(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return i(e,t)}}(f,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),R=k[0],S=k[1];function C(e){return document.body.appendChild(e)}function j(e){var t,n,r=R(function(e){return{config:e.config,reject:e.reject}}),o=(t="".concat(r.config.paths.vs,"/loader.js"),n=document.createElement("script"),t&&(n.src=t),n);return o.onload=function(){return e()},o.onerror=r.reject,o}function T(){var e=R(function(e){return{config:e.config,resolve:e.resolve,reject:e.reject}}),t=window.require;t.config(e.config),t(["vs/editor/editor.main"],function(t){O(t),e.resolve(t)},function(t){e.reject(t)})}function O(e){R().monaco||S({monaco:e})}var P=new Promise(function(e,t){return S({resolve:e,reject:t})}),A={config:function(e){var t=b.config(e),n=t.monaco,r=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(t,["monaco"]);S(function(e){return{config:function e(t,n){return Object.keys(n).forEach(function(r){n[r]instanceof Object&&t[r]&&Object.assign(n[r],e(t[r],n[r]))}),o(o({},t),n)}(e.config,r),monaco:n}})},init:function(){var e=R(function(e){return{monaco:e.monaco,isInitialized:e.isInitialized,resolve:e.resolve}});if(!e.isInitialized){if(S({isInitialized:!0}),e.monaco)return e.resolve(e.monaco),E(P);if(window.monaco&&window.monaco.editor)return O(window.monaco),e.resolve(window.monaco),E(P);x(C,j)(T)}return E(P)},__getMonacoInstance:function(){return R(function(e){return e.monaco})}},L=n(2265),D={wrapper:{display:"flex",position:"relative",textAlign:"initial"},fullWidth:{width:"100%"},hide:{display:"none"}},I={container:{display:"flex",height:"100%",width:"100%",justifyContent:"center",alignItems:"center"}},N=function({children:e}){return L.createElement("div",{style:I.container},e)},_=(0,L.memo)(function({width:e,height:t,isEditorReady:n,loading:r,_ref:o,className:i,wrapperProps:a}){return L.createElement("section",{style:{...D.wrapper,width:e,height:t},...a},!n&&L.createElement(N,null,r),L.createElement("div",{ref:o,style:{...D.fullWidth,...!n&&D.hide},className:i}))}),Z=function(e){(0,L.useEffect)(e,[])},W=function(e,t,n=!0){let r=(0,L.useRef)(!0);(0,L.useEffect)(r.current||!n?()=>{r.current=!1}:e,t)};function F(){}function V(e,t,n,r){return e.editor.getModel(z(e,r))||e.editor.createModel(t,n,r?z(e,r):void 0)}function z(e,t){return e.Uri.parse(t)}(0,L.memo)(function({original:e,modified:t,language:n,originalLanguage:r,modifiedLanguage:o,originalModelPath:i,modifiedModelPath:a,keepCurrentOriginalModel:l=!1,keepCurrentModifiedModel:u=!1,theme:c="light",loading:s="Loading...",options:d={},height:f="100%",width:p="100%",className:h,wrapperProps:g={},beforeMount:v=F,onMount:m=F}){let[y,w]=(0,L.useState)(!1),[b,x]=(0,L.useState)(!0),M=(0,L.useRef)(null),E=(0,L.useRef)(null),k=(0,L.useRef)(null),R=(0,L.useRef)(m),S=(0,L.useRef)(v),C=(0,L.useRef)(!1);Z(()=>{let e=A.init();return e.then(e=>(E.current=e)&&x(!1)).catch(e=>e?.type!=="cancelation"&&console.error("Monaco initialization: error:",e)),()=>{let t;return M.current?(t=M.current?.getModel(),void(l||t?.original?.dispose(),u||t?.modified?.dispose(),M.current?.dispose())):e.cancel()}}),W(()=>{if(M.current&&E.current){let t=M.current.getOriginalEditor(),o=V(E.current,e||"",r||n||"text",i||"");o!==t.getModel()&&t.setModel(o)}},[i],y),W(()=>{if(M.current&&E.current){let e=M.current.getModifiedEditor(),r=V(E.current,t||"",o||n||"text",a||"");r!==e.getModel()&&e.setModel(r)}},[a],y),W(()=>{let e=M.current.getModifiedEditor();e.getOption(E.current.editor.EditorOption.readOnly)?e.setValue(t||""):t!==e.getValue()&&(e.executeEdits("",[{range:e.getModel().getFullModelRange(),text:t||"",forceMoveMarkers:!0}]),e.pushUndoStop())},[t],y),W(()=>{M.current?.getModel()?.original.setValue(e||"")},[e],y),W(()=>{let{original:e,modified:t}=M.current.getModel();E.current.editor.setModelLanguage(e,r||n||"text"),E.current.editor.setModelLanguage(t,o||n||"text")},[n,r,o],y),W(()=>{E.current?.editor.setTheme(c)},[c],y),W(()=>{M.current?.updateOptions(d)},[d],y);let j=(0,L.useCallback)(()=>{if(!E.current)return;S.current(E.current);let l=V(E.current,e||"",r||n||"text",i||""),u=V(E.current,t||"",o||n||"text",a||"");M.current?.setModel({original:l,modified:u})},[n,t,o,e,r,i,a]),T=(0,L.useCallback)(()=>{!C.current&&k.current&&(M.current=E.current.editor.createDiffEditor(k.current,{automaticLayout:!0,...d}),j(),E.current?.editor.setTheme(c),w(!0),C.current=!0)},[d,c,j]);return(0,L.useEffect)(()=>{y&&R.current(M.current,E.current)},[y]),(0,L.useEffect)(()=>{b||y||T()},[b,y,T]),L.createElement(_,{width:p,height:f,isEditorReady:y,loading:s,_ref:k,className:h,wrapperProps:g})});var H=function(e){let t=(0,L.useRef)();return(0,L.useEffect)(()=>{t.current=e},[e]),t.current},B=new Map,K=(0,L.memo)(function({defaultValue:e,defaultLanguage:t,defaultPath:n,value:r,language:o,path:i,theme:a="light",line:l,loading:u="Loading...",options:c={},overrideServices:s={},saveViewState:d=!0,keepCurrentModel:f=!1,width:p="100%",height:h="100%",className:g,wrapperProps:v={},beforeMount:m=F,onMount:y=F,onChange:w,onValidate:b=F}){let[x,M]=(0,L.useState)(!1),[E,k]=(0,L.useState)(!0),R=(0,L.useRef)(null),S=(0,L.useRef)(null),C=(0,L.useRef)(null),j=(0,L.useRef)(y),T=(0,L.useRef)(m),O=(0,L.useRef)(),P=(0,L.useRef)(r),D=H(i),I=(0,L.useRef)(!1),N=(0,L.useRef)(!1);Z(()=>{let e=A.init();return e.then(e=>(R.current=e)&&k(!1)).catch(e=>e?.type!=="cancelation"&&console.error("Monaco initialization: error:",e)),()=>S.current?void(O.current?.dispose(),f?d&&B.set(i,S.current.saveViewState()):S.current.getModel()?.dispose(),S.current.dispose()):e.cancel()}),W(()=>{let a=V(R.current,e||r||"",t||o||"",i||n||"");a!==S.current?.getModel()&&(d&&B.set(D,S.current?.saveViewState()),S.current?.setModel(a),d&&S.current?.restoreViewState(B.get(i)))},[i],x),W(()=>{S.current?.updateOptions(c)},[c],x),W(()=>{S.current&&void 0!==r&&(S.current.getOption(R.current.editor.EditorOption.readOnly)?S.current.setValue(r):r===S.current.getValue()||(N.current=!0,S.current.executeEdits("",[{range:S.current.getModel().getFullModelRange(),text:r,forceMoveMarkers:!0}]),S.current.pushUndoStop(),N.current=!1))},[r],x),W(()=>{let e=S.current?.getModel();e&&o&&R.current?.editor.setModelLanguage(e,o)},[o],x),W(()=>{void 0!==l&&S.current?.revealLine(l)},[l],x),W(()=>{R.current?.editor.setTheme(a)},[a],x);let z=(0,L.useCallback)(()=>{if(!(!C.current||!R.current)&&!I.current){T.current(R.current);let u=i||n,f=V(R.current,r||e||"",t||o||"",u||"");S.current=R.current?.editor.create(C.current,{model:f,automaticLayout:!0,...c},s),d&&S.current.restoreViewState(B.get(u)),R.current.editor.setTheme(a),void 0!==l&&S.current.revealLine(l),M(!0),I.current=!0}},[e,t,n,r,o,i,c,s,d,a,l]);return(0,L.useEffect)(()=>{x&&j.current(S.current,R.current)},[x]),(0,L.useEffect)(()=>{E||x||z()},[E,x,z]),P.current=r,(0,L.useEffect)(()=>{x&&w&&(O.current?.dispose(),O.current=S.current?.onDidChangeModelContent(e=>{N.current||w(S.current.getValue(),e)}))},[x,w]),(0,L.useEffect)(()=>{if(x){let e=R.current.editor.onDidChangeMarkers(e=>{let t=S.current.getModel()?.uri;if(t&&e.find(e=>e.path===t.path)){let e=R.current.editor.getModelMarkers({resource:t});b?.(e)}});return()=>{e?.dispose()}}return()=>{}},[x,b]),L.createElement(_,{width:p,height:h,isEditorReady:x,loading:u,_ref:C,className:g,wrapperProps:v})})},1465:function(e,t,n){"use strict";n.d(t,{NY:function(){return k},Ee:function(){return E},fC:function(){return M}});var r=n(2265),o=n(6989),i=n(6459),a=n(1030),l=n(9381),u=n(6272);function c(){return()=>{}}var s=n(7437),d="Avatar",[f,p]=(0,o.b)(d),[h,g]=f(d),v=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[i,a]=r.useState("idle");return(0,s.jsx)(h,{scope:n,imageLoadingStatus:i,onImageLoadingStatusChange:a,children:(0,s.jsx)(l.WV.span,{...o,ref:t})})});v.displayName=d;var m="AvatarImage",y=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:d=()=>{},...f}=e,p=g(m,n),h=function(e,{referrerPolicy:t,crossOrigin:n}){let o=(0,u.useSyncExternalStore)(c,()=>!0,()=>!1),i=r.useRef(null),l=o?(i.current||(i.current=new window.Image),i.current):null,[s,d]=r.useState(()=>x(l,e));return(0,a.b)(()=>{d(x(l,e))},[l,e]),(0,a.b)(()=>{let e=e=>()=>{d(e)};if(!l)return;let r=e("loaded"),o=e("error");return l.addEventListener("load",r),l.addEventListener("error",o),t&&(l.referrerPolicy=t),"string"==typeof n&&(l.crossOrigin=n),()=>{l.removeEventListener("load",r),l.removeEventListener("error",o)}},[l,n,t]),s}(o,f),v=(0,i.W)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,a.b)(()=>{"idle"!==h&&v(h)},[h,v]),"loaded"===h?(0,s.jsx)(l.WV.img,{...f,ref:t,src:o}):null});y.displayName=m;var w="AvatarFallback",b=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...i}=e,a=g(w,n),[u,c]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(e)}},[o]),u&&"loaded"!==a.imageLoadingStatus?(0,s.jsx)(l.WV.span,{...i,ref:t}):null});function x(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}b.displayName=w;var M=v,E=y,k=b},5400:function(e,t,n){"use strict";n.d(t,{gm:function(){return i}});var r=n(2265);n(7437);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},9352:function(e,t,n){"use strict";let r;n.d(t,{oC:function(){return rW},VY:function(){return rI},ZA:function(){return rN},ck:function(){return rZ},wU:function(){return rz},__:function(){return r_},Uv:function(){return rD},Ee:function(){return rF},Rk:function(){return rV},fC:function(){return rA},Z0:function(){return rH},Tr:function(){return rB},tu:function(){return rU},fF:function(){return rK},xz:function(){return rL}});var o,i,a,l,u,c,s=n(2265),d=n.t(s,2),f=n(5744),p=n(2210),h=n(6989),g=n(3763),v=n(9381),m=n(7733),y=n(5400),w=n(9249),b=0;function x(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var M=n(6459),E=n(7437),k="focusScope.autoFocusOnMount",R="focusScope.autoFocusOnUnmount",S={bubbles:!1,cancelable:!0},C=s.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...a}=e,[l,u]=s.useState(null),c=(0,M.W)(o),d=(0,M.W)(i),f=s.useRef(null),h=(0,p.e)(t,e=>u(e)),g=s.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;s.useEffect(()=>{if(r){let e=function(e){if(g.paused||!l)return;let t=e.target;l.contains(t)?f.current=t:O(f.current,{select:!0})},t=function(e){if(g.paused||!l)return;let t=e.relatedTarget;null===t||l.contains(t)||O(f.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&O(l)});return l&&n.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,l,g.paused]),s.useEffect(()=>{if(l){P.add(g);let e=document.activeElement;if(!l.contains(e)){let t=new CustomEvent(k,S);l.addEventListener(k,c),l.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(O(r,{select:t}),document.activeElement!==n)return}(j(l).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&O(l))}return()=>{l.removeEventListener(k,c),setTimeout(()=>{let t=new CustomEvent(R,S);l.addEventListener(R,d),l.dispatchEvent(t),t.defaultPrevented||O(e??document.body,{select:!0}),l.removeEventListener(R,d),P.remove(g)},0)}}},[l,c,d,g]);let m=s.useCallback(e=>{if(!n&&!r||g.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=j(e);return[T(t,e),T(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&O(i,{select:!0})):(e.preventDefault(),n&&O(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,g.paused]);return(0,E.jsx)(v.WV.div,{tabIndex:-1,...a,ref:h,onKeyDown:m})});function j(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function T(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function O(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}C.displayName="FocusScope";var P=(r=[],{add(e){let t=r[0];e!==t&&t?.pause(),(r=A(r,e)).unshift(e)},remove(e){r=A(r,e),r[0]?.resume()}});function A(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var L=n(1030),D=d[" useId ".trim().toString()]||(()=>void 0),I=0;function N(e){let[t,n]=s.useState(D());return(0,L.b)(()=>{e||n(e=>e??String(I++))},[e]),e||(t?`radix-${t}`:"")}let _=["top","right","bottom","left"],Z=Math.min,W=Math.max,F=Math.round,V=Math.floor,z=e=>({x:e,y:e}),H={left:"right",right:"left",bottom:"top",top:"bottom"},B={start:"end",end:"start"};function K(e,t){return"function"==typeof e?e(t):e}function U(e){return e.split("-")[0]}function q(e){return e.split("-")[1]}function X(e){return"x"===e?"y":"x"}function Y(e){return"y"===e?"height":"width"}function $(e){return["top","bottom"].includes(U(e))?"y":"x"}function G(e){return e.replace(/start|end/g,e=>B[e])}function J(e){return e.replace(/left|right|bottom|top/g,e=>H[e])}function Q(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function ee(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function et(e,t,n){let r,{reference:o,floating:i}=e,a=$(t),l=X($(t)),u=Y(l),c=U(t),s="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(q(t)){case"start":r[l]-=p*(n&&s?-1:1);break;case"end":r[l]+=p*(n&&s?-1:1)}return r}let en=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=et(c,r,u),f=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:g}=l[n],{x:v,y:m,data:y,reset:w}=await g({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});s=null!=v?v:s,d=null!=m?m:d,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=et(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function er(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=K(t,e),h=Q(p),g=l[f?"floating"===d?"reference":"floating":d],v=ee(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(g)))||n?g:g.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:s,strategy:u})),m="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},b=ee(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:m,offsetParent:y,strategy:u}):m);return{top:(v.top-b.top+h.top)/w.y,bottom:(b.bottom-v.bottom+h.bottom)/w.y,left:(v.left-b.left+h.left)/w.x,right:(b.right-v.right+h.right)/w.x}}function eo(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ei(e){return _.some(t=>e[t]>=0)}async function ea(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=U(n),l=q(n),u="y"===$(n),c=["left","top"].includes(a)?-1:1,s=i&&u?-1:1,d=K(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof h&&(p="end"===l?-1*h:h),u?{x:p*s,y:f*c}:{x:f*c,y:p*s}}function el(){return"undefined"!=typeof window}function eu(e){return ed(e)?(e.nodeName||"").toLowerCase():"#document"}function ec(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function es(e){var t;return null==(t=(ed(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ed(e){return!!el()&&(e instanceof Node||e instanceof ec(e).Node)}function ef(e){return!!el()&&(e instanceof Element||e instanceof ec(e).Element)}function ep(e){return!!el()&&(e instanceof HTMLElement||e instanceof ec(e).HTMLElement)}function eh(e){return!!el()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ec(e).ShadowRoot)}function eg(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=eb(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function ev(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function em(e){let t=ey(),n=ef(e)?eb(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function ey(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function ew(e){return["html","body","#document"].includes(eu(e))}function eb(e){return ec(e).getComputedStyle(e)}function ex(e){return ef(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eM(e){if("html"===eu(e))return e;let t=e.assignedSlot||e.parentNode||eh(e)&&e.host||es(e);return eh(t)?t.host:t}function eE(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eM(t);return ew(n)?t.ownerDocument?t.ownerDocument.body:t.body:ep(n)&&eg(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=ec(o);if(i){let e=ek(a);return t.concat(a,a.visualViewport||[],eg(o)?o:[],e&&n?eE(e):[])}return t.concat(o,eE(o,[],n))}function ek(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eR(e){let t=eb(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=ep(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,l=F(n)!==i||F(r)!==a;return l&&(n=i,r=a),{width:n,height:r,$:l}}function eS(e){return ef(e)?e:e.contextElement}function eC(e){let t=eS(e);if(!ep(t))return z(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=eR(t),a=(i?F(n.width):n.width)/r,l=(i?F(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),l&&Number.isFinite(l)||(l=1),{x:a,y:l}}let ej=z(0);function eT(e){let t=ec(e);return ey()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ej}function eO(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=eS(e),l=z(1);t&&(r?ef(r)&&(l=eC(r)):l=eC(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===ec(a))&&o)?eT(a):z(0),c=(i.left+u.x)/l.x,s=(i.top+u.y)/l.y,d=i.width/l.x,f=i.height/l.y;if(a){let e=ec(a),t=r&&ef(r)?ec(r):r,n=e,o=ek(n);for(;o&&r&&t!==n;){let e=eC(o),t=o.getBoundingClientRect(),r=eb(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,f*=e.y,c+=i,s+=a,o=ek(n=ec(o))}}return ee({width:d,height:f,x:c,y:s})}function eP(e,t){let n=ex(e).scrollLeft;return t?t.left+n:eO(es(e)).left+n}function eA(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eP(e,r)),y:r.top+t.scrollTop}}function eL(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=ec(e),r=es(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,u=0;if(o){i=o.width,a=o.height;let e=ey();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}return{width:i,height:a,x:l,y:u}}(e,n);else if("document"===t)r=function(e){let t=es(e),n=ex(e),r=e.ownerDocument.body,o=W(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=W(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+eP(e),l=-n.scrollTop;return"rtl"===eb(r).direction&&(a+=W(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:l}}(es(e));else if(ef(t))r=function(e,t){let n=eO(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=ep(e)?eC(e):z(1),a=e.clientWidth*i.x;return{width:a,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{let n=eT(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return ee(r)}function eD(e){return"static"===eb(e).position}function eI(e,t){if(!ep(e)||"fixed"===eb(e).position)return null;if(t)return t(e);let n=e.offsetParent;return es(e)===n&&(n=n.ownerDocument.body),n}function eN(e,t){let n=ec(e);if(ev(e))return n;if(!ep(e)){let t=eM(e);for(;t&&!ew(t);){if(ef(t)&&!eD(t))return t;t=eM(t)}return n}let r=eI(e,t);for(;r&&["table","td","th"].includes(eu(r))&&eD(r);)r=eI(r,t);return r&&ew(r)&&eD(r)&&!em(r)?n:r||function(e){let t=eM(e);for(;ep(t)&&!ew(t);){if(em(t))return t;if(ev(t))break;t=eM(t)}return null}(e)||n}let e_=async function(e){let t=this.getOffsetParent||eN,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=ep(t),o=es(t),i="fixed"===n,a=eO(e,!0,i,t),l={scrollLeft:0,scrollTop:0},u=z(0);if(r||!r&&!i){if(("body"!==eu(t)||eg(o))&&(l=ex(t)),r){let e=eO(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eP(o))}i&&!r&&o&&(u.x=eP(o));let c=!o||r||i?z(0):eA(o,l);return{x:a.left+l.scrollLeft-u.x-c.x,y:a.top+l.scrollTop-u.y-c.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eZ={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=es(r),l=!!t&&ev(t.floating);if(r===a||l&&i)return n;let u={scrollLeft:0,scrollTop:0},c=z(1),s=z(0),d=ep(r);if((d||!d&&!i)&&(("body"!==eu(r)||eg(a))&&(u=ex(r)),ep(r))){let e=eO(r);c=eC(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let f=!a||d||i?z(0):eA(a,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+f.x,y:n.y*c.y-u.scrollTop*c.y+s.y+f.y}},getDocumentElement:es,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?ev(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eE(e,[],!1).filter(e=>ef(e)&&"body"!==eu(e)),o=null,i="fixed"===eb(e).position,a=i?eM(e):e;for(;ef(a)&&!ew(a);){let t=eb(a),n=em(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||eg(a)&&!n&&function e(t,n){let r=eM(t);return!(r===n||!ef(r)||ew(r))&&("fixed"===eb(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=eM(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((e,n)=>{let r=eL(t,n,o);return e.top=W(r.top,e.top),e.right=Z(r.right,e.right),e.bottom=Z(r.bottom,e.bottom),e.left=W(r.left,e.left),e},eL(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:eN,getElementRects:e_,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eR(e);return{width:t,height:n}},getScale:eC,isElement:ef,isRTL:function(e){return"rtl"===eb(e).direction}};function eW(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eF=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:a,elements:l,middlewareData:u}=t,{element:c,padding:s=0}=K(e,t)||{};if(null==c)return{};let d=Q(s),f={x:n,y:r},p=X($(o)),h=Y(p),g=await a.getDimensions(c),v="y"===p,m=v?"clientHeight":"clientWidth",y=i.reference[h]+i.reference[p]-f[p]-i.floating[h],w=f[p]-i.reference[p],b=await (null==a.getOffsetParent?void 0:a.getOffsetParent(c)),x=b?b[m]:0;x&&await (null==a.isElement?void 0:a.isElement(b))||(x=l.floating[m]||i.floating[h]);let M=x/2-g[h]/2-1,E=Z(d[v?"top":"left"],M),k=Z(d[v?"bottom":"right"],M),R=x-g[h]-k,S=x/2-g[h]/2+(y/2-w/2),C=W(E,Z(S,R)),j=!u.arrow&&null!=q(o)&&S!==C&&i.reference[h]/2-(S<E?E:k)-g[h]/2<0,T=j?S<E?S-E:S-R:0;return{[p]:f[p]+T,data:{[p]:C,centerOffset:S-C-T,...j&&{alignmentOffset:T}},reset:j}}}),eV=(e,t,n)=>{let r=new Map,o={platform:eZ,...n},i={...o.platform,_c:r};return en(e,t,{...o,platform:i})};var ez=n(4887),eH="undefined"!=typeof document?s.useLayoutEffect:function(){};function eB(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eB(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eB(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eK(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eU(e,t){let n=eK(e);return Math.round(t*n)/n}function eq(e){let t=s.useRef(e);return eH(()=>{t.current=e}),t}let eX=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eF({element:n.current,padding:r}).fn(t):{}:n?eF({element:n,padding:r}).fn(t):{}}}),eY=(e,t)=>{var n;return{...(void 0===(n=e)&&(n=0),{name:"offset",options:n,async fn(e){var t,r;let{x:o,y:i,placement:a,middlewareData:l}=e,u=await ea(e,n);return a===(null==(t=l.offset)?void 0:t.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:a}}}}),options:[e,t]}},e$=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"shift",options:n,async fn(e){let{x:t,y:r,placement:o}=e,{mainAxis:i=!0,crossAxis:a=!1,limiter:l={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=K(n,e),c={x:t,y:r},s=await er(e,u),d=$(U(o)),f=X(d),p=c[f],h=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+s[e],r=p-s[t];p=W(n,Z(p,r))}if(a){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+s[e],r=h-s[t];h=W(n,Z(h,r))}let g=l.fn({...e,[f]:p,[d]:h});return{...g,data:{x:g.x-t,y:g.y-r,enabled:{[f]:i,[d]:a}}}}}),options:[e,t]}},eG=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{options:n,fn(e){let{x:t,y:r,placement:o,rects:i,middlewareData:a}=e,{offset:l=0,mainAxis:u=!0,crossAxis:c=!0}=K(n,e),s={x:t,y:r},d=$(o),f=X(d),p=s[f],h=s[d],g=K(l,e),v="number"==typeof g?{mainAxis:g,crossAxis:0}:{mainAxis:0,crossAxis:0,...g};if(u){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,n=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var m,y;let e="y"===f?"width":"height",t=["top","left"].includes(U(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(m=a.offset)?void 0:m[d])||0)+(t?0:v.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=a.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}),options:[e,t]}},eJ=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"flip",options:n,async fn(e){var t,r,o,i,a;let{placement:l,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:f}=e,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:g,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:y=!0,...w}=K(n,e);if(null!=(t=u.arrow)&&t.alignmentOffset)return{};let b=U(l),x=$(s),M=U(s)===s,E=await (null==d.isRTL?void 0:d.isRTL(f.floating)),k=g||(M||!y?[J(s)]:function(e){let t=J(e);return[G(e),t,G(t)]}(s)),R="none"!==m;!g&&R&&k.push(...function(e,t,n,r){let o=q(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(U(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(G)))),i}(s,y,m,E));let S=[s,...k],C=await er(e,w),j=[],T=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&j.push(C[b]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=q(e),o=X($(e)),i=Y(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=J(a)),[a,J(a)]}(l,c,E);j.push(C[e[0]],C[e[1]])}if(T=[...T,{placement:l,overflows:j}],!j.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=S[e];if(t&&(!("alignment"===h&&x!==$(t))||T.every(e=>e.overflows[0]>0&&$(e.placement)===x)))return{data:{index:e,overflows:T},reset:{placement:t}};let n=null==(i=T.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(a=T.filter(e=>{if(R){let t=$(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=s}if(l!==n)return{reset:{placement:n}}}return{}}}),options:[e,t]}},eQ=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"size",options:n,async fn(e){var t,r;let o,i;let{placement:a,rects:l,platform:u,elements:c}=e,{apply:s=()=>{},...d}=K(n,e),f=await er(e,d),p=U(a),h=q(a),g="y"===$(a),{width:v,height:m}=l.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let y=m-f.top-f.bottom,w=v-f.left-f.right,b=Z(m-f[o],y),x=Z(v-f[i],w),M=!e.middlewareData.shift,E=b,k=x;if(null!=(t=e.middlewareData.shift)&&t.enabled.x&&(k=w),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(E=y),M&&!h){let e=W(f.left,0),t=W(f.right,0),n=W(f.top,0),r=W(f.bottom,0);g?k=v-2*(0!==e||0!==t?e+t:W(f.left,f.right)):E=m-2*(0!==n||0!==r?n+r:W(f.top,f.bottom))}await s({...e,availableWidth:k,availableHeight:E});let R=await u.getDimensions(c.floating);return v!==R.width||m!==R.height?{reset:{rects:!0}}:{}}}),options:[e,t]}},e0=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"hide",options:n,async fn(e){let{rects:t}=e,{strategy:r="referenceHidden",...o}=K(n,e);switch(r){case"referenceHidden":{let n=eo(await er(e,{...o,elementContext:"reference"}),t.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:ei(n)}}}case"escaped":{let n=eo(await er(e,{...o,altBoundary:!0}),t.floating);return{data:{escapedOffsets:n,escaped:ei(n)}}}default:return{}}}}),options:[e,t]}},e1=(e,t)=>({...eX(e),options:[e,t]});var e2=s.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,E.jsx)(v.WV.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,E.jsx)("polygon",{points:"0,0 30,0 15,10"})})});e2.displayName="Arrow";var e3="Popper",[e5,e8]=(0,h.b)(e3),[e9,e7]=e5(e3),e4=e=>{let{__scopePopper:t,children:n}=e,[r,o]=s.useState(null);return(0,E.jsx)(e9,{scope:t,anchor:r,onAnchorChange:o,children:n})};e4.displayName=e3;var e6="PopperAnchor",te=s.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,i=e7(e6,n),a=s.useRef(null),l=(0,p.e)(t,a);return s.useEffect(()=>{i.onAnchorChange(r?.current||a.current)}),r?null:(0,E.jsx)(v.WV.div,{...o,ref:l})});te.displayName=e6;var tt="PopperContent",[tn,tr]=e5(tt),to=s.forwardRef((e,t)=>{let{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:a=0,arrowPadding:l=0,avoidCollisions:u=!0,collisionBoundary:c=[],collisionPadding:d=0,sticky:f="partial",hideWhenDetached:h=!1,updatePositionStrategy:g="optimized",onPlaced:m,...y}=e,w=e7(tt,n),[b,x]=s.useState(null),k=(0,p.e)(t,e=>x(e)),[R,S]=s.useState(null),C=function(e){let[t,n]=s.useState(void 0);return(0,L.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(R),j=C?.width??0,T=C?.height??0,O="number"==typeof d?d:{top:0,right:0,bottom:0,left:0,...d},P=Array.isArray(c)?c:[c],A=P.length>0,D={padding:O,boundary:P.filter(tu),altBoundary:A},{refs:I,floatingStyles:N,placement:_,isPositioned:F,middlewareData:z}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:a}={},transform:l=!0,whileElementsMounted:u,open:c}=e,[d,f]=s.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=s.useState(r);eB(p,r)||h(r);let[g,v]=s.useState(null),[m,y]=s.useState(null),w=s.useCallback(e=>{e!==E.current&&(E.current=e,v(e))},[]),b=s.useCallback(e=>{e!==k.current&&(k.current=e,y(e))},[]),x=i||g,M=a||m,E=s.useRef(null),k=s.useRef(null),R=s.useRef(d),S=null!=u,C=eq(u),j=eq(o),T=eq(c),O=s.useCallback(()=>{if(!E.current||!k.current)return;let e={placement:t,strategy:n,middleware:p};j.current&&(e.platform=j.current),eV(E.current,k.current,e).then(e=>{let t={...e,isPositioned:!1!==T.current};P.current&&!eB(R.current,t)&&(R.current=t,ez.flushSync(()=>{f(t)}))})},[p,t,n,j,T]);eH(()=>{!1===c&&R.current.isPositioned&&(R.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let P=s.useRef(!1);eH(()=>(P.current=!0,()=>{P.current=!1}),[]),eH(()=>{if(x&&(E.current=x),M&&(k.current=M),x&&M){if(C.current)return C.current(x,M,O);O()}},[x,M,O,C,S]);let A=s.useMemo(()=>({reference:E,floating:k,setReference:w,setFloating:b}),[w,b]),L=s.useMemo(()=>({reference:x,floating:M}),[x,M]),D=s.useMemo(()=>{let e={position:n,left:0,top:0};if(!L.floating)return e;let t=eU(L.floating,d.x),r=eU(L.floating,d.y);return l?{...e,transform:"translate("+t+"px, "+r+"px)",...eK(L.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,l,L.floating,d.x,d.y]);return s.useMemo(()=>({...d,update:O,refs:A,elements:L,floatingStyles:D}),[d,O,A,L,D])}({strategy:"fixed",placement:r+("center"!==i?"-"+i:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:a=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=eS(e),d=i||a?[...s?eE(s):[],...eE(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)});let f=s&&u?function(e,t){let n,r=null,o=es(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function a(l,u){void 0===l&&(l=!1),void 0===u&&(u=1),i();let c=e.getBoundingClientRect(),{left:s,top:d,width:f,height:p}=c;if(l||t(),!f||!p)return;let h=V(d),g=V(o.clientWidth-(s+f)),v={rootMargin:-h+"px "+-g+"px "+-V(o.clientHeight-(d+p))+"px "+-V(s)+"px",threshold:W(0,Z(1,u))||1},m=!0;function y(t){let r=t[0].intersectionRatio;if(r!==u){if(!m)return a();r?a(!1,r):n=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==r||eW(c,e.getBoundingClientRect())||a(),m=!1}try{r=new IntersectionObserver(y,{...v,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,v)}r.observe(e)}(!0),i}(s,n):null,p=-1,h=null;l&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),s&&!c&&h.observe(s),h.observe(t));let g=c?eO(e):null;return c&&function t(){let r=eO(e);g&&!eW(g,r)&&n(),g=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===g}),elements:{reference:w.anchor},middleware:[eY({mainAxis:o+T,alignmentAxis:a}),u&&e$({mainAxis:!0,crossAxis:!1,limiter:"partial"===f?eG():void 0,...D}),u&&eJ({...D}),eQ({...D,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),R&&e1({element:R,padding:l}),tc({arrowWidth:j,arrowHeight:T}),h&&e0({strategy:"referenceHidden",...D})]}),[H,B]=ts(_),K=(0,M.W)(m);(0,L.b)(()=>{F&&K?.()},[F,K]);let U=z.arrow?.x,q=z.arrow?.y,X=z.arrow?.centerOffset!==0,[Y,$]=s.useState();return(0,L.b)(()=>{b&&$(window.getComputedStyle(b).zIndex)},[b]),(0,E.jsx)("div",{ref:I.setFloating,"data-radix-popper-content-wrapper":"",style:{...N,transform:F?N.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Y,"--radix-popper-transform-origin":[z.transformOrigin?.x,z.transformOrigin?.y].join(" "),...z.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,E.jsx)(tn,{scope:n,placedSide:H,onArrowChange:S,arrowX:U,arrowY:q,shouldHideArrow:X,children:(0,E.jsx)(v.WV.div,{"data-side":H,"data-align":B,...y,ref:k,style:{...y.style,animation:F?void 0:"none"}})})})});to.displayName=tt;var ti="PopperArrow",ta={top:"bottom",right:"left",bottom:"top",left:"right"},tl=s.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=tr(ti,n),i=ta[o.placedSide];return(0,E.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,E.jsx)(e2,{...r,ref:t,style:{...r.style,display:"block"}})})});function tu(e){return null!==e}tl.displayName=ti;var tc=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[u,c]=ts(n),s={start:"0%",center:"50%",end:"100%"}[c],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+l/2,p="",h="";return"bottom"===u?(p=i?s:`${d}px`,h=`${-l}px`):"top"===u?(p=i?s:`${d}px`,h=`${r.floating.height+l}px`):"right"===u?(p=`${-l}px`,h=i?s:`${f}px`):"left"===u&&(p=`${r.floating.width+l}px`,h=i?s:`${f}px`),{data:{x:p,y:h}}}});function ts(e){let[t,n="center"]=e.split("-");return[t,n]}var td=n(2730),tf=n(5606),tp="rovingFocusGroup.onEntryFocus",th={bubbles:!1,cancelable:!0},tg="RovingFocusGroup",[tv,tm,ty]=(0,m.B)(tg),[tw,tb]=(0,h.b)(tg,[ty]),[tx,tM]=tw(tg),tE=s.forwardRef((e,t)=>(0,E.jsx)(tv.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,E.jsx)(tv.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,E.jsx)(tk,{...e,ref:t})})}));tE.displayName=tg;var tk=s.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:i,currentTabStopId:a,defaultCurrentTabStopId:l,onCurrentTabStopIdChange:u,onEntryFocus:c,preventScrollOnEntryFocus:d=!1,...h}=e,m=s.useRef(null),w=(0,p.e)(t,m),b=(0,y.gm)(i),[x,k]=(0,g.T)({prop:a,defaultProp:l??null,onChange:u,caller:tg}),[R,S]=s.useState(!1),C=(0,M.W)(c),j=tm(n),T=s.useRef(!1),[O,P]=s.useState(0);return s.useEffect(()=>{let e=m.current;if(e)return e.addEventListener(tp,C),()=>e.removeEventListener(tp,C)},[C]),(0,E.jsx)(tx,{scope:n,orientation:r,dir:b,loop:o,currentTabStopId:x,onItemFocus:s.useCallback(e=>k(e),[k]),onItemShiftTab:s.useCallback(()=>S(!0),[]),onFocusableItemAdd:s.useCallback(()=>P(e=>e+1),[]),onFocusableItemRemove:s.useCallback(()=>P(e=>e-1),[]),children:(0,E.jsx)(v.WV.div,{tabIndex:R||0===O?-1:0,"data-orientation":r,...h,ref:w,style:{outline:"none",...e.style},onMouseDown:(0,f.M)(e.onMouseDown,()=>{T.current=!0}),onFocus:(0,f.M)(e.onFocus,e=>{let t=!T.current;if(e.target===e.currentTarget&&t&&!R){let t=new CustomEvent(tp,th);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=j().filter(e=>e.focusable);tj([e.find(e=>e.active),e.find(e=>e.id===x),...e].filter(Boolean).map(e=>e.ref.current),d)}}T.current=!1}),onBlur:(0,f.M)(e.onBlur,()=>S(!1))})})}),tR="RovingFocusGroupItem",tS=s.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:i,children:a,...l}=e,u=N(),c=i||u,d=tM(tR,n),p=d.currentTabStopId===c,h=tm(n),{onFocusableItemAdd:g,onFocusableItemRemove:m,currentTabStopId:y}=d;return s.useEffect(()=>{if(r)return g(),()=>m()},[r,g,m]),(0,E.jsx)(tv.ItemSlot,{scope:n,id:c,focusable:r,active:o,children:(0,E.jsx)(v.WV.span,{tabIndex:p?0:-1,"data-orientation":d.orientation,...l,ref:t,onMouseDown:(0,f.M)(e.onMouseDown,e=>{r?d.onItemFocus(c):e.preventDefault()}),onFocus:(0,f.M)(e.onFocus,()=>d.onItemFocus(c)),onKeyDown:(0,f.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){d.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return tC[o]}(e,d.orientation,d.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)o.reverse();else if("prev"===t||"next"===t){var n,r;"prev"===t&&o.reverse();let i=o.indexOf(e.currentTarget);o=d.loop?(n=o,r=i+1,n.map((e,t)=>n[(r+t)%n.length])):o.slice(i+1)}setTimeout(()=>tj(o))}}),children:"function"==typeof a?a({isCurrentTabStop:p,hasTabStop:null!=y}):a})})});tS.displayName=tR;var tC={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function tj(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var tT=n(7256),tO=new WeakMap,tP=new WeakMap,tA={},tL=0,tD=function(e){return e&&(e.host||tD(e.parentNode))},tI=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tD(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tA[n]||(tA[n]=new WeakMap);var i=tA[n],a=[],l=new Set,u=new Set(o),c=function(e){!e||l.has(e)||(l.add(e),c(e.parentNode))};o.forEach(c);var s=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(l.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(tO.get(e)||0)+1,c=(i.get(e)||0)+1;tO.set(e,u),i.set(e,c),a.push(e),1===u&&o&&tP.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),l.clear(),tL++,function(){a.forEach(function(e){var t=tO.get(e)-1,o=i.get(e)-1;tO.set(e,t),i.set(e,o),t||(tP.has(e)||e.removeAttribute(r),tP.delete(e)),o||e.removeAttribute(n)}),--tL||(tO=new WeakMap,tO=new WeakMap,tP=new WeakMap,tA={})}},tN=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),tI(r,o,n,"aria-hidden")):function(){return null}},t_=function(){return(t_=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function tZ(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}"function"==typeof SuppressedError&&SuppressedError;var tW="right-scroll-bar-position",tF="width-before-scroll-bar";function tV(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tz="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,tH=new WeakMap,tB=(void 0===o&&(o={}),(void 0===i&&(i=function(e){return e}),a=[],l=!1,u={read:function(){if(l)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return a.length?a[a.length-1]:null},useMedium:function(e){var t=i(e,l);return a.push(t),function(){a=a.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(l=!0;a.length;){var t=a;a=[],t.forEach(e)}a={push:function(t){return e(t)},filter:function(){return a}}},assignMedium:function(e){l=!0;var t=[];if(a.length){var n=a;a=[],n.forEach(e),t=a}var r=function(){var n=t;t=[],n.forEach(e)},o=function(){return Promise.resolve().then(r)};o(),a={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),a}}}}).options=t_({async:!0,ssr:!1},o),u),tK=function(){},tU=s.forwardRef(function(e,t){var n,r,o,i,a=s.useRef(null),l=s.useState({onScrollCapture:tK,onWheelCapture:tK,onTouchMoveCapture:tK}),u=l[0],c=l[1],d=e.forwardProps,f=e.children,p=e.className,h=e.removeScrollBar,g=e.enabled,v=e.shards,m=e.sideCar,y=e.noRelative,w=e.noIsolation,b=e.inert,x=e.allowPinchZoom,M=e.as,E=e.gapMode,k=tZ(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=(n=[a,t],r=function(e){return n.forEach(function(t){return tV(t,e)})},(o=(0,s.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,tz(function(){var e=tH.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||tV(e,null)}),r.forEach(function(e){t.has(e)||tV(e,o)})}tH.set(i,n)},[n]),i),S=t_(t_({},k),u);return s.createElement(s.Fragment,null,g&&s.createElement(m,{sideCar:tB,removeScrollBar:h,shards:v,noRelative:y,noIsolation:w,inert:b,setCallbacks:c,allowPinchZoom:!!x,lockRef:a,gapMode:E}),d?s.cloneElement(s.Children.only(f),t_(t_({},S),{ref:R})):s.createElement(void 0===M?"div":M,t_({},S,{className:p,ref:R}),f))});tU.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},tU.classNames={fullWidth:tF,zeroRight:tW};var tq=function(e){var t=e.sideCar,n=tZ(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return s.createElement(r,t_({},n))};tq.isSideCarExport=!0;var tX=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=c||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},tY=function(){var e=tX();return function(t,n){s.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},t$=function(){var e=tY();return function(t){return e(t.styles,t.dynamic),null}},tG={left:0,top:0,right:0,gap:0},tJ=function(e){return parseInt(e||"",10)||0},tQ=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[tJ(n),tJ(r),tJ(o)]},t0=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return tG;var t=tQ(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},t1=t$(),t2="data-scroll-locked",t3=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(t2,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tW," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(tF," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(tW," .").concat(tW," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(tF," .").concat(tF," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(t2,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},t5=function(){var e=parseInt(document.body.getAttribute(t2)||"0",10);return isFinite(e)?e:0},t8=function(){s.useEffect(function(){return document.body.setAttribute(t2,(t5()+1).toString()),function(){var e=t5()-1;e<=0?document.body.removeAttribute(t2):document.body.setAttribute(t2,e.toString())}},[])},t9=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;t8();var i=s.useMemo(function(){return t0(o)},[o]);return s.createElement(t1,{styles:t3(i,!t,o,n?"":"!important")})},t7=!1;if("undefined"!=typeof window)try{var t4=Object.defineProperty({},"passive",{get:function(){return t7=!0,!0}});window.addEventListener("test",t4,t4),window.removeEventListener("test",t4,t4)}catch(e){t7=!1}var t6=!!t7&&{passive:!1},ne=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},nt=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),nn(e,r)){var o=nr(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},nn=function(e,t){return"v"===e?ne(t,"overflowY"):ne(t,"overflowX")},nr=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},no=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,u=n.target,c=t.contains(u),s=!1,d=l>0,f=0,p=0;do{if(!u)break;var h=nr(e,u),g=h[0],v=h[1]-h[2]-a*g;(g||v)&&nn(e,u)&&(f+=v,p+=g);var m=u.parentNode;u=m&&m.nodeType===Node.DOCUMENT_FRAGMENT_NODE?m.host:m}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&l>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},ni=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},na=function(e){return[e.deltaX,e.deltaY]},nl=function(e){return e&&"current"in e?e.current:e},nu=0,nc=[],ns=(tB.useMedium(function(e){var t=s.useRef([]),n=s.useRef([0,0]),r=s.useRef(),o=s.useState(nu++)[0],i=s.useState(t$)[0],a=s.useRef(e);s.useEffect(function(){a.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(nl),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=s.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=ni(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=nt(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=nt(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return no(p,t,e,"h"===p?u:c,!0)},[]),u=s.useCallback(function(e){if(nc.length&&nc[nc.length-1]===i){var n="deltaY"in e?na(e):ni(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(nl).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=s.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=s.useCallback(function(e){n.current=ni(e),r.current=void 0},[]),f=s.useCallback(function(t){c(t.type,na(t),t.target,l(t,e.lockRef.current))},[]),p=s.useCallback(function(t){c(t.type,ni(t),t.target,l(t,e.lockRef.current))},[]);s.useEffect(function(){return nc.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",u,t6),document.addEventListener("touchmove",u,t6),document.addEventListener("touchstart",d,t6),function(){nc=nc.filter(function(e){return e!==i}),document.removeEventListener("wheel",u,t6),document.removeEventListener("touchmove",u,t6),document.removeEventListener("touchstart",d,t6)}},[]);var h=e.removeScrollBar,g=e.inert;return s.createElement(s.Fragment,null,g?s.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?s.createElement(t9,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),tq),nd=s.forwardRef(function(e,t){return s.createElement(tU,t_({},e,{ref:t,sideCar:ns}))});nd.classNames=tU.classNames;var nf=["Enter"," "],np=["ArrowUp","PageDown","End"],nh=["ArrowDown","PageUp","Home",...np],ng={ltr:[...nf,"ArrowRight"],rtl:[...nf,"ArrowLeft"]},nv={ltr:["ArrowLeft"],rtl:["ArrowRight"]},nm="Menu",[ny,nw,nb]=(0,m.B)(nm),[nx,nM]=(0,h.b)(nm,[nb,e8,tb]),nE=e8(),nk=tb(),[nR,nS]=nx(nm),[nC,nj]=nx(nm),nT=e=>{let{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:i,modal:a=!0}=e,l=nE(t),[u,c]=s.useState(null),d=s.useRef(!1),f=(0,M.W)(i),p=(0,y.gm)(o);return s.useEffect(()=>{let e=()=>{d.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>d.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,E.jsx)(e4,{...l,children:(0,E.jsx)(nR,{scope:t,open:n,onOpenChange:f,content:u,onContentChange:c,children:(0,E.jsx)(nC,{scope:t,onClose:s.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:d,dir:p,modal:a,children:r})})})};nT.displayName=nm;var nO=s.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=nE(n);return(0,E.jsx)(te,{...o,...r,ref:t})});nO.displayName="MenuAnchor";var nP="MenuPortal",[nA,nL]=nx(nP,{forceMount:void 0}),nD=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=nS(nP,t);return(0,E.jsx)(nA,{scope:t,forceMount:n,children:(0,E.jsx)(tf.z,{present:n||i.open,children:(0,E.jsx)(td.h,{asChild:!0,container:o,children:r})})})};nD.displayName=nP;var nI="MenuContent",[nN,n_]=nx(nI),nZ=s.forwardRef((e,t)=>{let n=nL(nI,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=nS(nI,e.__scopeMenu),a=nj(nI,e.__scopeMenu);return(0,E.jsx)(ny.Provider,{scope:e.__scopeMenu,children:(0,E.jsx)(tf.z,{present:r||i.open,children:(0,E.jsx)(ny.Slot,{scope:e.__scopeMenu,children:a.modal?(0,E.jsx)(nW,{...o,ref:t}):(0,E.jsx)(nF,{...o,ref:t})})})})}),nW=s.forwardRef((e,t)=>{let n=nS(nI,e.__scopeMenu),r=s.useRef(null),o=(0,p.e)(t,r);return s.useEffect(()=>{let e=r.current;if(e)return tN(e)},[]),(0,E.jsx)(nz,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,f.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),nF=s.forwardRef((e,t)=>{let n=nS(nI,e.__scopeMenu);return(0,E.jsx)(nz,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),nV=(0,tT.Z8)("MenuContent.ScrollLock"),nz=s.forwardRef((e,t)=>{let{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:a,disableOutsidePointerEvents:l,onEntryFocus:u,onEscapeKeyDown:c,onPointerDownOutside:d,onFocusOutside:h,onInteractOutside:g,onDismiss:v,disableOutsideScroll:m,...y}=e,M=nS(nI,n),k=nj(nI,n),R=nE(n),S=nk(n),j=nw(n),[T,O]=s.useState(null),P=s.useRef(null),A=(0,p.e)(t,P,M.onContentChange),L=s.useRef(0),D=s.useRef(""),I=s.useRef(0),N=s.useRef(null),_=s.useRef("right"),Z=s.useRef(0),W=m?nd:s.Fragment,F=e=>{let t=D.current+e,n=j().filter(e=>!e.disabled),r=document.activeElement,o=n.find(e=>e.ref.current===r)?.textValue,i=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let a=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return a!==n?a:void 0}(n.map(e=>e.textValue),t,o),a=n.find(e=>e.textValue===i)?.ref.current;!function e(t){D.current=t,window.clearTimeout(L.current),""!==t&&(L.current=window.setTimeout(()=>e(""),1e3))}(t),a&&setTimeout(()=>a.focus())};s.useEffect(()=>()=>window.clearTimeout(L.current),[]),s.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??x()),document.body.insertAdjacentElement("beforeend",e[1]??x()),b++,()=>{1===b&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),b--}},[]);let V=s.useCallback(e=>{var t;return _.current===N.current?.side&&!!(t=N.current?.area)&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],u=a.x,c=a.y,s=l.x,d=l.y;c>r!=d>r&&n<(s-u)*(r-c)/(d-c)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)},[]);return(0,E.jsx)(nN,{scope:n,searchRef:D,onItemEnter:s.useCallback(e=>{V(e)&&e.preventDefault()},[V]),onItemLeave:s.useCallback(e=>{V(e)||(P.current?.focus(),O(null))},[V]),onTriggerLeave:s.useCallback(e=>{V(e)&&e.preventDefault()},[V]),pointerGraceTimerRef:I,onPointerGraceIntentChange:s.useCallback(e=>{N.current=e},[]),children:(0,E.jsx)(W,{...m?{as:nV,allowPinchZoom:!0}:void 0,children:(0,E.jsx)(C,{asChild:!0,trapped:o,onMountAutoFocus:(0,f.M)(i,e=>{e.preventDefault(),P.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:a,children:(0,E.jsx)(w.XB,{asChild:!0,disableOutsidePointerEvents:l,onEscapeKeyDown:c,onPointerDownOutside:d,onFocusOutside:h,onInteractOutside:g,onDismiss:v,children:(0,E.jsx)(tE,{asChild:!0,...S,dir:k.dir,orientation:"vertical",loop:r,currentTabStopId:T,onCurrentTabStopIdChange:O,onEntryFocus:(0,f.M)(u,e=>{k.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,E.jsx)(to,{role:"menu","aria-orientation":"vertical","data-state":ra(M.open),"data-radix-menu-content":"",dir:k.dir,...R,...y,ref:A,style:{outline:"none",...y.style},onKeyDown:(0,f.M)(y.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&F(e.key));let o=P.current;if(e.target!==o||!nh.includes(e.key))return;e.preventDefault();let i=j().filter(e=>!e.disabled).map(e=>e.ref.current);np.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,f.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(L.current),D.current="")}),onPointerMove:(0,f.M)(e.onPointerMove,rc(e=>{let t=e.target,n=Z.current!==e.clientX;if(e.currentTarget.contains(t)&&n){let t=e.clientX>Z.current?"right":"left";_.current=t,Z.current=e.clientX}}))})})})})})})});nZ.displayName=nI;var nH=s.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,E.jsx)(v.WV.div,{role:"group",...r,ref:t})});nH.displayName="MenuGroup";var nB=s.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,E.jsx)(v.WV.div,{...r,ref:t})});nB.displayName="MenuLabel";var nK="MenuItem",nU="menu.itemSelect",nq=s.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:r,...o}=e,i=s.useRef(null),a=nj(nK,e.__scopeMenu),l=n_(nK,e.__scopeMenu),u=(0,p.e)(t,i),c=s.useRef(!1);return(0,E.jsx)(nX,{...o,ref:u,disabled:n,onClick:(0,f.M)(e.onClick,()=>{let e=i.current;if(!n&&e){let t=new CustomEvent(nU,{bubbles:!0,cancelable:!0});e.addEventListener(nU,e=>r?.(e),{once:!0}),(0,v.jH)(e,t),t.defaultPrevented?c.current=!1:a.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),c.current=!0},onPointerUp:(0,f.M)(e.onPointerUp,e=>{c.current||e.currentTarget?.click()}),onKeyDown:(0,f.M)(e.onKeyDown,e=>{let t=""!==l.searchRef.current;!n&&(!t||" "!==e.key)&&nf.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});nq.displayName=nK;var nX=s.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:r=!1,textValue:o,...i}=e,a=n_(nK,n),l=nk(n),u=s.useRef(null),c=(0,p.e)(t,u),[d,h]=s.useState(!1),[g,m]=s.useState("");return s.useEffect(()=>{let e=u.current;e&&m((e.textContent??"").trim())},[i.children]),(0,E.jsx)(ny.ItemSlot,{scope:n,disabled:r,textValue:o??g,children:(0,E.jsx)(tS,{asChild:!0,...l,focusable:!r,children:(0,E.jsx)(v.WV.div,{role:"menuitem","data-highlighted":d?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...i,ref:c,onPointerMove:(0,f.M)(e.onPointerMove,rc(e=>{r?a.onItemLeave(e):(a.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,f.M)(e.onPointerLeave,rc(e=>a.onItemLeave(e))),onFocus:(0,f.M)(e.onFocus,()=>h(!0)),onBlur:(0,f.M)(e.onBlur,()=>h(!1))})})})}),nY=s.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...o}=e;return(0,E.jsx)(n3,{scope:e.__scopeMenu,checked:n,children:(0,E.jsx)(nq,{role:"menuitemcheckbox","aria-checked":rl(n)?"mixed":n,...o,ref:t,"data-state":ru(n),onSelect:(0,f.M)(o.onSelect,()=>r?.(!!rl(n)||!n),{checkForDefaultPrevented:!1})})})});nY.displayName="MenuCheckboxItem";var n$="MenuRadioGroup",[nG,nJ]=nx(n$,{value:void 0,onValueChange:()=>{}}),nQ=s.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,M.W)(r);return(0,E.jsx)(nG,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,E.jsx)(nH,{...o,ref:t})})});nQ.displayName=n$;var n0="MenuRadioItem",n1=s.forwardRef((e,t)=>{let{value:n,...r}=e,o=nJ(n0,e.__scopeMenu),i=n===o.value;return(0,E.jsx)(n3,{scope:e.__scopeMenu,checked:i,children:(0,E.jsx)(nq,{role:"menuitemradio","aria-checked":i,...r,ref:t,"data-state":ru(i),onSelect:(0,f.M)(r.onSelect,()=>o.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});n1.displayName=n0;var n2="MenuItemIndicator",[n3,n5]=nx(n2,{checked:!1}),n8=s.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=n5(n2,n);return(0,E.jsx)(tf.z,{present:r||rl(i.checked)||!0===i.checked,children:(0,E.jsx)(v.WV.span,{...o,ref:t,"data-state":ru(i.checked)})})});n8.displayName=n2;var n9=s.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,E.jsx)(v.WV.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});n9.displayName="MenuSeparator";var n7=s.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=nE(n);return(0,E.jsx)(tl,{...o,...r,ref:t})});n7.displayName="MenuArrow";var n4="MenuSub",[n6,re]=nx(n4),rt=e=>{let{__scopeMenu:t,children:n,open:r=!1,onOpenChange:o}=e,i=nS(n4,t),a=nE(t),[l,u]=s.useState(null),[c,d]=s.useState(null),f=(0,M.W)(o);return s.useEffect(()=>(!1===i.open&&f(!1),()=>f(!1)),[i.open,f]),(0,E.jsx)(e4,{...a,children:(0,E.jsx)(nR,{scope:t,open:r,onOpenChange:f,content:c,onContentChange:d,children:(0,E.jsx)(n6,{scope:t,contentId:N(),triggerId:N(),trigger:l,onTriggerChange:u,children:n})})})};rt.displayName=n4;var rn="MenuSubTrigger",rr=s.forwardRef((e,t)=>{let n=nS(rn,e.__scopeMenu),r=nj(rn,e.__scopeMenu),o=re(rn,e.__scopeMenu),i=n_(rn,e.__scopeMenu),a=s.useRef(null),{pointerGraceTimerRef:l,onPointerGraceIntentChange:u}=i,c={__scopeMenu:e.__scopeMenu},d=s.useCallback(()=>{a.current&&window.clearTimeout(a.current),a.current=null},[]);return s.useEffect(()=>d,[d]),s.useEffect(()=>{let e=l.current;return()=>{window.clearTimeout(e),u(null)}},[l,u]),(0,E.jsx)(nO,{asChild:!0,...c,children:(0,E.jsx)(nX,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":ra(n.open),...e,ref:(0,p.F)(t,o.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,f.M)(e.onPointerMove,rc(t=>{i.onItemEnter(t),t.defaultPrevented||e.disabled||n.open||a.current||(i.onPointerGraceIntentChange(null),a.current=window.setTimeout(()=>{n.onOpenChange(!0),d()},100))})),onPointerLeave:(0,f.M)(e.onPointerLeave,rc(e=>{d();let t=n.content?.getBoundingClientRect();if(t){let r=n.content?.dataset.side,o="right"===r,a=t[o?"left":"right"],u=t[o?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:a,y:t.top},{x:u,y:t.top},{x:u,y:t.bottom},{x:a,y:t.bottom}],side:r}),window.clearTimeout(l.current),l.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,f.M)(e.onKeyDown,t=>{let o=""!==i.searchRef.current;!e.disabled&&(!o||" "!==t.key)&&ng[r.dir].includes(t.key)&&(n.onOpenChange(!0),n.content?.focus(),t.preventDefault())})})})});rr.displayName=rn;var ro="MenuSubContent",ri=s.forwardRef((e,t)=>{let n=nL(nI,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=nS(nI,e.__scopeMenu),a=nj(nI,e.__scopeMenu),l=re(ro,e.__scopeMenu),u=s.useRef(null),c=(0,p.e)(t,u);return(0,E.jsx)(ny.Provider,{scope:e.__scopeMenu,children:(0,E.jsx)(tf.z,{present:r||i.open,children:(0,E.jsx)(ny.Slot,{scope:e.__scopeMenu,children:(0,E.jsx)(nz,{id:l.contentId,"aria-labelledby":l.triggerId,...o,ref:c,align:"start",side:"rtl"===a.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{a.isUsingKeyboardRef.current&&u.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,f.M)(e.onFocusOutside,e=>{e.target!==l.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,f.M)(e.onEscapeKeyDown,e=>{a.onClose(),e.preventDefault()}),onKeyDown:(0,f.M)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=nv[a.dir].includes(e.key);t&&n&&(i.onOpenChange(!1),l.trigger?.focus(),e.preventDefault())})})})})})});function ra(e){return e?"open":"closed"}function rl(e){return"indeterminate"===e}function ru(e){return rl(e)?"indeterminate":e?"checked":"unchecked"}function rc(e){return t=>"mouse"===t.pointerType?e(t):void 0}ri.displayName=ro;var rs="DropdownMenu",[rd,rf]=(0,h.b)(rs,[nM]),rp=nM(),[rh,rg]=rd(rs),rv=e=>{let{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:i,onOpenChange:a,modal:l=!0}=e,u=rp(t),c=s.useRef(null),[d,f]=(0,g.T)({prop:o,defaultProp:i??!1,onChange:a,caller:rs});return(0,E.jsx)(rh,{scope:t,triggerId:N(),triggerRef:c,contentId:N(),open:d,onOpenChange:f,onOpenToggle:s.useCallback(()=>f(e=>!e),[f]),modal:l,children:(0,E.jsx)(nT,{...u,open:d,onOpenChange:f,dir:r,modal:l,children:n})})};rv.displayName=rs;var rm="DropdownMenuTrigger",ry=s.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,i=rg(rm,n),a=rp(n);return(0,E.jsx)(nO,{asChild:!0,...a,children:(0,E.jsx)(v.WV.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:(0,p.F)(t,i.triggerRef),onPointerDown:(0,f.M)(e.onPointerDown,e=>{r||0!==e.button||!1!==e.ctrlKey||(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,f.M)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});ry.displayName=rm;var rw=e=>{let{__scopeDropdownMenu:t,...n}=e,r=rp(t);return(0,E.jsx)(nD,{...r,...n})};rw.displayName="DropdownMenuPortal";var rb="DropdownMenuContent",rx=s.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rg(rb,n),i=rp(n),a=s.useRef(!1);return(0,E.jsx)(nZ,{id:o.contentId,"aria-labelledby":o.triggerId,...i,...r,ref:t,onCloseAutoFocus:(0,f.M)(e.onCloseAutoFocus,e=>{a.current||o.triggerRef.current?.focus(),a.current=!1,e.preventDefault()}),onInteractOutside:(0,f.M)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!o.modal||r)&&(a.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});rx.displayName=rb;var rM=s.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rp(n);return(0,E.jsx)(nH,{...o,...r,ref:t})});rM.displayName="DropdownMenuGroup";var rE=s.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rp(n);return(0,E.jsx)(nB,{...o,...r,ref:t})});rE.displayName="DropdownMenuLabel";var rk=s.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rp(n);return(0,E.jsx)(nq,{...o,...r,ref:t})});rk.displayName="DropdownMenuItem";var rR=s.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rp(n);return(0,E.jsx)(nY,{...o,...r,ref:t})});rR.displayName="DropdownMenuCheckboxItem";var rS=s.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rp(n);return(0,E.jsx)(nQ,{...o,...r,ref:t})});rS.displayName="DropdownMenuRadioGroup";var rC=s.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rp(n);return(0,E.jsx)(n1,{...o,...r,ref:t})});rC.displayName="DropdownMenuRadioItem";var rj=s.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rp(n);return(0,E.jsx)(n8,{...o,...r,ref:t})});rj.displayName="DropdownMenuItemIndicator";var rT=s.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rp(n);return(0,E.jsx)(n9,{...o,...r,ref:t})});rT.displayName="DropdownMenuSeparator",s.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rp(n);return(0,E.jsx)(n7,{...o,...r,ref:t})}).displayName="DropdownMenuArrow";var rO=s.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rp(n);return(0,E.jsx)(rr,{...o,...r,ref:t})});rO.displayName="DropdownMenuSubTrigger";var rP=s.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rp(n);return(0,E.jsx)(ri,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});rP.displayName="DropdownMenuSubContent";var rA=rv,rL=ry,rD=rw,rI=rx,rN=rM,r_=rE,rZ=rk,rW=rR,rF=rS,rV=rC,rz=rj,rH=rT,rB=e=>{let{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:o,defaultOpen:i}=e,a=rp(t),[l,u]=(0,g.T)({prop:r,defaultProp:i??!1,onChange:o,caller:"DropdownMenuSub"});return(0,E.jsx)(rt,{...a,open:l,onOpenChange:u,children:n})},rK=rO,rU=rP},7283:function(e,t,n){"use strict";n.d(t,{Ns:function(){return X},fC:function(){return U},gb:function(){return M},q4:function(){return L},l_:function(){return q}});var r=n(2265),o=n(9381),i=n(5606),a=n(6989),l=n(2210),u=n(6459),c=n(5400),s=n(1030),d=n(5744),f=n(7437),p="ScrollArea",[h,g]=(0,a.b)(p),[v,m]=h(p),y=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,type:i="hover",dir:a,scrollHideDelay:u=600,...s}=e,[d,p]=r.useState(null),[h,g]=r.useState(null),[m,y]=r.useState(null),[w,b]=r.useState(null),[x,M]=r.useState(null),[E,k]=r.useState(0),[R,S]=r.useState(0),[C,j]=r.useState(!1),[T,O]=r.useState(!1),P=(0,l.e)(t,e=>p(e)),A=(0,c.gm)(a);return(0,f.jsx)(v,{scope:n,type:i,dir:A,scrollHideDelay:u,scrollArea:d,viewport:h,onViewportChange:g,content:m,onContentChange:y,scrollbarX:w,onScrollbarXChange:b,scrollbarXEnabled:C,onScrollbarXEnabledChange:j,scrollbarY:x,onScrollbarYChange:M,scrollbarYEnabled:T,onScrollbarYEnabledChange:O,onCornerWidthChange:k,onCornerHeightChange:S,children:(0,f.jsx)(o.WV.div,{dir:A,...s,ref:P,style:{position:"relative","--radix-scroll-area-corner-width":E+"px","--radix-scroll-area-corner-height":R+"px",...e.style}})})});y.displayName=p;var w="ScrollAreaViewport",b=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,children:i,nonce:a,...u}=e,c=m(w,n),s=r.useRef(null),d=(0,l.e)(t,s,c.onViewportChange);return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,f.jsx)(o.WV.div,{"data-radix-scroll-area-viewport":"",...u,ref:d,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,f.jsx)("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:i})})]})});b.displayName=w;var x="ScrollAreaScrollbar",M=r.forwardRef((e,t)=>{let{forceMount:n,...o}=e,i=m(x,e.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:l}=i,u="horizontal"===e.orientation;return r.useEffect(()=>(u?a(!0):l(!0),()=>{u?a(!1):l(!1)}),[u,a,l]),"hover"===i.type?(0,f.jsx)(E,{...o,ref:t,forceMount:n}):"scroll"===i.type?(0,f.jsx)(k,{...o,ref:t,forceMount:n}):"auto"===i.type?(0,f.jsx)(R,{...o,ref:t,forceMount:n}):"always"===i.type?(0,f.jsx)(S,{...o,ref:t}):null});M.displayName=x;var E=r.forwardRef((e,t)=>{let{forceMount:n,...o}=e,a=m(x,e.__scopeScrollArea),[l,u]=r.useState(!1);return r.useEffect(()=>{let e=a.scrollArea,t=0;if(e){let n=()=>{window.clearTimeout(t),u(!0)},r=()=>{t=window.setTimeout(()=>u(!1),a.scrollHideDelay)};return e.addEventListener("pointerenter",n),e.addEventListener("pointerleave",r),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",n),e.removeEventListener("pointerleave",r)}}},[a.scrollArea,a.scrollHideDelay]),(0,f.jsx)(i.z,{present:n||l,children:(0,f.jsx)(R,{"data-state":l?"visible":"hidden",...o,ref:t})})}),k=r.forwardRef((e,t)=>{var n;let{forceMount:o,...a}=e,l=m(x,e.__scopeScrollArea),u="horizontal"===e.orientation,c=B(()=>p("SCROLL_END"),100),[s,p]=(n={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},r.useReducer((e,t)=>n[e][t]??e,"hidden"));return r.useEffect(()=>{if("idle"===s){let e=window.setTimeout(()=>p("HIDE"),l.scrollHideDelay);return()=>window.clearTimeout(e)}},[s,l.scrollHideDelay,p]),r.useEffect(()=>{let e=l.viewport,t=u?"scrollLeft":"scrollTop";if(e){let n=e[t],r=()=>{let r=e[t];n!==r&&(p("SCROLL"),c()),n=r};return e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[l.viewport,u,p,c]),(0,f.jsx)(i.z,{present:o||"hidden"!==s,children:(0,f.jsx)(S,{"data-state":"hidden"===s?"hidden":"visible",...a,ref:t,onPointerEnter:(0,d.M)(e.onPointerEnter,()=>p("POINTER_ENTER")),onPointerLeave:(0,d.M)(e.onPointerLeave,()=>p("POINTER_LEAVE"))})})}),R=r.forwardRef((e,t)=>{let n=m(x,e.__scopeScrollArea),{forceMount:o,...a}=e,[l,u]=r.useState(!1),c="horizontal"===e.orientation,s=B(()=>{if(n.viewport){let e=n.viewport.offsetWidth<n.viewport.scrollWidth,t=n.viewport.offsetHeight<n.viewport.scrollHeight;u(c?e:t)}},10);return K(n.viewport,s),K(n.content,s),(0,f.jsx)(i.z,{present:o||l,children:(0,f.jsx)(S,{"data-state":l?"visible":"hidden",...a,ref:t})})}),S=r.forwardRef((e,t)=>{let{orientation:n="vertical",...o}=e,i=m(x,e.__scopeScrollArea),a=r.useRef(null),l=r.useRef(0),[u,c]=r.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),s=W(u.viewport,u.content),d={...o,sizes:u,onSizesChange:c,hasThumb:!!(s>0&&s<1),onThumbChange:e=>a.current=e,onThumbPointerUp:()=>l.current=0,onThumbPointerDown:e=>l.current=e};function p(e,t){return function(e,t,n,r="ltr"){let o=F(n),i=t||o/2,a=n.scrollbar.paddingStart+i,l=n.scrollbar.size-n.scrollbar.paddingEnd-(o-i),u=n.content-n.viewport;return z([a,l],"ltr"===r?[0,u]:[-1*u,0])(e)}(e,l.current,u,t)}return"horizontal"===n?(0,f.jsx)(C,{...d,ref:t,onThumbPositionChange:()=>{if(i.viewport&&a.current){let e=V(i.viewport.scrollLeft,u,i.dir);a.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollLeft=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollLeft=p(e,i.dir))}}):"vertical"===n?(0,f.jsx)(j,{...d,ref:t,onThumbPositionChange:()=>{if(i.viewport&&a.current){let e=V(i.viewport.scrollTop,u);a.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollTop=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollTop=p(e))}}):null}),C=r.forwardRef((e,t)=>{let{sizes:n,onSizesChange:o,...i}=e,a=m(x,e.__scopeScrollArea),[u,c]=r.useState(),s=r.useRef(null),d=(0,l.e)(t,s,a.onScrollbarXChange);return r.useEffect(()=>{s.current&&c(getComputedStyle(s.current))},[s]),(0,f.jsx)(P,{"data-orientation":"horizontal",...i,ref:d,sizes:n,style:{bottom:0,left:"rtl"===a.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===a.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":F(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,n)=>{if(a.viewport){let r=a.viewport.scrollLeft+t.deltaX;e.onWheelScroll(r),r>0&&r<n&&t.preventDefault()}},onResize:()=>{s.current&&a.viewport&&u&&o({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:s.current.clientWidth,paddingStart:Z(u.paddingLeft),paddingEnd:Z(u.paddingRight)}})}})}),j=r.forwardRef((e,t)=>{let{sizes:n,onSizesChange:o,...i}=e,a=m(x,e.__scopeScrollArea),[u,c]=r.useState(),s=r.useRef(null),d=(0,l.e)(t,s,a.onScrollbarYChange);return r.useEffect(()=>{s.current&&c(getComputedStyle(s.current))},[s]),(0,f.jsx)(P,{"data-orientation":"vertical",...i,ref:d,sizes:n,style:{top:0,right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":F(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,n)=>{if(a.viewport){let r=a.viewport.scrollTop+t.deltaY;e.onWheelScroll(r),r>0&&r<n&&t.preventDefault()}},onResize:()=>{s.current&&a.viewport&&u&&o({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:s.current.clientHeight,paddingStart:Z(u.paddingTop),paddingEnd:Z(u.paddingBottom)}})}})}),[T,O]=h(x),P=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,sizes:i,hasThumb:a,onThumbChange:c,onThumbPointerUp:s,onThumbPointerDown:p,onThumbPositionChange:h,onDragScroll:g,onWheelScroll:v,onResize:y,...w}=e,b=m(x,n),[M,E]=r.useState(null),k=(0,l.e)(t,e=>E(e)),R=r.useRef(null),S=r.useRef(""),C=b.viewport,j=i.content-i.viewport,O=(0,u.W)(v),P=(0,u.W)(h),A=B(y,10);function L(e){R.current&&g({x:e.clientX-R.current.left,y:e.clientY-R.current.top})}return r.useEffect(()=>{let e=e=>{let t=e.target;M?.contains(t)&&O(e,j)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[C,M,j,O]),r.useEffect(P,[i,P]),K(M,A),K(b.content,A),(0,f.jsx)(T,{scope:n,scrollbar:M,hasThumb:a,onThumbChange:(0,u.W)(c),onThumbPointerUp:(0,u.W)(s),onThumbPositionChange:P,onThumbPointerDown:(0,u.W)(p),children:(0,f.jsx)(o.WV.div,{...w,ref:k,style:{position:"absolute",...w.style},onPointerDown:(0,d.M)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),R.current=M.getBoundingClientRect(),S.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",b.viewport&&(b.viewport.style.scrollBehavior="auto"),L(e))}),onPointerMove:(0,d.M)(e.onPointerMove,L),onPointerUp:(0,d.M)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=S.current,b.viewport&&(b.viewport.style.scrollBehavior=""),R.current=null})})})}),A="ScrollAreaThumb",L=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=O(A,e.__scopeScrollArea);return(0,f.jsx)(i.z,{present:n||o.hasThumb,children:(0,f.jsx)(D,{ref:t,...r})})}),D=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,style:i,...a}=e,u=m(A,n),c=O(A,n),{onThumbPositionChange:s}=c,p=(0,l.e)(t,e=>c.onThumbChange(e)),h=r.useRef(void 0),g=B(()=>{h.current&&(h.current(),h.current=void 0)},100);return r.useEffect(()=>{let e=u.viewport;if(e){let t=()=>{if(g(),!h.current){let t=H(e,s);h.current=t,s()}};return s(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[u.viewport,g,s]),(0,f.jsx)(o.WV.div,{"data-state":c.hasThumb?"visible":"hidden",...a,ref:p,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:(0,d.M)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),n=e.clientX-t.left,r=e.clientY-t.top;c.onThumbPointerDown({x:n,y:r})}),onPointerUp:(0,d.M)(e.onPointerUp,c.onThumbPointerUp)})});L.displayName=A;var I="ScrollAreaCorner",N=r.forwardRef((e,t)=>{let n=m(I,e.__scopeScrollArea),r=!!(n.scrollbarX&&n.scrollbarY);return"scroll"!==n.type&&r?(0,f.jsx)(_,{...e,ref:t}):null});N.displayName=I;var _=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,...i}=e,a=m(I,n),[l,u]=r.useState(0),[c,s]=r.useState(0),d=!!(l&&c);return K(a.scrollbarX,()=>{let e=a.scrollbarX?.offsetHeight||0;a.onCornerHeightChange(e),s(e)}),K(a.scrollbarY,()=>{let e=a.scrollbarY?.offsetWidth||0;a.onCornerWidthChange(e),u(e)}),d?(0,f.jsx)(o.WV.div,{...i,ref:t,style:{width:l,height:c,position:"absolute",right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:0,...e.style}}):null});function Z(e){return e?parseInt(e,10):0}function W(e,t){let n=e/t;return isNaN(n)?0:n}function F(e){let t=W(e.viewport,e.content),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-n)*t,18)}function V(e,t,n="ltr"){let r=F(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,i=t.scrollbar.size-o,a=t.content-t.viewport,l=function(e,[t,n]){return Math.min(n,Math.max(t,e))}(e,"ltr"===n?[0,a]:[-1*a,0]);return z([0,a],[0,i-r])(l)}function z(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}var H=(e,t=()=>{})=>{let n={left:e.scrollLeft,top:e.scrollTop},r=0;return!function o(){let i={left:e.scrollLeft,top:e.scrollTop},a=n.left!==i.left,l=n.top!==i.top;(a||l)&&t(),n=i,r=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(r)};function B(e,t){let n=(0,u.W)(e),o=r.useRef(0);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),r.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(n,t)},[n,t])}function K(e,t){let n=(0,u.W)(t);(0,s.b)(()=>{let t=0;if(e){let r=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(n)});return r.observe(e),()=>{window.cancelAnimationFrame(t),r.unobserve(e)}}},[e,n])}var U=y,q=b,X=N}}]);