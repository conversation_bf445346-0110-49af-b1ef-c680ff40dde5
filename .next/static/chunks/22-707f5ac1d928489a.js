(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[22],{1981:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},8940:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("AlignLeft",[["line",{x1:"21",x2:"3",y1:"6",y2:"6",key:"1fp77t"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}],["line",{x1:"17",x2:"3",y1:"18",y2:"18",key:"1awlsn"}]])},9865:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},4907:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("BrainCircuit",[["path",{d:"M12 4.5a2.5 2.5 0 0 0-4.96-.46 2.5 2.5 0 0 0-1.98 3 2.5 2.5 0 0 0-1.32 4.24 3 3 0 0 0 .34 5.58 2.5 2.5 0 0 0 2.96 3.08 2.5 2.5 0 0 0 4.91.05L12 20V4.5Z",key:"ixwj2a"}],["path",{d:"M16 8V5c0-1.1.9-2 2-2",key:"13dx7u"}],["path",{d:"M12 13h4",key:"1ku699"}],["path",{d:"M12 18h6a2 2 0 0 1 2 2v1",key:"105ag5"}],["path",{d:"M12 8h8",key:"1lhi5i"}],["path",{d:"M20.5 8a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"1s25gz"}],["path",{d:"M16.5 13a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"127460"}],["path",{d:"M20.5 21a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"fys062"}],["path",{d:"M18.5 3a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"1vib61"}]])},6369:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},6224:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},1738:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},5479:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},8244:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},1097:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]])},5883:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},8004:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},9883:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5432:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},6245:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},6020:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},1274:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Share",[["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["polyline",{points:"16 6 12 2 8 6",key:"m901s6"}],["line",{x1:"12",x2:"12",y1:"2",y2:"15",key:"1p0rca"}]])},2851:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},4658:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("ThumbsDown",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22h0a3.13 3.13 0 0 1-3-3.88Z",key:"s6e0r"}]])},9868:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z",key:"y3tblf"}]])},5790:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},7972:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},5750:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},2369:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},1396:function(e,t,r){e.exports=r(5250)},837:function(e,t,r){"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e){return function t(){for(var r=this,n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return o.length>=e.length?e.apply(this,o):function(){for(var e=arguments.length,n=Array(e),i=0;i<e;i++)n[i]=arguments[i];return t.apply(r,[].concat(o,n))}}}function c(e){return({}).toString.call(e).includes("Object")}function s(e){return"function"==typeof e}r.d(t,{ML:function(){return K}});var d,f,p=u(function(e,t){throw Error(e[t]||e.default)})({initialIsRequired:"initial state is required",initialType:"initial state should be an object",initialContent:"initial state shouldn't be an empty object",handlerType:"handler should be an object or a function",handlersType:"all handlers should be a functions",selectorType:"selector should be a function",changeType:"provided value of changes should be an object",changeField:'it seams you want to change a field in the state which is not specified in the "initial" state',default:"an unknown error accured in `state-local` package"}),h={changes:function(e,t){return c(t)||p("changeType"),Object.keys(t).some(function(t){return!Object.prototype.hasOwnProperty.call(e,t)})&&p("changeField"),t},selector:function(e){s(e)||p("selectorType")},handler:function(e){s(e)||c(e)||p("handlerType"),c(e)&&Object.values(e).some(function(e){return!s(e)})&&p("handlersType")},initial:function(e){e||p("initialIsRequired"),c(e)||p("initialType"),Object.keys(e).length||p("initialContent")}};function g(e,t){return s(t)?t(e.current):t}function v(e,t){return e.current=l(l({},e.current),t),t}function y(e,t,r){return s(t)?t(e.current):Object.keys(r).forEach(function(r){var n;return null===(n=t[r])||void 0===n?void 0:n.call(t,e.current[r])}),r}var w={configIsRequired:"the configuration object is required",configType:"the configuration object should be an object",default:"an unknown error accured in `@monaco-editor/loader` package",deprecation:"Deprecation warning!\n    You are using deprecated way of configuration.\n\n    Instead of using\n      monaco.config({ urls: { monacoBase: '...' } })\n    use\n      monaco.config({ paths: { vs: '...' } })\n\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\n  "},m=(d=function(e,t){throw Error(e[t]||e.default)},function e(){for(var t=this,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return n.length>=d.length?d.apply(this,n):function(){for(var r=arguments.length,o=Array(r),i=0;i<r;i++)o[i]=arguments[i];return e.apply(t,[].concat(n,o))}})(w),b={config:function(e){return e||m("configIsRequired"),({}).toString.call(e).includes("Object")||m("configType"),e.urls?(console.warn(w.deprecation),{paths:{vs:e.urls.monacoBase}}):e}},x=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){return t.reduceRight(function(e,t){return t(e)},e)}},M={type:"cancelation",msg:"operation is manually canceled"},j=function(e){var t=!1,r=new Promise(function(r,n){e.then(function(e){return t?n(M):r(e)}),e.catch(n)});return r.cancel=function(){return t=!0},r},k=function(e){if(Array.isArray(e))return e}(f=({create:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};h.initial(e),h.handler(t);var r={current:e},n=u(y)(r,t),o=u(v)(r),i=u(h.changes)(e),a=u(g)(r);return[function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(e){return e};return h.selector(e),e(r.current)},function(e){(function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){return t.reduceRight(function(e,t){return t(e)},e)}})(n,o,i,a)(e)}]}}).create({config:{paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}},isInitialized:!1,resolve:null,reject:null,monaco:null}))||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,l=e[Symbol.iterator]();!(n=(a=l.next()).done)&&(r.push(a.value),!t||r.length!==t);n=!0);}catch(e){o=!0,i=e}finally{try{n||null==l.return||l.return()}finally{if(o)throw i}}return r}}(f,2)||function(e,t){if(e){if("string"==typeof e)return i(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return i(e,t)}}(f,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),C=k[0],R=k[1];function S(e){return document.body.appendChild(e)}function E(e){var t,r,n=C(function(e){return{config:e.config,reject:e.reject}}),o=(t="".concat(n.config.paths.vs,"/loader.js"),r=document.createElement("script"),t&&(r.src=t),r);return o.onload=function(){return e()},o.onerror=n.reject,o}function O(){var e=C(function(e){return{config:e.config,resolve:e.resolve,reject:e.reject}}),t=window.require;t.config(e.config),t(["vs/editor/editor.main"],function(t){P(t),e.resolve(t)},function(t){e.reject(t)})}function P(e){C().monaco||R({monaco:e})}var T=new Promise(function(e,t){return R({resolve:e,reject:t})}),D={config:function(e){var t=b.config(e),r=t.monaco,n=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(t,["monaco"]);R(function(e){return{config:function e(t,r){return Object.keys(r).forEach(function(n){r[n]instanceof Object&&t[n]&&Object.assign(r[n],e(t[n],r[n]))}),o(o({},t),r)}(e.config,n),monaco:r}})},init:function(){var e=C(function(e){return{monaco:e.monaco,isInitialized:e.isInitialized,resolve:e.resolve}});if(!e.isInitialized){if(R({isInitialized:!0}),e.monaco)return e.resolve(e.monaco),j(T);if(window.monaco&&window.monaco.editor)return P(window.monaco),e.resolve(window.monaco),j(T);x(S,E)(O)}return j(T)},__getMonacoInstance:function(){return C(function(e){return e.monaco})}},L=r(2265),_={wrapper:{display:"flex",position:"relative",textAlign:"initial"},fullWidth:{width:"100%"},hide:{display:"none"}},I={container:{display:"flex",height:"100%",width:"100%",justifyContent:"center",alignItems:"center"}},A=function({children:e}){return L.createElement("div",{style:I.container},e)},N=(0,L.memo)(function({width:e,height:t,isEditorReady:r,loading:n,_ref:o,className:i,wrapperProps:a}){return L.createElement("section",{style:{..._.wrapper,width:e,height:t},...a},!r&&L.createElement(A,null,n),L.createElement("div",{ref:o,style:{..._.fullWidth,...!r&&_.hide},className:i}))}),Z=function(e){(0,L.useEffect)(e,[])},F=function(e,t,r=!0){let n=(0,L.useRef)(!0);(0,L.useEffect)(n.current||!r?()=>{n.current=!1}:e,t)};function V(){}function W(e,t,r,n){return e.editor.getModel(z(e,n))||e.editor.createModel(t,r,n?z(e,n):void 0)}function z(e,t){return e.Uri.parse(t)}(0,L.memo)(function({original:e,modified:t,language:r,originalLanguage:n,modifiedLanguage:o,originalModelPath:i,modifiedModelPath:a,keepCurrentOriginalModel:l=!1,keepCurrentModifiedModel:u=!1,theme:c="light",loading:s="Loading...",options:d={},height:f="100%",width:p="100%",className:h,wrapperProps:g={},beforeMount:v=V,onMount:y=V}){let[w,m]=(0,L.useState)(!1),[b,x]=(0,L.useState)(!0),M=(0,L.useRef)(null),j=(0,L.useRef)(null),k=(0,L.useRef)(null),C=(0,L.useRef)(y),R=(0,L.useRef)(v),S=(0,L.useRef)(!1);Z(()=>{let e=D.init();return e.then(e=>(j.current=e)&&x(!1)).catch(e=>e?.type!=="cancelation"&&console.error("Monaco initialization: error:",e)),()=>{let t;return M.current?(t=M.current?.getModel(),void(l||t?.original?.dispose(),u||t?.modified?.dispose(),M.current?.dispose())):e.cancel()}}),F(()=>{if(M.current&&j.current){let t=M.current.getOriginalEditor(),o=W(j.current,e||"",n||r||"text",i||"");o!==t.getModel()&&t.setModel(o)}},[i],w),F(()=>{if(M.current&&j.current){let e=M.current.getModifiedEditor(),n=W(j.current,t||"",o||r||"text",a||"");n!==e.getModel()&&e.setModel(n)}},[a],w),F(()=>{let e=M.current.getModifiedEditor();e.getOption(j.current.editor.EditorOption.readOnly)?e.setValue(t||""):t!==e.getValue()&&(e.executeEdits("",[{range:e.getModel().getFullModelRange(),text:t||"",forceMoveMarkers:!0}]),e.pushUndoStop())},[t],w),F(()=>{M.current?.getModel()?.original.setValue(e||"")},[e],w),F(()=>{let{original:e,modified:t}=M.current.getModel();j.current.editor.setModelLanguage(e,n||r||"text"),j.current.editor.setModelLanguage(t,o||r||"text")},[r,n,o],w),F(()=>{j.current?.editor.setTheme(c)},[c],w),F(()=>{M.current?.updateOptions(d)},[d],w);let E=(0,L.useCallback)(()=>{if(!j.current)return;R.current(j.current);let l=W(j.current,e||"",n||r||"text",i||""),u=W(j.current,t||"",o||r||"text",a||"");M.current?.setModel({original:l,modified:u})},[r,t,o,e,n,i,a]),O=(0,L.useCallback)(()=>{!S.current&&k.current&&(M.current=j.current.editor.createDiffEditor(k.current,{automaticLayout:!0,...d}),E(),j.current?.editor.setTheme(c),m(!0),S.current=!0)},[d,c,E]);return(0,L.useEffect)(()=>{w&&C.current(M.current,j.current)},[w]),(0,L.useEffect)(()=>{b||w||O()},[b,w,O]),L.createElement(N,{width:p,height:f,isEditorReady:w,loading:s,_ref:k,className:h,wrapperProps:g})});var U=function(e){let t=(0,L.useRef)();return(0,L.useEffect)(()=>{t.current=e},[e]),t.current},H=new Map,K=(0,L.memo)(function({defaultValue:e,defaultLanguage:t,defaultPath:r,value:n,language:o,path:i,theme:a="light",line:l,loading:u="Loading...",options:c={},overrideServices:s={},saveViewState:d=!0,keepCurrentModel:f=!1,width:p="100%",height:h="100%",className:g,wrapperProps:v={},beforeMount:y=V,onMount:w=V,onChange:m,onValidate:b=V}){let[x,M]=(0,L.useState)(!1),[j,k]=(0,L.useState)(!0),C=(0,L.useRef)(null),R=(0,L.useRef)(null),S=(0,L.useRef)(null),E=(0,L.useRef)(w),O=(0,L.useRef)(y),P=(0,L.useRef)(),T=(0,L.useRef)(n),_=U(i),I=(0,L.useRef)(!1),A=(0,L.useRef)(!1);Z(()=>{let e=D.init();return e.then(e=>(C.current=e)&&k(!1)).catch(e=>e?.type!=="cancelation"&&console.error("Monaco initialization: error:",e)),()=>R.current?void(P.current?.dispose(),f?d&&H.set(i,R.current.saveViewState()):R.current.getModel()?.dispose(),R.current.dispose()):e.cancel()}),F(()=>{let a=W(C.current,e||n||"",t||o||"",i||r||"");a!==R.current?.getModel()&&(d&&H.set(_,R.current?.saveViewState()),R.current?.setModel(a),d&&R.current?.restoreViewState(H.get(i)))},[i],x),F(()=>{R.current?.updateOptions(c)},[c],x),F(()=>{R.current&&void 0!==n&&(R.current.getOption(C.current.editor.EditorOption.readOnly)?R.current.setValue(n):n===R.current.getValue()||(A.current=!0,R.current.executeEdits("",[{range:R.current.getModel().getFullModelRange(),text:n,forceMoveMarkers:!0}]),R.current.pushUndoStop(),A.current=!1))},[n],x),F(()=>{let e=R.current?.getModel();e&&o&&C.current?.editor.setModelLanguage(e,o)},[o],x),F(()=>{void 0!==l&&R.current?.revealLine(l)},[l],x),F(()=>{C.current?.editor.setTheme(a)},[a],x);let z=(0,L.useCallback)(()=>{if(!(!S.current||!C.current)&&!I.current){O.current(C.current);let u=i||r,f=W(C.current,n||e||"",t||o||"",u||"");R.current=C.current?.editor.create(S.current,{model:f,automaticLayout:!0,...c},s),d&&R.current.restoreViewState(H.get(u)),C.current.editor.setTheme(a),void 0!==l&&R.current.revealLine(l),M(!0),I.current=!0}},[e,t,r,n,o,i,c,s,d,a,l]);return(0,L.useEffect)(()=>{x&&E.current(R.current,C.current)},[x]),(0,L.useEffect)(()=>{j||x||z()},[j,x,z]),T.current=n,(0,L.useEffect)(()=>{x&&m&&(P.current?.dispose(),P.current=R.current?.onDidChangeModelContent(e=>{A.current||m(R.current.getValue(),e)}))},[x,m]),(0,L.useEffect)(()=>{if(x){let e=C.current.editor.onDidChangeMarkers(e=>{let t=R.current.getModel()?.uri;if(t&&e.find(e=>e.path===t.path)){let e=C.current.editor.getModelMarkers({resource:t});b?.(e)}});return()=>{e?.dispose()}}return()=>{}},[x,b]),L.createElement(N,{width:p,height:h,isEditorReady:x,loading:u,_ref:S,className:g,wrapperProps:v})})},1465:function(e,t,r){"use strict";r.d(t,{NY:function(){return k},Ee:function(){return j},fC:function(){return M}});var n=r(2265),o=r(6989),i=r(6459),a=r(5655),l=r(9381),u=r(6272);function c(){return()=>{}}var s=r(7437),d="Avatar",[f,p]=(0,o.b)(d),[h,g]=f(d),v=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...o}=e,[i,a]=n.useState("idle");return(0,s.jsx)(h,{scope:r,imageLoadingStatus:i,onImageLoadingStatusChange:a,children:(0,s.jsx)(l.WV.span,{...o,ref:t})})});v.displayName=d;var y="AvatarImage",w=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:o,onLoadingStatusChange:d=()=>{},...f}=e,p=g(y,r),h=function(e,{referrerPolicy:t,crossOrigin:r}){let o=(0,u.useSyncExternalStore)(c,()=>!0,()=>!1),i=n.useRef(null),l=o?(i.current||(i.current=new window.Image),i.current):null,[s,d]=n.useState(()=>x(l,e));return(0,a.b)(()=>{d(x(l,e))},[l,e]),(0,a.b)(()=>{let e=e=>()=>{d(e)};if(!l)return;let n=e("loaded"),o=e("error");return l.addEventListener("load",n),l.addEventListener("error",o),t&&(l.referrerPolicy=t),"string"==typeof r&&(l.crossOrigin=r),()=>{l.removeEventListener("load",n),l.removeEventListener("error",o)}},[l,r,t]),s}(o,f),v=(0,i.W)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,a.b)(()=>{"idle"!==h&&v(h)},[h,v]),"loaded"===h?(0,s.jsx)(l.WV.img,{...f,ref:t,src:o}):null});w.displayName=y;var m="AvatarFallback",b=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:o,...i}=e,a=g(m,r),[u,c]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(e)}},[o]),u&&"loaded"!==a.imageLoadingStatus?(0,s.jsx)(l.WV.span,{...i,ref:t}):null});function x(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}b.displayName=m;var M=v,j=w,k=b},8883:function(e,t,r){"use strict";r.d(t,{oC:function(){return tf},VY:function(){return tu},ZA:function(){return tc},ck:function(){return td},wU:function(){return tg},__:function(){return ts},Uv:function(){return tl},Ee:function(){return tp},Rk:function(){return th},fC:function(){return ti},Z0:function(){return tv},Tr:function(){return ty},tu:function(){return tm},fF:function(){return tw},xz:function(){return ta}});var n=r(2265),o=r(5744),i=r(2210),a=r(6989),l=r(3763),u=r(9381),c=r(7733),s=r(5400),d=r(9249),f=r(1244),p=r(2759),h=r(966),g=r(3995),v=r(2730),y=r(5606),w=r(6459),m=r(7437),b="rovingFocusGroup.onEntryFocus",x={bubbles:!1,cancelable:!0},M="RovingFocusGroup",[j,k,C]=(0,c.B)(M),[R,S]=(0,a.b)(M,[C]),[E,O]=R(M),P=n.forwardRef((e,t)=>(0,m.jsx)(j.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(j.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(T,{...e,ref:t})})}));P.displayName=M;var T=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:c=!1,dir:d,currentTabStopId:f,defaultCurrentTabStopId:p,onCurrentTabStopIdChange:h,onEntryFocus:g,preventScrollOnEntryFocus:v=!1,...y}=e,j=n.useRef(null),C=(0,i.e)(t,j),R=(0,s.gm)(d),[S,O]=(0,l.T)({prop:f,defaultProp:p??null,onChange:h,caller:M}),[P,T]=n.useState(!1),D=(0,w.W)(g),L=k(r),_=n.useRef(!1),[A,N]=n.useState(0);return n.useEffect(()=>{let e=j.current;if(e)return e.addEventListener(b,D),()=>e.removeEventListener(b,D)},[D]),(0,m.jsx)(E,{scope:r,orientation:a,dir:R,loop:c,currentTabStopId:S,onItemFocus:n.useCallback(e=>O(e),[O]),onItemShiftTab:n.useCallback(()=>T(!0),[]),onFocusableItemAdd:n.useCallback(()=>N(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>N(e=>e-1),[]),children:(0,m.jsx)(u.WV.div,{tabIndex:P||0===A?-1:0,"data-orientation":a,...y,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{_.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!_.current;if(e.target===e.currentTarget&&t&&!P){let t=new CustomEvent(b,x);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=L().filter(e=>e.focusable);I([e.find(e=>e.active),e.find(e=>e.id===S),...e].filter(Boolean).map(e=>e.ref.current),v)}}_.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>T(!1))})})}),D="RovingFocusGroupItem",L=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:a=!1,tabStopId:l,children:c,...s}=e,d=(0,h.M)(),f=l||d,p=O(D,r),g=p.currentTabStopId===f,v=k(r),{onFocusableItemAdd:y,onFocusableItemRemove:w,currentTabStopId:b}=p;return n.useEffect(()=>{if(i)return y(),()=>w()},[i,y,w]),(0,m.jsx)(j.ItemSlot,{scope:r,id:f,focusable:i,active:a,children:(0,m.jsx)(u.WV.span,{tabIndex:g?0:-1,"data-orientation":p.orientation,...s,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{i?p.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return _[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)o.reverse();else if("prev"===t||"next"===t){var r,n;"prev"===t&&o.reverse();let i=o.indexOf(e.currentTarget);o=p.loop?(r=o,n=i+1,r.map((e,t)=>r[(n+t)%r.length])):o.slice(i+1)}setTimeout(()=>I(o))}}),children:"function"==typeof c?c({isCurrentTabStop:g,hasTabStop:null!=b}):c})})});L.displayName=D;var _={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function I(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var A=r(7256),N=r(5859),Z=r(3386),F=["Enter"," "],V=["ArrowUp","PageDown","End"],W=["ArrowDown","PageUp","Home",...V],z={ltr:[...F,"ArrowRight"],rtl:[...F,"ArrowLeft"]},U={ltr:["ArrowLeft"],rtl:["ArrowRight"]},H="Menu",[K,q,B]=(0,c.B)(H),[X,Y]=(0,a.b)(H,[B,g.D7,S]),G=(0,g.D7)(),$=S(),[J,Q]=X(H),[ee,et]=X(H),er=e=>{let{__scopeMenu:t,open:r=!1,children:o,dir:i,onOpenChange:a,modal:l=!0}=e,u=G(t),[c,d]=n.useState(null),f=n.useRef(!1),p=(0,w.W)(a),h=(0,s.gm)(i);return n.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,m.jsx)(g.fC,{...u,children:(0,m.jsx)(J,{scope:t,open:r,onOpenChange:p,content:c,onContentChange:d,children:(0,m.jsx)(ee,{scope:t,onClose:n.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:h,modal:l,children:o})})})};er.displayName=H;var en=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=G(r);return(0,m.jsx)(g.ee,{...o,...n,ref:t})});en.displayName="MenuAnchor";var eo="MenuPortal",[ei,ea]=X(eo,{forceMount:void 0}),el=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,i=Q(eo,t);return(0,m.jsx)(ei,{scope:t,forceMount:r,children:(0,m.jsx)(y.z,{present:r||i.open,children:(0,m.jsx)(v.h,{asChild:!0,container:o,children:n})})})};el.displayName=eo;var eu="MenuContent",[ec,es]=X(eu),ed=n.forwardRef((e,t)=>{let r=ea(eu,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,i=Q(eu,e.__scopeMenu),a=et(eu,e.__scopeMenu);return(0,m.jsx)(K.Provider,{scope:e.__scopeMenu,children:(0,m.jsx)(y.z,{present:n||i.open,children:(0,m.jsx)(K.Slot,{scope:e.__scopeMenu,children:a.modal?(0,m.jsx)(ef,{...o,ref:t}):(0,m.jsx)(ep,{...o,ref:t})})})})}),ef=n.forwardRef((e,t)=>{let r=Q(eu,e.__scopeMenu),a=n.useRef(null),l=(0,i.e)(t,a);return n.useEffect(()=>{let e=a.current;if(e)return(0,N.Ry)(e)},[]),(0,m.jsx)(eg,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),ep=n.forwardRef((e,t)=>{let r=Q(eu,e.__scopeMenu);return(0,m.jsx)(eg,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),eh=(0,A.Z8)("MenuContent.ScrollLock"),eg=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:a=!1,trapFocus:l,onOpenAutoFocus:u,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:h,onEscapeKeyDown:v,onPointerDownOutside:y,onFocusOutside:w,onInteractOutside:b,onDismiss:x,disableOutsideScroll:M,...j}=e,k=Q(eu,r),C=et(eu,r),R=G(r),S=$(r),E=q(r),[O,T]=n.useState(null),D=n.useRef(null),L=(0,i.e)(t,D,k.onContentChange),_=n.useRef(0),I=n.useRef(""),A=n.useRef(0),N=n.useRef(null),F=n.useRef("right"),z=n.useRef(0),U=M?Z.Z:n.Fragment,H=e=>{let t=I.current+e,r=E().filter(e=>!e.disabled),n=document.activeElement,o=r.find(e=>e.ref.current===n)?.textValue,i=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(i=i.filter(e=>e!==r));let a=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return a!==r?a:void 0}(r.map(e=>e.textValue),t,o),a=r.find(e=>e.textValue===i)?.ref.current;!function e(t){I.current=t,window.clearTimeout(_.current),""!==t&&(_.current=window.setTimeout(()=>e(""),1e3))}(t),a&&setTimeout(()=>a.focus())};n.useEffect(()=>()=>window.clearTimeout(_.current),[]),(0,f.EW)();let K=n.useCallback(e=>{var t;return F.current===N.current?.side&&!!(t=N.current?.area)&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],u=a.x,c=a.y,s=l.x,d=l.y;c>n!=d>n&&r<(s-u)*(n-c)/(d-c)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)},[]);return(0,m.jsx)(ec,{scope:r,searchRef:I,onItemEnter:n.useCallback(e=>{K(e)&&e.preventDefault()},[K]),onItemLeave:n.useCallback(e=>{K(e)||(D.current?.focus(),T(null))},[K]),onTriggerLeave:n.useCallback(e=>{K(e)&&e.preventDefault()},[K]),pointerGraceTimerRef:A,onPointerGraceIntentChange:n.useCallback(e=>{N.current=e},[]),children:(0,m.jsx)(U,{...M?{as:eh,allowPinchZoom:!0}:void 0,children:(0,m.jsx)(p.M,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.M)(u,e=>{e.preventDefault(),D.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,m.jsx)(d.XB,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:v,onPointerDownOutside:y,onFocusOutside:w,onInteractOutside:b,onDismiss:x,children:(0,m.jsx)(P,{asChild:!0,...S,dir:C.dir,orientation:"vertical",loop:a,currentTabStopId:O,onCurrentTabStopIdChange:T,onEntryFocus:(0,o.M)(h,e=>{C.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,m.jsx)(g.VY,{role:"menu","aria-orientation":"vertical","data-state":eU(k.open),"data-radix-menu-content":"",dir:C.dir,...R,...j,ref:L,style:{outline:"none",...j.style},onKeyDown:(0,o.M)(j.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&H(e.key));let o=D.current;if(e.target!==o||!W.includes(e.key))return;e.preventDefault();let i=E().filter(e=>!e.disabled).map(e=>e.ref.current);V.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,o.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(_.current),I.current="")}),onPointerMove:(0,o.M)(e.onPointerMove,eq(e=>{let t=e.target,r=z.current!==e.clientX;if(e.currentTarget.contains(t)&&r){let t=e.clientX>z.current?"right":"left";F.current=t,z.current=e.clientX}}))})})})})})})});ed.displayName=eu;var ev=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,m.jsx)(u.WV.div,{role:"group",...n,ref:t})});ev.displayName="MenuGroup";var ey=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,m.jsx)(u.WV.div,{...n,ref:t})});ey.displayName="MenuLabel";var ew="MenuItem",em="menu.itemSelect",eb=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:a,...l}=e,c=n.useRef(null),s=et(ew,e.__scopeMenu),d=es(ew,e.__scopeMenu),f=(0,i.e)(t,c),p=n.useRef(!1);return(0,m.jsx)(ex,{...l,ref:f,disabled:r,onClick:(0,o.M)(e.onClick,()=>{let e=c.current;if(!r&&e){let t=new CustomEvent(em,{bubbles:!0,cancelable:!0});e.addEventListener(em,e=>a?.(e),{once:!0}),(0,u.jH)(e,t),t.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),p.current=!0},onPointerUp:(0,o.M)(e.onPointerUp,e=>{p.current||e.currentTarget?.click()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;!r&&(!t||" "!==e.key)&&F.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eb.displayName=ew;var ex=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:a=!1,textValue:l,...c}=e,s=es(ew,r),d=$(r),f=n.useRef(null),p=(0,i.e)(t,f),[h,g]=n.useState(!1),[v,y]=n.useState("");return n.useEffect(()=>{let e=f.current;e&&y((e.textContent??"").trim())},[c.children]),(0,m.jsx)(K.ItemSlot,{scope:r,disabled:a,textValue:l??v,children:(0,m.jsx)(L,{asChild:!0,...d,focusable:!a,children:(0,m.jsx)(u.WV.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...c,ref:p,onPointerMove:(0,o.M)(e.onPointerMove,eq(e=>{a?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eq(e=>s.onItemLeave(e))),onFocus:(0,o.M)(e.onFocus,()=>g(!0)),onBlur:(0,o.M)(e.onBlur,()=>g(!1))})})})}),eM=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...i}=e;return(0,m.jsx)(eP,{scope:e.__scopeMenu,checked:r,children:(0,m.jsx)(eb,{role:"menuitemcheckbox","aria-checked":eH(r)?"mixed":r,...i,ref:t,"data-state":eK(r),onSelect:(0,o.M)(i.onSelect,()=>n?.(!!eH(r)||!r),{checkForDefaultPrevented:!1})})})});eM.displayName="MenuCheckboxItem";var ej="MenuRadioGroup",[ek,eC]=X(ej,{value:void 0,onValueChange:()=>{}}),eR=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,i=(0,w.W)(n);return(0,m.jsx)(ek,{scope:e.__scopeMenu,value:r,onValueChange:i,children:(0,m.jsx)(ev,{...o,ref:t})})});eR.displayName=ej;var eS="MenuRadioItem",eE=n.forwardRef((e,t)=>{let{value:r,...n}=e,i=eC(eS,e.__scopeMenu),a=r===i.value;return(0,m.jsx)(eP,{scope:e.__scopeMenu,checked:a,children:(0,m.jsx)(eb,{role:"menuitemradio","aria-checked":a,...n,ref:t,"data-state":eK(a),onSelect:(0,o.M)(n.onSelect,()=>i.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});eE.displayName=eS;var eO="MenuItemIndicator",[eP,eT]=X(eO,{checked:!1}),eD=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,i=eT(eO,r);return(0,m.jsx)(y.z,{present:n||eH(i.checked)||!0===i.checked,children:(0,m.jsx)(u.WV.span,{...o,ref:t,"data-state":eK(i.checked)})})});eD.displayName=eO;var eL=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,m.jsx)(u.WV.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});eL.displayName="MenuSeparator";var e_=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=G(r);return(0,m.jsx)(g.Eh,{...o,...n,ref:t})});e_.displayName="MenuArrow";var eI="MenuSub",[eA,eN]=X(eI),eZ=e=>{let{__scopeMenu:t,children:r,open:o=!1,onOpenChange:i}=e,a=Q(eI,t),l=G(t),[u,c]=n.useState(null),[s,d]=n.useState(null),f=(0,w.W)(i);return n.useEffect(()=>(!1===a.open&&f(!1),()=>f(!1)),[a.open,f]),(0,m.jsx)(g.fC,{...l,children:(0,m.jsx)(J,{scope:t,open:o,onOpenChange:f,content:s,onContentChange:d,children:(0,m.jsx)(eA,{scope:t,contentId:(0,h.M)(),triggerId:(0,h.M)(),trigger:u,onTriggerChange:c,children:r})})})};eZ.displayName=eI;var eF="MenuSubTrigger",eV=n.forwardRef((e,t)=>{let r=Q(eF,e.__scopeMenu),a=et(eF,e.__scopeMenu),l=eN(eF,e.__scopeMenu),u=es(eF,e.__scopeMenu),c=n.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:d}=u,f={__scopeMenu:e.__scopeMenu},p=n.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return n.useEffect(()=>p,[p]),n.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),d(null)}},[s,d]),(0,m.jsx)(en,{asChild:!0,...f,children:(0,m.jsx)(ex,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":l.contentId,"data-state":eU(r.open),...e,ref:(0,i.F)(t,l.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.M)(e.onPointerMove,eq(t=>{u.onItemEnter(t),t.defaultPrevented||e.disabled||r.open||c.current||(u.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eq(e=>{p();let t=r.content?.getBoundingClientRect();if(t){let n=r.content?.dataset.side,o="right"===n,i=t[o?"left":"right"],a=t[o?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:i,y:t.top},{x:a,y:t.top},{x:a,y:t.bottom},{x:i,y:t.bottom}],side:n}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.M)(e.onKeyDown,t=>{let n=""!==u.searchRef.current;!e.disabled&&(!n||" "!==t.key)&&z[a.dir].includes(t.key)&&(r.onOpenChange(!0),r.content?.focus(),t.preventDefault())})})})});eV.displayName=eF;var eW="MenuSubContent",ez=n.forwardRef((e,t)=>{let r=ea(eu,e.__scopeMenu),{forceMount:a=r.forceMount,...l}=e,u=Q(eu,e.__scopeMenu),c=et(eu,e.__scopeMenu),s=eN(eW,e.__scopeMenu),d=n.useRef(null),f=(0,i.e)(t,d);return(0,m.jsx)(K.Provider,{scope:e.__scopeMenu,children:(0,m.jsx)(y.z,{present:a||u.open,children:(0,m.jsx)(K.Slot,{scope:e.__scopeMenu,children:(0,m.jsx)(eg,{id:s.contentId,"aria-labelledby":s.triggerId,...l,ref:f,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{c.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>{e.target!==s.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.M)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=U[c.dir].includes(e.key);t&&r&&(u.onOpenChange(!1),s.trigger?.focus(),e.preventDefault())})})})})})});function eU(e){return e?"open":"closed"}function eH(e){return"indeterminate"===e}function eK(e){return eH(e)?"indeterminate":e?"checked":"unchecked"}function eq(e){return t=>"mouse"===t.pointerType?e(t):void 0}ez.displayName=eW;var eB="DropdownMenu",[eX,eY]=(0,a.b)(eB,[Y]),eG=Y(),[e$,eJ]=eX(eB),eQ=e=>{let{__scopeDropdownMenu:t,children:r,dir:o,open:i,defaultOpen:a,onOpenChange:u,modal:c=!0}=e,s=eG(t),d=n.useRef(null),[f,p]=(0,l.T)({prop:i,defaultProp:a??!1,onChange:u,caller:eB});return(0,m.jsx)(e$,{scope:t,triggerId:(0,h.M)(),triggerRef:d,contentId:(0,h.M)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,m.jsx)(er,{...s,open:f,onOpenChange:p,dir:o,modal:c,children:r})})};eQ.displayName=eB;var e0="DropdownMenuTrigger",e1=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...a}=e,l=eJ(e0,r),c=eG(r);return(0,m.jsx)(en,{asChild:!0,...c,children:(0,m.jsx)(u.WV.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...a,ref:(0,i.F)(t,l.triggerRef),onPointerDown:(0,o.M)(e.onPointerDown,e=>{n||0!==e.button||!1!==e.ctrlKey||(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});e1.displayName=e0;var e2=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eG(t);return(0,m.jsx)(el,{...n,...r})};e2.displayName="DropdownMenuPortal";var e5="DropdownMenuContent",e8=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...i}=e,a=eJ(e5,r),l=eG(r),u=n.useRef(!1);return(0,m.jsx)(ed,{id:a.contentId,"aria-labelledby":a.triggerId,...l,...i,ref:t,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{u.current||a.triggerRef.current?.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.M)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!a.modal||n)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e8.displayName=e5;var e3=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eG(r);return(0,m.jsx)(ev,{...o,...n,ref:t})});e3.displayName="DropdownMenuGroup";var e9=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eG(r);return(0,m.jsx)(ey,{...o,...n,ref:t})});e9.displayName="DropdownMenuLabel";var e4=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eG(r);return(0,m.jsx)(eb,{...o,...n,ref:t})});e4.displayName="DropdownMenuItem";var e7=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eG(r);return(0,m.jsx)(eM,{...o,...n,ref:t})});e7.displayName="DropdownMenuCheckboxItem";var e6=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eG(r);return(0,m.jsx)(eR,{...o,...n,ref:t})});e6.displayName="DropdownMenuRadioGroup";var te=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eG(r);return(0,m.jsx)(eE,{...o,...n,ref:t})});te.displayName="DropdownMenuRadioItem";var tt=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eG(r);return(0,m.jsx)(eD,{...o,...n,ref:t})});tt.displayName="DropdownMenuItemIndicator";var tr=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eG(r);return(0,m.jsx)(eL,{...o,...n,ref:t})});tr.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eG(r);return(0,m.jsx)(e_,{...o,...n,ref:t})}).displayName="DropdownMenuArrow";var tn=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eG(r);return(0,m.jsx)(eV,{...o,...n,ref:t})});tn.displayName="DropdownMenuSubTrigger";var to=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eG(r);return(0,m.jsx)(ez,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});to.displayName="DropdownMenuSubContent";var ti=eQ,ta=e1,tl=e2,tu=e8,tc=e3,ts=e9,td=e4,tf=e7,tp=e6,th=te,tg=tt,tv=tr,ty=e=>{let{__scopeDropdownMenu:t,children:r,open:n,onOpenChange:o,defaultOpen:i}=e,a=eG(t),[u,c]=(0,l.T)({prop:n,defaultProp:i??!1,onChange:o,caller:"DropdownMenuSub"});return(0,m.jsx)(eZ,{...a,open:u,onOpenChange:c,children:r})},tw=tn,tm=to},5331:function(e,t,r){"use strict";r.d(t,{Ns:function(){return Y},fC:function(){return B},gb:function(){return j},l_:function(){return X},q4:function(){return _}});var n=r(2265),o=r(9381),i=r(5606),a=r(6989),l=r(2210),u=r(6459),c=r(5400),s=r(5655),d=r(760),f=r(5744),p=r(7437),h="ScrollArea",[g,v]=(0,a.b)(h),[y,w]=g(h),m=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:i="hover",dir:a,scrollHideDelay:u=600,...s}=e,[d,f]=n.useState(null),[h,g]=n.useState(null),[v,w]=n.useState(null),[m,b]=n.useState(null),[x,M]=n.useState(null),[j,k]=n.useState(0),[C,R]=n.useState(0),[S,E]=n.useState(!1),[O,P]=n.useState(!1),T=(0,l.e)(t,e=>f(e)),D=(0,c.gm)(a);return(0,p.jsx)(y,{scope:r,type:i,dir:D,scrollHideDelay:u,scrollArea:d,viewport:h,onViewportChange:g,content:v,onContentChange:w,scrollbarX:m,onScrollbarXChange:b,scrollbarXEnabled:S,onScrollbarXEnabledChange:E,scrollbarY:x,onScrollbarYChange:M,scrollbarYEnabled:O,onScrollbarYEnabledChange:P,onCornerWidthChange:k,onCornerHeightChange:R,children:(0,p.jsx)(o.WV.div,{dir:D,...s,ref:T,style:{position:"relative","--radix-scroll-area-corner-width":j+"px","--radix-scroll-area-corner-height":C+"px",...e.style}})})});m.displayName=h;var b="ScrollAreaViewport",x=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:i,nonce:a,...u}=e,c=w(b,r),s=n.useRef(null),d=(0,l.e)(t,s,c.onViewportChange);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,p.jsx)(o.WV.div,{"data-radix-scroll-area-viewport":"",...u,ref:d,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,p.jsx)("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:i})})]})});x.displayName=b;var M="ScrollAreaScrollbar",j=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,i=w(M,e.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:l}=i,u="horizontal"===e.orientation;return n.useEffect(()=>(u?a(!0):l(!0),()=>{u?a(!1):l(!1)}),[u,a,l]),"hover"===i.type?(0,p.jsx)(k,{...o,ref:t,forceMount:r}):"scroll"===i.type?(0,p.jsx)(C,{...o,ref:t,forceMount:r}):"auto"===i.type?(0,p.jsx)(R,{...o,ref:t,forceMount:r}):"always"===i.type?(0,p.jsx)(S,{...o,ref:t}):null});j.displayName=M;var k=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,a=w(M,e.__scopeScrollArea),[l,u]=n.useState(!1);return n.useEffect(()=>{let e=a.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),u(!0)},n=()=>{t=window.setTimeout(()=>u(!1),a.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[a.scrollArea,a.scrollHideDelay]),(0,p.jsx)(i.z,{present:r||l,children:(0,p.jsx)(R,{"data-state":l?"visible":"hidden",...o,ref:t})})}),C=n.forwardRef((e,t)=>{var r;let{forceMount:o,...a}=e,l=w(M,e.__scopeScrollArea),u="horizontal"===e.orientation,c=K(()=>d("SCROLL_END"),100),[s,d]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>r[e][t]??e,"hidden"));return n.useEffect(()=>{if("idle"===s){let e=window.setTimeout(()=>d("HIDE"),l.scrollHideDelay);return()=>window.clearTimeout(e)}},[s,l.scrollHideDelay,d]),n.useEffect(()=>{let e=l.viewport,t=u?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(d("SCROLL"),c()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[l.viewport,u,d,c]),(0,p.jsx)(i.z,{present:o||"hidden"!==s,children:(0,p.jsx)(S,{"data-state":"hidden"===s?"hidden":"visible",...a,ref:t,onPointerEnter:(0,f.M)(e.onPointerEnter,()=>d("POINTER_ENTER")),onPointerLeave:(0,f.M)(e.onPointerLeave,()=>d("POINTER_LEAVE"))})})}),R=n.forwardRef((e,t)=>{let r=w(M,e.__scopeScrollArea),{forceMount:o,...a}=e,[l,u]=n.useState(!1),c="horizontal"===e.orientation,s=K(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;u(c?e:t)}},10);return q(r.viewport,s),q(r.content,s),(0,p.jsx)(i.z,{present:o||l,children:(0,p.jsx)(S,{"data-state":l?"visible":"hidden",...a,ref:t})})}),S=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,i=w(M,e.__scopeScrollArea),a=n.useRef(null),l=n.useRef(0),[u,c]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),s=V(u.viewport,u.content),d={...o,sizes:u,onSizesChange:c,hasThumb:!!(s>0&&s<1),onThumbChange:e=>a.current=e,onThumbPointerUp:()=>l.current=0,onThumbPointerDown:e=>l.current=e};function f(e,t){return function(e,t,r,n="ltr"){let o=W(r),i=t||o/2,a=r.scrollbar.paddingStart+i,l=r.scrollbar.size-r.scrollbar.paddingEnd-(o-i),u=r.content-r.viewport;return U([a,l],"ltr"===n?[0,u]:[-1*u,0])(e)}(e,l.current,u,t)}return"horizontal"===r?(0,p.jsx)(E,{...d,ref:t,onThumbPositionChange:()=>{if(i.viewport&&a.current){let e=z(i.viewport.scrollLeft,u,i.dir);a.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollLeft=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollLeft=f(e,i.dir))}}):"vertical"===r?(0,p.jsx)(O,{...d,ref:t,onThumbPositionChange:()=>{if(i.viewport&&a.current){let e=z(i.viewport.scrollTop,u);a.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollTop=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollTop=f(e))}}):null}),E=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...i}=e,a=w(M,e.__scopeScrollArea),[u,c]=n.useState(),s=n.useRef(null),d=(0,l.e)(t,s,a.onScrollbarXChange);return n.useEffect(()=>{s.current&&c(getComputedStyle(s.current))},[s]),(0,p.jsx)(D,{"data-orientation":"horizontal",...i,ref:d,sizes:r,style:{bottom:0,left:"rtl"===a.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===a.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":W(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(a.viewport){let n=a.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),n>0&&n<r&&t.preventDefault()}},onResize:()=>{s.current&&a.viewport&&u&&o({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:s.current.clientWidth,paddingStart:F(u.paddingLeft),paddingEnd:F(u.paddingRight)}})}})}),O=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...i}=e,a=w(M,e.__scopeScrollArea),[u,c]=n.useState(),s=n.useRef(null),d=(0,l.e)(t,s,a.onScrollbarYChange);return n.useEffect(()=>{s.current&&c(getComputedStyle(s.current))},[s]),(0,p.jsx)(D,{"data-orientation":"vertical",...i,ref:d,sizes:r,style:{top:0,right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":W(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(a.viewport){let n=a.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),n>0&&n<r&&t.preventDefault()}},onResize:()=>{s.current&&a.viewport&&u&&o({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:s.current.clientHeight,paddingStart:F(u.paddingTop),paddingEnd:F(u.paddingBottom)}})}})}),[P,T]=g(M),D=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:i,hasThumb:a,onThumbChange:c,onThumbPointerUp:s,onThumbPointerDown:d,onThumbPositionChange:h,onDragScroll:g,onWheelScroll:v,onResize:y,...m}=e,b=w(M,r),[x,j]=n.useState(null),k=(0,l.e)(t,e=>j(e)),C=n.useRef(null),R=n.useRef(""),S=b.viewport,E=i.content-i.viewport,O=(0,u.W)(v),T=(0,u.W)(h),D=K(y,10);function L(e){C.current&&g({x:e.clientX-C.current.left,y:e.clientY-C.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;x?.contains(t)&&O(e,E)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[S,x,E,O]),n.useEffect(T,[i,T]),q(x,D),q(b.content,D),(0,p.jsx)(P,{scope:r,scrollbar:x,hasThumb:a,onThumbChange:(0,u.W)(c),onThumbPointerUp:(0,u.W)(s),onThumbPositionChange:T,onThumbPointerDown:(0,u.W)(d),children:(0,p.jsx)(o.WV.div,{...m,ref:k,style:{position:"absolute",...m.style},onPointerDown:(0,f.M)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),C.current=x.getBoundingClientRect(),R.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",b.viewport&&(b.viewport.style.scrollBehavior="auto"),L(e))}),onPointerMove:(0,f.M)(e.onPointerMove,L),onPointerUp:(0,f.M)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=R.current,b.viewport&&(b.viewport.style.scrollBehavior=""),C.current=null})})})}),L="ScrollAreaThumb",_=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=T(L,e.__scopeScrollArea);return(0,p.jsx)(i.z,{present:r||o.hasThumb,children:(0,p.jsx)(I,{ref:t,...n})})}),I=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:i,...a}=e,u=w(L,r),c=T(L,r),{onThumbPositionChange:s}=c,d=(0,l.e)(t,e=>c.onThumbChange(e)),h=n.useRef(void 0),g=K(()=>{h.current&&(h.current(),h.current=void 0)},100);return n.useEffect(()=>{let e=u.viewport;if(e){let t=()=>{if(g(),!h.current){let t=H(e,s);h.current=t,s()}};return s(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[u.viewport,g,s]),(0,p.jsx)(o.WV.div,{"data-state":c.hasThumb?"visible":"hidden",...a,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:(0,f.M)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;c.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,f.M)(e.onPointerUp,c.onThumbPointerUp)})});_.displayName=L;var A="ScrollAreaCorner",N=n.forwardRef((e,t)=>{let r=w(A,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,p.jsx)(Z,{...e,ref:t}):null});N.displayName=A;var Z=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...i}=e,a=w(A,r),[l,u]=n.useState(0),[c,s]=n.useState(0),d=!!(l&&c);return q(a.scrollbarX,()=>{let e=a.scrollbarX?.offsetHeight||0;a.onCornerHeightChange(e),s(e)}),q(a.scrollbarY,()=>{let e=a.scrollbarY?.offsetWidth||0;a.onCornerWidthChange(e),u(e)}),d?(0,p.jsx)(o.WV.div,{...i,ref:t,style:{width:l,height:c,position:"absolute",right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:0,...e.style}}):null});function F(e){return e?parseInt(e,10):0}function V(e,t){let r=e/t;return isNaN(r)?0:r}function W(e){let t=V(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function z(e,t,r="ltr"){let n=W(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,i=t.scrollbar.size-o,a=t.content-t.viewport,l=(0,d.u)(e,"ltr"===r?[0,a]:[-1*a,0]);return U([0,a],[0,i-n])(l)}function U(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var H=(e,t=()=>{})=>{let r={left:e.scrollLeft,top:e.scrollTop},n=0;return!function o(){let i={left:e.scrollLeft,top:e.scrollTop},a=r.left!==i.left,l=r.top!==i.top;(a||l)&&t(),r=i,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function K(e,t){let r=(0,u.W)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function q(e,t){let r=(0,u.W)(t);(0,s.b)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var B=m,X=x,Y=N}}]);