(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[247],{2894:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},3966:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},1291:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},9224:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},7706:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Columns",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["line",{x1:"12",x2:"12",y1:"3",y2:"21",key:"1efggb"}]])},9670:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},6637:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},1813:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},4689:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])},6264:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2176:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]])},4280:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},1827:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},6357:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]])},6104:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("TestTube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5h0c-1.4 0-2.5-1.1-2.5-2.5V2",key:"187lwq"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]])},3480:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("UserMinus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},4527:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},8784:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("WifiOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 4.17-2.65",key:"11utq1"}],["path",{d:"M10.66 5c4.01-.36 8.14.9 11.34 3.76",key:"hxefdu"}],["path",{d:"M16.85 11.25a10 10 0 0 1 2.22 1.68",key:"q734kn"}],["path",{d:"M5 13a10 10 0 0 1 5.24-2.76",key:"piq4yl"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]])},261:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Wifi",[["path",{d:"M5 13a10 10 0 0 1 14 0",key:"6v8j51"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]])},2104:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},9840:function(e,t,r){var n=r(2601);r(472);var i=r(2265),s=i&&"object"==typeof i&&"default"in i?i:{default:i};function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var a=void 0!==n&&n.env&&!0,l=function(e){return"[object String]"===Object.prototype.toString.call(e)},c=function(){function e(e){var t=void 0===e?{}:e,r=t.name,n=void 0===r?"stylesheet":r,i=t.optimizeForSpeed,s=void 0===i?a:i;h(l(n),"`name` must be a string"),this._name=n,this._deletedRulePlaceholder="#"+n+"-deleted-rule____{}",h("boolean"==typeof s,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=s,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var o=document.querySelector('meta[property="csp-nonce"]');this._nonce=o?o.getAttribute("content"):null}var t,r=e.prototype;return r.setOptimizeForSpeed=function(e){h("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),h(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},r.isOptimizeForSpeed=function(){return this._optimizeForSpeed},r.inject=function(){var e=this;if(h(!this._injected,"sheet already injected"),this._injected=!0,this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(a||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},r.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},r.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},r.insertRule=function(e,t){if(h(l(e),"`insertRule` accepts only strings"),this._optimizeForSpeed){var r=this.getSheet();"number"!=typeof t&&(t=r.cssRules.length);try{r.insertRule(e,t)}catch(t){return a||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var n=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,n))}return this._rulesCount++},r.replaceRule=function(e,t){if(this._optimizeForSpeed){var r=this.getSheet();if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(n){a||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}}else{var n=this._tags[e];h(n,"old rule at index `"+e+"` not found"),n.textContent=t}return e},r.deleteRule=function(e){if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];h(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},r.flush=function(){this._injected=!1,this._rulesCount=0,this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]},r.cssRules=function(){var e=this;return this._tags.reduce(function(t,r){return r?t=t.concat(Array.prototype.map.call(e.getSheetForTag(r).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},r.makeStyleTag=function(e,t,r){t&&h(l(t),"makeStyleTag accepts only strings as second parameter");var n=document.createElement("style");this._nonce&&n.setAttribute("nonce",this._nonce),n.type="text/css",n.setAttribute("data-"+e,""),t&&n.appendChild(document.createTextNode(t));var i=document.head||document.getElementsByTagName("head")[0];return r?i.insertBefore(n,r):i.appendChild(n),n},o(e.prototype,[{key:"length",get:function(){return this._rulesCount}}]),t&&o(e,t),e}();function h(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var u=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},d={};function p(e,t){if(!t)return"jsx-"+e;var r=String(t),n=e+r;return d[n]||(d[n]="jsx-"+u(e+"-"+r)),d[n]}function f(e,t){var r=e+t;return d[r]||(d[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),d[r]}var y=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,n=void 0===r?null:r,i=t.optimizeForSpeed,s=void 0!==i&&i;this._sheet=n||new c({name:"styled-jsx",optimizeForSpeed:s}),this._sheet.inject(),n&&"boolean"==typeof s&&(this._sheet.setOptimizeForSpeed(s),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var r=this.getIdAndRules(e),n=r.styleId,i=r.rules;if(n in this._instancesCounts){this._instancesCounts[n]+=1;return}var s=i.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[n]=s,this._instancesCounts[n]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var n=this._fromServer&&this._fromServer[r];n?(n.parentNode.removeChild(n),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],n=e[1];return s.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:n}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,n=e.id;if(r){var i=p(n,r);return{styleId:i,rules:Array.isArray(t)?t.map(function(e){return f(i,e)}):[f(i,t)]}}return{styleId:p(n),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=i.createContext(null);m.displayName="StyleSheetContext";var g=s.default.useInsertionEffect||s.default.useLayoutEffect,v=new y;function _(e){var t=v||i.useContext(m);return t&&g(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)]),null}_.dynamic=function(e){return e.map(function(e){return p(e[0],e[1])}).join(" ")},t.style=_},9102:function(e,t,r){e.exports=r(9840).style},472:function(){},6062:function(e,t,r){"use strict";r.d(t,{fC:function(){return w},z$:function(){return x}});var n=r(2265),i=r(2210),s=r(6989),o=r(5744),a=r(3763),l=r(5184),c=r(4977),h=r(5606),u=r(9381),d=r(7437),p="Checkbox",[f,y]=(0,s.b)(p),[m,g]=f(p);function v(e){let{__scopeCheckbox:t,checked:r,children:i,defaultChecked:s,disabled:o,form:l,name:c,onCheckedChange:h,required:u,value:f="on",internal_do_not_use_render:y}=e,[g,v]=(0,a.T)({prop:r,defaultProp:s??!1,onChange:h,caller:p}),[_,b]=n.useState(null),[w,k]=n.useState(null),x=n.useRef(!1),S=!_||!!l||!!_.closest("form"),C={checked:g,disabled:o,setChecked:v,control:_,setControl:b,name:c,form:l,value:f,hasConsumerStoppedPropagationRef:x,required:u,defaultChecked:!R(s)&&s,isFormControl:S,bubbleInput:w,setBubbleInput:k};return(0,d.jsx)(m,{scope:t,...C,children:"function"==typeof y?y(C):i})}var _="CheckboxTrigger",b=n.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...s},a)=>{let{control:l,value:c,disabled:h,checked:p,required:f,setControl:y,setChecked:m,hasConsumerStoppedPropagationRef:v,isFormControl:b,bubbleInput:w}=g(_,e),k=(0,i.e)(a,y),x=n.useRef(p);return n.useEffect(()=>{let e=l?.form;if(e){let t=()=>m(x.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[l,m]),(0,d.jsx)(u.WV.button,{type:"button",role:"checkbox","aria-checked":R(p)?"mixed":p,"aria-required":f,"data-state":E(p),"data-disabled":h?"":void 0,disabled:h,value:c,...s,ref:k,onKeyDown:(0,o.M)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,o.M)(r,e=>{m(e=>!!R(e)||!e),w&&b&&(v.current=e.isPropagationStopped(),v.current||e.stopPropagation())})})});b.displayName=_;var w=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:i,defaultChecked:s,required:o,disabled:a,value:l,onCheckedChange:c,form:h,...u}=e;return(0,d.jsx)(v,{__scopeCheckbox:r,checked:i,defaultChecked:s,disabled:a,required:o,onCheckedChange:c,name:n,form:h,value:l,internal_do_not_use_render:({isFormControl:e})=>(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(b,{...u,ref:t,__scopeCheckbox:r}),e&&(0,d.jsx)(C,{__scopeCheckbox:r})]})})});w.displayName=p;var k="CheckboxIndicator",x=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...i}=e,s=g(k,r);return(0,d.jsx)(h.z,{present:n||R(s.checked)||!0===s.checked,children:(0,d.jsx)(u.WV.span,{"data-state":E(s.checked),"data-disabled":s.disabled?"":void 0,...i,ref:t,style:{pointerEvents:"none",...e.style}})})});x.displayName=k;var S="CheckboxBubbleInput",C=n.forwardRef(({__scopeCheckbox:e,...t},r)=>{let{control:s,hasConsumerStoppedPropagationRef:o,checked:a,defaultChecked:h,required:p,disabled:f,name:y,value:m,form:v,bubbleInput:_,setBubbleInput:b}=g(S,e),w=(0,i.e)(r,b),k=(0,l.D)(a),x=(0,c.t)(s);n.useEffect(()=>{if(!_)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!o.current;if(k!==a&&e){let r=new Event("click",{bubbles:t});_.indeterminate=R(a),e.call(_,!R(a)&&a),_.dispatchEvent(r)}},[_,k,a,o]);let C=n.useRef(!R(a)&&a);return(0,d.jsx)(u.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:h??C.current,required:p,disabled:f,name:y,value:m,form:v,...t,tabIndex:-1,ref:w,style:{...t.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function R(e){return"indeterminate"===e}function E(e){return R(e)?"indeterminate":e?"checked":"unchecked"}C.displayName=S},1927:function(e,t,r){"use strict";r.d(t,{Fw:function(){return k},fC:function(){return C},wy:function(){return b}});var n=r(2265),i=r(5744),s=r(6989),o=r(3763),a=r(5655),l=r(2210),c=r(9381),h=r(5606),u=r(966),d=r(7437),p="Collapsible",[f,y]=(0,s.b)(p),[m,g]=f(p),v=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:i,defaultOpen:s,disabled:a,onOpenChange:l,...h}=e,[f,y]=(0,o.T)({prop:i,defaultProp:s??!1,onChange:l,caller:p});return(0,d.jsx)(m,{scope:r,disabled:a,contentId:(0,u.M)(),open:f,onOpenToggle:n.useCallback(()=>y(e=>!e),[y]),children:(0,d.jsx)(c.WV.div,{"data-state":S(f),"data-disabled":a?"":void 0,...h,ref:t})})});v.displayName=p;var _="CollapsibleTrigger",b=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,...n}=e,s=g(_,r);return(0,d.jsx)(c.WV.button,{type:"button","aria-controls":s.contentId,"aria-expanded":s.open||!1,"data-state":S(s.open),"data-disabled":s.disabled?"":void 0,disabled:s.disabled,...n,ref:t,onClick:(0,i.M)(e.onClick,s.onOpenToggle)})});b.displayName=_;var w="CollapsibleContent",k=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,i=g(w,e.__scopeCollapsible);return(0,d.jsx)(h.z,{present:r||i.open,children:({present:e})=>(0,d.jsx)(x,{...n,ref:t,present:e})})});k.displayName=w;var x=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:i,children:s,...o}=e,h=g(w,r),[u,p]=n.useState(i),f=n.useRef(null),y=(0,l.e)(t,f),m=n.useRef(0),v=m.current,_=n.useRef(0),b=_.current,k=h.open||u,x=n.useRef(k),C=n.useRef(void 0);return n.useEffect(()=>{let e=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.b)(()=>{let e=f.current;if(e){C.current=C.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();m.current=t.height,_.current=t.width,x.current||(e.style.transitionDuration=C.current.transitionDuration,e.style.animationName=C.current.animationName),p(i)}},[h.open,i]),(0,d.jsx)(c.WV.div,{"data-state":S(h.open),"data-disabled":h.disabled?"":void 0,id:h.contentId,hidden:!k,...o,ref:y,style:{"--radix-collapsible-content-height":v?`${v}px`:void 0,"--radix-collapsible-content-width":b?`${b}px`:void 0,...e.style},children:k&&s})});function S(e){return e?"open":"closed"}var C=v},6743:function(e,t,r){"use strict";r.d(t,{f:function(){return a}});var n=r(2265),i=r(9381),s=r(7437),o=n.forwardRef((e,t)=>(0,s.jsx)(i.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var a=o},8010:function(e,t,r){"use strict";r.d(t,{$G:function(){return eU},B4:function(){return eN},JO:function(){return eB},VY:function(){return eL},Z0:function(){return eW},ZA:function(){return eI},__:function(){return eD},ck:function(){return eq},eT:function(){return eF},fC:function(){return ej},h_:function(){return eM},l_:function(){return eP},u_:function(){return ez},wU:function(){return eV},xz:function(){return eO}});var n=r(2265),i=r(4887),s=r(760),o=r(5744),a=r(7733),l=r(2210),c=r(6989),h=r(5400),u=r(9249),d=r(1244),p=r(2759),f=r(966),y=r(3995),m=r(2730),g=r(9381),v=r(7256),_=r(6459),b=r(3763),w=r(5655),k=r(5184),x=r(8281),S=r(5859),C=r(3386),R=r(7437),E=[" ","Enter","ArrowUp","ArrowDown"],T=[" ","Enter"],A="Select",[j,O,N]=(0,a.B)(A),[B,M]=(0,c.b)(A,[N,y.D7]),L=(0,y.D7)(),[P,I]=B(A),[D,q]=B(A),F=e=>{let{__scopeSelect:t,children:r,open:i,defaultOpen:s,onOpenChange:o,value:a,defaultValue:l,onValueChange:c,dir:u,name:d,autoComplete:p,disabled:m,required:g,form:v}=e,_=L(t),[w,k]=n.useState(null),[x,S]=n.useState(null),[C,E]=n.useState(!1),T=(0,h.gm)(u),[O,N]=(0,b.T)({prop:i,defaultProp:s??!1,onChange:o,caller:A}),[B,M]=(0,b.T)({prop:a,defaultProp:l,onChange:c,caller:A}),I=n.useRef(null),q=!w||v||!!w.closest("form"),[F,V]=n.useState(new Set),z=Array.from(F).map(e=>e.props.value).join(";");return(0,R.jsx)(y.fC,{..._,children:(0,R.jsxs)(P,{required:g,scope:t,trigger:w,onTriggerChange:k,valueNode:x,onValueNodeChange:S,valueNodeHasChildren:C,onValueNodeHasChildrenChange:E,contentId:(0,f.M)(),value:B,onValueChange:M,open:O,onOpenChange:N,dir:T,triggerPointerDownPosRef:I,disabled:m,children:[(0,R.jsx)(j.Provider,{scope:t,children:(0,R.jsx)(D,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{V(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{V(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),q?(0,R.jsxs)(eR,{"aria-hidden":!0,required:g,tabIndex:-1,name:d,autoComplete:p,value:B,onChange:e=>M(e.target.value),disabled:m,form:v,children:[void 0===B?(0,R.jsx)("option",{value:""}):null,Array.from(F)]},z):null]})})};F.displayName=A;var V="SelectTrigger",z=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:i=!1,...s}=e,a=L(r),c=I(V,r),h=c.disabled||i,u=(0,l.e)(t,c.onTriggerChange),d=O(r),p=n.useRef("touch"),[f,m,v]=eT(e=>{let t=d().filter(e=>!e.disabled),r=t.find(e=>e.value===c.value),n=eA(t,e,r);void 0!==n&&c.onValueChange(n.value)}),_=e=>{h||(c.onOpenChange(!0),v()),e&&(c.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,R.jsx)(y.ee,{asChild:!0,...a,children:(0,R.jsx)(g.WV.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:h,"data-disabled":h?"":void 0,"data-placeholder":eE(c.value)?"":void 0,...s,ref:u,onClick:(0,o.M)(s.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&_(e)}),onPointerDown:(0,o.M)(s.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(_(e),e.preventDefault())}),onKeyDown:(0,o.M)(s.onKeyDown,e=>{let t=""!==f.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&E.includes(e.key)&&(_(),e.preventDefault())})})})});z.displayName=V;var U="SelectValue",W=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:i,children:s,placeholder:o="",...a}=e,c=I(U,r),{onValueNodeHasChildrenChange:h}=c,u=void 0!==s,d=(0,l.e)(t,c.onValueNodeChange);return(0,w.b)(()=>{h(u)},[h,u]),(0,R.jsx)(g.WV.span,{...a,ref:d,style:{pointerEvents:"none"},children:eE(c.value)?(0,R.jsx)(R.Fragment,{children:o}):s})});W.displayName=U;var H=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...i}=e;return(0,R.jsx)(g.WV.span,{"aria-hidden":!0,...i,ref:t,children:n||"▼"})});H.displayName="SelectIcon";var Z=e=>(0,R.jsx)(m.h,{asChild:!0,...e});Z.displayName="SelectPortal";var K="SelectContent",Y=n.forwardRef((e,t)=>{let r=I(K,e.__scopeSelect),[s,o]=n.useState();return((0,w.b)(()=>{o(new DocumentFragment)},[]),r.open)?(0,R.jsx)(Q,{...e,ref:t}):s?i.createPortal((0,R.jsx)($,{scope:e.__scopeSelect,children:(0,R.jsx)(j.Slot,{scope:e.__scopeSelect,children:(0,R.jsx)("div",{children:e.children})})}),s):null});Y.displayName=K;var[$,X]=B(K),J=(0,v.Z8)("SelectContent.RemoveScroll"),Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:i="item-aligned",onCloseAutoFocus:s,onEscapeKeyDown:a,onPointerDownOutside:c,side:h,sideOffset:f,align:y,alignOffset:m,arrowPadding:g,collisionBoundary:v,collisionPadding:_,sticky:b,hideWhenDetached:w,avoidCollisions:k,...x}=e,E=I(K,r),[T,A]=n.useState(null),[j,N]=n.useState(null),B=(0,l.e)(t,e=>A(e)),[M,L]=n.useState(null),[P,D]=n.useState(null),q=O(r),[F,V]=n.useState(!1),z=n.useRef(!1);n.useEffect(()=>{if(T)return(0,S.Ry)(T)},[T]),(0,d.EW)();let U=n.useCallback(e=>{let[t,...r]=q().map(e=>e.ref.current),[n]=r.slice(-1),i=document.activeElement;for(let r of e)if(r===i||(r?.scrollIntoView({block:"nearest"}),r===t&&j&&(j.scrollTop=0),r===n&&j&&(j.scrollTop=j.scrollHeight),r?.focus(),document.activeElement!==i))return},[q,j]),W=n.useCallback(()=>U([M,T]),[U,M,T]);n.useEffect(()=>{F&&W()},[F,W]);let{onOpenChange:H,triggerPointerDownPosRef:Z}=E;n.useEffect(()=>{if(T){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(Z.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(Z.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():T.contains(r.target)||H(!1),document.removeEventListener("pointermove",t),Z.current=null};return null!==Z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[T,H,Z]),n.useEffect(()=>{let e=()=>H(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[H]);let[Y,X]=eT(e=>{let t=q().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eA(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),Q=n.useCallback((e,t,r)=>{let n=!z.current&&!r;(void 0!==E.value&&E.value===t||n)&&(L(e),n&&(z.current=!0))},[E.value]),et=n.useCallback(()=>T?.focus(),[T]),er=n.useCallback((e,t,r)=>{let n=!z.current&&!r;(void 0!==E.value&&E.value===t||n)&&D(e)},[E.value]),en="popper"===i?ee:G,ei=en===ee?{side:h,sideOffset:f,align:y,alignOffset:m,arrowPadding:g,collisionBoundary:v,collisionPadding:_,sticky:b,hideWhenDetached:w,avoidCollisions:k}:{};return(0,R.jsx)($,{scope:r,content:T,viewport:j,onViewportChange:N,itemRefCallback:Q,selectedItem:M,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:W,selectedItemText:P,position:i,isPositioned:F,searchRef:Y,children:(0,R.jsx)(C.Z,{as:J,allowPinchZoom:!0,children:(0,R.jsx)(p.M,{asChild:!0,trapped:E.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,o.M)(s,e=>{E.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,R.jsx)(u.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>E.onOpenChange(!1),children:(0,R.jsx)(en,{role:"listbox",id:E.contentId,"data-state":E.open?"open":"closed",dir:E.dir,onContextMenu:e=>e.preventDefault(),...x,...ei,onPlaced:()=>V(!0),ref:B,style:{display:"flex",flexDirection:"column",outline:"none",...x.style},onKeyDown:(0,o.M)(x.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||X(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=q().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>U(t)),e.preventDefault()}})})})})})})});Q.displayName="SelectContentImpl";var G=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:i,...o}=e,a=I(K,r),c=X(K,r),[h,u]=n.useState(null),[d,p]=n.useState(null),f=(0,l.e)(t,e=>p(e)),y=O(r),m=n.useRef(!1),v=n.useRef(!0),{viewport:_,selectedItem:b,selectedItemText:k,focusSelectedItem:x}=c,S=n.useCallback(()=>{if(a.trigger&&a.valueNode&&h&&d&&_&&b&&k){let e=a.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),r=a.valueNode.getBoundingClientRect(),n=k.getBoundingClientRect();if("rtl"!==a.dir){let i=n.left-t.left,o=r.left-i,a=e.left-o,l=e.width+a,c=Math.max(l,t.width),u=window.innerWidth-10,d=(0,s.u)(o,[10,Math.max(10,u-c)]);h.style.minWidth=l+"px",h.style.left=d+"px"}else{let i=t.right-n.right,o=window.innerWidth-r.right-i,a=window.innerWidth-e.right-o,l=e.width+a,c=Math.max(l,t.width),u=window.innerWidth-10,d=(0,s.u)(o,[10,Math.max(10,u-c)]);h.style.minWidth=l+"px",h.style.right=d+"px"}let o=y(),l=window.innerHeight-20,c=_.scrollHeight,u=window.getComputedStyle(d),p=parseInt(u.borderTopWidth,10),f=parseInt(u.paddingTop,10),g=parseInt(u.borderBottomWidth,10),v=p+f+c+parseInt(u.paddingBottom,10)+g,w=Math.min(5*b.offsetHeight,v),x=window.getComputedStyle(_),S=parseInt(x.paddingTop,10),C=parseInt(x.paddingBottom,10),R=e.top+e.height/2-10,E=b.offsetHeight/2,T=p+f+(b.offsetTop+E);if(T<=R){let e=o.length>0&&b===o[o.length-1].ref.current;h.style.bottom="0px";let t=d.clientHeight-_.offsetTop-_.offsetHeight;h.style.height=T+Math.max(l-R,E+(e?C:0)+t+g)+"px"}else{let e=o.length>0&&b===o[0].ref.current;h.style.top="0px";let t=Math.max(R,p+_.offsetTop+(e?S:0)+E);h.style.height=t+(v-T)+"px",_.scrollTop=T-R+_.offsetTop}h.style.margin="10px 0",h.style.minHeight=w+"px",h.style.maxHeight=l+"px",i?.(),requestAnimationFrame(()=>m.current=!0)}},[y,a.trigger,a.valueNode,h,d,_,b,k,a.dir,i]);(0,w.b)(()=>S(),[S]);let[C,E]=n.useState();(0,w.b)(()=>{d&&E(window.getComputedStyle(d).zIndex)},[d]);let T=n.useCallback(e=>{e&&!0===v.current&&(S(),x?.(),v.current=!1)},[S,x]);return(0,R.jsx)(et,{scope:r,contentWrapper:h,shouldExpandOnScrollRef:m,onScrollButtonChange:T,children:(0,R.jsx)("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C},children:(0,R.jsx)(g.WV.div,{...o,ref:f,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});G.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:i=10,...s}=e,o=L(r);return(0,R.jsx)(y.VY,{...o,...s,ref:t,align:n,collisionPadding:i,style:{boxSizing:"border-box",...s.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=B(K,{}),en="SelectViewport",ei=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:i,...s}=e,a=X(en,r),c=er(en,r),h=(0,l.e)(t,a.onViewportChange),u=n.useRef(0);return(0,R.jsxs)(R.Fragment,{children:[(0,R.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,R.jsx)(j.Slot,{scope:r,children:(0,R.jsx)(g.WV.div,{"data-radix-select-viewport":"",role:"presentation",...s,ref:h,style:{position:"relative",flex:1,overflow:"hidden auto",...s.style},onScroll:(0,o.M)(s.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=c;if(n?.current&&r){let e=Math.abs(u.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,i=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(i<n){let s=i+e,o=Math.min(n,s),a=s-o;r.style.height=o+"px","0px"===r.style.bottom&&(t.scrollTop=a>0?a:0,r.style.justifyContent="flex-end")}}}u.current=t.scrollTop})})})]})});ei.displayName=en;var es="SelectGroup",[eo,ea]=B(es),el=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,i=(0,f.M)();return(0,R.jsx)(eo,{scope:r,id:i,children:(0,R.jsx)(g.WV.div,{role:"group","aria-labelledby":i,...n,ref:t})})});el.displayName=es;var ec="SelectLabel",eh=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,i=ea(ec,r);return(0,R.jsx)(g.WV.div,{id:i.id,...n,ref:t})});eh.displayName=ec;var eu="SelectItem",[ed,ep]=B(eu),ef=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:i,disabled:s=!1,textValue:a,...c}=e,h=I(eu,r),u=X(eu,r),d=h.value===i,[p,y]=n.useState(a??""),[m,v]=n.useState(!1),_=(0,l.e)(t,e=>u.itemRefCallback?.(e,i,s)),b=(0,f.M)(),w=n.useRef("touch"),k=()=>{s||(h.onValueChange(i),h.onOpenChange(!1))};if(""===i)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,R.jsx)(ed,{scope:r,value:i,disabled:s,textId:b,isSelected:d,onItemTextChange:n.useCallback(e=>{y(t=>t||(e?.textContent??"").trim())},[]),children:(0,R.jsx)(j.ItemSlot,{scope:r,value:i,disabled:s,textValue:p,children:(0,R.jsx)(g.WV.div,{role:"option","aria-labelledby":b,"data-highlighted":m?"":void 0,"aria-selected":d&&m,"data-state":d?"checked":"unchecked","aria-disabled":s||void 0,"data-disabled":s?"":void 0,tabIndex:s?void 0:-1,...c,ref:_,onFocus:(0,o.M)(c.onFocus,()=>v(!0)),onBlur:(0,o.M)(c.onBlur,()=>v(!1)),onClick:(0,o.M)(c.onClick,()=>{"mouse"!==w.current&&k()}),onPointerUp:(0,o.M)(c.onPointerUp,()=>{"mouse"===w.current&&k()}),onPointerDown:(0,o.M)(c.onPointerDown,e=>{w.current=e.pointerType}),onPointerMove:(0,o.M)(c.onPointerMove,e=>{w.current=e.pointerType,s?u.onItemLeave?.():"mouse"===w.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,o.M)(c.onPointerLeave,e=>{e.currentTarget===document.activeElement&&u.onItemLeave?.()}),onKeyDown:(0,o.M)(c.onKeyDown,e=>{u.searchRef?.current!==""&&" "===e.key||(T.includes(e.key)&&k()," "===e.key&&e.preventDefault())})})})})});ef.displayName=eu;var ey="SelectItemText",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:s,style:o,...a}=e,c=I(ey,r),h=X(ey,r),u=ep(ey,r),d=q(ey,r),[p,f]=n.useState(null),y=(0,l.e)(t,e=>f(e),u.onItemTextChange,e=>h.itemTextRefCallback?.(e,u.value,u.disabled)),m=p?.textContent,v=n.useMemo(()=>(0,R.jsx)("option",{value:u.value,disabled:u.disabled,children:m},u.value),[u.disabled,u.value,m]),{onNativeOptionAdd:_,onNativeOptionRemove:b}=d;return(0,w.b)(()=>(_(v),()=>b(v)),[_,b,v]),(0,R.jsxs)(R.Fragment,{children:[(0,R.jsx)(g.WV.span,{id:u.textId,...a,ref:y}),u.isSelected&&c.valueNode&&!c.valueNodeHasChildren?i.createPortal(a.children,c.valueNode):null]})});em.displayName=ey;var eg="SelectItemIndicator",ev=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ep(eg,r).isSelected?(0,R.jsx)(g.WV.span,{"aria-hidden":!0,...n,ref:t}):null});ev.displayName=eg;var e_="SelectScrollUpButton",eb=n.forwardRef((e,t)=>{let r=X(e_,e.__scopeSelect),i=er(e_,e.__scopeSelect),[s,o]=n.useState(!1),a=(0,l.e)(t,i.onScrollButtonChange);return(0,w.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){o(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),s?(0,R.jsx)(ex,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eb.displayName=e_;var ew="SelectScrollDownButton",ek=n.forwardRef((e,t)=>{let r=X(ew,e.__scopeSelect),i=er(ew,e.__scopeSelect),[s,o]=n.useState(!1),a=(0,l.e)(t,i.onScrollButtonChange);return(0,w.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;o(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),s?(0,R.jsx)(ex,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ek.displayName=ew;var ex=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:i,...s}=e,a=X("SelectScrollButton",r),l=n.useRef(null),c=O(r),h=n.useCallback(()=>{null!==l.current&&(window.clearInterval(l.current),l.current=null)},[]);return n.useEffect(()=>()=>h(),[h]),(0,w.b)(()=>{let e=c().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[c]),(0,R.jsx)(g.WV.div,{"aria-hidden":!0,...s,ref:t,style:{flexShrink:0,...s.style},onPointerDown:(0,o.M)(s.onPointerDown,()=>{null===l.current&&(l.current=window.setInterval(i,50))}),onPointerMove:(0,o.M)(s.onPointerMove,()=>{a.onItemLeave?.(),null===l.current&&(l.current=window.setInterval(i,50))}),onPointerLeave:(0,o.M)(s.onPointerLeave,()=>{h()})})}),eS=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,R.jsx)(g.WV.div,{"aria-hidden":!0,...n,ref:t})});eS.displayName="SelectSeparator";var eC="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,i=L(r),s=I(eC,r),o=X(eC,r);return s.open&&"popper"===o.position?(0,R.jsx)(y.Eh,{...i,...n,ref:t}):null}).displayName=eC;var eR=n.forwardRef(({__scopeSelect:e,value:t,...r},i)=>{let s=n.useRef(null),o=(0,l.e)(i,s),a=(0,k.D)(t);return n.useEffect(()=>{let e=s.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==t&&r){let n=new Event("change",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[a,t]),(0,R.jsx)(g.WV.select,{...r,style:{...x.C2,...r.style},ref:o,defaultValue:t})});function eE(e){return""===e||void 0===e}function eT(e){let t=(0,_.W)(e),r=n.useRef(""),i=n.useRef(0),s=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(i.current),""!==t&&(i.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),o=n.useCallback(()=>{r.current="",window.clearTimeout(i.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(i.current),[]),[r,s,o]}function eA(e,t,r){var n;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,s=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===i.length&&(s=s.filter(e=>e!==r));let o=s.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return o!==r?o:void 0}eR.displayName="SelectBubbleInput";var ej=F,eO=z,eN=W,eB=H,eM=Z,eL=Y,eP=ei,eI=el,eD=eh,eq=ef,eF=em,eV=ev,ez=eb,eU=ek,eW=eS},5184:function(e,t,r){"use strict";r.d(t,{D:function(){return i}});var n=r(2265);function i(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},8281:function(e,t,r){"use strict";r.d(t,{C2:function(){return o},TX:function(){return a}});var n=r(2265),i=r(9381),s=r(7437),o=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=n.forwardRef((e,t)=>(0,s.jsx)(i.WV.span,{...e,ref:t,style:{...o,...e.style}}));a.displayName="VisuallyHidden"},4337:function(e,t,r){"use strict";let n,i;r.d(t,{io:function(){return eR}});var s,o,a={};r.r(a),r.d(a,{Decoder:function(){return ev},Encoder:function(){return em},PacketType:function(){return o},protocol:function(){return ey}});let l=Object.create(null);l.open="0",l.close="1",l.ping="2",l.pong="3",l.message="4",l.upgrade="5",l.noop="6";let c=Object.create(null);Object.keys(l).forEach(e=>{c[l[e]]=e});let h={type:"error",data:"parser error"},u="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),d="function"==typeof ArrayBuffer,p=e=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer instanceof ArrayBuffer,f=({type:e,data:t},r,n)=>u&&t instanceof Blob?r?n(t):y(t,n):d&&(t instanceof ArrayBuffer||p(t))?r?n(t):y(new Blob([t]),n):n(l[e]+(t||"")),y=(e,t)=>{let r=new FileReader;return r.onload=function(){t("b"+(r.result.split(",")[1]||""))},r.readAsDataURL(e)};function m(e){return e instanceof Uint8Array?e:e instanceof ArrayBuffer?new Uint8Array(e):new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}let g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",v="undefined"==typeof Uint8Array?[]:new Uint8Array(256);for(let e=0;e<g.length;e++)v[g.charCodeAt(e)]=e;let _=e=>{let t=.75*e.length,r=e.length,n,i=0,s,o,a,l;"="===e[e.length-1]&&(t--,"="===e[e.length-2]&&t--);let c=new ArrayBuffer(t),h=new Uint8Array(c);for(n=0;n<r;n+=4)s=v[e.charCodeAt(n)],o=v[e.charCodeAt(n+1)],a=v[e.charCodeAt(n+2)],l=v[e.charCodeAt(n+3)],h[i++]=s<<2|o>>4,h[i++]=(15&o)<<4|a>>2,h[i++]=(3&a)<<6|63&l;return c},b="function"==typeof ArrayBuffer,w=(e,t)=>{if("string"!=typeof e)return{type:"message",data:x(e,t)};let r=e.charAt(0);return"b"===r?{type:"message",data:k(e.substring(1),t)}:c[r]?e.length>1?{type:c[r],data:e.substring(1)}:{type:c[r]}:h},k=(e,t)=>b?x(_(e),t):{base64:!0,data:e},x=(e,t)=>"blob"===t?e instanceof Blob?e:new Blob([e]):e instanceof ArrayBuffer?e:e.buffer,S=(e,t)=>{let r=e.length,n=Array(r),i=0;e.forEach((e,s)=>{f(e,!1,e=>{n[s]=e,++i===r&&t(n.join("\x1e"))})})},C=(e,t)=>{let r=e.split("\x1e"),n=[];for(let e=0;e<r.length;e++){let i=w(r[e],t);if(n.push(i),"error"===i.type)break}return n};function R(e){return e.reduce((e,t)=>e+t.length,0)}function E(e,t){if(e[0].length===t)return e.shift();let r=new Uint8Array(t),n=0;for(let i=0;i<t;i++)r[i]=e[0][n++],n===e[0].length&&(e.shift(),n=0);return e.length&&n<e[0].length&&(e[0]=e[0].slice(n)),r}function T(e){if(e)return function(e){for(var t in T.prototype)e[t]=T.prototype[t];return e}(e)}T.prototype.on=T.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},T.prototype.once=function(e,t){function r(){this.off(e,r),t.apply(this,arguments)}return r.fn=t,this.on(e,r),this},T.prototype.off=T.prototype.removeListener=T.prototype.removeAllListeners=T.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var r,n=this._callbacks["$"+e];if(!n)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var i=0;i<n.length;i++)if((r=n[i])===t||r.fn===t){n.splice(i,1);break}return 0===n.length&&delete this._callbacks["$"+e],this},T.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=Array(arguments.length-1),r=this._callbacks["$"+e],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if(r){r=r.slice(0);for(var n=0,i=r.length;n<i;++n)r[n].apply(this,t)}return this},T.prototype.emitReserved=T.prototype.emit,T.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},T.prototype.hasListeners=function(e){return!!this.listeners(e).length};let A="function"==typeof Promise&&"function"==typeof Promise.resolve?e=>Promise.resolve().then(e):(e,t)=>t(e,0),j="undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")();function O(e,...t){return t.reduce((t,r)=>(e.hasOwnProperty(r)&&(t[r]=e[r]),t),{})}let N=j.setTimeout,B=j.clearTimeout;function M(e,t){t.useNativeTimers?(e.setTimeoutFn=N.bind(j),e.clearTimeoutFn=B.bind(j)):(e.setTimeoutFn=j.setTimeout.bind(j),e.clearTimeoutFn=j.clearTimeout.bind(j))}function L(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}class P extends Error{constructor(e,t,r){super(e),this.description=t,this.context=r,this.type="TransportError"}}class I extends T{constructor(e){super(),this.writable=!1,M(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64}onError(e,t,r){return super.emitReserved("error",new P(e,t,r)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return("opening"===this.readyState||"open"===this.readyState)&&(this.doClose(),this.onClose()),this}send(e){"open"===this.readyState&&this.write(e)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){let t=w(e,this.socket.binaryType);this.onPacket(t)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e,t={}){return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(t)}_hostname(){let e=this.opts.hostname;return -1===e.indexOf(":")?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(e){let t=function(e){let t="";for(let r in e)e.hasOwnProperty(r)&&(t.length&&(t+="&"),t+=encodeURIComponent(r)+"="+encodeURIComponent(e[r]));return t}(e);return t.length?"?"+t:""}}class D extends I{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(e){this.readyState="pausing";let t=()=>{this.readyState="paused",e()};if(this._polling||!this.writable){let e=0;this._polling&&(e++,this.once("pollComplete",function(){--e||t()})),this.writable||(e++,this.once("drain",function(){--e||t()}))}else t()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){C(e,this.socket.binaryType).forEach(e=>{if("opening"===this.readyState&&"open"===e.type&&this.onOpen(),"close"===e.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(e)}),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this._poll())}doClose(){let e=()=>{this.write([{type:"close"}])};"open"===this.readyState?e():this.once("open",e)}write(e){this.writable=!1,S(e,e=>{this.doWrite(e,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){let e=this.opts.secure?"https":"http",t=this.query||{};return!1!==this.opts.timestampRequests&&(t[this.opts.timestampParam]=L()),this.supportsBinary||t.sid||(t.b64=1),this.createUri(e,t)}}let q=!1;try{q="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(e){}let F=q;function V(){}class z extends D{constructor(e){if(super(e),"undefined"!=typeof location){let t="https:"===location.protocol,r=location.port;r||(r=t?"443":"80"),this.xd="undefined"!=typeof location&&e.hostname!==location.hostname||r!==e.port}}doWrite(e,t){let r=this.request({method:"POST",data:e});r.on("success",t),r.on("error",(e,t)=>{this.onError("xhr post error",e,t)})}doPoll(){let e=this.request();e.on("data",this.onData.bind(this)),e.on("error",(e,t)=>{this.onError("xhr poll error",e,t)}),this.pollXhr=e}}class U extends T{constructor(e,t,r){super(),this.createRequest=e,M(this,r),this._opts=r,this._method=r.method||"GET",this._uri=t,this._data=void 0!==r.data?r.data:null,this._create()}_create(){var e;let t=O(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");t.xdomain=!!this._opts.xd;let r=this._xhr=this.createRequest(t);try{r.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders)for(let e in r.setDisableHeaderCheck&&r.setDisableHeaderCheck(!0),this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(e)&&r.setRequestHeader(e,this._opts.extraHeaders[e])}catch(e){}if("POST"===this._method)try{r.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(e){}try{r.setRequestHeader("Accept","*/*")}catch(e){}null===(e=this._opts.cookieJar)||void 0===e||e.addCookies(r),"withCredentials"in r&&(r.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(r.timeout=this._opts.requestTimeout),r.onreadystatechange=()=>{var e;3===r.readyState&&(null===(e=this._opts.cookieJar)||void 0===e||e.parseCookies(r.getResponseHeader("set-cookie"))),4===r.readyState&&(200===r.status||1223===r.status?this._onLoad():this.setTimeoutFn(()=>{this._onError("number"==typeof r.status?r.status:0)},0))},r.send(this._data)}catch(e){this.setTimeoutFn(()=>{this._onError(e)},0);return}"undefined"!=typeof document&&(this._index=U.requestsCount++,U.requests[this._index]=this)}_onError(e){this.emitReserved("error",e,this._xhr),this._cleanup(!0)}_cleanup(e){if(void 0!==this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=V,e)try{this._xhr.abort()}catch(e){}"undefined"!=typeof document&&delete U.requests[this._index],this._xhr=null}}_onLoad(){let e=this._xhr.responseText;null!==e&&(this.emitReserved("data",e),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}if(U.requestsCount=0,U.requests={},"undefined"!=typeof document){if("function"==typeof attachEvent)attachEvent("onunload",W);else if("function"==typeof addEventListener){let e="onpagehide"in j?"pagehide":"unload";addEventListener(e,W,!1)}}function W(){for(let e in U.requests)U.requests.hasOwnProperty(e)&&U.requests[e].abort()}let H=function(){let e=K({xdomain:!1});return e&&null!==e.responseType}();class Z extends z{constructor(e){super(e);let t=e&&e.forceBase64;this.supportsBinary=H&&!t}request(e={}){return Object.assign(e,{xd:this.xd},this.opts),new U(K,this.uri(),e)}}function K(e){let t=e.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!t||F))return new XMLHttpRequest}catch(e){}if(!t)try{return new j[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch(e){}}let Y="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class $ extends I{get name(){return"websocket"}doOpen(){let e=this.uri(),t=this.opts.protocols,r=Y?{}:O(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(r.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,t,r)}catch(e){return this.emitReserved("error",e)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let t=0;t<e.length;t++){let r=e[t],n=t===e.length-1;f(r,this.supportsBinary,e=>{try{this.doWrite(r,e)}catch(e){}n&&A(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){void 0!==this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){let e=this.opts.secure?"wss":"ws",t=this.query||{};return this.opts.timestampRequests&&(t[this.opts.timestampParam]=L()),this.supportsBinary||(t.b64=1),this.createUri(e,t)}}let X=j.WebSocket||j.MozWebSocket;class J extends ${createSocket(e,t,r){return Y?new X(e,t,r):t?new X(e,t):new X(e)}doWrite(e,t){this.ws.send(t)}}class Q extends I{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(e){return this.emitReserved("error",e)}this._transport.closed.then(()=>{this.onClose()}).catch(e=>{this.onError("webtransport error",e)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(e=>{let t=function(e,t){i||(i=new TextDecoder);let r=[],n=0,s=-1,o=!1;return new TransformStream({transform(a,l){for(r.push(a);;){if(0===n){if(1>R(r))break;let e=E(r,1);o=(128&e[0])==128,n=(s=127&e[0])<126?3:126===s?1:2}else if(1===n){if(2>R(r))break;let e=E(r,2);s=new DataView(e.buffer,e.byteOffset,e.length).getUint16(0),n=3}else if(2===n){if(8>R(r))break;let e=E(r,8),t=new DataView(e.buffer,e.byteOffset,e.length),i=t.getUint32(0);if(i>2097151){l.enqueue(h);break}s=4294967296*i+t.getUint32(4),n=3}else{if(R(r)<s)break;let e=E(r,s);l.enqueue(w(o?e:i.decode(e),t)),n=0}if(0===s||s>e){l.enqueue(h);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),r=e.readable.pipeThrough(t).getReader(),s=new TransformStream({transform(e,t){var r;r=r=>{let n;let i=r.length;if(i<126)n=new Uint8Array(1),new DataView(n.buffer).setUint8(0,i);else if(i<65536){n=new Uint8Array(3);let e=new DataView(n.buffer);e.setUint8(0,126),e.setUint16(1,i)}else{n=new Uint8Array(9);let e=new DataView(n.buffer);e.setUint8(0,127),e.setBigUint64(1,BigInt(i))}e.data&&"string"!=typeof e.data&&(n[0]|=128),t.enqueue(n),t.enqueue(r)},u&&e.data instanceof Blob?e.data.arrayBuffer().then(m).then(r):d&&(e.data instanceof ArrayBuffer||p(e.data))?r(m(e.data)):f(e,!1,e=>{n||(n=new TextEncoder),r(n.encode(e))})}});s.readable.pipeTo(e.writable),this._writer=s.writable.getWriter();let o=()=>{r.read().then(({done:e,value:t})=>{e||(this.onPacket(t),o())}).catch(e=>{})};o();let a={type:"open"};this.query.sid&&(a.data=`{"sid":"${this.query.sid}"}`),this._writer.write(a).then(()=>this.onOpen())})})}write(e){this.writable=!1;for(let t=0;t<e.length;t++){let r=e[t],n=t===e.length-1;this._writer.write(r).then(()=>{n&&A(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var e;null===(e=this._transport)||void 0===e||e.close()}}let G={websocket:J,webtransport:Q,polling:Z},ee=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,et=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function er(e){if(e.length>8e3)throw"URI too long";let t=e,r=e.indexOf("["),n=e.indexOf("]");-1!=r&&-1!=n&&(e=e.substring(0,r)+e.substring(r,n).replace(/:/g,";")+e.substring(n,e.length));let i=ee.exec(e||""),s={},o=14;for(;o--;)s[et[o]]=i[o]||"";return -1!=r&&-1!=n&&(s.source=t,s.host=s.host.substring(1,s.host.length-1).replace(/;/g,":"),s.authority=s.authority.replace("[","").replace("]","").replace(/;/g,":"),s.ipv6uri=!0),s.pathNames=function(e,t){let r=t.replace(/\/{2,9}/g,"/").split("/");return("/"==t.slice(0,1)||0===t.length)&&r.splice(0,1),"/"==t.slice(-1)&&r.splice(r.length-1,1),r}(0,s.path),s.queryKey=function(e,t){let r={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(e,t,n){t&&(r[t]=n)}),r}(0,s.query),s}let en="function"==typeof addEventListener&&"function"==typeof removeEventListener,ei=[];en&&addEventListener("offline",()=>{ei.forEach(e=>e())},!1);class es extends T{constructor(e,t){if(super(),this.binaryType="arraybuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&"object"==typeof e&&(t=e,e=null),e){let r=er(e);t.hostname=r.host,t.secure="https"===r.protocol||"wss"===r.protocol,t.port=r.port,r.query&&(t.query=r.query)}else t.host&&(t.hostname=er(t.host).host);M(this,t),this.secure=null!=t.secure?t.secure:"undefined"!=typeof location&&"https:"===location.protocol,t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.hostname=t.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=t.port||("undefined"!=typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},t.transports.forEach(e=>{let t=e.prototype.name;this.transports.push(t),this._transportsByName[t]=e}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},t),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"==typeof this.opts.query&&(this.opts.query=function(e){let t={},r=e.split("&");for(let e=0,n=r.length;e<n;e++){let n=r[e].split("=");t[decodeURIComponent(n[0])]=decodeURIComponent(n[1])}return t}(this.opts.query)),en&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},ei.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(e){let t=Object.assign({},this.opts.query);t.EIO=4,t.transport=e,this.id&&(t.sid=this.id);let r=Object.assign({},this.opts,{query:t,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return new this._transportsByName[e](r)}_open(){if(0===this.transports.length){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}let e=this.opts.rememberUpgrade&&es.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";let t=this.createTransport(e);t.open(),this.setTransport(t)}setTransport(e){this.transport&&this.transport.removeAllListeners(),this.transport=e,e.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",e=>this._onClose("transport close",e))}onOpen(){this.readyState="open",es.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":let t=Error("server error");t.code=e.data,this._onError(t);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data)}}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);let e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},e),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){let e=this._getWritablePackets();this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let e=1;for(let t=0;t<this.writeBuffer.length;t++){let r=this.writeBuffer[t].data;if(r&&(e+="string"==typeof r?function(e){let t=0,r=0;for(let n=0,i=e.length;n<i;n++)(t=e.charCodeAt(n))<128?r+=1:t<2048?r+=2:t<55296||t>=57344?r+=3:(n++,r+=4);return r}(r):Math.ceil(1.33*(r.byteLength||r.size))),t>0&&e>this._maxPayload)return this.writeBuffer.slice(0,t);e+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;let e=Date.now()>this._pingTimeoutTime;return e&&(this._pingTimeoutTime=0,A(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),e}write(e,t,r){return this._sendPacket("message",e,t,r),this}send(e,t,r){return this._sendPacket("message",e,t,r),this}_sendPacket(e,t,r,n){if("function"==typeof t&&(n=t,t=void 0),"function"==typeof r&&(n=r,r=null),"closing"===this.readyState||"closed"===this.readyState)return;(r=r||{}).compress=!1!==r.compress;let i={type:e,data:t,options:r};this.emitReserved("packetCreate",i),this.writeBuffer.push(i),n&&this.once("flush",n),this.flush()}close(){let e=()=>{this._onClose("forced close"),this.transport.close()},t=()=>{this.off("upgrade",t),this.off("upgradeError",t),e()},r=()=>{this.once("upgrade",t),this.once("upgradeError",t)};return("opening"===this.readyState||"open"===this.readyState)&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?r():e()}):this.upgrading?r():e()),this}_onError(e){if(es.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return this.transports.shift(),this._open();this.emitReserved("error",e),this._onClose("transport error",e)}_onClose(e,t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),en&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){let e=ei.indexOf(this._offlineEventListener);-1!==e&&ei.splice(e,1)}this.readyState="closed",this.id=null,this.emitReserved("close",e,t),this.writeBuffer=[],this._prevBufferLen=0}}}es.protocol=4;class eo extends es{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade)for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e])}_probe(e){let t=this.createTransport(e),r=!1;es.priorWebsocketSuccess=!1;let n=()=>{r||(t.send([{type:"ping",data:"probe"}]),t.once("packet",e=>{if(!r){if("pong"===e.type&&"probe"===e.data)this.upgrading=!0,this.emitReserved("upgrading",t),t&&(es.priorWebsocketSuccess="websocket"===t.name,this.transport.pause(()=>{r||"closed"===this.readyState||(c(),this.setTransport(t),t.send([{type:"upgrade"}]),this.emitReserved("upgrade",t),t=null,this.upgrading=!1,this.flush())}));else{let e=Error("probe error");e.transport=t.name,this.emitReserved("upgradeError",e)}}}))};function i(){r||(r=!0,c(),t.close(),t=null)}let s=e=>{let r=Error("probe error: "+e);r.transport=t.name,i(),this.emitReserved("upgradeError",r)};function o(){s("transport closed")}function a(){s("socket closed")}function l(e){t&&e.name!==t.name&&i()}let c=()=>{t.removeListener("open",n),t.removeListener("error",s),t.removeListener("close",o),this.off("close",a),this.off("upgrading",l)};t.once("open",n),t.once("error",s),t.once("close",o),this.once("close",a),this.once("upgrading",l),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==e?this.setTimeoutFn(()=>{r||t.open()},200):t.open()}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e)}_filterUpgrades(e){let t=[];for(let r=0;r<e.length;r++)~this.transports.indexOf(e[r])&&t.push(e[r]);return t}}class ea extends eo{constructor(e,t={}){let r="object"==typeof e?e:t;(!r.transports||r.transports&&"string"==typeof r.transports[0])&&(r.transports=(r.transports||["polling","websocket","webtransport"]).map(e=>G[e]).filter(e=>!!e)),super(e,r)}}ea.protocol;let el="function"==typeof ArrayBuffer,ec=e=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer,eh=Object.prototype.toString,eu="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===eh.call(Blob),ed="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===eh.call(File);function ep(e){return el&&(e instanceof ArrayBuffer||ec(e))||eu&&e instanceof Blob||ed&&e instanceof File}let ef=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],ey=5;(s=o||(o={}))[s.CONNECT=0]="CONNECT",s[s.DISCONNECT=1]="DISCONNECT",s[s.EVENT=2]="EVENT",s[s.ACK=3]="ACK",s[s.CONNECT_ERROR=4]="CONNECT_ERROR",s[s.BINARY_EVENT=5]="BINARY_EVENT",s[s.BINARY_ACK=6]="BINARY_ACK";class em{constructor(e){this.replacer=e}encode(e){return(e.type===o.EVENT||e.type===o.ACK)&&function e(t,r){if(!t||"object"!=typeof t)return!1;if(Array.isArray(t)){for(let r=0,n=t.length;r<n;r++)if(e(t[r]))return!0;return!1}if(ep(t))return!0;if(t.toJSON&&"function"==typeof t.toJSON&&1==arguments.length)return e(t.toJSON(),!0);for(let r in t)if(Object.prototype.hasOwnProperty.call(t,r)&&e(t[r]))return!0;return!1}(e)?this.encodeAsBinary({type:e.type===o.EVENT?o.BINARY_EVENT:o.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id}):[this.encodeAsString(e)]}encodeAsString(e){let t=""+e.type;return(e.type===o.BINARY_EVENT||e.type===o.BINARY_ACK)&&(t+=e.attachments+"-"),e.nsp&&"/"!==e.nsp&&(t+=e.nsp+","),null!=e.id&&(t+=e.id),null!=e.data&&(t+=JSON.stringify(e.data,this.replacer)),t}encodeAsBinary(e){let t=function(e){let t=[],r=e.data;return e.data=function e(t,r){if(!t)return t;if(ep(t)){let e={_placeholder:!0,num:r.length};return r.push(t),e}if(Array.isArray(t)){let n=Array(t.length);for(let i=0;i<t.length;i++)n[i]=e(t[i],r);return n}if("object"==typeof t&&!(t instanceof Date)){let n={};for(let i in t)Object.prototype.hasOwnProperty.call(t,i)&&(n[i]=e(t[i],r));return n}return t}(r,t),e.attachments=t.length,{packet:e,buffers:t}}(e),r=this.encodeAsString(t.packet),n=t.buffers;return n.unshift(r),n}}function eg(e){return"[object Object]"===Object.prototype.toString.call(e)}class ev extends T{constructor(e){super(),this.reviver=e}add(e){let t;if("string"==typeof e){if(this.reconstructor)throw Error("got plaintext data when reconstructing a packet");let r=(t=this.decodeString(e)).type===o.BINARY_EVENT;r||t.type===o.BINARY_ACK?(t.type=r?o.EVENT:o.ACK,this.reconstructor=new e_(t),0===t.attachments&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else if(ep(e)||e.base64){if(this.reconstructor)(t=this.reconstructor.takeBinaryData(e))&&(this.reconstructor=null,super.emitReserved("decoded",t));else throw Error("got binary data when not reconstructing a packet")}else throw Error("Unknown type: "+e)}decodeString(e){let t=0,r={type:Number(e.charAt(0))};if(void 0===o[r.type])throw Error("unknown packet type "+r.type);if(r.type===o.BINARY_EVENT||r.type===o.BINARY_ACK){let n=t+1;for(;"-"!==e.charAt(++t)&&t!=e.length;);let i=e.substring(n,t);if(i!=Number(i)||"-"!==e.charAt(t))throw Error("Illegal attachments");r.attachments=Number(i)}if("/"===e.charAt(t+1)){let n=t+1;for(;++t&&","!==e.charAt(t)&&t!==e.length;);r.nsp=e.substring(n,t)}else r.nsp="/";let n=e.charAt(t+1);if(""!==n&&Number(n)==n){let n=t+1;for(;++t;){let r=e.charAt(t);if(null==r||Number(r)!=r){--t;break}if(t===e.length)break}r.id=Number(e.substring(n,t+1))}if(e.charAt(++t)){let n=this.tryParse(e.substr(t));if(ev.isPayloadValid(r.type,n))r.data=n;else throw Error("invalid payload")}return r}tryParse(e){try{return JSON.parse(e,this.reviver)}catch(e){return!1}}static isPayloadValid(e,t){switch(e){case o.CONNECT:return eg(t);case o.DISCONNECT:return void 0===t;case o.CONNECT_ERROR:return"string"==typeof t||eg(t);case o.EVENT:case o.BINARY_EVENT:return Array.isArray(t)&&("number"==typeof t[0]||"string"==typeof t[0]&&-1===ef.indexOf(t[0]));case o.ACK:case o.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class e_{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){var t,r;let e=(t=this.reconPack,r=this.buffers,t.data=function e(t,r){if(!t)return t;if(t&&!0===t._placeholder){if("number"==typeof t.num&&t.num>=0&&t.num<r.length)return r[t.num];throw Error("illegal attachments")}if(Array.isArray(t))for(let n=0;n<t.length;n++)t[n]=e(t[n],r);else if("object"==typeof t)for(let n in t)Object.prototype.hasOwnProperty.call(t,n)&&(t[n]=e(t[n],r));return t}(t.data,r),delete t.attachments,t);return this.finishedReconstruction(),e}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function eb(e,t,r){return e.on(t,r),function(){e.off(t,r)}}let ew=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class ek extends T{constructor(e,t,r){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=t,r&&r.auth&&(this.auth=r.auth),this._opts=Object.assign({},r),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;let e=this.io;this.subs=[eb(e,"open",this.onopen.bind(this)),eb(e,"packet",this.onpacket.bind(this)),eb(e,"error",this.onerror.bind(this)),eb(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(...e){return e.unshift("message"),this.emit.apply(this,e),this}emit(e,...t){var r,n,i;if(ew.hasOwnProperty(e))throw Error('"'+e.toString()+'" is a reserved event name');if(t.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(t),this;let s={type:o.EVENT,data:t};if(s.options={},s.options.compress=!1!==this.flags.compress,"function"==typeof t[t.length-1]){let e=this.ids++,r=t.pop();this._registerAckCallback(e,r),s.id=e}let a=null===(n=null===(r=this.io.engine)||void 0===r?void 0:r.transport)||void 0===n?void 0:n.writable,l=this.connected&&!(null===(i=this.io.engine)||void 0===i?void 0:i._hasPingExpired());return this.flags.volatile&&!a||(l?(this.notifyOutgoingListeners(s),this.packet(s)):this.sendBuffer.push(s)),this.flags={},this}_registerAckCallback(e,t){var r;let n=null!==(r=this.flags.timeout)&&void 0!==r?r:this._opts.ackTimeout;if(void 0===n){this.acks[e]=t;return}let i=this.io.setTimeoutFn(()=>{delete this.acks[e];for(let t=0;t<this.sendBuffer.length;t++)this.sendBuffer[t].id===e&&this.sendBuffer.splice(t,1);t.call(this,Error("operation has timed out"))},n),s=(...e)=>{this.io.clearTimeoutFn(i),t.apply(this,e)};s.withError=!0,this.acks[e]=s}emitWithAck(e,...t){return new Promise((r,n)=>{let i=(e,t)=>e?n(e):r(t);i.withError=!0,t.push(i),this.emit(e,...t)})}_addToQueue(e){let t;"function"==typeof e[e.length-1]&&(t=e.pop());let r={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push((e,...n)=>{if(r===this._queue[0])return null!==e?r.tryCount>this._opts.retries&&(this._queue.shift(),t&&t(e)):(this._queue.shift(),t&&t(null,...n)),r.pending=!1,this._drainQueue()}),this._queue.push(r),this._drainQueue()}_drainQueue(e=!1){if(!this.connected||0===this._queue.length)return;let t=this._queue[0];(!t.pending||e)&&(t.pending=!0,t.tryCount++,this.flags=t.flags,this.emit.apply(this,t.args))}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){"function"==typeof this.auth?this.auth(e=>{this._sendConnectPacket(e)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:o.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,t){this.connected=!1,delete this.id,this.emitReserved("disconnect",e,t),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(e=>{if(!this.sendBuffer.some(t=>String(t.id)===e)){let t=this.acks[e];delete this.acks[e],t.withError&&t.call(this,Error("socket has been disconnected"))}})}onpacket(e){if(!(e.nsp!==this.nsp))switch(e.type){case o.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case o.EVENT:case o.BINARY_EVENT:this.onevent(e);break;case o.ACK:case o.BINARY_ACK:this.onack(e);break;case o.DISCONNECT:this.ondisconnect();break;case o.CONNECT_ERROR:this.destroy();let t=Error(e.data.message);t.data=e.data.data,this.emitReserved("connect_error",t)}}onevent(e){let t=e.data||[];null!=e.id&&t.push(this.ack(e.id)),this.connected?this.emitEvent(t):this.receiveBuffer.push(Object.freeze(t))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length)for(let t of this._anyListeners.slice())t.apply(this,e);super.emit.apply(this,e),this._pid&&e.length&&"string"==typeof e[e.length-1]&&(this._lastOffset=e[e.length-1])}ack(e){let t=this,r=!1;return function(...n){r||(r=!0,t.packet({type:o.ACK,id:e,data:n}))}}onack(e){let t=this.acks[e.id];"function"==typeof t&&(delete this.acks[e.id],t.withError&&e.data.unshift(null),t.apply(this,e.data))}onconnect(e,t){this.id=e,this.recovered=t&&this._pid===t,this._pid=t,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(e=>this.emitEvent(e)),this.receiveBuffer=[],this.sendBuffer.forEach(e=>{this.notifyOutgoingListeners(e),this.packet(e)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(e=>e()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:o.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){let t=this._anyListeners;for(let r=0;r<t.length;r++)if(e===t[r]){t.splice(r,1);break}}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){let t=this._anyOutgoingListeners;for(let r=0;r<t.length;r++)if(e===t[r]){t.splice(r,1);break}}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length)for(let t of this._anyOutgoingListeners.slice())t.apply(this,e.data)}}function ex(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}ex.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),r=Math.floor(t*this.jitter*e);e=(1&Math.floor(10*t))==0?e-r:e+r}return 0|Math.min(e,this.max)},ex.prototype.reset=function(){this.attempts=0},ex.prototype.setMin=function(e){this.ms=e},ex.prototype.setMax=function(e){this.max=e},ex.prototype.setJitter=function(e){this.jitter=e};class eS extends T{constructor(e,t){var r;super(),this.nsps={},this.subs=[],e&&"object"==typeof e&&(t=e,e=void 0),(t=t||{}).path=t.path||"/socket.io",this.opts=t,M(this,t),this.reconnection(!1!==t.reconnection),this.reconnectionAttempts(t.reconnectionAttempts||1/0),this.reconnectionDelay(t.reconnectionDelay||1e3),this.reconnectionDelayMax(t.reconnectionDelayMax||5e3),this.randomizationFactor(null!==(r=t.randomizationFactor)&&void 0!==r?r:.5),this.backoff=new ex({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==t.timeout?2e4:t.timeout),this._readyState="closed",this.uri=e;let n=t.parser||a;this.encoder=new n.Encoder,this.decoder=new n.Decoder,this._autoConnect=!1!==t.autoConnect,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(e){return void 0===e?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var t;return void 0===e?this._reconnectionDelay:(this._reconnectionDelay=e,null===(t=this.backoff)||void 0===t||t.setMin(e),this)}randomizationFactor(e){var t;return void 0===e?this._randomizationFactor:(this._randomizationFactor=e,null===(t=this.backoff)||void 0===t||t.setJitter(e),this)}reconnectionDelayMax(e){var t;return void 0===e?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,null===(t=this.backoff)||void 0===t||t.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(e){if(~this._readyState.indexOf("open"))return this;this.engine=new ea(this.uri,this.opts);let t=this.engine,r=this;this._readyState="opening",this.skipReconnect=!1;let n=eb(t,"open",function(){r.onopen(),e&&e()}),i=t=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",t),e?e(t):this.maybeReconnectOnOpen()},s=eb(t,"error",i);if(!1!==this._timeout){let e=this._timeout,r=this.setTimeoutFn(()=>{n(),i(Error("timeout")),t.close()},e);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}return this.subs.push(n),this.subs.push(s),this}connect(e){return this.open(e)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");let e=this.engine;this.subs.push(eb(e,"ping",this.onping.bind(this)),eb(e,"data",this.ondata.bind(this)),eb(e,"error",this.onerror.bind(this)),eb(e,"close",this.onclose.bind(this)),eb(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(e){this.onclose("parse error",e)}}ondecoded(e){A(()=>{this.emitReserved("packet",e)},this.setTimeoutFn)}onerror(e){this.emitReserved("error",e)}socket(e,t){let r=this.nsps[e];return r?this._autoConnect&&!r.active&&r.connect():(r=new ek(this,e,t),this.nsps[e]=r),r}_destroy(e){for(let e of Object.keys(this.nsps))if(this.nsps[e].active)return;this._close()}_packet(e){let t=this.encoder.encode(e);for(let r=0;r<t.length;r++)this.engine.write(t[r],e.options)}cleanup(){this.subs.forEach(e=>e()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(e,t){var r;this.cleanup(),null===(r=this.engine)||void 0===r||r.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,t),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;let e=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{let t=this.backoff.duration();this._reconnecting=!0;let r=this.setTimeoutFn(()=>{!e.skipReconnect&&(this.emitReserved("reconnect_attempt",e.backoff.attempts),e.skipReconnect||e.open(t=>{t?(e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",t)):e.onreconnect()}))},t);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}}onreconnect(){let e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}}let eC={};function eR(e,t){let r;"object"==typeof e&&(t=e,e=void 0);let n=function(e,t="",r){let n=e;r=r||"undefined"!=typeof location&&location,null==e&&(e=r.protocol+"//"+r.host),"string"==typeof e&&("/"===e.charAt(0)&&(e="/"===e.charAt(1)?r.protocol+e:r.host+e),/^(https?|wss?):\/\//.test(e)||(e=void 0!==r?r.protocol+"//"+e:"https://"+e),n=er(e)),!n.port&&(/^(http|ws)$/.test(n.protocol)?n.port="80":/^(http|ws)s$/.test(n.protocol)&&(n.port="443")),n.path=n.path||"/";let i=-1!==n.host.indexOf(":")?"["+n.host+"]":n.host;return n.id=n.protocol+"://"+i+":"+n.port+t,n.href=n.protocol+"://"+i+(r&&r.port===n.port?"":":"+n.port),n}(e,(t=t||{}).path||"/socket.io"),i=n.source,s=n.id,o=n.path,a=eC[s]&&o in eC[s].nsps;return t.forceNew||t["force new connection"]||!1===t.multiplex||a?r=new eS(i,t):(eC[s]||(eC[s]=new eS(i,t)),r=eC[s]),n.query&&!t.query&&(t.query=n.queryKey),r.socket(n.path,t)}Object.assign(eR,{Manager:eS,Socket:ek,io:eR,connect:eR})}}]);