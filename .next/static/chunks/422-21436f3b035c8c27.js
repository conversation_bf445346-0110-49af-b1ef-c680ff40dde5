(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[422],{7005:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},6518:function(e){e.exports={style:{fontFamily:"'__JetBrains_Mono_3c557b', '__JetBrains_Mono_Fallback_3c557b'",fontStyle:"normal"},className:"__className_3c557b",variable:"__variable_3c557b"}},3022:function(e,t,s){"use strict";s.d(t,{aU:function(){return es},x8:function(){return ei},dk:function(){return et},zt:function(){return X},fC:function(){return Z},Dx:function(){return ee},l_:function(){return Y}});var i=s(2265),r=s(4887),n=s(5744),a=s(2210),o=s(7733),u=s(6989),c=s(9249),l=s(2730),h=s(5606),d=s(9381),f=s(6459),p=s(3763),y=s(5655),v=s(7437),m=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),b=i.forwardRef((e,t)=>(0,v.jsx)(d.WV.span,{...e,ref:t,style:{...m,...e.style}}));b.displayName="VisuallyHidden";var g="ToastProvider",[w,C,E]=(0,o.B)("Toast"),[x,P]=(0,u.b)("Toast",[E]),[T,S]=x(g),O=e=>{let{__scopeToast:t,label:s="Notification",duration:r=5e3,swipeDirection:n="right",swipeThreshold:a=50,children:o}=e,[u,c]=i.useState(null),[l,h]=i.useState(0),d=i.useRef(!1),f=i.useRef(!1);return s.trim()||console.error(`Invalid prop \`label\` supplied to \`${g}\`. Expected non-empty \`string\`.`),(0,v.jsx)(w.Provider,{scope:t,children:(0,v.jsx)(T,{scope:t,label:s,duration:r,swipeDirection:n,swipeThreshold:a,toastCount:l,viewport:u,onViewportChange:c,onToastAdd:i.useCallback(()=>h(e=>e+1),[]),onToastRemove:i.useCallback(()=>h(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:d,isClosePausedRef:f,children:o})})};O.displayName=g;var F="ToastViewport",q=["F8"],R="toast.viewportPause",D="toast.viewportResume",M=i.forwardRef((e,t)=>{let{__scopeToast:s,hotkey:r=q,label:n="Notifications ({hotkey})",...o}=e,u=S(F,s),l=C(s),h=i.useRef(null),f=i.useRef(null),p=i.useRef(null),y=i.useRef(null),m=(0,a.e)(t,y,u.onViewportChange),b=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),g=u.toastCount>0;i.useEffect(()=>{let e=e=>{0!==r.length&&r.every(t=>e[t]||e.code===t)&&y.current?.focus()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[r]),i.useEffect(()=>{let e=h.current,t=y.current;if(g&&e&&t){let s=()=>{if(!u.isClosePausedRef.current){let e=new CustomEvent(R);t.dispatchEvent(e),u.isClosePausedRef.current=!0}},i=()=>{if(u.isClosePausedRef.current){let e=new CustomEvent(D);t.dispatchEvent(e),u.isClosePausedRef.current=!1}},r=t=>{e.contains(t.relatedTarget)||i()},n=()=>{e.contains(document.activeElement)||i()};return e.addEventListener("focusin",s),e.addEventListener("focusout",r),e.addEventListener("pointermove",s),e.addEventListener("pointerleave",n),window.addEventListener("blur",s),window.addEventListener("focus",i),()=>{e.removeEventListener("focusin",s),e.removeEventListener("focusout",r),e.removeEventListener("pointermove",s),e.removeEventListener("pointerleave",n),window.removeEventListener("blur",s),window.removeEventListener("focus",i)}}},[g,u.isClosePausedRef]);let E=i.useCallback(({tabbingDirection:e})=>{let t=l().map(t=>{let s=t.ref.current,i=[s,...function(e){let t=[],s=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;s.nextNode();)t.push(s.currentNode);return t}(s)];return"forwards"===e?i:i.reverse()});return("forwards"===e?t.reverse():t).flat()},[l]);return i.useEffect(()=>{let e=y.current;if(e){let t=t=>{let s=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!s){let s=document.activeElement,i=t.shiftKey;if(t.target===e&&i){f.current?.focus();return}let r=E({tabbingDirection:i?"backwards":"forwards"}),n=r.findIndex(e=>e===s);J(r.slice(n+1))?t.preventDefault():i?f.current?.focus():p.current?.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[l,E]),(0,v.jsxs)(c.I0,{ref:h,role:"region","aria-label":n.replace("{hotkey}",b),tabIndex:-1,style:{pointerEvents:g?void 0:"none"},children:[g&&(0,v.jsx)(j,{ref:f,onFocusFromOutsideViewport:()=>{J(E({tabbingDirection:"forwards"}))}}),(0,v.jsx)(w.Slot,{scope:s,children:(0,v.jsx)(d.WV.ol,{tabIndex:-1,...o,ref:m})}),g&&(0,v.jsx)(j,{ref:p,onFocusFromOutsideViewport:()=>{J(E({tabbingDirection:"backwards"}))}})]})});M.displayName=F;var A="ToastFocusProxy",j=i.forwardRef((e,t)=>{let{__scopeToast:s,onFocusFromOutsideViewport:i,...r}=e,n=S(A,s);return(0,v.jsx)(b,{"aria-hidden":!0,tabIndex:0,...r,ref:t,style:{position:"fixed"},onFocus:e=>{let t=e.relatedTarget;n.viewport?.contains(t)||i()}})});j.displayName=A;var k="Toast",L=i.forwardRef((e,t)=>{let{forceMount:s,open:i,defaultOpen:r,onOpenChange:a,...o}=e,[u,c]=(0,p.T)({prop:i,defaultProp:r??!0,onChange:a,caller:k});return(0,v.jsx)(h.z,{present:s||u,children:(0,v.jsx)(N,{open:u,...o,ref:t,onClose:()=>c(!1),onPause:(0,f.W)(e.onPause),onResume:(0,f.W)(e.onResume),onSwipeStart:(0,n.M)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,n.M)(e.onSwipeMove,e=>{let{x:t,y:s}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${s}px`)}),onSwipeCancel:(0,n.M)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,n.M)(e.onSwipeEnd,e=>{let{x:t,y:s}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${s}px`),c(!1)})})})});L.displayName=k;var[_,Q]=x(k,{onClose(){}}),N=i.forwardRef((e,t)=>{let{__scopeToast:s,type:o="foreground",duration:u,open:l,onClose:h,onEscapeKeyDown:p,onPause:y,onResume:m,onSwipeStart:b,onSwipeMove:g,onSwipeCancel:C,onSwipeEnd:E,...x}=e,P=S(k,s),[T,O]=i.useState(null),F=(0,a.e)(t,e=>O(e)),q=i.useRef(null),M=i.useRef(null),A=u||P.duration,j=i.useRef(0),L=i.useRef(A),Q=i.useRef(0),{onToastAdd:N,onToastRemove:K}=P,U=(0,f.W)(()=>{T?.contains(document.activeElement)&&P.viewport?.focus(),h()}),H=i.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(Q.current),j.current=new Date().getTime(),Q.current=window.setTimeout(U,e))},[U]);i.useEffect(()=>{let e=P.viewport;if(e){let t=()=>{H(L.current),m?.()},s=()=>{let e=new Date().getTime()-j.current;L.current=L.current-e,window.clearTimeout(Q.current),y?.()};return e.addEventListener(R,s),e.addEventListener(D,t),()=>{e.removeEventListener(R,s),e.removeEventListener(D,t)}}},[P.viewport,A,y,m,H]),i.useEffect(()=>{l&&!P.isClosePausedRef.current&&H(A)},[l,A,P.isClosePausedRef,H]),i.useEffect(()=>(N(),()=>K()),[N,K]);let G=i.useMemo(()=>T?function e(t){let s=[];return Array.from(t.childNodes).forEach(t=>{if(t.nodeType===t.TEXT_NODE&&t.textContent&&s.push(t.textContent),t.nodeType===t.ELEMENT_NODE){let i=t.ariaHidden||t.hidden||"none"===t.style.display,r=""===t.dataset.radixToastAnnounceExclude;if(!i){if(r){let e=t.dataset.radixToastAnnounceAlt;e&&s.push(e)}else s.push(...e(t))}}}),s}(T):null,[T]);return P.viewport?(0,v.jsxs)(v.Fragment,{children:[G&&(0,v.jsx)(I,{__scopeToast:s,role:"status","aria-live":"foreground"===o?"assertive":"polite","aria-atomic":!0,children:G}),(0,v.jsx)(_,{scope:s,onClose:U,children:r.createPortal((0,v.jsx)(w.ItemSlot,{scope:s,children:(0,v.jsx)(c.fC,{asChild:!0,onEscapeKeyDown:(0,n.M)(p,()=>{P.isFocusedToastEscapeKeyDownRef.current||U(),P.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,v.jsx)(d.WV.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":l?"open":"closed","data-swipe-direction":P.swipeDirection,...x,ref:F,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,n.M)(e.onKeyDown,e=>{"Escape"!==e.key||(p?.(e.nativeEvent),e.nativeEvent.defaultPrevented||(P.isFocusedToastEscapeKeyDownRef.current=!0,U()))}),onPointerDown:(0,n.M)(e.onPointerDown,e=>{0===e.button&&(q.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,n.M)(e.onPointerMove,e=>{if(!q.current)return;let t=e.clientX-q.current.x,s=e.clientY-q.current.y,i=!!M.current,r=["left","right"].includes(P.swipeDirection),n=["left","up"].includes(P.swipeDirection)?Math.min:Math.max,a=r?n(0,t):0,o=r?0:n(0,s),u="touch"===e.pointerType?10:2,c={x:a,y:o},l={originalEvent:e,delta:c};i?(M.current=c,B("toast.swipeMove",g,l,{discrete:!1})):z(c,P.swipeDirection,u)?(M.current=c,B("toast.swipeStart",b,l,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>u||Math.abs(s)>u)&&(q.current=null)}),onPointerUp:(0,n.M)(e.onPointerUp,e=>{let t=M.current,s=e.target;if(s.hasPointerCapture(e.pointerId)&&s.releasePointerCapture(e.pointerId),M.current=null,q.current=null,t){let s=e.currentTarget,i={originalEvent:e,delta:t};z(t,P.swipeDirection,P.swipeThreshold)?B("toast.swipeEnd",E,i,{discrete:!0}):B("toast.swipeCancel",C,i,{discrete:!0}),s.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),P.viewport)})]}):null}),I=e=>{let{__scopeToast:t,children:s,...r}=e,n=S(k,t),[a,o]=i.useState(!1),[u,c]=i.useState(!1);return function(e=()=>{}){let t=(0,f.W)(e);(0,y.b)(()=>{let e=0,s=0;return e=window.requestAnimationFrame(()=>s=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(s)}},[t])}(()=>o(!0)),i.useEffect(()=>{let e=window.setTimeout(()=>c(!0),1e3);return()=>window.clearTimeout(e)},[]),u?null:(0,v.jsx)(l.h,{asChild:!0,children:(0,v.jsx)(b,{...r,children:a&&(0,v.jsxs)(v.Fragment,{children:[n.label," ",s]})})})},K=i.forwardRef((e,t)=>{let{__scopeToast:s,...i}=e;return(0,v.jsx)(d.WV.div,{...i,ref:t})});K.displayName="ToastTitle";var U=i.forwardRef((e,t)=>{let{__scopeToast:s,...i}=e;return(0,v.jsx)(d.WV.div,{...i,ref:t})});U.displayName="ToastDescription";var H="ToastAction",G=i.forwardRef((e,t)=>{let{altText:s,...i}=e;return s.trim()?(0,v.jsx)($,{altText:s,asChild:!0,children:(0,v.jsx)(V,{...i,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${H}\`. Expected non-empty \`string\`.`),null)});G.displayName=H;var W="ToastClose",V=i.forwardRef((e,t)=>{let{__scopeToast:s,...i}=e,r=Q(W,s);return(0,v.jsx)($,{asChild:!0,children:(0,v.jsx)(d.WV.button,{type:"button",...i,ref:t,onClick:(0,n.M)(e.onClick,r.onClose)})})});V.displayName=W;var $=i.forwardRef((e,t)=>{let{__scopeToast:s,altText:i,...r}=e;return(0,v.jsx)(d.WV.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":i||void 0,...r,ref:t})});function B(e,t,s,{discrete:i}){let r=s.originalEvent.currentTarget,n=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:s});t&&r.addEventListener(e,t,{once:!0}),i?(0,d.jH)(r,n):r.dispatchEvent(n)}var z=(e,t,s=0)=>{let i=Math.abs(e.x),r=Math.abs(e.y),n=i>r;return"left"===t||"right"===t?n&&i>s:!n&&r>s};function J(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var X=O,Y=M,Z=L,ee=K,et=U,es=G,ei=V},9715:function(e,t,s){"use strict";s.d(t,{S:function(){return L}});var i="undefined"==typeof window||"Deno"in globalThis;function r(){}function n(e,t){return"function"==typeof e?e(t):e}function a(e,t){let{type:s="all",exact:i,fetchStatus:r,predicate:n,queryKey:a,stale:o}=e;if(a){if(i){if(t.queryHash!==u(a,t.options))return!1}else if(!l(t.queryKey,a))return!1}if("all"!==s){let e=t.isActive();if("active"===s&&!e||"inactive"===s&&e)return!1}return("boolean"!=typeof o||t.isStale()===o)&&(!r||r===t.state.fetchStatus)&&(!n||!!n(t))}function o(e,t){let{exact:s,status:i,predicate:r,mutationKey:n}=e;if(n){if(!t.options.mutationKey)return!1;if(s){if(c(t.options.mutationKey)!==c(n))return!1}else if(!l(t.options.mutationKey,n))return!1}return(!i||t.state.status===i)&&(!r||!!r(t))}function u(e,t){return(t?.queryKeyHashFn||c)(e)}function c(e){return JSON.stringify(e,(e,t)=>d(t)?Object.keys(t).sort().reduce((e,s)=>(e[s]=t[s],e),{}):t)}function l(e,t){return e===t||typeof e==typeof t&&!!e&&!!t&&"object"==typeof e&&"object"==typeof t&&Object.keys(t).every(s=>l(e[s],t[s]))}function h(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function d(e){if(!f(e))return!1;let t=e.constructor;if(void 0===t)return!0;let s=t.prototype;return!!(f(s)&&s.hasOwnProperty("isPrototypeOf"))&&Object.getPrototypeOf(e)===Object.prototype}function f(e){return"[object Object]"===Object.prototype.toString.call(e)}function p(e,t,s=0){let i=[...e,t];return s&&i.length>s?i.slice(1):i}function y(e,t,s=0){let i=[t,...e];return s&&i.length>s?i.slice(0,-1):i}var v=Symbol();function m(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==v?e.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${e.queryHash}'`))}var b=e=>setTimeout(e,0),g=function(){let e=[],t=0,s=e=>{e()},i=e=>{e()},r=b,n=i=>{t?e.push(i):r(()=>{s(i)})},a=()=>{let t=e;e=[],t.length&&r(()=>{i(()=>{t.forEach(e=>{s(e)})})})};return{batch:e=>{let s;t++;try{s=e()}finally{--t||a()}return s},batchCalls:e=>(...t)=>{n(()=>{e(...t)})},schedule:n,setNotifyFunction:e=>{s=e},setBatchNotifyFunction:e=>{i=e},setScheduler:e=>{r=e}}}(),w=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},C=new class extends w{#e;#t;#s;constructor(){super(),this.#s=e=>{if(!i&&window.addEventListener){let t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#s=e,this.#t?.(),this.#t=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return"boolean"==typeof this.#e?this.#e:globalThis.document?.visibilityState!=="hidden"}},E=new class extends w{#i=!0;#t;#s;constructor(){super(),this.#s=e=>{if(!i&&window.addEventListener){let t=()=>e(!0),s=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",s)}}}}onSubscribe(){this.#t||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#s=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#i!==e&&(this.#i=e,this.listeners.forEach(t=>{t(e)}))}isOnline(){return this.#i}};function x(e){return Math.min(1e3*2**e,3e4)}function P(e){return(e??"online")!=="online"||E.isOnline()}var T=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function S(e){return e instanceof T}function O(e){let t,s=!1,r=0,n=!1,a=function(){let e,t;let s=new Promise((s,i)=>{e=s,t=i});function i(e){Object.assign(s,e),delete s.resolve,delete s.reject}return s.status="pending",s.catch(()=>{}),s.resolve=t=>{i({status:"fulfilled",value:t}),e(t)},s.reject=e=>{i({status:"rejected",reason:e}),t(e)},s}(),o=()=>C.isFocused()&&("always"===e.networkMode||E.isOnline())&&e.canRun(),u=()=>P(e.networkMode)&&e.canRun(),c=s=>{n||(n=!0,e.onSuccess?.(s),t?.(),a.resolve(s))},l=s=>{n||(n=!0,e.onError?.(s),t?.(),a.reject(s))},h=()=>new Promise(s=>{t=e=>{(n||o())&&s(e)},e.onPause?.()}).then(()=>{t=void 0,n||e.onContinue?.()}),d=()=>{let t;if(n)return;let a=0===r?e.initialPromise:void 0;try{t=a??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(c).catch(t=>{if(n)return;let a=e.retry??(i?0:3),u=e.retryDelay??x,c="function"==typeof u?u(r,t):u,f=!0===a||"number"==typeof a&&r<a||"function"==typeof a&&a(r,t);if(s||!f){l(t);return}r++,e.onFail?.(r,t),new Promise(e=>{setTimeout(e,c)}).then(()=>o()?void 0:h()).then(()=>{s?l(t):d()})})};return{promise:a,cancel:t=>{n||(l(new T(t)),e.abort?.())},continue:()=>(t?.(),a),cancelRetry:()=>{s=!0},continueRetry:()=>{s=!1},canStart:u,start:()=>(u()?d():h().then(d),a)}}var F=class{#r;destroy(){this.clearGcTimeout()}scheduleGc(){var e;this.clearGcTimeout(),"number"==typeof(e=this.gcTime)&&e>=0&&e!==1/0&&(this.#r=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(i?1/0:3e5))}clearGcTimeout(){this.#r&&(clearTimeout(this.#r),this.#r=void 0)}},q=class extends F{#n;#a;#o;#u;#c;#l;#h;constructor(e){super(),this.#h=!1,this.#l=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#u=e.client,this.#o=this.#u.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#n=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,s=void 0!==t,i=s?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:s?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#n,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#c?.promise}setOptions(e){this.options={...this.#l,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#o.remove(this)}setData(e,t){var s,i;let r=(s=this.state.data,"function"==typeof(i=this.options).structuralSharing?i.structuralSharing(s,e):!1!==i.structuralSharing?function e(t,s){if(t===s)return t;let i=h(t)&&h(s);if(i||d(t)&&d(s)){let r=i?t:Object.keys(t),n=r.length,a=i?s:Object.keys(s),o=a.length,u=i?[]:{},c=new Set(r),l=0;for(let r=0;r<o;r++){let n=i?r:a[r];(!i&&c.has(n)||i)&&void 0===t[n]&&void 0===s[n]?(u[n]=void 0,l++):(u[n]=e(t[n],s[n]),u[n]===t[n]&&void 0!==t[n]&&l++)}return n===o&&l===n?t:u}return s}(s,e):e);return this.#d({data:r,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),r}setState(e,t){this.#d({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#c?.promise;return this.#c?.cancel(e),t?t.then(r).catch(r):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#n)}isActive(){return this.observers.some(e=>{var t;return!1!==("function"==typeof(t=e.options.enabled)?t(this):t)})}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===v||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(e=>"static"===n(e.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(e=0){return void 0===this.state.data||"static"!==e&&(!!this.state.isInvalidated||!Math.max(this.state.dataUpdatedAt+(e||0)-Date.now(),0))}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#c?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#c?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#o.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#c&&(this.#h?this.#c.cancel({revert:!0}):this.#c.cancelRetry()),this.scheduleGc()),this.#o.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#d({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#c)return this.#c.continueRetry(),this.#c.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let s=new AbortController,i=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#h=!0,s.signal)})},r=()=>{let e=m(this.options,t),s=(()=>{let e={client:this.#u,queryKey:this.queryKey,meta:this.meta};return i(e),e})();return(this.#h=!1,this.options.persister)?this.options.persister(e,s,this):e(s)},n=(()=>{let e={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#u,state:this.state,fetchFn:r};return i(e),e})();this.options.behavior?.onFetch(n,this),this.#a=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==n.fetchOptions?.meta)&&this.#d({type:"fetch",meta:n.fetchOptions?.meta});let a=e=>{S(e)&&e.silent||this.#d({type:"error",error:e}),S(e)||(this.#o.config.onError?.(e,this),this.#o.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#c=O({initialPromise:t?.initialPromise,fn:n.fetchFn,abort:s.abort.bind(s),onSuccess:e=>{if(void 0===e){a(Error(`${this.queryHash} data is undefined`));return}try{this.setData(e)}catch(e){a(e);return}this.#o.config.onSuccess?.(e,this),this.#o.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError:a,onFail:(e,t)=>{this.#d({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#d({type:"pause"})},onContinue:()=>{this.#d({type:"continue"})},retry:n.options.retry,retryDelay:n.options.retryDelay,networkMode:n.options.networkMode,canRun:()=>!0}),this.#c.start()}#d(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":var s;return{...t,...(s=t.data,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:P(this.options.networkMode)?"fetching":"paused",...void 0===s&&{error:null,status:"pending"}}),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let i=e.error;if(S(i)&&i.revert&&this.#a)return{...this.#a,fetchStatus:"idle"};return{...t,error:i,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:i,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),g.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#o.notify({query:this,type:"updated",action:e})})}},R=class extends w{constructor(e={}){super(),this.config=e,this.#f=new Map}#f;build(e,t,s){let i=t.queryKey,r=t.queryHash??u(i,t),n=this.get(r);return n||(n=new q({client:e,queryKey:i,queryHash:r,options:e.defaultQueryOptions(t),state:s,defaultOptions:e.getQueryDefaults(i)}),this.add(n)),n}add(e){this.#f.has(e.queryHash)||(this.#f.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#f.get(e.queryHash);t&&(e.destroy(),t===e&&this.#f.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){g.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#f.get(e)}getAll(){return[...this.#f.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>a(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>a(e,t)):t}notify(e){g.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){g.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){g.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},D=class extends F{#p;#y;#c;constructor(e){super(),this.mutationId=e.mutationId,this.#y=e.mutationCache,this.#p=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#p.includes(e)||(this.#p.push(e),this.clearGcTimeout(),this.#y.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#p=this.#p.filter(t=>t!==e),this.scheduleGc(),this.#y.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#p.length||("pending"===this.state.status?this.scheduleGc():this.#y.remove(this))}continue(){return this.#c?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#d({type:"continue"})};this.#c=O({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#d({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#d({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#y.canRun(this)});let s="pending"===this.state.status,i=!this.#c.canStart();try{if(s)t();else{this.#d({type:"pending",variables:e,isPaused:i}),await this.#y.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#d({type:"pending",context:t,variables:e,isPaused:i})}let r=await this.#c.start();return await this.#y.config.onSuccess?.(r,e,this.state.context,this),await this.options.onSuccess?.(r,e,this.state.context),await this.#y.config.onSettled?.(r,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(r,null,e,this.state.context),this.#d({type:"success",data:r}),r}catch(t){try{throw await this.#y.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#y.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#d({type:"error",error:t})}}finally{this.#y.runNext(this)}}#d(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),g.batch(()=>{this.#p.forEach(t=>{t.onMutationUpdate(e)}),this.#y.notify({mutation:this,type:"updated",action:e})})}},M=class extends w{constructor(e={}){super(),this.config=e,this.#v=new Set,this.#m=new Map,this.#b=0}#v;#m;#b;build(e,t,s){let i=new D({mutationCache:this,mutationId:++this.#b,options:e.defaultMutationOptions(t),state:s});return this.add(i),i}add(e){this.#v.add(e);let t=A(e);if("string"==typeof t){let s=this.#m.get(t);s?s.push(e):this.#m.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#v.delete(e)){let t=A(e);if("string"==typeof t){let s=this.#m.get(t);if(s){if(s.length>1){let t=s.indexOf(e);-1!==t&&s.splice(t,1)}else s[0]===e&&this.#m.delete(t)}}}this.notify({type:"removed",mutation:e})}canRun(e){let t=A(e);if("string"!=typeof t)return!0;{let s=this.#m.get(t),i=s?.find(e=>"pending"===e.state.status);return!i||i===e}}runNext(e){let t=A(e);if("string"!=typeof t)return Promise.resolve();{let s=this.#m.get(t)?.find(t=>t!==e&&t.state.isPaused);return s?.continue()??Promise.resolve()}}clear(){g.batch(()=>{this.#v.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#v.clear(),this.#m.clear()})}getAll(){return Array.from(this.#v)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>o(t,e))}findAll(e={}){return this.getAll().filter(t=>o(e,t))}notify(e){g.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return g.batch(()=>Promise.all(e.map(e=>e.continue().catch(r))))}};function A(e){return e.options.scope?.id}function j(e){return{onFetch:(t,s)=>{let i=t.options,r=t.fetchOptions?.meta?.fetchMore?.direction,n=t.state.data?.pages||[],a=t.state.data?.pageParams||[],o={pages:[],pageParams:[]},u=0,c=async()=>{let s=!1,c=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?s=!0:t.signal.addEventListener("abort",()=>{s=!0}),t.signal)})},l=m(t.options,t.fetchOptions),h=async(e,i,r)=>{if(s)return Promise.reject();if(null==i&&e.pages.length)return Promise.resolve(e);let n=(()=>{let e={client:t.client,queryKey:t.queryKey,pageParam:i,direction:r?"backward":"forward",meta:t.options.meta};return c(e),e})(),a=await l(n),{maxPages:o}=t.options,u=r?y:p;return{pages:u(e.pages,a,o),pageParams:u(e.pageParams,i,o)}};if(r&&n.length){let e="backward"===r,t={pages:n,pageParams:a},s=(e?function(e,{pages:t,pageParams:s}){return t.length>0?e.getPreviousPageParam?.(t[0],t,s[0],s):void 0}:k)(i,t);o=await h(t,s,e)}else{let t=e??n.length;do{let e=0===u?a[0]??i.initialPageParam:k(i,o);if(u>0&&null==e)break;o=await h(o,e),u++}while(u<t)}return o};t.options.persister?t.fetchFn=()=>t.options.persister?.(c,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s):t.fetchFn=c}}}function k(e,{pages:t,pageParams:s}){let i=t.length-1;return t.length>0?e.getNextPageParam(t[i],t,s[i],s):void 0}var L=class{#g;#y;#l;#w;#C;#E;#x;#P;constructor(e={}){this.#g=e.queryCache||new R,this.#y=e.mutationCache||new M,this.#l=e.defaultOptions||{},this.#w=new Map,this.#C=new Map,this.#E=0}mount(){this.#E++,1===this.#E&&(this.#x=C.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#g.onFocus())}),this.#P=E.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#g.onOnline())}))}unmount(){this.#E--,0===this.#E&&(this.#x?.(),this.#x=void 0,this.#P?.(),this.#P=void 0)}isFetching(e){return this.#g.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#y.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#g.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),s=this.#g.build(this,t),i=s.state.data;return void 0===i?this.fetchQuery(e):(e.revalidateIfStale&&s.isStaleByTime(n(t.staleTime,s))&&this.prefetchQuery(t),Promise.resolve(i))}getQueriesData(e){return this.#g.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,s){let i=this.defaultQueryOptions({queryKey:e}),r=this.#g.get(i.queryHash),n=r?.state.data,a="function"==typeof t?t(n):t;if(void 0!==a)return this.#g.build(this,i).setData(a,{...s,manual:!0})}setQueriesData(e,t,s){return g.batch(()=>this.#g.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,s)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#g.get(t.queryHash)?.state}removeQueries(e){let t=this.#g;g.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let s=this.#g;return g.batch(()=>(s.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let s={revert:!0,...t};return Promise.all(g.batch(()=>this.#g.findAll(e).map(e=>e.cancel(s)))).then(r).catch(r)}invalidateQueries(e,t={}){return g.batch(()=>(this.#g.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let s={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(g.batch(()=>this.#g.findAll(e).filter(e=>!e.isDisabled()&&!e.isStatic()).map(e=>{let t=e.fetch(void 0,s);return s.throwOnError||(t=t.catch(r)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(r)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let s=this.#g.build(this,t);return s.isStaleByTime(n(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(r).catch(r)}fetchInfiniteQuery(e){return e.behavior=j(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(r).catch(r)}ensureInfiniteQueryData(e){return e.behavior=j(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return E.isOnline()?this.#y.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#g}getMutationCache(){return this.#y}getDefaultOptions(){return this.#l}setDefaultOptions(e){this.#l=e}setQueryDefaults(e,t){this.#w.set(c(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#w.values()],s={};return t.forEach(t=>{l(e,t.queryKey)&&Object.assign(s,t.defaultOptions)}),s}setMutationDefaults(e,t){this.#C.set(c(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#C.values()],s={};return t.forEach(t=>{l(e,t.mutationKey)&&Object.assign(s,t.defaultOptions)}),s}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#l.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=u(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===v&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#l.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#g.clear(),this.#y.clear()}}},8038:function(e,t,s){"use strict";s.d(t,{aH:function(){return a}});var i=s(2265),r=s(7437),n=i.createContext(void 0),a=({client:e,children:t})=>(i.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),(0,r.jsx)(n.Provider,{value:e,children:t}))}}]);