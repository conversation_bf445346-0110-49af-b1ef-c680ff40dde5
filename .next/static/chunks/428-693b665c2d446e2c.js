"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[428],{2894:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1291:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},9224:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},7706:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Columns",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["line",{x1:"12",x2:"12",y1:"3",y2:"21",key:"1efggb"}]])},9670:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},6637:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},1813:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},4689:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])},6264:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},4280:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},1827:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},6357:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]])},6104:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("TestTube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5h0c-1.4 0-2.5-1.1-2.5-2.5V2",key:"187lwq"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]])},2104:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},6062:function(e,t,n){n.d(t,{fC:function(){return b},z$:function(){return C}});var r=n(2265),o=n(2210),l=n(6989),a=n(5744),i=n(3763),u=n(5184),s=n(4977),c=n(5606),d=n(9381),p=n(7437),f="Checkbox",[h,v]=(0,l.b)(f),[y,m]=h(f);function x(e){let{__scopeCheckbox:t,checked:n,children:o,defaultChecked:l,disabled:a,form:u,name:s,onCheckedChange:c,required:d,value:h="on",internal_do_not_use_render:v}=e,[m,x]=(0,i.T)({prop:n,defaultProp:l??!1,onChange:c,caller:f}),[g,w]=r.useState(null),[b,k]=r.useState(null),C=r.useRef(!1),S=!g||!!u||!!g.closest("form"),j={checked:m,disabled:a,setChecked:x,control:g,setControl:w,name:s,form:u,value:h,hasConsumerStoppedPropagationRef:C,required:d,defaultChecked:!M(l)&&l,isFormControl:S,bubbleInput:b,setBubbleInput:k};return(0,p.jsx)(y,{scope:t,...j,children:"function"==typeof v?v(j):o})}var g="CheckboxTrigger",w=r.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:n,...l},i)=>{let{control:u,value:s,disabled:c,checked:f,required:h,setControl:v,setChecked:y,hasConsumerStoppedPropagationRef:x,isFormControl:w,bubbleInput:b}=m(g,e),k=(0,o.e)(i,v),C=r.useRef(f);return r.useEffect(()=>{let e=u?.form;if(e){let t=()=>y(C.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[u,y]),(0,p.jsx)(d.WV.button,{type:"button",role:"checkbox","aria-checked":M(f)?"mixed":f,"aria-required":h,"data-state":R(f),"data-disabled":c?"":void 0,disabled:c,value:s,...l,ref:k,onKeyDown:(0,a.M)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,a.M)(n,e=>{y(e=>!!M(e)||!e),b&&w&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})})});w.displayName=g;var b=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:r,checked:o,defaultChecked:l,required:a,disabled:i,value:u,onCheckedChange:s,form:c,...d}=e;return(0,p.jsx)(x,{__scopeCheckbox:n,checked:o,defaultChecked:l,disabled:i,required:a,onCheckedChange:s,name:r,form:c,value:u,internal_do_not_use_render:({isFormControl:e})=>(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(w,{...d,ref:t,__scopeCheckbox:n}),e&&(0,p.jsx)(j,{__scopeCheckbox:n})]})})});b.displayName=f;var k="CheckboxIndicator",C=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...o}=e,l=m(k,n);return(0,p.jsx)(c.z,{present:r||M(l.checked)||!0===l.checked,children:(0,p.jsx)(d.WV.span,{"data-state":R(l.checked),"data-disabled":l.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});C.displayName=k;var S="CheckboxBubbleInput",j=r.forwardRef(({__scopeCheckbox:e,...t},n)=>{let{control:l,hasConsumerStoppedPropagationRef:a,checked:i,defaultChecked:c,required:f,disabled:h,name:v,value:y,form:x,bubbleInput:g,setBubbleInput:w}=m(S,e),b=(0,o.e)(n,w),k=(0,u.D)(i),C=(0,s.t)(l);r.useEffect(()=>{if(!g)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!a.current;if(k!==i&&e){let n=new Event("click",{bubbles:t});g.indeterminate=M(i),e.call(g,!M(i)&&i),g.dispatchEvent(n)}},[g,k,i,a]);let j=r.useRef(!M(i)&&i);return(0,p.jsx)(d.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:c??j.current,required:f,disabled:h,name:v,value:y,form:x,...t,tabIndex:-1,ref:b,style:{...t.style,...C,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function M(e){return"indeterminate"===e}function R(e){return M(e)?"indeterminate":e?"checked":"unchecked"}j.displayName=S},1927:function(e,t,n){n.d(t,{Fw:function(){return k},fC:function(){return j},wy:function(){return w}});var r=n(2265),o=n(5744),l=n(6989),a=n(3763),i=n(5655),u=n(2210),s=n(9381),c=n(5606),d=n(966),p=n(7437),f="Collapsible",[h,v]=(0,l.b)(f),[y,m]=h(f),x=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,open:o,defaultOpen:l,disabled:i,onOpenChange:u,...c}=e,[h,v]=(0,a.T)({prop:o,defaultProp:l??!1,onChange:u,caller:f});return(0,p.jsx)(y,{scope:n,disabled:i,contentId:(0,d.M)(),open:h,onOpenToggle:r.useCallback(()=>v(e=>!e),[v]),children:(0,p.jsx)(s.WV.div,{"data-state":S(h),"data-disabled":i?"":void 0,...c,ref:t})})});x.displayName=f;var g="CollapsibleTrigger",w=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,...r}=e,l=m(g,n);return(0,p.jsx)(s.WV.button,{type:"button","aria-controls":l.contentId,"aria-expanded":l.open||!1,"data-state":S(l.open),"data-disabled":l.disabled?"":void 0,disabled:l.disabled,...r,ref:t,onClick:(0,o.M)(e.onClick,l.onOpenToggle)})});w.displayName=g;var b="CollapsibleContent",k=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=m(b,e.__scopeCollapsible);return(0,p.jsx)(c.z,{present:n||o.open,children:({present:e})=>(0,p.jsx)(C,{...r,ref:t,present:e})})});k.displayName=b;var C=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,present:o,children:l,...a}=e,c=m(b,n),[d,f]=r.useState(o),h=r.useRef(null),v=(0,u.e)(t,h),y=r.useRef(0),x=y.current,g=r.useRef(0),w=g.current,k=c.open||d,C=r.useRef(k),j=r.useRef(void 0);return r.useEffect(()=>{let e=requestAnimationFrame(()=>C.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,i.b)(()=>{let e=h.current;if(e){j.current=j.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();y.current=t.height,g.current=t.width,C.current||(e.style.transitionDuration=j.current.transitionDuration,e.style.animationName=j.current.animationName),f(o)}},[c.open,o]),(0,p.jsx)(s.WV.div,{"data-state":S(c.open),"data-disabled":c.disabled?"":void 0,id:c.contentId,hidden:!k,...a,ref:v,style:{"--radix-collapsible-content-height":x?`${x}px`:void 0,"--radix-collapsible-content-width":w?`${w}px`:void 0,...e.style},children:k&&l})});function S(e){return e?"open":"closed"}var j=x},6743:function(e,t,n){n.d(t,{f:function(){return i}});var r=n(2265),o=n(9381),l=n(7437),a=r.forwardRef((e,t)=>(0,l.jsx)(o.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var i=a},8010:function(e,t,n){n.d(t,{$G:function(){return eO},B4:function(){return eI},JO:function(){return eP},VY:function(){return eL},Z0:function(){return eF},ZA:function(){return eW},__:function(){return e_},ck:function(){return eH},eT:function(){return eA},fC:function(){return eD},h_:function(){return eV},l_:function(){return eZ},u_:function(){return ez},wU:function(){return eB},xz:function(){return eN}});var r=n(2265),o=n(4887),l=n(760),a=n(5744),i=n(7733),u=n(2210),s=n(6989),c=n(5400),d=n(9249),p=n(1244),f=n(2759),h=n(966),v=n(3995),y=n(2730),m=n(9381),x=n(7256),g=n(6459),w=n(3763),b=n(5655),k=n(5184),C=n(8281),S=n(5859),j=n(3386),M=n(7437),R=[" ","Enter","ArrowUp","ArrowDown"],T=[" ","Enter"],E="Select",[D,N,I]=(0,i.B)(E),[P,V]=(0,s.b)(E,[I,v.D7]),L=(0,v.D7)(),[Z,W]=P(E),[_,H]=P(E),A=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:l,onOpenChange:a,value:i,defaultValue:u,onValueChange:s,dir:d,name:p,autoComplete:f,disabled:y,required:m,form:x}=e,g=L(t),[b,k]=r.useState(null),[C,S]=r.useState(null),[j,R]=r.useState(!1),T=(0,c.gm)(d),[N,I]=(0,w.T)({prop:o,defaultProp:l??!1,onChange:a,caller:E}),[P,V]=(0,w.T)({prop:i,defaultProp:u,onChange:s,caller:E}),W=r.useRef(null),H=!b||x||!!b.closest("form"),[A,B]=r.useState(new Set),z=Array.from(A).map(e=>e.props.value).join(";");return(0,M.jsx)(v.fC,{...g,children:(0,M.jsxs)(Z,{required:m,scope:t,trigger:b,onTriggerChange:k,valueNode:C,onValueNodeChange:S,valueNodeHasChildren:j,onValueNodeHasChildrenChange:R,contentId:(0,h.M)(),value:P,onValueChange:V,open:N,onOpenChange:I,dir:T,triggerPointerDownPosRef:W,disabled:y,children:[(0,M.jsx)(D.Provider,{scope:t,children:(0,M.jsx)(_,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{B(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{B(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),H?(0,M.jsxs)(eM,{"aria-hidden":!0,required:m,tabIndex:-1,name:p,autoComplete:f,value:P,onChange:e=>V(e.target.value),disabled:y,form:x,children:[void 0===P?(0,M.jsx)("option",{value:""}):null,Array.from(A)]},z):null]})})};A.displayName=E;var B="SelectTrigger",z=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...l}=e,i=L(n),s=W(B,n),c=s.disabled||o,d=(0,u.e)(t,s.onTriggerChange),p=N(n),f=r.useRef("touch"),[h,y,x]=eT(e=>{let t=p().filter(e=>!e.disabled),n=t.find(e=>e.value===s.value),r=eE(t,e,n);void 0!==r&&s.onValueChange(r.value)}),g=e=>{c||(s.onOpenChange(!0),x()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,M.jsx)(v.ee,{asChild:!0,...i,children:(0,M.jsx)(m.WV.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":eR(s.value)?"":void 0,...l,ref:d,onClick:(0,a.M)(l.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&g(e)}),onPointerDown:(0,a.M)(l.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,a.M)(l.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||y(e.key),(!t||" "!==e.key)&&R.includes(e.key)&&(g(),e.preventDefault())})})})});z.displayName=B;var O="SelectValue",F=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:l,placeholder:a="",...i}=e,s=W(O,n),{onValueNodeHasChildrenChange:c}=s,d=void 0!==l,p=(0,u.e)(t,s.onValueNodeChange);return(0,b.b)(()=>{c(d)},[c,d]),(0,M.jsx)(m.WV.span,{...i,ref:p,style:{pointerEvents:"none"},children:eR(s.value)?(0,M.jsx)(M.Fragment,{children:a}):l})});F.displayName=O;var K=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,M.jsx)(m.WV.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});K.displayName="SelectIcon";var q=e=>(0,M.jsx)(y.h,{asChild:!0,...e});q.displayName="SelectPortal";var U="SelectContent",X=r.forwardRef((e,t)=>{let n=W(U,e.__scopeSelect),[l,a]=r.useState();return((0,b.b)(()=>{a(new DocumentFragment)},[]),n.open)?(0,M.jsx)(J,{...e,ref:t}):l?o.createPortal((0,M.jsx)(Y,{scope:e.__scopeSelect,children:(0,M.jsx)(D.Slot,{scope:e.__scopeSelect,children:(0,M.jsx)("div",{children:e.children})})}),l):null});X.displayName=U;var[Y,$]=P(U),G=(0,x.Z8)("SelectContent.RemoveScroll"),J=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:i,onPointerDownOutside:s,side:c,sideOffset:h,align:v,alignOffset:y,arrowPadding:m,collisionBoundary:x,collisionPadding:g,sticky:w,hideWhenDetached:b,avoidCollisions:k,...C}=e,R=W(U,n),[T,E]=r.useState(null),[D,I]=r.useState(null),P=(0,u.e)(t,e=>E(e)),[V,L]=r.useState(null),[Z,_]=r.useState(null),H=N(n),[A,B]=r.useState(!1),z=r.useRef(!1);r.useEffect(()=>{if(T)return(0,S.Ry)(T)},[T]),(0,p.EW)();let O=r.useCallback(e=>{let[t,...n]=H().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&D&&(D.scrollTop=0),n===r&&D&&(D.scrollTop=D.scrollHeight),n?.focus(),document.activeElement!==o))return},[H,D]),F=r.useCallback(()=>O([V,T]),[O,V,T]);r.useEffect(()=>{A&&F()},[A,F]);let{onOpenChange:K,triggerPointerDownPosRef:q}=R;r.useEffect(()=>{if(T){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(q.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(q.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():T.contains(n.target)||K(!1),document.removeEventListener("pointermove",t),q.current=null};return null!==q.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[T,K,q]),r.useEffect(()=>{let e=()=>K(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[K]);let[X,$]=eT(e=>{let t=H().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=eE(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),J=r.useCallback((e,t,n)=>{let r=!z.current&&!n;(void 0!==R.value&&R.value===t||r)&&(L(e),r&&(z.current=!0))},[R.value]),et=r.useCallback(()=>T?.focus(),[T]),en=r.useCallback((e,t,n)=>{let r=!z.current&&!n;(void 0!==R.value&&R.value===t||r)&&_(e)},[R.value]),er="popper"===o?ee:Q,eo=er===ee?{side:c,sideOffset:h,align:v,alignOffset:y,arrowPadding:m,collisionBoundary:x,collisionPadding:g,sticky:w,hideWhenDetached:b,avoidCollisions:k}:{};return(0,M.jsx)(Y,{scope:n,content:T,viewport:D,onViewportChange:I,itemRefCallback:J,selectedItem:V,onItemLeave:et,itemTextRefCallback:en,focusSelectedItem:F,selectedItemText:Z,position:o,isPositioned:A,searchRef:X,children:(0,M.jsx)(j.Z,{as:G,allowPinchZoom:!0,children:(0,M.jsx)(f.M,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.M)(l,e=>{R.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,M.jsx)(d.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,M.jsx)(er,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...C,...eo,onPlaced:()=>B(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,a.M)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||$(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=H().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>O(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var Q=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...a}=e,i=W(U,n),s=$(U,n),[c,d]=r.useState(null),[p,f]=r.useState(null),h=(0,u.e)(t,e=>f(e)),v=N(n),y=r.useRef(!1),x=r.useRef(!0),{viewport:g,selectedItem:w,selectedItemText:k,focusSelectedItem:C}=s,S=r.useCallback(()=>{if(i.trigger&&i.valueNode&&c&&p&&g&&w&&k){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),r=k.getBoundingClientRect();if("rtl"!==i.dir){let o=r.left-t.left,a=n.left-o,i=e.left-a,u=e.width+i,s=Math.max(u,t.width),d=window.innerWidth-10,p=(0,l.u)(a,[10,Math.max(10,d-s)]);c.style.minWidth=u+"px",c.style.left=p+"px"}else{let o=t.right-r.right,a=window.innerWidth-n.right-o,i=window.innerWidth-e.right-a,u=e.width+i,s=Math.max(u,t.width),d=window.innerWidth-10,p=(0,l.u)(a,[10,Math.max(10,d-s)]);c.style.minWidth=u+"px",c.style.right=p+"px"}let a=v(),u=window.innerHeight-20,s=g.scrollHeight,d=window.getComputedStyle(p),f=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),m=parseInt(d.borderBottomWidth,10),x=f+h+s+parseInt(d.paddingBottom,10)+m,b=Math.min(5*w.offsetHeight,x),C=window.getComputedStyle(g),S=parseInt(C.paddingTop,10),j=parseInt(C.paddingBottom,10),M=e.top+e.height/2-10,R=w.offsetHeight/2,T=f+h+(w.offsetTop+R);if(T<=M){let e=a.length>0&&w===a[a.length-1].ref.current;c.style.bottom="0px";let t=p.clientHeight-g.offsetTop-g.offsetHeight;c.style.height=T+Math.max(u-M,R+(e?j:0)+t+m)+"px"}else{let e=a.length>0&&w===a[0].ref.current;c.style.top="0px";let t=Math.max(M,f+g.offsetTop+(e?S:0)+R);c.style.height=t+(x-T)+"px",g.scrollTop=T-M+g.offsetTop}c.style.margin="10px 0",c.style.minHeight=b+"px",c.style.maxHeight=u+"px",o?.(),requestAnimationFrame(()=>y.current=!0)}},[v,i.trigger,i.valueNode,c,p,g,w,k,i.dir,o]);(0,b.b)(()=>S(),[S]);let[j,R]=r.useState();(0,b.b)(()=>{p&&R(window.getComputedStyle(p).zIndex)},[p]);let T=r.useCallback(e=>{e&&!0===x.current&&(S(),C?.(),x.current=!1)},[S,C]);return(0,M.jsx)(et,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:y,onScrollButtonChange:T,children:(0,M.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:j},children:(0,M.jsx)(m.WV.div,{...a,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Q.displayName="SelectItemAlignedPosition";var ee=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...l}=e,a=L(n);return(0,M.jsx)(v.VY,{...a,...l,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,en]=P(U,{}),er="SelectViewport",eo=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...l}=e,i=$(er,n),s=en(er,n),c=(0,u.e)(t,i.onViewportChange),d=r.useRef(0);return(0,M.jsxs)(M.Fragment,{children:[(0,M.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,M.jsx)(D.Slot,{scope:n,children:(0,M.jsx)(m.WV.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:(0,a.M)(l.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=s;if(r?.current&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let l=o+e,a=Math.min(r,l),i=l-a;n.style.height=a+"px","0px"===n.style.bottom&&(t.scrollTop=i>0?i:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});eo.displayName=er;var el="SelectGroup",[ea,ei]=P(el),eu=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,h.M)();return(0,M.jsx)(ea,{scope:n,id:o,children:(0,M.jsx)(m.WV.div,{role:"group","aria-labelledby":o,...r,ref:t})})});eu.displayName=el;var es="SelectLabel",ec=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=ei(es,n);return(0,M.jsx)(m.WV.div,{id:o.id,...r,ref:t})});ec.displayName=es;var ed="SelectItem",[ep,ef]=P(ed),eh=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:l=!1,textValue:i,...s}=e,c=W(ed,n),d=$(ed,n),p=c.value===o,[f,v]=r.useState(i??""),[y,x]=r.useState(!1),g=(0,u.e)(t,e=>d.itemRefCallback?.(e,o,l)),w=(0,h.M)(),b=r.useRef("touch"),k=()=>{l||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,M.jsx)(ep,{scope:n,value:o,disabled:l,textId:w,isSelected:p,onItemTextChange:r.useCallback(e=>{v(t=>t||(e?.textContent??"").trim())},[]),children:(0,M.jsx)(D.ItemSlot,{scope:n,value:o,disabled:l,textValue:f,children:(0,M.jsx)(m.WV.div,{role:"option","aria-labelledby":w,"data-highlighted":y?"":void 0,"aria-selected":p&&y,"data-state":p?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...s,ref:g,onFocus:(0,a.M)(s.onFocus,()=>x(!0)),onBlur:(0,a.M)(s.onBlur,()=>x(!1)),onClick:(0,a.M)(s.onClick,()=>{"mouse"!==b.current&&k()}),onPointerUp:(0,a.M)(s.onPointerUp,()=>{"mouse"===b.current&&k()}),onPointerDown:(0,a.M)(s.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.M)(s.onPointerMove,e=>{b.current=e.pointerType,l?d.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.M)(s.onPointerLeave,e=>{e.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:(0,a.M)(s.onKeyDown,e=>{d.searchRef?.current!==""&&" "===e.key||(T.includes(e.key)&&k()," "===e.key&&e.preventDefault())})})})})});eh.displayName=ed;var ev="SelectItemText",ey=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:l,style:a,...i}=e,s=W(ev,n),c=$(ev,n),d=ef(ev,n),p=H(ev,n),[f,h]=r.useState(null),v=(0,u.e)(t,e=>h(e),d.onItemTextChange,e=>c.itemTextRefCallback?.(e,d.value,d.disabled)),y=f?.textContent,x=r.useMemo(()=>(0,M.jsx)("option",{value:d.value,disabled:d.disabled,children:y},d.value),[d.disabled,d.value,y]),{onNativeOptionAdd:g,onNativeOptionRemove:w}=p;return(0,b.b)(()=>(g(x),()=>w(x)),[g,w,x]),(0,M.jsxs)(M.Fragment,{children:[(0,M.jsx)(m.WV.span,{id:d.textId,...i,ref:v}),d.isSelected&&s.valueNode&&!s.valueNodeHasChildren?o.createPortal(i.children,s.valueNode):null]})});ey.displayName=ev;var em="SelectItemIndicator",ex=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return ef(em,n).isSelected?(0,M.jsx)(m.WV.span,{"aria-hidden":!0,...r,ref:t}):null});ex.displayName=em;var eg="SelectScrollUpButton",ew=r.forwardRef((e,t)=>{let n=$(eg,e.__scopeSelect),o=en(eg,e.__scopeSelect),[l,a]=r.useState(!1),i=(0,u.e)(t,o.onScrollButtonChange);return(0,b.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){a(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,M.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ew.displayName=eg;var eb="SelectScrollDownButton",ek=r.forwardRef((e,t)=>{let n=$(eb,e.__scopeSelect),o=en(eb,e.__scopeSelect),[l,a]=r.useState(!1),i=(0,u.e)(t,o.onScrollButtonChange);return(0,b.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,M.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ek.displayName=eb;var eC=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...l}=e,i=$("SelectScrollButton",n),u=r.useRef(null),s=N(n),c=r.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return r.useEffect(()=>()=>c(),[c]),(0,b.b)(()=>{let e=s().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[s]),(0,M.jsx)(m.WV.div,{"aria-hidden":!0,...l,ref:t,style:{flexShrink:0,...l.style},onPointerDown:(0,a.M)(l.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(o,50))}),onPointerMove:(0,a.M)(l.onPointerMove,()=>{i.onItemLeave?.(),null===u.current&&(u.current=window.setInterval(o,50))}),onPointerLeave:(0,a.M)(l.onPointerLeave,()=>{c()})})}),eS=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,M.jsx)(m.WV.div,{"aria-hidden":!0,...r,ref:t})});eS.displayName="SelectSeparator";var ej="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=L(n),l=W(ej,n),a=$(ej,n);return l.open&&"popper"===a.position?(0,M.jsx)(v.Eh,{...o,...r,ref:t}):null}).displayName=ej;var eM=r.forwardRef(({__scopeSelect:e,value:t,...n},o)=>{let l=r.useRef(null),a=(0,u.e)(o,l),i=(0,k.D)(t);return r.useEffect(()=>{let e=l.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==t&&n){let r=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[i,t]),(0,M.jsx)(m.WV.select,{...n,style:{...C.C2,...n.style},ref:a,defaultValue:t})});function eR(e){return""===e||void 0===e}function eT(e){let t=(0,g.W)(e),n=r.useRef(""),o=r.useRef(0),l=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),a=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,l,a]}function eE(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(l=l.filter(e=>e!==n));let a=l.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return a!==n?a:void 0}eM.displayName="SelectBubbleInput";var eD=A,eN=z,eI=F,eP=K,eV=q,eL=X,eZ=eo,eW=eu,e_=ec,eH=eh,eA=ey,eB=ex,ez=ew,eO=ek,eF=eS},5184:function(e,t,n){n.d(t,{D:function(){return o}});var r=n(2265);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},8281:function(e,t,n){n.d(t,{C2:function(){return a},TX:function(){return i}});var r=n(2265),o=n(9381),l=n(7437),a=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),i=r.forwardRef((e,t)=>(0,l.jsx)(o.WV.span,{...e,ref:t,style:{...a,...e.style}}));i.displayName="VisuallyHidden"}}]);