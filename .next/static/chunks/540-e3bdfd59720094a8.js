(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[540],{1981:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},8940:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("AlignLeft",[["line",{x1:"21",x2:"3",y1:"6",y2:"6",key:"1fp77t"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}],["line",{x1:"17",x2:"3",y1:"18",y2:"18",key:"1awlsn"}]])},9865:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},4907:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("BrainCircuit",[["path",{d:"M12 4.5a2.5 2.5 0 0 0-4.96-.46 2.5 2.5 0 0 0-1.98 3 2.5 2.5 0 0 0-1.32 4.24 3 3 0 0 0 .34 5.58 2.5 2.5 0 0 0 2.96 3.08 2.5 2.5 0 0 0 4.91.05L12 20V4.5Z",key:"ixwj2a"}],["path",{d:"M16 8V5c0-1.1.9-2 2-2",key:"13dx7u"}],["path",{d:"M12 13h4",key:"1ku699"}],["path",{d:"M12 18h6a2 2 0 0 1 2 2v1",key:"105ag5"}],["path",{d:"M12 8h8",key:"1lhi5i"}],["path",{d:"M20.5 8a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"1s25gz"}],["path",{d:"M16.5 13a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"127460"}],["path",{d:"M20.5 21a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"fys062"}],["path",{d:"M18.5 3a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"1vib61"}]])},6369:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},1738:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},5479:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},8244:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},1097:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]])},5883:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},8004:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},9883:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5432:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},6245:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},2851:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},4658:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("ThumbsDown",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22h0a3.13 3.13 0 0 1-3-3.88Z",key:"s6e0r"}]])},9868:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z",key:"y3tblf"}]])},5790:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},2369:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(2898).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},1396:function(e,t,r){e.exports=r(5250)},837:function(e,t,r){"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function u(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function c(e){return function t(){for(var r=this,n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];return o.length>=e.length?e.apply(this,o):function(){for(var e=arguments.length,n=Array(e),a=0;a<e;a++)n[a]=arguments[a];return t.apply(r,[].concat(o,n))}}}function l(e){return({}).toString.call(e).includes("Object")}function s(e){return"function"==typeof e}r.d(t,{ML:function(){return B}});var d,f,p=c(function(e,t){throw Error(e[t]||e.default)})({initialIsRequired:"initial state is required",initialType:"initial state should be an object",initialContent:"initial state shouldn't be an empty object",handlerType:"handler should be an object or a function",handlersType:"all handlers should be a functions",selectorType:"selector should be a function",changeType:"provided value of changes should be an object",changeField:'it seams you want to change a field in the state which is not specified in the "initial" state',default:"an unknown error accured in `state-local` package"}),h={changes:function(e,t){return l(t)||p("changeType"),Object.keys(t).some(function(t){return!Object.prototype.hasOwnProperty.call(e,t)})&&p("changeField"),t},selector:function(e){s(e)||p("selectorType")},handler:function(e){s(e)||l(e)||p("handlerType"),l(e)&&Object.values(e).some(function(e){return!s(e)})&&p("handlersType")},initial:function(e){e||p("initialIsRequired"),l(e)||p("initialType"),Object.keys(e).length||p("initialContent")}};function g(e,t){return s(t)?t(e.current):t}function y(e,t){return e.current=u(u({},e.current),t),t}function v(e,t,r){return s(t)?t(e.current):Object.keys(r).forEach(function(r){var n;return null===(n=t[r])||void 0===n?void 0:n.call(t,e.current[r])}),r}var m={configIsRequired:"the configuration object is required",configType:"the configuration object should be an object",default:"an unknown error accured in `@monaco-editor/loader` package",deprecation:"Deprecation warning!\n    You are using deprecated way of configuration.\n\n    Instead of using\n      monaco.config({ urls: { monacoBase: '...' } })\n    use\n      monaco.config({ paths: { vs: '...' } })\n\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\n  "},w=(d=function(e,t){throw Error(e[t]||e.default)},function e(){for(var t=this,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return n.length>=d.length?d.apply(this,n):function(){for(var r=arguments.length,o=Array(r),a=0;a<r;a++)o[a]=arguments[a];return e.apply(t,[].concat(n,o))}})(m),M={config:function(e){return e||w("configIsRequired"),({}).toString.call(e).includes("Object")||w("configType"),e.urls?(console.warn(m.deprecation),{paths:{vs:e.urls.monacoBase}}):e}},b=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){return t.reduceRight(function(e,t){return t(e)},e)}},x={type:"cancelation",msg:"operation is manually canceled"},k=function(e){var t=!1,r=new Promise(function(r,n){e.then(function(e){return t?n(x):r(e)}),e.catch(n)});return r.cancel=function(){return t=!0},r},j=function(e){if(Array.isArray(e))return e}(f=({create:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};h.initial(e),h.handler(t);var r={current:e},n=c(v)(r,t),o=c(y)(r),a=c(h.changes)(e),i=c(g)(r);return[function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(e){return e};return h.selector(e),e(r.current)},function(e){(function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){return t.reduceRight(function(e,t){return t(e)},e)}})(n,o,a,i)(e)}]}}).create({config:{paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}},isInitialized:!1,resolve:null,reject:null,monaco:null}))||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,o=!1,a=void 0;try{for(var i,u=e[Symbol.iterator]();!(n=(i=u.next()).done)&&(r.push(i.value),!t||r.length!==t);n=!0);}catch(e){o=!0,a=e}finally{try{n||null==u.return||u.return()}finally{if(o)throw a}}return r}}(f,2)||function(e,t){if(e){if("string"==typeof e)return a(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return a(e,t)}}(f,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),R=j[0],C=j[1];function O(e){return document.body.appendChild(e)}function E(e){var t,r,n=R(function(e){return{config:e.config,reject:e.reject}}),o=(t="".concat(n.config.paths.vs,"/loader.js"),r=document.createElement("script"),t&&(r.src=t),r);return o.onload=function(){return e()},o.onerror=n.reject,o}function D(){var e=R(function(e){return{config:e.config,resolve:e.resolve,reject:e.reject}}),t=window.require;t.config(e.config),t(["vs/editor/editor.main"],function(t){P(t),e.resolve(t)},function(t){e.reject(t)})}function P(e){R().monaco||C({monaco:e})}var S=new Promise(function(e,t){return C({resolve:e,reject:t})}),I={config:function(e){var t=M.config(e),r=t.monaco,n=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(t,["monaco"]);C(function(e){return{config:function e(t,r){return Object.keys(r).forEach(function(n){r[n]instanceof Object&&t[n]&&Object.assign(r[n],e(t[n],r[n]))}),o(o({},t),r)}(e.config,n),monaco:r}})},init:function(){var e=R(function(e){return{monaco:e.monaco,isInitialized:e.isInitialized,resolve:e.resolve}});if(!e.isInitialized){if(C({isInitialized:!0}),e.monaco)return e.resolve(e.monaco),k(S);if(window.monaco&&window.monaco.editor)return P(window.monaco),e.resolve(window.monaco),k(S);b(O,E)(D)}return k(S)},__getMonacoInstance:function(){return R(function(e){return e.monaco})}},T=r(2265),_={wrapper:{display:"flex",position:"relative",textAlign:"initial"},fullWidth:{width:"100%"},hide:{display:"none"}},L={container:{display:"flex",height:"100%",width:"100%",justifyContent:"center",alignItems:"center"}},A=function({children:e}){return T.createElement("div",{style:L.container},e)},Z=(0,T.memo)(function({width:e,height:t,isEditorReady:r,loading:n,_ref:o,className:a,wrapperProps:i}){return T.createElement("section",{style:{..._.wrapper,width:e,height:t},...i},!r&&T.createElement(A,null,n),T.createElement("div",{ref:o,style:{..._.fullWidth,...!r&&_.hide},className:a}))}),F=function(e){(0,T.useEffect)(e,[])},N=function(e,t,r=!0){let n=(0,T.useRef)(!0);(0,T.useEffect)(n.current||!r?()=>{n.current=!1}:e,t)};function V(){}function K(e,t,r,n){return e.editor.getModel(z(e,n))||e.editor.createModel(t,r,n?z(e,n):void 0)}function z(e,t){return e.Uri.parse(t)}(0,T.memo)(function({original:e,modified:t,language:r,originalLanguage:n,modifiedLanguage:o,originalModelPath:a,modifiedModelPath:i,keepCurrentOriginalModel:u=!1,keepCurrentModifiedModel:c=!1,theme:l="light",loading:s="Loading...",options:d={},height:f="100%",width:p="100%",className:h,wrapperProps:g={},beforeMount:y=V,onMount:v=V}){let[m,w]=(0,T.useState)(!1),[M,b]=(0,T.useState)(!0),x=(0,T.useRef)(null),k=(0,T.useRef)(null),j=(0,T.useRef)(null),R=(0,T.useRef)(v),C=(0,T.useRef)(y),O=(0,T.useRef)(!1);F(()=>{let e=I.init();return e.then(e=>(k.current=e)&&b(!1)).catch(e=>e?.type!=="cancelation"&&console.error("Monaco initialization: error:",e)),()=>{let t;return x.current?(t=x.current?.getModel(),void(u||t?.original?.dispose(),c||t?.modified?.dispose(),x.current?.dispose())):e.cancel()}}),N(()=>{if(x.current&&k.current){let t=x.current.getOriginalEditor(),o=K(k.current,e||"",n||r||"text",a||"");o!==t.getModel()&&t.setModel(o)}},[a],m),N(()=>{if(x.current&&k.current){let e=x.current.getModifiedEditor(),n=K(k.current,t||"",o||r||"text",i||"");n!==e.getModel()&&e.setModel(n)}},[i],m),N(()=>{let e=x.current.getModifiedEditor();e.getOption(k.current.editor.EditorOption.readOnly)?e.setValue(t||""):t!==e.getValue()&&(e.executeEdits("",[{range:e.getModel().getFullModelRange(),text:t||"",forceMoveMarkers:!0}]),e.pushUndoStop())},[t],m),N(()=>{x.current?.getModel()?.original.setValue(e||"")},[e],m),N(()=>{let{original:e,modified:t}=x.current.getModel();k.current.editor.setModelLanguage(e,n||r||"text"),k.current.editor.setModelLanguage(t,o||r||"text")},[r,n,o],m),N(()=>{k.current?.editor.setTheme(l)},[l],m),N(()=>{x.current?.updateOptions(d)},[d],m);let E=(0,T.useCallback)(()=>{if(!k.current)return;C.current(k.current);let u=K(k.current,e||"",n||r||"text",a||""),c=K(k.current,t||"",o||r||"text",i||"");x.current?.setModel({original:u,modified:c})},[r,t,o,e,n,a,i]),D=(0,T.useCallback)(()=>{!O.current&&j.current&&(x.current=k.current.editor.createDiffEditor(j.current,{automaticLayout:!0,...d}),E(),k.current?.editor.setTheme(l),w(!0),O.current=!0)},[d,l,E]);return(0,T.useEffect)(()=>{m&&R.current(x.current,k.current)},[m]),(0,T.useEffect)(()=>{M||m||D()},[M,m,D]),T.createElement(Z,{width:p,height:f,isEditorReady:m,loading:s,_ref:j,className:h,wrapperProps:g})});var W=function(e){let t=(0,T.useRef)();return(0,T.useEffect)(()=>{t.current=e},[e]),t.current},U=new Map,B=(0,T.memo)(function({defaultValue:e,defaultLanguage:t,defaultPath:r,value:n,language:o,path:a,theme:i="light",line:u,loading:c="Loading...",options:l={},overrideServices:s={},saveViewState:d=!0,keepCurrentModel:f=!1,width:p="100%",height:h="100%",className:g,wrapperProps:y={},beforeMount:v=V,onMount:m=V,onChange:w,onValidate:M=V}){let[b,x]=(0,T.useState)(!1),[k,j]=(0,T.useState)(!0),R=(0,T.useRef)(null),C=(0,T.useRef)(null),O=(0,T.useRef)(null),E=(0,T.useRef)(m),D=(0,T.useRef)(v),P=(0,T.useRef)(),S=(0,T.useRef)(n),_=W(a),L=(0,T.useRef)(!1),A=(0,T.useRef)(!1);F(()=>{let e=I.init();return e.then(e=>(R.current=e)&&j(!1)).catch(e=>e?.type!=="cancelation"&&console.error("Monaco initialization: error:",e)),()=>C.current?void(P.current?.dispose(),f?d&&U.set(a,C.current.saveViewState()):C.current.getModel()?.dispose(),C.current.dispose()):e.cancel()}),N(()=>{let i=K(R.current,e||n||"",t||o||"",a||r||"");i!==C.current?.getModel()&&(d&&U.set(_,C.current?.saveViewState()),C.current?.setModel(i),d&&C.current?.restoreViewState(U.get(a)))},[a],b),N(()=>{C.current?.updateOptions(l)},[l],b),N(()=>{C.current&&void 0!==n&&(C.current.getOption(R.current.editor.EditorOption.readOnly)?C.current.setValue(n):n===C.current.getValue()||(A.current=!0,C.current.executeEdits("",[{range:C.current.getModel().getFullModelRange(),text:n,forceMoveMarkers:!0}]),C.current.pushUndoStop(),A.current=!1))},[n],b),N(()=>{let e=C.current?.getModel();e&&o&&R.current?.editor.setModelLanguage(e,o)},[o],b),N(()=>{void 0!==u&&C.current?.revealLine(u)},[u],b),N(()=>{R.current?.editor.setTheme(i)},[i],b);let z=(0,T.useCallback)(()=>{if(!(!O.current||!R.current)&&!L.current){D.current(R.current);let c=a||r,f=K(R.current,n||e||"",t||o||"",c||"");C.current=R.current?.editor.create(O.current,{model:f,automaticLayout:!0,...l},s),d&&C.current.restoreViewState(U.get(c)),R.current.editor.setTheme(i),void 0!==u&&C.current.revealLine(u),x(!0),L.current=!0}},[e,t,r,n,o,a,l,s,d,i,u]);return(0,T.useEffect)(()=>{b&&E.current(C.current,R.current)},[b]),(0,T.useEffect)(()=>{k||b||z()},[k,b,z]),S.current=n,(0,T.useEffect)(()=>{b&&w&&(P.current?.dispose(),P.current=C.current?.onDidChangeModelContent(e=>{A.current||w(C.current.getValue(),e)}))},[b,w]),(0,T.useEffect)(()=>{if(b){let e=R.current.editor.onDidChangeMarkers(e=>{let t=C.current.getModel()?.uri;if(t&&e.find(e=>e.path===t.path)){let e=R.current.editor.getModelMarkers({resource:t});M?.(e)}});return()=>{e?.dispose()}}return()=>{}},[b,M]),T.createElement(Z,{width:p,height:h,isEditorReady:b,loading:c,_ref:O,className:g,wrapperProps:y})})},1465:function(e,t,r){"use strict";r.d(t,{NY:function(){return j},Ee:function(){return k},fC:function(){return x}});var n=r(2265),o=r(6989),a=r(6459),i=r(5655),u=r(9381),c=r(6272);function l(){return()=>{}}var s=r(7437),d="Avatar",[f,p]=(0,o.b)(d),[h,g]=f(d),y=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...o}=e,[a,i]=n.useState("idle");return(0,s.jsx)(h,{scope:r,imageLoadingStatus:a,onImageLoadingStatusChange:i,children:(0,s.jsx)(u.WV.span,{...o,ref:t})})});y.displayName=d;var v="AvatarImage",m=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:o,onLoadingStatusChange:d=()=>{},...f}=e,p=g(v,r),h=function(e,{referrerPolicy:t,crossOrigin:r}){let o=(0,c.useSyncExternalStore)(l,()=>!0,()=>!1),a=n.useRef(null),u=o?(a.current||(a.current=new window.Image),a.current):null,[s,d]=n.useState(()=>b(u,e));return(0,i.b)(()=>{d(b(u,e))},[u,e]),(0,i.b)(()=>{let e=e=>()=>{d(e)};if(!u)return;let n=e("loaded"),o=e("error");return u.addEventListener("load",n),u.addEventListener("error",o),t&&(u.referrerPolicy=t),"string"==typeof r&&(u.crossOrigin=r),()=>{u.removeEventListener("load",n),u.removeEventListener("error",o)}},[u,r,t]),s}(o,f),y=(0,a.W)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,i.b)(()=>{"idle"!==h&&y(h)},[h,y]),"loaded"===h?(0,s.jsx)(u.WV.img,{...f,ref:t,src:o}):null});m.displayName=v;var w="AvatarFallback",M=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:o,...a}=e,i=g(w,r),[c,l]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>l(!0),o);return()=>window.clearTimeout(e)}},[o]),c&&"loaded"!==i.imageLoadingStatus?(0,s.jsx)(u.WV.span,{...a,ref:t}):null});function b(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}M.displayName=w;var x=y,k=m,j=M},8883:function(e,t,r){"use strict";r.d(t,{oC:function(){return tf},VY:function(){return tc},ZA:function(){return tl},ck:function(){return td},wU:function(){return tg},__:function(){return ts},Uv:function(){return tu},Ee:function(){return tp},Rk:function(){return th},fC:function(){return ta},Z0:function(){return ty},Tr:function(){return tv},tu:function(){return tw},fF:function(){return tm},xz:function(){return ti}});var n=r(2265),o=r(5744),a=r(2210),i=r(6989),u=r(3763),c=r(9381),l=r(7733),s=r(5400),d=r(9249),f=r(1244),p=r(2759),h=r(966),g=r(3995),y=r(2730),v=r(5606),m=r(6459),w=r(7437),M="rovingFocusGroup.onEntryFocus",b={bubbles:!1,cancelable:!0},x="RovingFocusGroup",[k,j,R]=(0,l.B)(x),[C,O]=(0,i.b)(x,[R]),[E,D]=C(x),P=n.forwardRef((e,t)=>(0,w.jsx)(k.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,w.jsx)(k.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,w.jsx)(S,{...e,ref:t})})}));P.displayName=x;var S=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:l=!1,dir:d,currentTabStopId:f,defaultCurrentTabStopId:p,onCurrentTabStopIdChange:h,onEntryFocus:g,preventScrollOnEntryFocus:y=!1,...v}=e,k=n.useRef(null),R=(0,a.e)(t,k),C=(0,s.gm)(d),[O,D]=(0,u.T)({prop:f,defaultProp:p??null,onChange:h,caller:x}),[P,S]=n.useState(!1),I=(0,m.W)(g),T=j(r),_=n.useRef(!1),[A,Z]=n.useState(0);return n.useEffect(()=>{let e=k.current;if(e)return e.addEventListener(M,I),()=>e.removeEventListener(M,I)},[I]),(0,w.jsx)(E,{scope:r,orientation:i,dir:C,loop:l,currentTabStopId:O,onItemFocus:n.useCallback(e=>D(e),[D]),onItemShiftTab:n.useCallback(()=>S(!0),[]),onFocusableItemAdd:n.useCallback(()=>Z(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>Z(e=>e-1),[]),children:(0,w.jsx)(c.WV.div,{tabIndex:P||0===A?-1:0,"data-orientation":i,...v,ref:R,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{_.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!_.current;if(e.target===e.currentTarget&&t&&!P){let t=new CustomEvent(M,b);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=T().filter(e=>e.focusable);L([e.find(e=>e.active),e.find(e=>e.id===O),...e].filter(Boolean).map(e=>e.ref.current),y)}}_.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>S(!1))})})}),I="RovingFocusGroupItem",T=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:i=!1,tabStopId:u,children:l,...s}=e,d=(0,h.M)(),f=u||d,p=D(I,r),g=p.currentTabStopId===f,y=j(r),{onFocusableItemAdd:v,onFocusableItemRemove:m,currentTabStopId:M}=p;return n.useEffect(()=>{if(a)return v(),()=>m()},[a,v,m]),(0,w.jsx)(k.ItemSlot,{scope:r,id:f,focusable:a,active:i,children:(0,w.jsx)(c.WV.span,{tabIndex:g?0:-1,"data-orientation":p.orientation,...s,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a?p.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return _[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)o.reverse();else if("prev"===t||"next"===t){var r,n;"prev"===t&&o.reverse();let a=o.indexOf(e.currentTarget);o=p.loop?(r=o,n=a+1,r.map((e,t)=>r[(n+t)%r.length])):o.slice(a+1)}setTimeout(()=>L(o))}}),children:"function"==typeof l?l({isCurrentTabStop:g,hasTabStop:null!=M}):l})})});T.displayName=I;var _={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function L(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var A=r(7256),Z=r(5859),F=r(3386),N=["Enter"," "],V=["ArrowUp","PageDown","End"],K=["ArrowDown","PageUp","Home",...V],z={ltr:[...N,"ArrowRight"],rtl:[...N,"ArrowLeft"]},W={ltr:["ArrowLeft"],rtl:["ArrowRight"]},U="Menu",[B,q,G]=(0,l.B)(U),[H,X]=(0,i.b)(U,[G,g.D7,O]),Y=(0,g.D7)(),$=O(),[J,Q]=H(U),[ee,et]=H(U),er=e=>{let{__scopeMenu:t,open:r=!1,children:o,dir:a,onOpenChange:i,modal:u=!0}=e,c=Y(t),[l,d]=n.useState(null),f=n.useRef(!1),p=(0,m.W)(i),h=(0,s.gm)(a);return n.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,w.jsx)(g.fC,{...c,children:(0,w.jsx)(J,{scope:t,open:r,onOpenChange:p,content:l,onContentChange:d,children:(0,w.jsx)(ee,{scope:t,onClose:n.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:h,modal:u,children:o})})})};er.displayName=U;var en=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=Y(r);return(0,w.jsx)(g.ee,{...o,...n,ref:t})});en.displayName="MenuAnchor";var eo="MenuPortal",[ea,ei]=H(eo,{forceMount:void 0}),eu=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,a=Q(eo,t);return(0,w.jsx)(ea,{scope:t,forceMount:r,children:(0,w.jsx)(v.z,{present:r||a.open,children:(0,w.jsx)(y.h,{asChild:!0,container:o,children:n})})})};eu.displayName=eo;var ec="MenuContent",[el,es]=H(ec),ed=n.forwardRef((e,t)=>{let r=ei(ec,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,a=Q(ec,e.__scopeMenu),i=et(ec,e.__scopeMenu);return(0,w.jsx)(B.Provider,{scope:e.__scopeMenu,children:(0,w.jsx)(v.z,{present:n||a.open,children:(0,w.jsx)(B.Slot,{scope:e.__scopeMenu,children:i.modal?(0,w.jsx)(ef,{...o,ref:t}):(0,w.jsx)(ep,{...o,ref:t})})})})}),ef=n.forwardRef((e,t)=>{let r=Q(ec,e.__scopeMenu),i=n.useRef(null),u=(0,a.e)(t,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,Z.Ry)(e)},[]),(0,w.jsx)(eg,{...e,ref:u,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),ep=n.forwardRef((e,t)=>{let r=Q(ec,e.__scopeMenu);return(0,w.jsx)(eg,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),eh=(0,A.Z8)("MenuContent.ScrollLock"),eg=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:i=!1,trapFocus:u,onOpenAutoFocus:c,onCloseAutoFocus:l,disableOutsidePointerEvents:s,onEntryFocus:h,onEscapeKeyDown:y,onPointerDownOutside:v,onFocusOutside:m,onInteractOutside:M,onDismiss:b,disableOutsideScroll:x,...k}=e,j=Q(ec,r),R=et(ec,r),C=Y(r),O=$(r),E=q(r),[D,S]=n.useState(null),I=n.useRef(null),T=(0,a.e)(t,I,j.onContentChange),_=n.useRef(0),L=n.useRef(""),A=n.useRef(0),Z=n.useRef(null),N=n.useRef("right"),z=n.useRef(0),W=x?F.Z:n.Fragment,U=e=>{let t=L.current+e,r=E().filter(e=>!e.disabled),n=document.activeElement,o=r.find(e=>e.ref.current===n)?.textValue,a=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(a=a.filter(e=>e!==r));let i=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return i!==r?i:void 0}(r.map(e=>e.textValue),t,o),i=r.find(e=>e.textValue===a)?.ref.current;!function e(t){L.current=t,window.clearTimeout(_.current),""!==t&&(_.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};n.useEffect(()=>()=>window.clearTimeout(_.current),[]),(0,f.EW)();let B=n.useCallback(e=>{var t;return N.current===Z.current?.side&&!!(t=Z.current?.area)&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let i=t[e],u=t[a],c=i.x,l=i.y,s=u.x,d=u.y;l>n!=d>n&&r<(s-c)*(n-l)/(d-l)+c&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)},[]);return(0,w.jsx)(el,{scope:r,searchRef:L,onItemEnter:n.useCallback(e=>{B(e)&&e.preventDefault()},[B]),onItemLeave:n.useCallback(e=>{B(e)||(I.current?.focus(),S(null))},[B]),onTriggerLeave:n.useCallback(e=>{B(e)&&e.preventDefault()},[B]),pointerGraceTimerRef:A,onPointerGraceIntentChange:n.useCallback(e=>{Z.current=e},[]),children:(0,w.jsx)(W,{...x?{as:eh,allowPinchZoom:!0}:void 0,children:(0,w.jsx)(p.M,{asChild:!0,trapped:u,onMountAutoFocus:(0,o.M)(c,e=>{e.preventDefault(),I.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:l,children:(0,w.jsx)(d.XB,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:y,onPointerDownOutside:v,onFocusOutside:m,onInteractOutside:M,onDismiss:b,children:(0,w.jsx)(P,{asChild:!0,...O,dir:R.dir,orientation:"vertical",loop:i,currentTabStopId:D,onCurrentTabStopIdChange:S,onEntryFocus:(0,o.M)(h,e=>{R.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,w.jsx)(g.VY,{role:"menu","aria-orientation":"vertical","data-state":eW(j.open),"data-radix-menu-content":"",dir:R.dir,...C,...k,ref:T,style:{outline:"none",...k.style},onKeyDown:(0,o.M)(k.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&U(e.key));let o=I.current;if(e.target!==o||!K.includes(e.key))return;e.preventDefault();let a=E().filter(e=>!e.disabled).map(e=>e.ref.current);V.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,o.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(_.current),L.current="")}),onPointerMove:(0,o.M)(e.onPointerMove,eq(e=>{let t=e.target,r=z.current!==e.clientX;if(e.currentTarget.contains(t)&&r){let t=e.clientX>z.current?"right":"left";N.current=t,z.current=e.clientX}}))})})})})})})});ed.displayName=ec;var ey=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,w.jsx)(c.WV.div,{role:"group",...n,ref:t})});ey.displayName="MenuGroup";var ev=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,w.jsx)(c.WV.div,{...n,ref:t})});ev.displayName="MenuLabel";var em="MenuItem",ew="menu.itemSelect",eM=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:i,...u}=e,l=n.useRef(null),s=et(em,e.__scopeMenu),d=es(em,e.__scopeMenu),f=(0,a.e)(t,l),p=n.useRef(!1);return(0,w.jsx)(eb,{...u,ref:f,disabled:r,onClick:(0,o.M)(e.onClick,()=>{let e=l.current;if(!r&&e){let t=new CustomEvent(ew,{bubbles:!0,cancelable:!0});e.addEventListener(ew,e=>i?.(e),{once:!0}),(0,c.jH)(e,t),t.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),p.current=!0},onPointerUp:(0,o.M)(e.onPointerUp,e=>{p.current||e.currentTarget?.click()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;!r&&(!t||" "!==e.key)&&N.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eM.displayName=em;var eb=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:i=!1,textValue:u,...l}=e,s=es(em,r),d=$(r),f=n.useRef(null),p=(0,a.e)(t,f),[h,g]=n.useState(!1),[y,v]=n.useState("");return n.useEffect(()=>{let e=f.current;e&&v((e.textContent??"").trim())},[l.children]),(0,w.jsx)(B.ItemSlot,{scope:r,disabled:i,textValue:u??y,children:(0,w.jsx)(T,{asChild:!0,...d,focusable:!i,children:(0,w.jsx)(c.WV.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":i||void 0,"data-disabled":i?"":void 0,...l,ref:p,onPointerMove:(0,o.M)(e.onPointerMove,eq(e=>{i?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eq(e=>s.onItemLeave(e))),onFocus:(0,o.M)(e.onFocus,()=>g(!0)),onBlur:(0,o.M)(e.onBlur,()=>g(!1))})})})}),ex=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...a}=e;return(0,w.jsx)(eP,{scope:e.__scopeMenu,checked:r,children:(0,w.jsx)(eM,{role:"menuitemcheckbox","aria-checked":eU(r)?"mixed":r,...a,ref:t,"data-state":eB(r),onSelect:(0,o.M)(a.onSelect,()=>n?.(!!eU(r)||!r),{checkForDefaultPrevented:!1})})})});ex.displayName="MenuCheckboxItem";var ek="MenuRadioGroup",[ej,eR]=H(ek,{value:void 0,onValueChange:()=>{}}),eC=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,a=(0,m.W)(n);return(0,w.jsx)(ej,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,w.jsx)(ey,{...o,ref:t})})});eC.displayName=ek;var eO="MenuRadioItem",eE=n.forwardRef((e,t)=>{let{value:r,...n}=e,a=eR(eO,e.__scopeMenu),i=r===a.value;return(0,w.jsx)(eP,{scope:e.__scopeMenu,checked:i,children:(0,w.jsx)(eM,{role:"menuitemradio","aria-checked":i,...n,ref:t,"data-state":eB(i),onSelect:(0,o.M)(n.onSelect,()=>a.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});eE.displayName=eO;var eD="MenuItemIndicator",[eP,eS]=H(eD,{checked:!1}),eI=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,a=eS(eD,r);return(0,w.jsx)(v.z,{present:n||eU(a.checked)||!0===a.checked,children:(0,w.jsx)(c.WV.span,{...o,ref:t,"data-state":eB(a.checked)})})});eI.displayName=eD;var eT=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,w.jsx)(c.WV.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});eT.displayName="MenuSeparator";var e_=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=Y(r);return(0,w.jsx)(g.Eh,{...o,...n,ref:t})});e_.displayName="MenuArrow";var eL="MenuSub",[eA,eZ]=H(eL),eF=e=>{let{__scopeMenu:t,children:r,open:o=!1,onOpenChange:a}=e,i=Q(eL,t),u=Y(t),[c,l]=n.useState(null),[s,d]=n.useState(null),f=(0,m.W)(a);return n.useEffect(()=>(!1===i.open&&f(!1),()=>f(!1)),[i.open,f]),(0,w.jsx)(g.fC,{...u,children:(0,w.jsx)(J,{scope:t,open:o,onOpenChange:f,content:s,onContentChange:d,children:(0,w.jsx)(eA,{scope:t,contentId:(0,h.M)(),triggerId:(0,h.M)(),trigger:c,onTriggerChange:l,children:r})})})};eF.displayName=eL;var eN="MenuSubTrigger",eV=n.forwardRef((e,t)=>{let r=Q(eN,e.__scopeMenu),i=et(eN,e.__scopeMenu),u=eZ(eN,e.__scopeMenu),c=es(eN,e.__scopeMenu),l=n.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:d}=c,f={__scopeMenu:e.__scopeMenu},p=n.useCallback(()=>{l.current&&window.clearTimeout(l.current),l.current=null},[]);return n.useEffect(()=>p,[p]),n.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),d(null)}},[s,d]),(0,w.jsx)(en,{asChild:!0,...f,children:(0,w.jsx)(eb,{id:u.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":u.contentId,"data-state":eW(r.open),...e,ref:(0,a.F)(t,u.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.M)(e.onPointerMove,eq(t=>{c.onItemEnter(t),t.defaultPrevented||e.disabled||r.open||l.current||(c.onPointerGraceIntentChange(null),l.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eq(e=>{p();let t=r.content?.getBoundingClientRect();if(t){let n=r.content?.dataset.side,o="right"===n,a=t[o?"left":"right"],i=t[o?"right":"left"];c.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:a,y:t.top},{x:i,y:t.top},{x:i,y:t.bottom},{x:a,y:t.bottom}],side:n}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>c.onPointerGraceIntentChange(null),300)}else{if(c.onTriggerLeave(e),e.defaultPrevented)return;c.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.M)(e.onKeyDown,t=>{let n=""!==c.searchRef.current;!e.disabled&&(!n||" "!==t.key)&&z[i.dir].includes(t.key)&&(r.onOpenChange(!0),r.content?.focus(),t.preventDefault())})})})});eV.displayName=eN;var eK="MenuSubContent",ez=n.forwardRef((e,t)=>{let r=ei(ec,e.__scopeMenu),{forceMount:i=r.forceMount,...u}=e,c=Q(ec,e.__scopeMenu),l=et(ec,e.__scopeMenu),s=eZ(eK,e.__scopeMenu),d=n.useRef(null),f=(0,a.e)(t,d);return(0,w.jsx)(B.Provider,{scope:e.__scopeMenu,children:(0,w.jsx)(v.z,{present:i||c.open,children:(0,w.jsx)(B.Slot,{scope:e.__scopeMenu,children:(0,w.jsx)(eg,{id:s.contentId,"aria-labelledby":s.triggerId,...u,ref:f,align:"start",side:"rtl"===l.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{l.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>{e.target!==s.trigger&&c.onOpenChange(!1)}),onEscapeKeyDown:(0,o.M)(e.onEscapeKeyDown,e=>{l.onClose(),e.preventDefault()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=W[l.dir].includes(e.key);t&&r&&(c.onOpenChange(!1),s.trigger?.focus(),e.preventDefault())})})})})})});function eW(e){return e?"open":"closed"}function eU(e){return"indeterminate"===e}function eB(e){return eU(e)?"indeterminate":e?"checked":"unchecked"}function eq(e){return t=>"mouse"===t.pointerType?e(t):void 0}ez.displayName=eK;var eG="DropdownMenu",[eH,eX]=(0,i.b)(eG,[X]),eY=X(),[e$,eJ]=eH(eG),eQ=e=>{let{__scopeDropdownMenu:t,children:r,dir:o,open:a,defaultOpen:i,onOpenChange:c,modal:l=!0}=e,s=eY(t),d=n.useRef(null),[f,p]=(0,u.T)({prop:a,defaultProp:i??!1,onChange:c,caller:eG});return(0,w.jsx)(e$,{scope:t,triggerId:(0,h.M)(),triggerRef:d,contentId:(0,h.M)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:l,children:(0,w.jsx)(er,{...s,open:f,onOpenChange:p,dir:o,modal:l,children:r})})};eQ.displayName=eG;var e0="DropdownMenuTrigger",e1=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...i}=e,u=eJ(e0,r),l=eY(r);return(0,w.jsx)(en,{asChild:!0,...l,children:(0,w.jsx)(c.WV.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...i,ref:(0,a.F)(t,u.triggerRef),onPointerDown:(0,o.M)(e.onPointerDown,e=>{n||0!==e.button||!1!==e.ctrlKey||(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});e1.displayName=e0;var e2=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eY(t);return(0,w.jsx)(eu,{...n,...r})};e2.displayName="DropdownMenuPortal";var e5="DropdownMenuContent",e3=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,i=eJ(e5,r),u=eY(r),c=n.useRef(!1);return(0,w.jsx)(ed,{id:i.contentId,"aria-labelledby":i.triggerId,...u,...a,ref:t,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{c.current||i.triggerRef.current?.focus(),c.current=!1,e.preventDefault()}),onInteractOutside:(0,o.M)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!i.modal||n)&&(c.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e3.displayName=e5;var e8=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,w.jsx)(ey,{...o,...n,ref:t})});e8.displayName="DropdownMenuGroup";var e9=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,w.jsx)(ev,{...o,...n,ref:t})});e9.displayName="DropdownMenuLabel";var e7=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,w.jsx)(eM,{...o,...n,ref:t})});e7.displayName="DropdownMenuItem";var e4=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,w.jsx)(ex,{...o,...n,ref:t})});e4.displayName="DropdownMenuCheckboxItem";var e6=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,w.jsx)(eC,{...o,...n,ref:t})});e6.displayName="DropdownMenuRadioGroup";var te=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,w.jsx)(eE,{...o,...n,ref:t})});te.displayName="DropdownMenuRadioItem";var tt=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,w.jsx)(eI,{...o,...n,ref:t})});tt.displayName="DropdownMenuItemIndicator";var tr=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,w.jsx)(eT,{...o,...n,ref:t})});tr.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,w.jsx)(e_,{...o,...n,ref:t})}).displayName="DropdownMenuArrow";var tn=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,w.jsx)(eV,{...o,...n,ref:t})});tn.displayName="DropdownMenuSubTrigger";var to=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,w.jsx)(ez,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});to.displayName="DropdownMenuSubContent";var ta=eQ,ti=e1,tu=e2,tc=e3,tl=e8,ts=e9,td=e7,tf=e4,tp=e6,th=te,tg=tt,ty=tr,tv=e=>{let{__scopeDropdownMenu:t,children:r,open:n,onOpenChange:o,defaultOpen:a}=e,i=eY(t),[c,l]=(0,u.T)({prop:n,defaultProp:a??!1,onChange:o,caller:"DropdownMenuSub"});return(0,w.jsx)(eF,{...i,open:c,onOpenChange:l,children:r})},tm=tn,tw=to}}]);