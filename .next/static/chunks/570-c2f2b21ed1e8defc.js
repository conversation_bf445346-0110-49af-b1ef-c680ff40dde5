(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[570],{2898:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var n=r(2265),o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),i=(e,t)=>{let r=(0,n.forwardRef)(({color:r="currentColor",size:i=24,strokeWidth:s=2,absoluteStrokeWidth:l,className:u="",children:c,...d},f)=>(0,n.createElement)("svg",{ref:f,...o,width:i,height:i,stroke:r,strokeWidth:l?24*Number(s)/Number(i):s,className:["lucide",`lucide-${a(e)}`,u].join(" "),...d},[...t.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(c)?c:[c]]));return r.displayName=`${e}`,r}},9974:function(e,t,r){"use strict";var n=r(6314);Object.defineProperty(t,"__esModule",{value:!0}),t.BroadcastChannel=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"nextauth.message";return{receive:function(t){var r=function(r){if(r.key===e){var n,o=JSON.parse(null!==(n=r.newValue)&&void 0!==n?n:"{}");(null==o?void 0:o.event)==="session"&&null!=o&&o.data&&t(o)}};return window.addEventListener("storage",r),function(){return window.removeEventListener("storage",r)}},post:function(t){if("undefined"!=typeof window)try{localStorage.setItem(e,JSON.stringify(l(l({},t),{},{timestamp:d()})))}catch(e){}}}},t.apiBaseUrl=c,t.fetchData=function(e,t,r){return u.apply(this,arguments)},t.now=d;var o=n(r(9171)),a=n(r(8104)),i=n(r(4662));function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){(0,a.default)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(){return(u=(0,i.default)(o.default.mark(function e(t,r,n){var a,i,s,u,d,f,p,b,v,h=arguments;return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return i=(a=h.length>3&&void 0!==h[3]?h[3]:{}).ctx,u=void 0===(s=a.req)?null==i?void 0:i.req:s,d="".concat(c(r),"/").concat(t),e.prev=2,p={headers:l({"Content-Type":"application/json"},null!=u&&null!==(f=u.headers)&&void 0!==f&&f.cookie?{cookie:u.headers.cookie}:{})},null!=u&&u.body&&(p.body=JSON.stringify(u.body),p.method="POST"),e.next=7,fetch(d,p);case 7:return b=e.sent,e.next=10,b.json();case 10:if(v=e.sent,b.ok){e.next=13;break}throw v;case 13:return e.abrupt("return",Object.keys(v).length>0?v:null);case 16:return e.prev=16,e.t0=e.catch(2),n.error("CLIENT_FETCH_ERROR",{error:e.t0,url:d}),e.abrupt("return",null);case 20:case"end":return e.stop()}},e,null,[[2,16]])}))).apply(this,arguments)}function c(e){return"undefined"==typeof window?"".concat(e.baseUrlServer).concat(e.basePathServer):e.basePath}function d(){return Math.floor(Date.now()/1e3)}},2118:function(e,t,r){"use strict";var n=r(6314);Object.defineProperty(t,"__esModule",{value:!0}),t.UnsupportedStrategy=t.UnknownError=t.OAuthCallbackError=t.MissingSecret=t.MissingAuthorize=t.MissingAdapterMethods=t.MissingAdapter=t.MissingAPIRoute=t.InvalidCallbackUrl=t.AccountNotLinkedError=void 0,t.adapterErrorHandler=function(e,t){if(e)return Object.keys(e).reduce(function(r,n){return r[n]=(0,a.default)(o.default.mark(function r(){var a,i,s,l,u,c=arguments;return o.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:for(r.prev=0,i=Array(a=c.length),s=0;s<a;s++)i[s]=c[s];return t.debug("adapter_".concat(n),{args:i}),l=e[n],r.next=6,l.apply(void 0,i);case 6:return r.abrupt("return",r.sent);case 9:throw r.prev=9,r.t0=r.catch(0),t.error("adapter_error_".concat(n),r.t0),(u=new v(r.t0)).name="".concat(g(n),"Error"),u;case 15:case"end":return r.stop()}},r,null,[[0,9]])})),r},{})},t.capitalize=g,t.eventsErrorHandler=function(e,t){return Object.keys(e).reduce(function(r,n){return r[n]=(0,a.default)(o.default.mark(function r(){var a,i=arguments;return o.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,a=e[n],r.next=4,a.apply(void 0,i);case 4:return r.abrupt("return",r.sent);case 7:r.prev=7,r.t0=r.catch(0),t.error("".concat(h(n),"_EVENT_ERROR"),r.t0);case 10:case"end":return r.stop()}},r,null,[[0,7]])})),r},{})},t.upperSnake=h;var o=n(r(9171)),a=n(r(4662)),i=n(r(8104)),s=n(r(6358)),l=n(r(1127)),u=n(r(746)),c=n(r(3654)),d=n(r(5684)),f=n(r(5058));function p(e,t,r){return t=(0,c.default)(t),(0,u.default)(e,b()?Reflect.construct(t,r||[],(0,c.default)(e).constructor):t.apply(e,r))}function b(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(b=function(){return!!e})()}var v=t.UnknownError=function(e){function t(e){var r,n;return(0,s.default)(this,t),(n=p(this,t,[null!==(r=null==e?void 0:e.message)&&void 0!==r?r:e])).name="UnknownError",n.code=e.code,e instanceof Error&&(n.stack=e.stack),n}return(0,d.default)(t,e),(0,l.default)(t,[{key:"toJSON",value:function(){return{name:this.name,message:this.message,stack:this.stack}}}])}((0,f.default)(Error));function h(e){return e.replace(/([A-Z])/g,"_$1").toUpperCase()}function g(e){return"".concat(e[0].toUpperCase()).concat(e.slice(1))}t.OAuthCallbackError=function(e){function t(){var e;(0,s.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=p(this,t,[].concat(n)),(0,i.default)(e,"name","OAuthCallbackError"),e}return(0,d.default)(t,e),(0,l.default)(t)}(v),t.AccountNotLinkedError=function(e){function t(){var e;(0,s.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=p(this,t,[].concat(n)),(0,i.default)(e,"name","AccountNotLinkedError"),e}return(0,d.default)(t,e),(0,l.default)(t)}(v),t.MissingAPIRoute=function(e){function t(){var e;(0,s.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=p(this,t,[].concat(n)),(0,i.default)(e,"name","MissingAPIRouteError"),(0,i.default)(e,"code","MISSING_NEXTAUTH_API_ROUTE_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(v),t.MissingSecret=function(e){function t(){var e;(0,s.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=p(this,t,[].concat(n)),(0,i.default)(e,"name","MissingSecretError"),(0,i.default)(e,"code","NO_SECRET"),e}return(0,d.default)(t,e),(0,l.default)(t)}(v),t.MissingAuthorize=function(e){function t(){var e;(0,s.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=p(this,t,[].concat(n)),(0,i.default)(e,"name","MissingAuthorizeError"),(0,i.default)(e,"code","CALLBACK_CREDENTIALS_HANDLER_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(v),t.MissingAdapter=function(e){function t(){var e;(0,s.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=p(this,t,[].concat(n)),(0,i.default)(e,"name","MissingAdapterError"),(0,i.default)(e,"code","EMAIL_REQUIRES_ADAPTER_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(v),t.MissingAdapterMethods=function(e){function t(){var e;(0,s.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=p(this,t,[].concat(n)),(0,i.default)(e,"name","MissingAdapterMethodsError"),(0,i.default)(e,"code","MISSING_ADAPTER_METHODS_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(v),t.UnsupportedStrategy=function(e){function t(){var e;(0,s.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=p(this,t,[].concat(n)),(0,i.default)(e,"name","UnsupportedStrategyError"),(0,i.default)(e,"code","CALLBACK_CREDENTIALS_JWT_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(v),t.InvalidCallbackUrl=function(e){function t(){var e;(0,s.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=p(this,t,[].concat(n)),(0,i.default)(e,"name","InvalidCallbackUrl"),(0,i.default)(e,"code","INVALID_CALLBACK_URL_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(v)},2749:function(e,t,r){"use strict";var n,o,a,i,s,l=r(2601),u=r(6314),c=r(1565);Object.defineProperty(t,"__esModule",{value:!0});var d={SessionContext:!0,useSession:!0,getSession:!0,getCsrfToken:!0,getProviders:!0,signIn:!0,signOut:!0,SessionProvider:!0};t.SessionContext=void 0,t.SessionProvider=function(e){if(!R)throw Error("React Context is unavailable in Server Components");var t,r,n,o,a,i,s=e.children,l=e.basePath,u=e.refetchInterval,c=e.refetchWhenOffline;l&&(E.basePath=l);var d=void 0!==e.session;E._lastSync=d?(0,m.now)():0;var p=h.useState(function(){return d&&(E._session=e.session),e.session}),g=(0,v.default)(p,2),y=g[0],w=g[1],_=h.useState(!d),k=(0,v.default)(_,2),O=k[0],j=k[1];h.useEffect(function(){return E._getSession=(0,b.default)(f.default.mark(function e(){var t,r,n=arguments;return f.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=(n.length>0&&void 0!==n[0]?n[0]:{}).event,e.prev=1,!((r="storage"===t)||void 0===E._session)){e.next=10;break}return E._lastSync=(0,m.now)(),e.next=7,M({broadcast:!r});case 7:return E._session=e.sent,w(E._session),e.abrupt("return");case 10:if(!(!t||null===E._session||(0,m.now)()<E._lastSync)){e.next=12;break}return e.abrupt("return");case 12:return E._lastSync=(0,m.now)(),e.next=15,M();case 15:E._session=e.sent,w(E._session),e.next=22;break;case 19:e.prev=19,e.t0=e.catch(1),P.error("CLIENT_SESSION_ERROR",e.t0);case 22:return e.prev=22,j(!1),e.finish(22);case 25:case"end":return e.stop()}},e,null,[[1,19,22,25]])})),E._getSession(),function(){E._lastSync=0,E._session=void 0,E._getSession=function(){}}},[]),h.useEffect(function(){var e=S.receive(function(){return E._getSession({event:"storage"})});return function(){return e()}},[]),h.useEffect(function(){var t=e.refetchOnWindowFocus,r=void 0===t||t,n=function(){r&&"visible"===document.visibilityState&&E._getSession({event:"visibilitychange"})};return document.addEventListener("visibilitychange",n,!1),function(){return document.removeEventListener("visibilitychange",n,!1)}},[e.refetchOnWindowFocus]);var T=(t=h.useState("undefined"!=typeof navigator&&navigator.onLine),n=(r=(0,v.default)(t,2))[0],o=r[1],a=function(){return o(!0)},i=function(){return o(!1)},h.useEffect(function(){return window.addEventListener("online",a),window.addEventListener("offline",i),function(){window.removeEventListener("online",a),window.removeEventListener("offline",i)}},[]),n),C=!1!==c||T;h.useEffect(function(){if(u&&C){var e=setInterval(function(){E._session&&E._getSession({event:"poll"})},1e3*u);return function(){return clearInterval(e)}}},[u,C]);var L=h.useMemo(function(){return{data:y,status:O?"loading":y?"authenticated":"unauthenticated",update:function(e){return(0,b.default)(f.default.mark(function t(){var r;return f.default.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!(O||!y)){t.next=2;break}return t.abrupt("return");case 2:return j(!0),t.t0=m.fetchData,t.t1=E,t.t2=P,t.next=8,A();case 8:return t.t3=t.sent,t.t4=e,t.t5={csrfToken:t.t3,data:t.t4},t.t6={body:t.t5},t.t7={req:t.t6},t.next=15,(0,t.t0)("session",t.t1,t.t2,t.t7);case 15:return r=t.sent,j(!1),r&&(w(r),S.post({event:"session",data:{trigger:"getSession"}})),t.abrupt("return",r);case 19:case"end":return t.stop()}},t)}))()}}},[y,O]);return(0,x.jsx)(R.Provider,{value:L,children:s})},t.getCsrfToken=A,t.getProviders=L,t.getSession=M,t.signIn=function(e,t,r){return U.apply(this,arguments)},t.signOut=function(e){return z.apply(this,arguments)},t.useSession=function(e){if(!R)throw Error("React Context is unavailable in Server Components");var t=h.useContext(R),r=null!=e?e:{},n=r.required,o=r.onUnauthenticated,a=n&&"unauthenticated"===t.status;return(h.useEffect(function(){if(a){var e="/api/auth/signin?".concat(new URLSearchParams({error:"SessionRequired",callbackUrl:window.location.href}));o?o():window.location.href=e}},[a,o]),a)?{data:t.data,update:t.update,status:"loading"}:t};var f=u(r(9171)),p=u(r(8104)),b=u(r(4662)),v=u(r(2184)),h=k(r(2265)),g=k(r(2126)),y=u(r(2606)),m=r(9974),x=r(7437),w=r(2083);function _(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_=function(e){return e?r:t})(e)}function k(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=c(e)&&"function"!=typeof e)return{default:e};var r=_(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var i=o?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(n,a,i):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}function O(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function j(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?O(Object(r),!0).forEach(function(t){(0,p.default)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}Object.keys(w).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(d,e))&&(e in t&&t[e]===w[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return w[e]}}))});var E={baseUrl:(0,y.default)(null!==(n=l.env.NEXTAUTH_URL)&&void 0!==n?n:l.env.VERCEL_URL).origin,basePath:(0,y.default)(l.env.NEXTAUTH_URL).path,baseUrlServer:(0,y.default)(null!==(o=null!==(a=l.env.NEXTAUTH_URL_INTERNAL)&&void 0!==a?a:l.env.NEXTAUTH_URL)&&void 0!==o?o:l.env.VERCEL_URL).origin,basePathServer:(0,y.default)(null!==(i=l.env.NEXTAUTH_URL_INTERNAL)&&void 0!==i?i:l.env.NEXTAUTH_URL).path,_lastSync:0,_session:void 0,_getSession:function(){}},S=(0,m.BroadcastChannel)(),P=(0,g.proxyLogger)(g.default,E.basePath),R=t.SessionContext=null===(s=h.createContext)||void 0===s?void 0:s.call(h,void 0);function M(e){return T.apply(this,arguments)}function T(){return(T=(0,b.default)(f.default.mark(function e(t){var r,n;return f.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,m.fetchData)("session",E,P,t);case 2:return n=e.sent,(null===(r=null==t?void 0:t.broadcast)||void 0===r||r)&&S.post({event:"session",data:{trigger:"getSession"}}),e.abrupt("return",n);case 5:case"end":return e.stop()}},e)}))).apply(this,arguments)}function A(e){return C.apply(this,arguments)}function C(){return(C=(0,b.default)(f.default.mark(function e(t){var r;return f.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,m.fetchData)("csrf",E,P,t);case 2:return r=e.sent,e.abrupt("return",null==r?void 0:r.csrfToken);case 4:case"end":return e.stop()}},e)}))).apply(this,arguments)}function L(){return N.apply(this,arguments)}function N(){return(N=(0,b.default)(f.default.mark(function e(){return f.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,m.fetchData)("providers",E,P);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function U(){return(U=(0,b.default)(f.default.mark(function e(t,r,n){var o,a,i,s,l,u,c,d,p,b,v,h,g,y,x,w,_;return f.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return i=void 0===(a=(o=null!=r?r:{}).callbackUrl)?window.location.href:a,l=void 0===(s=o.redirect)||s,u=(0,m.apiBaseUrl)(E),e.next=4,L();case 4:if(c=e.sent){e.next=8;break}return window.location.href="".concat(u,"/error"),e.abrupt("return");case 8:if(!(!t||!(t in c))){e.next=11;break}return window.location.href="".concat(u,"/signin?").concat(new URLSearchParams({callbackUrl:i})),e.abrupt("return");case 11:return d="credentials"===c[t].type,p="email"===c[t].type,b=d||p,v="".concat(u,"/").concat(d?"callback":"signin","/").concat(t),h="".concat(v).concat(n?"?".concat(new URLSearchParams(n)):""),e.t0=fetch,e.t1=h,e.t2={"Content-Type":"application/x-www-form-urlencoded"},e.t3=URLSearchParams,e.t4=j,e.t5=j({},r),e.t6={},e.next=25,A();case 25:return e.t7=e.sent,e.t8=i,e.t9={csrfToken:e.t7,callbackUrl:e.t8,json:!0},e.t10=(0,e.t4)(e.t5,e.t6,e.t9),e.t11=new e.t3(e.t10),e.t12={method:"post",headers:e.t2,body:e.t11},e.next=33,(0,e.t0)(e.t1,e.t12);case 33:return g=e.sent,e.next=36,g.json();case 36:if(y=e.sent,!(l||!b)){e.next=42;break}return w=null!==(x=y.url)&&void 0!==x?x:i,window.location.href=w,w.includes("#")&&window.location.reload(),e.abrupt("return");case 42:if(_=new URL(y.url).searchParams.get("error"),!g.ok){e.next=46;break}return e.next=46,E._getSession({event:"storage"});case 46:return e.abrupt("return",{error:_,status:g.status,ok:g.ok,url:_?null:y.url});case 47:case"end":return e.stop()}},e)}))).apply(this,arguments)}function z(){return(z=(0,b.default)(f.default.mark(function e(t){var r,n,o,a,i,s,l,u,c;return f.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return o=void 0===(n=(null!=t?t:{}).callbackUrl)?window.location.href:n,a=(0,m.apiBaseUrl)(E),e.t0={"Content-Type":"application/x-www-form-urlencoded"},e.t1=URLSearchParams,e.next=6,A();case 6:return e.t2=e.sent,e.t3=o,e.t4={csrfToken:e.t2,callbackUrl:e.t3,json:!0},e.t5=new e.t1(e.t4),i={method:"post",headers:e.t0,body:e.t5},e.next=13,fetch("".concat(a,"/signout"),i);case 13:return s=e.sent,e.next=16,s.json();case 16:if(l=e.sent,S.post({event:"session",data:{trigger:"signout"}}),!(null===(r=null==t?void 0:t.redirect)||void 0===r||r)){e.next=23;break}return c=null!==(u=l.url)&&void 0!==u?u:o,window.location.href=c,c.includes("#")&&window.location.reload(),e.abrupt("return");case 23:return e.next=25,E._getSession({event:"storage"});case 25:return e.abrupt("return",l);case 26:case"end":return e.stop()}},e)}))).apply(this,arguments)}},2083:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0})},2126:function(e,t,r){"use strict";var n=r(6314);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.proxyLogger=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=arguments.length>1?arguments[1]:void 0;try{if("undefined"==typeof window)return e;var r={},n=function(e){var n;r[e]=(n=(0,i.default)(o.default.mark(function r(n,i){var s,d;return o.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if(c[e](n,i),"error"===e&&(i=u(i)),i.client=!0,s="".concat(t,"/_log"),d=new URLSearchParams(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach(function(t){(0,a.default)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({level:e,code:n},i)),!navigator.sendBeacon){r.next=8;break}return r.abrupt("return",navigator.sendBeacon(s,d));case 8:return r.next=10,fetch(s,{method:"POST",body:d,keepalive:!0});case 10:return r.abrupt("return",r.sent);case 11:case"end":return r.stop()}},r)})),function(e,t){return n.apply(this,arguments)})};for(var s in e)n(s);return r}catch(e){return c}},t.setLogger=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;t||(c.debug=function(){}),e.error&&(c.error=e.error),e.warn&&(c.warn=e.warn),e.debug&&(c.debug=e.debug)};var o=n(r(9171)),a=n(r(8104)),i=n(r(4662)),s=r(2118);function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function u(e){var t;return e instanceof Error&&!(e instanceof s.UnknownError)?{message:e.message,stack:e.stack,name:e.name}:(null!=e&&e.error&&(e.error=u(e.error),e.message=null!==(t=e.message)&&void 0!==t?t:e.error.message),e)}var c={error:function(e,t){t=u(t),console.error("[next-auth][error][".concat(e,"]"),"\nhttps://next-auth.js.org/errors#".concat(e.toLowerCase()),t.message,t)},warn:function(e){console.warn("[next-auth][warn][".concat(e,"]"),"\nhttps://next-auth.js.org/warnings#".concat(e.toLowerCase()))},debug:function(e,t){console.log("[next-auth][debug][".concat(e,"]"),t)}};t.default=c},2606:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t;let r=new URL("http://localhost:3000/api/auth");e&&!e.startsWith("http")&&(e=`https://${e}`);let n=new URL(null!==(t=e)&&void 0!==t?t:r),o=("/"===n.pathname?r.pathname:n.pathname).replace(/\/$/,""),a=`${n.origin}${o}`;return{origin:n.origin,host:n.host,path:o,base:a,toString:()=>a}}},2601:function(e,t,r){"use strict";var n,o;e.exports=(null==(n=r.g.process)?void 0:n.env)&&"object"==typeof(null==(o=r.g.process)?void 0:o.env)?r.g.process:r(9026)},9026:function(e){!function(){var t={229:function(e){var t,r,n,o=e.exports={};function a(){throw Error("setTimeout has not been defined")}function i(){throw Error("clearTimeout has not been defined")}function s(e){if(t===setTimeout)return setTimeout(e,0);if((t===a||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(r){try{return t.call(null,e,0)}catch(r){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:a}catch(e){t=a}try{r="function"==typeof clearTimeout?clearTimeout:i}catch(e){r=i}}();var l=[],u=!1,c=-1;function d(){u&&n&&(u=!1,n.length?l=n.concat(l):c=-1,l.length&&f())}function f(){if(!u){var e=s(d);u=!0;for(var t=l.length;t;){for(n=l,l=[];++c<t;)n&&n[c].run();c=-1,t=l.length}n=null,u=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===i||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function p(e,t){this.fun=e,this.array=t}function b(){}o.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];l.push(new p(e,t)),1!==l.length||u||s(f)},p.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=b,o.addListener=b,o.once=b,o.off=b,o.removeListener=b,o.removeAllListeners=b,o.emit=b,o.prependListener=b,o.prependOnceListener=b,o.listeners=function(e){return[]},o.binding=function(e){throw Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw Error("process.chdir is not supported")},o.umask=function(){return 0}}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var a=r[e]={exports:{}},i=!0;try{t[e](a,a.exports,n),i=!1}finally{i&&delete r[e]}return a.exports}n.ab="//";var o=n(229);e.exports=o}()},622:function(e,t,r){"use strict";var n=r(2265),o=Symbol.for("react.element"),a=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,s=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,r){var n,a={},u=null,c=null;for(n in void 0!==r&&(u=""+r),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)i.call(t,n)&&!l.hasOwnProperty(n)&&(a[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps)void 0===a[n]&&(a[n]=t[n]);return{$$typeof:o,type:e,key:u,ref:c,props:a,_owner:s.current}}t.Fragment=a,t.jsx=u,t.jsxs=u},7437:function(e,t,r){"use strict";e.exports=r(622)},9629:function(e){e.exports=function(e,t){this.v=e,this.k=t},e.exports.__esModule=!0,e.exports.default=e.exports},7563:function(e){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n},e.exports.__esModule=!0,e.exports.default=e.exports},8158:function(e){e.exports=function(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},5248:function(e){e.exports=function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},4662:function(e){function t(e,t,r,n,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,o)}e.exports=function(e){return function(){var r=this,n=arguments;return new Promise(function(o,a){var i=e.apply(r,n);function s(e){t(i,o,a,s,l,"next",e)}function l(e){t(i,o,a,s,l,"throw",e)}s(void 0)})}},e.exports.__esModule=!0,e.exports.default=e.exports},6358:function(e){e.exports=function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},4951:function(e,t,r){var n=r(720),o=r(795);e.exports=function(e,t,r){if(n())return Reflect.construct.apply(null,arguments);var a=[null];a.push.apply(a,t);var i=new(e.bind.apply(e,a));return r&&o(i,r.prototype),i},e.exports.__esModule=!0,e.exports.default=e.exports},1127:function(e,t,r){var n=r(9040);function o(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,n(o.key),o)}}e.exports=function(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},8104:function(e,t,r){var n=r(9040);e.exports=function(e,t,r){return(t=n(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e},e.exports.__esModule=!0,e.exports.default=e.exports},3654:function(e){function t(r){return e.exports=t=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},5684:function(e,t,r){var n=r(795);e.exports=function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&n(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},6314:function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},8960:function(e){e.exports=function(e){try{return -1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}},e.exports.__esModule=!0,e.exports.default=e.exports},720:function(e){function t(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(e.exports=t=function(){return!!r},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},2731:function(e){e.exports=function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,s=[],l=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){u=!0,o=e}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return s}},e.exports.__esModule=!0,e.exports.default=e.exports},4980:function(e){e.exports=function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},746:function(e,t,r){var n=r(1565).default,o=r(5248);e.exports=function(e,t){if(t&&("object"==n(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return o(e)},e.exports.__esModule=!0,e.exports.default=e.exports},8537:function(e,t,r){var n=r(6863);function o(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,r,a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",s=a.toStringTag||"@@toStringTag";function l(e,o,a,i){var s=Object.create((o&&o.prototype instanceof c?o:c).prototype);return n(s,"_invoke",function(e,n,o){var a,i,s,l=0,c=o||[],d=!1,f={p:0,n:0,v:t,a:p,f:p.bind(t,4),d:function(e,r){return a=e,i=0,s=t,f.n=r,u}};function p(e,n){for(i=e,s=n,r=0;!d&&l&&!o&&r<c.length;r++){var o,a=c[r],p=f.p,b=a[2];e>3?(o=b===n)&&(i=a[4]||3,s=a[5]===t?a[3]:a[5],a[4]=3,a[5]=t):a[0]<=p&&((o=e<2&&p<a[1])?(i=0,f.v=n,f.n=a[1]):p<b&&(o=e<3||a[0]>n||n>b)&&(a[4]=e,a[5]=n,f.n=b,i=0))}if(o||e>1)return u;throw d=!0,n}return function(o,c,b){if(l>1)throw TypeError("Generator is already running");for(d&&1===c&&p(c,b),i=c,s=b;(r=i<2?t:s)||!d;){a||(i?i<3?(i>1&&(f.n=-1),p(i,s)):f.n=s:f.v=s);try{if(l=2,a){if(i||(o="next"),r=a[o]){if(!(r=r.call(a,s)))throw TypeError("iterator result is not an object");if(!r.done)return r;s=r.value,i<2&&(i=0)}else 1===i&&(r=a.return)&&r.call(a),i<2&&(s=TypeError("The iterator does not provide a '"+o+"' method"),i=1);a=t}else if((r=(d=f.n<0)?s:e.call(n,f))!==u)break}catch(e){a=t,i=1,s=e}finally{l=1}}return{value:r,done:d}}}(e,a,i),!0),s}var u={};function c(){}function d(){}function f(){}r=Object.getPrototypeOf;var p=[][i]?r(r([][i]())):(n(r={},i,function(){return this}),r),b=f.prototype=c.prototype=Object.create(p);function v(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,n(e,s,"GeneratorFunction")),e.prototype=Object.create(b),e}return d.prototype=f,n(b,"constructor",f),n(f,"constructor",d),d.displayName="GeneratorFunction",n(f,s,"GeneratorFunction"),n(b),n(b,s,"Generator"),n(b,i,function(){return this}),n(b,"toString",function(){return"[object Generator]"}),(e.exports=o=function(){return{w:l,m:v}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=o,e.exports.__esModule=!0,e.exports.default=e.exports},5199:function(e,t,r){var n=r(6255);e.exports=function(e,t,r,o,a){var i=n(e,t,r,o,a);return i.next().then(function(e){return e.done?e.value:i.next()})},e.exports.__esModule=!0,e.exports.default=e.exports},6255:function(e,t,r){var n=r(8537),o=r(2695);e.exports=function(e,t,r,a,i){return new o(n().w(e,t,r,a),i||Promise)},e.exports.__esModule=!0,e.exports.default=e.exports},2695:function(e,t,r){var n=r(9629),o=r(6863);e.exports=function e(t,r){var a;this.next||(o(e.prototype),o(e.prototype,"function"==typeof Symbol&&Symbol.asyncIterator||"@asyncIterator",function(){return this})),o(this,"_invoke",function(e,o,i){function s(){return new r(function(o,a){(function e(o,a,i,s){try{var l=t[o](a),u=l.value;return u instanceof n?r.resolve(u.v).then(function(t){e("next",t,i,s)},function(t){e("throw",t,i,s)}):r.resolve(u).then(function(e){l.value=e,i(l)},function(t){return e("throw",t,i,s)})}catch(e){s(e)}})(e,i,o,a)})}return a=a?a.then(s,s):s()},!0)},e.exports.__esModule=!0,e.exports.default=e.exports},6863:function(e){function t(r,n,o,a){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}e.exports=t=function(e,r,n,o){if(r)i?i(e,r,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[r]=n;else{var a=function(r,n){t(e,r,function(e){return this._invoke(r,n,e)})};a("next",0),a("throw",1),a("return",2)}},e.exports.__esModule=!0,e.exports.default=e.exports,t(r,n,o,a)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},4738:function(e){e.exports=function(e){var t=Object(e),r=[];for(var n in t)r.unshift(n);return function e(){for(;r.length;)if((n=r.pop())in t)return e.value=n,e.done=!1,e;return e.done=!0,e}},e.exports.__esModule=!0,e.exports.default=e.exports},4436:function(e,t,r){var n=r(9629),o=r(8537),a=r(5199),i=r(6255),s=r(2695),l=r(4738),u=r(6553);function c(){"use strict";var t=o(),r=t.m(c),d=(Object.getPrototypeOf?Object.getPrototypeOf(r):r.__proto__).constructor;function f(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===d||"GeneratorFunction"===(t.displayName||t.name))}var p={throw:1,return:2,break:3,continue:3};function b(e){var t,r;return function(n){t||(t={stop:function(){return r(n.a,2)},catch:function(){return n.v},abrupt:function(e,t){return r(n.a,p[e],t)},delegateYield:function(e,o,a){return t.resultName=o,r(n.d,u(e),a)},finish:function(e){return r(n.f,e)}},r=function(e,r,o){n.p=t.prev,n.n=t.next;try{return e(r,o)}finally{t.next=n.n}}),t.resultName&&(t[t.resultName]=n.v,t.resultName=void 0),t.sent=n.v,t.next=n.n;try{return e.call(this,t)}finally{n.p=t.prev,n.n=t.next}}}return(e.exports=c=function(){return{wrap:function(e,r,n,o){return t.w(b(e),r,n,o&&o.reverse())},isGeneratorFunction:f,mark:t.m,awrap:function(e,t){return new n(e,t)},AsyncIterator:s,async:function(e,t,r,n,o){return(f(t)?i:a)(b(e),t,r,n,o)},keys:l,values:u}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=c,e.exports.__esModule=!0,e.exports.default=e.exports},6553:function(e,t,r){var n=r(1565).default;e.exports=function(e){if(null!=e){var t=e["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],r=0;if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}}}throw TypeError(n(e)+" is not iterable")},e.exports.__esModule=!0,e.exports.default=e.exports},795:function(e){function t(r,n){return e.exports=t=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r,n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},2184:function(e,t,r){var n=r(8158),o=r(2731),a=r(2214),i=r(4980);e.exports=function(e,t){return n(e)||o(e,t)||a(e,t)||i()},e.exports.__esModule=!0,e.exports.default=e.exports},9944:function(e,t,r){var n=r(1565).default;e.exports=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!=n(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},9040:function(e,t,r){var n=r(1565).default,o=r(9944);e.exports=function(e){var t=o(e,"string");return"symbol"==n(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},1565:function(e){function t(r){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},2214:function(e,t,r){var n=r(7563);e.exports=function(e,t){if(e){if("string"==typeof e)return n(e,t);var r=({}).toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},5058:function(e,t,r){var n=r(3654),o=r(795),a=r(8960),i=r(4951);function s(t){var r="function"==typeof Map?new Map:void 0;return e.exports=s=function(e){if(null===e||!a(e))return e;if("function"!=typeof e)throw TypeError("Super expression must either be null or a function");if(void 0!==r){if(r.has(e))return r.get(e);r.set(e,t)}function t(){return i(e,arguments,n(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),o(t,e)},e.exports.__esModule=!0,e.exports.default=e.exports,s(t)}e.exports=s,e.exports.__esModule=!0,e.exports.default=e.exports},9171:function(e,t,r){var n=r(4436)();e.exports=n;try{regeneratorRuntime=n}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=n:Function("r","regeneratorRuntime = r")(n)}},2210:function(e,t,r){"use strict";r.d(t,{F:function(){return a},e:function(){return i}});var n=r(2265);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function i(...e){return n.useCallback(a(...e),e)}},9381:function(e,t,r){"use strict";r.d(t,{WV:function(){return s},jH:function(){return l}});var n=r(2265),o=r(4887),a=r(7256),i=r(7437),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,a.Z8)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...a}=e,s=o?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(s,{...a,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function l(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},7256:function(e,t,r){"use strict";r.d(t,{Z8:function(){return i},g7:function(){return s}});var n=r(2265),o=r(2210),a=r(7437);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...a}=e;if(n.isValidElement(r)){let e,i;let s=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,l=function(e,t){let r={...t};for(let n in t){let o=e[n],a=t[n];/^on[A-Z]/.test(n)?o&&a?r[n]=(...e)=>{let t=a(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...a}:"className"===n&&(r[n]=[o,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==n.Fragment&&(l.ref=t?(0,o.F)(t,s):s),n.cloneElement(r,l)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...i}=e,s=n.Children.toArray(o),l=s.find(u);if(l){let e=l.props.children,o=s.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,a.jsx)(t,{...i,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}var s=i("Slot"),l=Symbol("radix.slottable");function u(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},6061:function(e,t,r){"use strict";r.d(t,{j:function(){return i}});var n=r(7042);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.W,i=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:s}=t,l=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==s?void 0:s[e];if(null===t)return null;let a=o(t)||o(n);return i[e][a]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return a(e,l,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...u}[t]):({...s,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},7042:function(e,t,r){"use strict";function n(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n)}return o}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{W:function(){return n}})},4769:function(e,t,r){"use strict";r.d(t,{m6:function(){return Z}});let n=e=>{let t=s(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),o(r,t)||i(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}},o=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),a=n?o(e.slice(1),n):void 0;if(a)return a;if(0===t.validators.length)return;let i=e.join("-");return t.validators.find(({validator:e})=>e(i))?.classGroupId},a=/^\[(.+)\]$/,i=e=>{if(a.test(e)){let t=a.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},s=e=>{let{theme:t,prefix:r}=e,n={nextPart:new Map,validators:[]};return d(Object.entries(e.classGroups),r).forEach(([e,r])=>{l(r,n,e,t)}),n},l=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=r;return}if("function"==typeof e){if(c(e)){l(e(n),t,r,n);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,o])=>{l(o,u(t,e),r,n)})})},u=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},c=e=>e.isThemeGetter,d=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,f=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,a)=>{r.set(o,a),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},p=e=>{let{separator:t,experimentalParseClassName:r}=e,n=1===t.length,o=t[0],a=t.length,i=e=>{let r;let i=[],s=0,l=0;for(let u=0;u<e.length;u++){let c=e[u];if(0===s){if(c===o&&(n||e.slice(u,u+a)===t)){i.push(e.slice(l,u)),l=u+a;continue}if("/"===c){r=u;continue}}"["===c?s++:"]"===c&&s--}let u=0===i.length?e:e.substring(l),c=u.startsWith("!"),d=c?u.substring(1):u;return{modifiers:i,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:r&&r>l?r-l:void 0}};return r?e=>r({className:e,parseClassName:i}):i},b=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},v=e=>({cache:f(e.cacheSize),parseClassName:p(e),...n(e)}),h=/\s+/,g=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o}=t,a=[],i=e.trim().split(h),s="";for(let e=i.length-1;e>=0;e-=1){let t=i[e],{modifiers:l,hasImportantModifier:u,baseClassName:c,maybePostfixModifierPosition:d}=r(t),f=!!d,p=n(f?c.substring(0,d):c);if(!p){if(!f||!(p=n(c))){s=t+(s.length>0?" "+s:s);continue}f=!1}let v=b(l).join(":"),h=u?v+"!":v,g=h+p;if(a.includes(g))continue;a.push(g);let y=o(p,f);for(let e=0;e<y.length;++e){let t=y[e];a.push(h+t)}s=t+(s.length>0?" "+s:s)}return s};function y(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=m(e))&&(n&&(n+=" "),n+=t);return n}let m=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=m(e[n]))&&(r&&(r+=" "),r+=t);return r},x=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},w=/^\[(?:([a-z-]+):)?(.+)\]$/i,_=/^\d+\/\d+$/,k=new Set(["px","full","screen"]),O=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,j=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,E=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,S=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,P=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,R=e=>T(e)||k.has(e)||_.test(e),M=e=>F(e,"length",H),T=e=>!!e&&!Number.isNaN(Number(e)),A=e=>F(e,"number",T),C=e=>!!e&&Number.isInteger(Number(e)),L=e=>e.endsWith("%")&&T(e.slice(0,-1)),N=e=>w.test(e),U=e=>O.test(e),z=new Set(["length","size","percentage"]),I=e=>F(e,z,V),D=e=>F(e,"position",V),$=new Set(["image","url"]),W=e=>F(e,$,X),G=e=>F(e,"",q),B=()=>!0,F=(e,t,r)=>{let n=w.exec(e);return!!n&&(n[1]?"string"==typeof t?n[1]===t:t.has(n[1]):r(n[2]))},H=e=>j.test(e)&&!E.test(e),V=()=>!1,q=e=>S.test(e),X=e=>P.test(e),Z=function(e){let t,r,n;let o=function(i){return r=(t=v([].reduce((e,t)=>t(e),e()))).cache.get,n=t.cache.set,o=a,a(i)};function a(e){let o=r(e);if(o)return o;let a=g(e,t);return n(e,a),a}return function(){return o(y.apply(null,arguments))}}(()=>{let e=x("colors"),t=x("spacing"),r=x("blur"),n=x("brightness"),o=x("borderColor"),a=x("borderRadius"),i=x("borderSpacing"),s=x("borderWidth"),l=x("contrast"),u=x("grayscale"),c=x("hueRotate"),d=x("invert"),f=x("gap"),p=x("gradientColorStops"),b=x("gradientColorStopPositions"),v=x("inset"),h=x("margin"),g=x("opacity"),y=x("padding"),m=x("saturate"),w=x("scale"),_=x("sepia"),k=x("skew"),O=x("space"),j=x("translate"),E=()=>["auto","contain","none"],S=()=>["auto","hidden","clip","visible","scroll"],P=()=>["auto",N,t],z=()=>[N,t],$=()=>["",R,M],F=()=>["auto",T,N],H=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],V=()=>["solid","dashed","dotted","double","none"],q=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],X=()=>["start","end","center","between","around","evenly","stretch"],Z=()=>["","0",N],J=()=>["auto","avoid","all","avoid-page","page","left","right","column"],K=()=>[T,N];return{cacheSize:500,separator:":",theme:{colors:[B],spacing:[R,M],blur:["none","",U,N],brightness:K(),borderColor:[e],borderRadius:["none","","full",U,N],borderSpacing:z(),borderWidth:$(),contrast:K(),grayscale:Z(),hueRotate:K(),invert:Z(),gap:z(),gradientColorStops:[e],gradientColorStopPositions:[L,M],inset:P(),margin:P(),opacity:K(),padding:z(),saturate:K(),scale:K(),sepia:Z(),skew:K(),space:z(),translate:z()},classGroups:{aspect:[{aspect:["auto","square","video",N]}],container:["container"],columns:[{columns:[U]}],"break-after":[{"break-after":J()}],"break-before":[{"break-before":J()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...H(),N]}],overflow:[{overflow:S()}],"overflow-x":[{"overflow-x":S()}],"overflow-y":[{"overflow-y":S()}],overscroll:[{overscroll:E()}],"overscroll-x":[{"overscroll-x":E()}],"overscroll-y":[{"overscroll-y":E()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[v]}],"inset-x":[{"inset-x":[v]}],"inset-y":[{"inset-y":[v]}],start:[{start:[v]}],end:[{end:[v]}],top:[{top:[v]}],right:[{right:[v]}],bottom:[{bottom:[v]}],left:[{left:[v]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",C,N]}],basis:[{basis:P()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",N]}],grow:[{grow:Z()}],shrink:[{shrink:Z()}],order:[{order:["first","last","none",C,N]}],"grid-cols":[{"grid-cols":[B]}],"col-start-end":[{col:["auto",{span:["full",C,N]},N]}],"col-start":[{"col-start":F()}],"col-end":[{"col-end":F()}],"grid-rows":[{"grid-rows":[B]}],"row-start-end":[{row:["auto",{span:[C,N]},N]}],"row-start":[{"row-start":F()}],"row-end":[{"row-end":F()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",N]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",N]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...X()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...X(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...X(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[y]}],px:[{px:[y]}],py:[{py:[y]}],ps:[{ps:[y]}],pe:[{pe:[y]}],pt:[{pt:[y]}],pr:[{pr:[y]}],pb:[{pb:[y]}],pl:[{pl:[y]}],m:[{m:[h]}],mx:[{mx:[h]}],my:[{my:[h]}],ms:[{ms:[h]}],me:[{me:[h]}],mt:[{mt:[h]}],mr:[{mr:[h]}],mb:[{mb:[h]}],ml:[{ml:[h]}],"space-x":[{"space-x":[O]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[O]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",N,t]}],"min-w":[{"min-w":[N,t,"min","max","fit"]}],"max-w":[{"max-w":[N,t,"none","full","min","max","fit","prose",{screen:[U]},U]}],h:[{h:[N,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[N,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[N,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[N,t,"auto","min","max","fit"]}],"font-size":[{text:["base",U,M]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",A]}],"font-family":[{font:[B]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",N]}],"line-clamp":[{"line-clamp":["none",T,A]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",R,N]}],"list-image":[{"list-image":["none",N]}],"list-style-type":[{list:["none","disc","decimal",N]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...V(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",R,M]}],"underline-offset":[{"underline-offset":["auto",R,N]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:z()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",N]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",N]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...H(),D]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",I]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},W]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[b]}],"gradient-via-pos":[{via:[b]}],"gradient-to-pos":[{to:[b]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[a]}],"rounded-s":[{"rounded-s":[a]}],"rounded-e":[{"rounded-e":[a]}],"rounded-t":[{"rounded-t":[a]}],"rounded-r":[{"rounded-r":[a]}],"rounded-b":[{"rounded-b":[a]}],"rounded-l":[{"rounded-l":[a]}],"rounded-ss":[{"rounded-ss":[a]}],"rounded-se":[{"rounded-se":[a]}],"rounded-ee":[{"rounded-ee":[a]}],"rounded-es":[{"rounded-es":[a]}],"rounded-tl":[{"rounded-tl":[a]}],"rounded-tr":[{"rounded-tr":[a]}],"rounded-br":[{"rounded-br":[a]}],"rounded-bl":[{"rounded-bl":[a]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[...V(),"hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:V()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...V()]}],"outline-offset":[{"outline-offset":[R,N]}],"outline-w":[{outline:[R,M]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:$()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[R,M]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",U,G]}],"shadow-color":[{shadow:[B]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":[...q(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":q()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[n]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",U,N]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[m]}],sepia:[{sepia:[_]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[m]}],"backdrop-sepia":[{"backdrop-sepia":[_]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",N]}],duration:[{duration:K()}],ease:[{ease:["linear","in","out","in-out",N]}],delay:[{delay:K()}],animate:[{animate:["none","spin","ping","pulse","bounce",N]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[C,N]}],"translate-x":[{"translate-x":[j]}],"translate-y":[{"translate-y":[j]}],"skew-x":[{"skew-x":[k]}],"skew-y":[{"skew-y":[k]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",N]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",N]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":z()}],"scroll-mx":[{"scroll-mx":z()}],"scroll-my":[{"scroll-my":z()}],"scroll-ms":[{"scroll-ms":z()}],"scroll-me":[{"scroll-me":z()}],"scroll-mt":[{"scroll-mt":z()}],"scroll-mr":[{"scroll-mr":z()}],"scroll-mb":[{"scroll-mb":z()}],"scroll-ml":[{"scroll-ml":z()}],"scroll-p":[{"scroll-p":z()}],"scroll-px":[{"scroll-px":z()}],"scroll-py":[{"scroll-py":z()}],"scroll-ps":[{"scroll-ps":z()}],"scroll-pe":[{"scroll-pe":z()}],"scroll-pt":[{"scroll-pt":z()}],"scroll-pr":[{"scroll-pr":z()}],"scroll-pb":[{"scroll-pb":z()}],"scroll-pl":[{"scroll-pl":z()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",N]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[R,M,A]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})}}]);