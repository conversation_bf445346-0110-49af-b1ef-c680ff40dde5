"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[82],{2549:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},9808:function(e,t,n){/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n(2265),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,a=r.useEffect,u=r.useLayoutEffect,s=r.useDebugValue;function l(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,c=r[1];return u(function(){o.value=n,o.getSnapshot=t,l(o)&&c({inst:o})},[e,n,t]),a(function(){return l(o)&&c({inst:o}),e(function(){l(o)&&c({inst:o})})},[e]),s(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},3176:function(e,t,n){/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n(2265),o=n(6272),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=o.useSyncExternalStore,u=r.useRef,s=r.useEffect,l=r.useMemo,c=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var d=u(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;var v=a(e,(d=l(function(){function e(e){if(!s){if(s=!0,a=e,e=r(e),void 0!==o&&f.hasValue){var t=f.value;if(o(t,e))return u=t}return u=e}if(t=u,i(a,e))return t;var n=r(e);return void 0!==o&&o(t,n)?(a=e,t):(a=e,u=n)}var a,u,s=!1,l=void 0===n?null:n;return[function(){return e(t())},null===l?void 0:function(){return e(l())}]},[t,n,r,o]))[0],d[1]);return s(function(){f.hasValue=!0,f.value=v},[v]),c(v),v}},6272:function(e,t,n){e.exports=n(9808)},5401:function(e,t,n){e.exports=n(3176)},5744:function(e,t,n){n.d(t,{M:function(){return r}});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},7733:function(e,t,n){n.d(t,{B:function(){return s}});var r=n(2265),o=n(6989),i=n(2210),a=n(7256),u=n(7437);function s(e){let t=e+"CollectionProvider",[n,s]=(0,o.b)(t),[l,c]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:n}=e,o=r.useRef(null),i=r.useRef(new Map).current;return(0,u.jsx)(l,{scope:t,itemMap:i,collectionRef:o,children:n})};d.displayName=t;let f=e+"CollectionSlot",v=(0,a.Z8)(f),m=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=c(f,n),a=(0,i.e)(t,o.collectionRef);return(0,u.jsx)(v,{ref:a,children:r})});m.displayName=f;let p=e+"CollectionItemSlot",y="data-radix-collection-item",g=(0,a.Z8)(p),h=r.forwardRef((e,t)=>{let{scope:n,children:o,...a}=e,s=r.useRef(null),l=(0,i.e)(t,s),d=c(p,n);return r.useEffect(()=>(d.itemMap.set(s,{ref:s,...a}),()=>void d.itemMap.delete(s))),(0,u.jsx)(g,{[y]:"",ref:l,children:o})});return h.displayName=p,[{Provider:d,Slot:m,ItemSlot:h},function(t){let n=c(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${y}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},s]}},6989:function(e,t,n){n.d(t,{b:function(){return i}});var r=n(2265),o=n(7437);function i(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let a=r.createContext(i),u=n.length;n=[...n,i];let s=t=>{let{scope:n,children:i,...s}=t,l=n?.[e]?.[u]||a,c=r.useMemo(()=>s,Object.values(s));return(0,o.jsx)(l.Provider,{value:c,children:i})};return s.displayName=t+"Provider",[s,function(n,o){let s=o?.[e]?.[u]||a,l=r.useContext(s);if(l)return l;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},9249:function(e,t,n){n.d(t,{I0:function(){return g},XB:function(){return f},fC:function(){return y}});var r,o=n(2265),i=n(5744),a=n(9381),u=n(2210),s=n(6459),l=n(7437),c="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:f,onPointerDownOutside:v,onFocusOutside:y,onInteractOutside:g,onDismiss:h,...b}=e,E=o.useContext(d),[S,w]=o.useState(null),O=S?.ownerDocument??globalThis?.document,[,N]=o.useState({}),T=(0,u.e)(t,e=>w(e)),C=Array.from(E.layers),[R]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),D=C.indexOf(R),P=S?C.indexOf(S):-1,I=E.layersWithOutsidePointerEventsDisabled.size>0,M=P>=D,_=function(e,t=globalThis?.document){let n=(0,s.W)(e),r=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){p("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=r,t.addEventListener("click",i.current,{once:!0})):r()}else t.removeEventListener("click",i.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));!M||n||(v?.(e),g?.(e),e.defaultPrevented||h?.())},O),L=function(e,t=globalThis?.document){let n=(0,s.W)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&p("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;[...E.branches].some(e=>e.contains(t))||(y?.(e),g?.(e),e.defaultPrevented||h?.())},O);return!function(e,t=globalThis?.document){let n=(0,s.W)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{P!==E.layers.size-1||(f?.(e),!e.defaultPrevented&&h&&(e.preventDefault(),h()))},O),o.useEffect(()=>{if(S)return n&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=O.body.style.pointerEvents,O.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(S)),E.layers.add(S),m(),()=>{n&&1===E.layersWithOutsidePointerEventsDisabled.size&&(O.body.style.pointerEvents=r)}},[S,O,n,E]),o.useEffect(()=>()=>{S&&(E.layers.delete(S),E.layersWithOutsidePointerEventsDisabled.delete(S),m())},[S,E]),o.useEffect(()=>{let e=()=>N({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,l.jsx)(a.WV.div,{...b,ref:T,style:{pointerEvents:I?M?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.M)(e.onFocusCapture,L.onFocusCapture),onBlurCapture:(0,i.M)(e.onBlurCapture,L.onBlurCapture),onPointerDownCapture:(0,i.M)(e.onPointerDownCapture,_.onPointerDownCapture)})});f.displayName="DismissableLayer";var v=o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,u.e)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,l.jsx)(a.WV.div,{...e,ref:i})});function m(){let e=new CustomEvent(c);document.dispatchEvent(e)}function p(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,a.jH)(o,i):o.dispatchEvent(i)}v.displayName="DismissableLayerBranch";var y=f,g=v},2730:function(e,t,n){n.d(t,{h:function(){return s}});var r=n(2265),o=n(4887),i=n(9381),a=n(1030),u=n(7437),s=r.forwardRef((e,t)=>{let{container:n,...s}=e,[l,c]=r.useState(!1);(0,a.b)(()=>c(!0),[]);let d=n||l&&globalThis?.document?.body;return d?o.createPortal((0,u.jsx)(i.WV.div,{...s,ref:t}),d):null});s.displayName="Portal"},5606:function(e,t,n){n.d(t,{z:function(){return a}});var r=n(2265),o=n(2210),i=n(1030),a=e=>{let t,n;let{present:a,children:s}=e,l=function(e){var t,n;let[o,a]=r.useState(),s=r.useRef(null),l=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=u(s.current);c.current="mounted"===d?e:"none"},[d]),(0,i.b)(()=>{let t=s.current,n=l.current;if(n!==e){let r=c.current,o=u(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),l.current=e}},[e,f]),(0,i.b)(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,n=n=>{let r=u(s.current).includes(n.animationName);if(n.target===o&&r&&(f("ANIMATION_END"),!l.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(c.current=u(s.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{s.current=e?getComputedStyle(e):null,a(e)},[])}}(a),c="function"==typeof s?s({present:l.isPresent}):r.Children.only(s),d=(0,o.e)(l.ref,(t=Object.getOwnPropertyDescriptor(c.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?c.ref:(t=Object.getOwnPropertyDescriptor(c,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?c.props.ref:c.props.ref||c.ref);return"function"==typeof s||l.isPresent?r.cloneElement(c,{ref:d}):null};function u(e){return e?.animationName||"none"}a.displayName="Presence"},6459:function(e,t,n){n.d(t,{W:function(){return o}});var r=n(2265);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},3763:function(e,t,n){n.d(t,{T:function(){return u}});var r,o=n(2265),i=n(1030),a=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.b;function u({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,u,s]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),u=o.useRef(t);return a(()=>{u.current=t},[t]),o.useEffect(()=>{i.current!==n&&(u.current?.(n),i.current=n)},[n,i]),[n,r,u]}({defaultProp:t,onChange:n}),l=void 0!==e,c=l?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,r])}return[c,o.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&s.current?.(n)}else u(t)},[l,e,u,s])]}Symbol("RADIX:SYNC_STATE")},1030:function(e,t,n){n.d(t,{b:function(){return o}});var r=n(2265),o=globalThis?.document?r.useLayoutEffect:()=>{}},4660:function(e,t,n){n.d(t,{Ue:function(){return f}});let r=e=>{let t;let n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},a=t=e(r,o,i);return i},o=e=>e?r(e):r;var i=n(2265),a=n(5401);let{useDebugValue:u}=i,{useSyncExternalStoreWithSelector:s}=a,l=!1,c=e=>e,d=e=>{"function"!=typeof e&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");let t="function"==typeof e?o(e):e,n=(e,n)=>(function(e,t=c,n){n&&!l&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),l=!0);let r=s(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return u(r),r})(t,e,n);return Object.assign(n,t),n},f=e=>e?d(e):d},4810:function(e,t,n){n.d(t,{mW:function(){return a},tJ:function(){return d}});let r=new Map,o=e=>{let t=r.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([e,t])=>[e,t.getState()])):{}},i=(e,t,n)=>{if(void 0===e)return{type:"untracked",connection:t.connect(n)};let o=r.get(n.name);if(o)return{type:"tracked",store:e,...o};let i={connection:t.connect(n),stores:{}};return r.set(n.name,i),{type:"tracked",store:e,...i}},a=(e,t={})=>(n,r,a)=>{let s;let{enabled:l,anonymousActionType:c,store:d,...f}=t;try{s=(null==l||l)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(e){}if(!s)return l&&console.warn("[zustand devtools middleware] Please install/enable Redux devtools extension"),e(n,r,a);let{connection:v,...m}=i(d,s,f),p=!0;a.setState=(e,t,i)=>{let u=n(e,t);if(!p)return u;let s=void 0===i?{type:c||"anonymous"}:"string"==typeof i?{type:i}:i;return void 0===d?null==v||v.send(s,r()):null==v||v.send({...s,type:`${d}/${s.type}`},{...o(f.name),[d]:a.getState()}),u};let y=(...e)=>{let t=p;p=!1,n(...e),p=t},g=e(a.setState,r,a);if("untracked"===m.type?null==v||v.init(g):(m.stores[m.store]=a,null==v||v.init(Object.fromEntries(Object.entries(m.stores).map(([e,t])=>[e,e===m.store?g:t.getState()])))),a.dispatchFromDevtools&&"function"==typeof a.dispatch){let e=!1,t=a.dispatch;a.dispatch=(...n)=>{"__setState"!==n[0].type||e||(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),e=!0),t(...n)}}return v.subscribe(e=>{var t;switch(e.type){case"ACTION":if("string"!=typeof e.payload){console.error("[zustand devtools middleware] Unsupported action format");return}return u(e.payload,e=>{if("__setState"===e.type){if(void 0===d){y(e.state);return}1!==Object.keys(e.state).length&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format. 
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);let t=e.state[d];if(null==t)return;JSON.stringify(a.getState())!==JSON.stringify(t)&&y(t);return}a.dispatchFromDevtools&&"function"==typeof a.dispatch&&a.dispatch(e)});case"DISPATCH":switch(e.payload.type){case"RESET":if(y(g),void 0===d)return null==v?void 0:v.init(a.getState());return null==v?void 0:v.init(o(f.name));case"COMMIT":if(void 0===d){null==v||v.init(a.getState());break}return null==v?void 0:v.init(o(f.name));case"ROLLBACK":return u(e.state,e=>{if(void 0===d){y(e),null==v||v.init(a.getState());return}y(e[d]),null==v||v.init(o(f.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return u(e.state,e=>{if(void 0===d){y(e);return}JSON.stringify(a.getState())!==JSON.stringify(e[d])&&y(e[d])});case"IMPORT_STATE":{let{nextLiftedState:n}=e.payload,r=null==(t=n.computedStates.slice(-1)[0])?void 0:t.state;if(!r)return;void 0===d?y(r):y(r[d]),null==v||v.send(null,n);break}case"PAUSE_RECORDING":return p=!p}return}}),g},u=(e,t)=>{let n;try{n=JSON.parse(e)}catch(e){console.error("[zustand devtools middleware] Could not parse the received json",e)}void 0!==n&&t(n)},s=e=>t=>{try{let n=e(t);if(n instanceof Promise)return n;return{then:e=>s(e)(n),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>s(t)(e)}}},l=(e,t)=>(n,r,o)=>{let i,a,u={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},l=!1,c=new Set,d=new Set;try{i=u.getStorage()}catch(e){}if(!i)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${u.name}', the given storage is currently unavailable.`),n(...e)},r,o);let f=s(u.serialize),v=()=>{let e;let t=f({state:u.partialize({...r()}),version:u.version}).then(e=>i.setItem(u.name,e)).catch(t=>{e=t});if(e)throw e;return t},m=o.setState;o.setState=(e,t)=>{m(e,t),v()};let p=e((...e)=>{n(...e),v()},r,o),y=()=>{var e;if(!i)return;l=!1,c.forEach(e=>e(r()));let t=(null==(e=u.onRehydrateStorage)?void 0:e.call(u,r()))||void 0;return s(i.getItem.bind(i))(u.name).then(e=>{if(e)return u.deserialize(e)}).then(e=>{if(e){if("number"!=typeof e.version||e.version===u.version)return e.state;if(u.migrate)return u.migrate(e.state,e.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}}).then(e=>{var t;return n(a=u.merge(e,null!=(t=r())?t:p),!0),v()}).then(()=>{null==t||t(a,void 0),l=!0,d.forEach(e=>e(a))}).catch(e=>{null==t||t(void 0,e)})};return o.persist={setOptions:e=>{u={...u,...e},e.getStorage&&(i=e.getStorage())},clearStorage:()=>{null==i||i.removeItem(u.name)},getOptions:()=>u,rehydrate:()=>y(),hasHydrated:()=>l,onHydrate:e=>(c.add(e),()=>{c.delete(e)}),onFinishHydration:e=>(d.add(e),()=>{d.delete(e)})},y(),a||p},c=(e,t)=>(n,r,o)=>{let i,a={storage:function(e,t){let n;try{n=e()}catch(e){return}return{getItem:e=>{var r;let o=e=>null===e?null:JSON.parse(e,null==t?void 0:t.reviver),i=null!=(r=n.getItem(e))?r:null;return i instanceof Promise?i.then(o):o(i)},setItem:(e,r)=>n.setItem(e,JSON.stringify(r,null==t?void 0:t.replacer)),removeItem:e=>n.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},u=!1,l=new Set,c=new Set,d=a.storage;if(!d)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${a.name}', the given storage is currently unavailable.`),n(...e)},r,o);let f=()=>{let e=a.partialize({...r()});return d.setItem(a.name,{state:e,version:a.version})},v=o.setState;o.setState=(e,t)=>{v(e,t),f()};let m=e((...e)=>{n(...e),f()},r,o);o.getInitialState=()=>m;let p=()=>{var e,t;if(!d)return;u=!1,l.forEach(e=>{var t;return e(null!=(t=r())?t:m)});let o=(null==(t=a.onRehydrateStorage)?void 0:t.call(a,null!=(e=r())?e:m))||void 0;return s(d.getItem.bind(d))(a.name).then(e=>{if(e){if("number"!=typeof e.version||e.version===a.version)return[!1,e.state];if(a.migrate)return[!0,a.migrate(e.state,e.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[o,u]=e;if(n(i=a.merge(u,null!=(t=r())?t:m),!0),o)return f()}).then(()=>{null==o||o(i,void 0),i=r(),u=!0,c.forEach(e=>e(i))}).catch(e=>{null==o||o(void 0,e)})};return o.persist={setOptions:e=>{a={...a,...e},e.storage&&(d=e.storage)},clearStorage:()=>{null==d||d.removeItem(a.name)},getOptions:()=>a,rehydrate:()=>p(),hasHydrated:()=>u,onHydrate:e=>(l.add(e),()=>{l.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},a.skipHydration||p(),i||m},d=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?(console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),l(e,t)):c(e,t)}}]);