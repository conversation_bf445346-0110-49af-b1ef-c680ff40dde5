"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[932],{5859:function(e,t,n){n.d(t,{Ry:function(){return u}});var r=new WeakMap,o=new WeakMap,i={},l=0,a=function(e){return e&&(e.host||a(e.parentNode))},c=function(e,t,n,c){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=a(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var s=i[n],f=[],d=new Set,p=new Set(u),h=function(e){!e||d.has(e)||(d.add(e),h(e.parentNode))};u.forEach(h);var v=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(d.has(e))v(e);else try{var t=e.getAttribute(c),i=null!==t&&"false"!==t,l=(r.get(e)||0)+1,a=(s.get(e)||0)+1;r.set(e,l),s.set(e,a),f.push(e),1===l&&i&&o.set(e,!0),1===a&&e.setAttribute(n,"true"),i||e.setAttribute(c,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return v(t),d.clear(),l++,function(){f.forEach(function(e){var t=r.get(e)-1,i=s.get(e)-1;r.set(e,t),s.set(e,i),t||(o.has(e)||e.removeAttribute(c),o.delete(e)),i||e.removeAttribute(n)}),--l||(r=new WeakMap,r=new WeakMap,o=new WeakMap,i={})}},u=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),c(r,o,n,"aria-hidden")):function(){return null}}},3008:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2442:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},3523:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},7158:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},6141:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},6224:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},7332:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},5817:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},4056:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},774:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]])},4900:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},6020:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},9409:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1274:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Share",[["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["polyline",{points:"16 6 12 2 8 6",key:"m901s6"}],["line",{x1:"12",x2:"12",y1:"2",y2:"15",key:"1p0rca"}]])},3223:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]])},7972:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},5750:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},3386:function(e,t,n){n.d(t,{Z:function(){return U}});var r,o,i,l,a,c,u=function(){return(u=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function s(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}"function"==typeof SuppressedError&&SuppressedError;var f=n(2265),d="right-scroll-bar-position",p="width-before-scroll-bar";function h(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var v="undefined"!=typeof window?f.useLayoutEffect:f.useEffect,m=new WeakMap,g=(void 0===o&&(o={}),(void 0===i&&(i=function(e){return e}),l=[],a=!1,c={read:function(){if(a)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return l.length?l[l.length-1]:null},useMedium:function(e){var t=i(e,a);return l.push(t),function(){l=l.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(a=!0;l.length;){var t=l;l=[],t.forEach(e)}l={push:function(t){return e(t)},filter:function(){return l}}},assignMedium:function(e){a=!0;var t=[];if(l.length){var n=l;l=[],n.forEach(e),t=l}var r=function(){var n=t;t=[],n.forEach(e)},o=function(){return Promise.resolve().then(r)};o(),l={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),l}}}}).options=u({async:!0,ssr:!1},o),c),y=function(){},w=f.forwardRef(function(e,t){var n,r,o,i,l=f.useRef(null),a=f.useState({onScrollCapture:y,onWheelCapture:y,onTouchMoveCapture:y}),c=a[0],d=a[1],p=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,S=e.shards,R=e.sideCar,C=e.noRelative,A=e.noIsolation,T=e.inert,k=e.allowPinchZoom,L=e.as,P=e.gapMode,M=s(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),W=(n=[l,t],r=function(e){return n.forEach(function(t){return h(t,e)})},(o=(0,f.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,v(function(){var e=m.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||h(e,null)}),r.forEach(function(e){t.has(e)||h(e,o)})}m.set(i,n)},[n]),i),N=u(u({},M),c);return f.createElement(f.Fragment,null,E&&f.createElement(R,{sideCar:g,removeScrollBar:x,shards:S,noRelative:C,noIsolation:A,inert:T,setCallbacks:d,allowPinchZoom:!!k,lockRef:l,gapMode:P}),p?f.cloneElement(f.Children.only(w),u(u({},N),{ref:W})):f.createElement(void 0===L?"div":L,u({},N,{className:b,ref:W}),w))});w.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},w.classNames={fullWidth:p,zeroRight:d};var b=function(e){var t=e.sideCar,n=s(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return f.createElement(r,u({},n))};b.isSideCarExport=!0;var x=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},E=function(){var e=x();return function(t,n){f.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},S=function(){var e=E();return function(t){return e(t.styles,t.dynamic),null}},R={left:0,top:0,right:0,gap:0},C=function(e){return parseInt(e||"",10)||0},A=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[C(n),C(r),C(o)]},T=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return R;var t=A(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},k=S(),L="data-scroll-locked",P=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(L,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(d," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(p," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(d," .").concat(d," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(p," .").concat(p," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(L,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},M=function(){var e=parseInt(document.body.getAttribute(L)||"0",10);return isFinite(e)?e:0},W=function(){f.useEffect(function(){return document.body.setAttribute(L,(M()+1).toString()),function(){var e=M()-1;e<=0?document.body.removeAttribute(L):document.body.setAttribute(L,e.toString())}},[])},N=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;W();var i=f.useMemo(function(){return T(o)},[o]);return f.createElement(k,{styles:P(i,!t,o,n?"":"!important")})},O=!1;if("undefined"!=typeof window)try{var D=Object.defineProperty({},"passive",{get:function(){return O=!0,!0}});window.addEventListener("test",D,D),window.removeEventListener("test",D,D)}catch(e){O=!1}var j=!!O&&{passive:!1},H=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},_=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),z(e,r)){var o=Z(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},z=function(e,t){return"v"===e?H(t,"overflowY"):H(t,"overflowX")},Z=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,c=n.target,u=t.contains(c),s=!1,f=a>0,d=0,p=0;do{if(!c)break;var h=Z(e,c),v=h[0],m=h[1]-h[2]-l*v;(v||m)&&z(e,c)&&(d+=m,p+=v);var g=c.parentNode;c=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!u&&c!==document.body||u&&(t.contains(c)||t===c));return f&&(o&&1>Math.abs(d)||!o&&a>d)?s=!0:!f&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},I=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},V=function(e){return[e.deltaX,e.deltaY]},B=function(e){return e&&"current"in e?e.current:e},Y=0,X=[],$=(g.useMedium(function(e){var t=f.useRef([]),n=f.useRef([0,0]),r=f.useRef(),o=f.useState(Y++)[0],i=f.useState(S)[0],l=f.useRef(e);f.useEffect(function(){l.current=e},[e]),f.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(B),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=f.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=I(e),a=n.current,c="deltaX"in e?e.deltaX:a[0]-i[0],u="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,f=Math.abs(c)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=_(f,s);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=_(f,s)),!d)return!1;if(!r.current&&"changedTouches"in e&&(c||u)&&(r.current=o),!o)return!0;var p=r.current||o;return F(p,t,e,"h"===p?c:u,!0)},[]),c=f.useCallback(function(e){if(X.length&&X[X.length-1]===i){var n="deltaY"in e?V(e):I(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(B).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?a(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),u=f.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),s=f.useCallback(function(e){n.current=I(e),r.current=void 0},[]),d=f.useCallback(function(t){u(t.type,V(t),t.target,a(t,e.lockRef.current))},[]),p=f.useCallback(function(t){u(t.type,I(t),t.target,a(t,e.lockRef.current))},[]);f.useEffect(function(){return X.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",c,j),document.addEventListener("touchmove",c,j),document.addEventListener("touchstart",s,j),function(){X=X.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,j),document.removeEventListener("touchmove",c,j),document.removeEventListener("touchstart",s,j)}},[]);var h=e.removeScrollBar,v=e.inert;return f.createElement(f.Fragment,null,v?f.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?f.createElement(N,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),b),q=f.forwardRef(function(e,t){return f.createElement(w,u({},e,{ref:t,sideCar:$}))});q.classNames=w.classNames;var U=q},760:function(e,t,n){n.d(t,{u:function(){return r}});function r(e,[t,n]){return Math.min(n,Math.max(t,e))}},5400:function(e,t,n){n.d(t,{gm:function(){return i}});var r=n(2265);n(7437);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},1244:function(e,t,n){n.d(t,{EW:function(){return i}});var r=n(2265),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??l()),document.body.insertAdjacentElement("beforeend",e[1]??l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2759:function(e,t,n){let r;n.d(t,{M:function(){return d}});var o=n(2265),i=n(2210),l=n(9381),a=n(6459),c=n(7437),u="focusScope.autoFocusOnMount",s="focusScope.autoFocusOnUnmount",f={bubbles:!1,cancelable:!0},d=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:d,onUnmountAutoFocus:g,...y}=e,[w,b]=o.useState(null),x=(0,a.W)(d),E=(0,a.W)(g),S=o.useRef(null),R=(0,i.e)(t,e=>b(e)),C=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(C.paused||!w)return;let t=e.target;w.contains(t)?S.current=t:v(S.current,{select:!0})},t=function(e){if(C.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||v(S.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&v(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,w,C.paused]),o.useEffect(()=>{if(w){m.add(C);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,f);w.addEventListener(u,x),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(v(r,{select:t}),document.activeElement!==n)return}(p(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&v(w))}return()=>{w.removeEventListener(u,x),setTimeout(()=>{let t=new CustomEvent(s,f);w.addEventListener(s,E),w.dispatchEvent(t),t.defaultPrevented||v(e??document.body,{select:!0}),w.removeEventListener(s,E),m.remove(C)},0)}}},[w,x,E,C]);let A=o.useCallback(e=>{if(!n&&!r||C.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=p(e);return[h(t,e),h(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&v(i,{select:!0})):(e.preventDefault(),n&&v(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,C.paused]);return(0,c.jsx)(l.WV.div,{tabIndex:-1,...y,ref:R,onKeyDown:A})});function p(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function h(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function v(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var m=(r=[],{add(e){let t=r[0];e!==t&&t?.pause(),(r=g(r,e)).unshift(e)},remove(e){r=g(r,e),r[0]?.resume()}});function g(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},966:function(e,t,n){n.d(t,{M:function(){return c}});var r,o=n(2265),i=n(5655),l=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function c(e){let[t,n]=o.useState(l());return(0,i.b)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},3995:function(e,t,n){n.d(t,{ee:function(){return eK},Eh:function(){return eJ},VY:function(){return eG},fC:function(){return eU},D7:function(){return eN}});var r=n(2265);let o=["top","right","bottom","left"],i=Math.min,l=Math.max,a=Math.round,c=Math.floor,u=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function v(e){return"x"===e?"y":"x"}function m(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>f[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function b(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function E(e,t,n){let r,{reference:o,floating:i}=e,l=g(t),a=v(g(t)),c=m(a),u=p(t),s="y"===l,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,y=o[c]/2-i[c]/2;switch(u){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[a]-=y*(n&&s?-1:1);break;case"end":r[a]+=y*(n&&s?-1:1)}return r}let S=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),c=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:f}=E(u,r,c),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:v}=a[n],{x:m,y:g,data:y,reset:w}=await v({x:s,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});s=null!=m?m:s,f=null!=g?g:f,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(u=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:f}=E(u,d,c)),n=-1)}return{x:s,y:f,placement:d,strategy:o,middlewareData:p}};async function R(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:c}=e,{boundary:u="clippingAncestors",rootBoundary:s="viewport",elementContext:f="floating",altBoundary:p=!1,padding:h=0}=d(t,e),v=b(h),m=a[p?"floating"===f?"reference":"floating":f],g=x(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:s,strategy:c})),y="floating"===f?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),E=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},S=x(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:w,strategy:c}):y);return{top:(g.top-S.top+v.top)/E.y,bottom:(S.bottom-g.bottom+v.bottom)/E.y,left:(g.left-S.left+v.left)/E.x,right:(S.right-g.right+v.right)/E.x}}function C(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function A(e){return o.some(t=>e[t]>=0)}async function T(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=p(n),a=h(n),c="y"===g(n),u=["left","top"].includes(l)?-1:1,s=i&&c?-1:1,f=d(t,e),{mainAxis:v,crossAxis:m,alignmentAxis:y}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof y&&(m="end"===a?-1*y:y),c?{x:m*s,y:v*u}:{x:v*u,y:m*s}}function k(){return"undefined"!=typeof window}function L(e){return W(e)?(e.nodeName||"").toLowerCase():"#document"}function P(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function M(e){var t;return null==(t=(W(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function W(e){return!!k()&&(e instanceof Node||e instanceof P(e).Node)}function N(e){return!!k()&&(e instanceof Element||e instanceof P(e).Element)}function O(e){return!!k()&&(e instanceof HTMLElement||e instanceof P(e).HTMLElement)}function D(e){return!!k()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof P(e).ShadowRoot)}function j(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=F(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function H(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function _(e){let t=z(),n=N(e)?F(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function Z(e){return["html","body","#document"].includes(L(e))}function F(e){return P(e).getComputedStyle(e)}function I(e){return N(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function V(e){if("html"===L(e))return e;let t=e.assignedSlot||e.parentNode||D(e)&&e.host||M(e);return D(t)?t.host:t}function B(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=V(t);return Z(n)?t.ownerDocument?t.ownerDocument.body:t.body:O(n)&&j(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=P(o);if(i){let e=Y(l);return t.concat(l,l.visualViewport||[],j(o)?o:[],e&&n?B(e):[])}return t.concat(o,B(o,[],n))}function Y(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function X(e){let t=F(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=O(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,c=a(n)!==i||a(r)!==l;return c&&(n=i,r=l),{width:n,height:r,$:c}}function $(e){return N(e)?e:e.contextElement}function q(e){let t=$(e);if(!O(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=X(t),l=(i?a(n.width):n.width)/r,c=(i?a(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),c&&Number.isFinite(c)||(c=1),{x:l,y:c}}let U=u(0);function K(e){let t=P(e);return z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:U}function G(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=$(e),a=u(1);t&&(r?N(r)&&(a=q(r)):a=q(e));let c=(void 0===(o=n)&&(o=!1),r&&(!o||r===P(l))&&o)?K(l):u(0),s=(i.left+c.x)/a.x,f=(i.top+c.y)/a.y,d=i.width/a.x,p=i.height/a.y;if(l){let e=P(l),t=r&&N(r)?P(r):r,n=e,o=Y(n);for(;o&&r&&t!==n;){let e=q(o),t=o.getBoundingClientRect(),r=F(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,f*=e.y,d*=e.x,p*=e.y,s+=i,f+=l,o=Y(n=P(o))}}return x({width:d,height:p,x:s,y:f})}function J(e,t){let n=I(e).scrollLeft;return t?t.left+n:G(M(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=P(e),r=M(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,c=0;if(o){i=o.width,l=o.height;let e=z();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,c=o.offsetTop)}return{width:i,height:l,x:a,y:c}}(e,n);else if("document"===t)r=function(e){let t=M(e),n=I(e),r=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+J(e),c=-n.scrollTop;return"rtl"===F(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:c}}(M(e));else if(N(t))r=function(e,t){let n=G(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=O(e)?q(e):u(1),l=e.clientWidth*i.x;return{width:l,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{let n=K(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return x(r)}function et(e){return"static"===F(e).position}function en(e,t){if(!O(e)||"fixed"===F(e).position)return null;if(t)return t(e);let n=e.offsetParent;return M(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=P(e);if(H(e))return n;if(!O(e)){let t=V(e);for(;t&&!Z(t);){if(N(t)&&!et(t))return t;t=V(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(L(r))&&et(r);)r=en(r,t);return r&&Z(r)&&et(r)&&!_(r)?n:r||function(e){let t=V(e);for(;O(t)&&!Z(t);){if(_(t))return t;if(H(t))break;t=V(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=O(t),o=M(t),i="fixed"===n,l=G(e,!0,i,t),a={scrollLeft:0,scrollTop:0},c=u(0);if(r||!r&&!i){if(("body"!==L(t)||j(o))&&(a=I(t)),r){let e=G(t,!0,i,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else o&&(c.x=J(o))}i&&!r&&o&&(c.x=J(o));let s=!o||r||i?u(0):Q(o,a);return{x:l.left+a.scrollLeft-c.x-s.x,y:l.top+a.scrollTop-c.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=M(r),a=!!t&&H(t.floating);if(r===l||a&&i)return n;let c={scrollLeft:0,scrollTop:0},s=u(1),f=u(0),d=O(r);if((d||!d&&!i)&&(("body"!==L(r)||j(l))&&(c=I(r)),O(r))){let e=G(r);s=q(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let p=!l||d||i?u(0):Q(l,c,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-c.scrollLeft*s.x+f.x+p.x,y:n.y*s.y-c.scrollTop*s.y+f.y+p.y}},getDocumentElement:M,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,a=[..."clippingAncestors"===n?H(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=B(e,[],!1).filter(e=>N(e)&&"body"!==L(e)),o=null,i="fixed"===F(e).position,l=i?V(e):e;for(;N(l)&&!Z(l);){let t=F(l),n=_(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||j(l)&&!n&&function e(t,n){let r=V(t);return!(r===n||!N(r)||Z(r))&&("fixed"===F(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=V(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],c=a[0],u=a.reduce((e,n)=>{let r=ee(t,n,o);return e.top=l(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ee(t,c,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=X(e);return{width:t,height:n}},getScale:q,isElement:N,isRTL:function(e){return"rtl"===F(e).direction}};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:a,platform:c,elements:u,middlewareData:s}=t,{element:f,padding:p=0}=d(e,t)||{};if(null==f)return{};let y=b(p),w={x:n,y:r},x=v(g(o)),E=m(x),S=await c.getDimensions(f),R="y"===x,C=R?"clientHeight":"clientWidth",A=a.reference[E]+a.reference[x]-w[x]-a.floating[E],T=w[x]-a.reference[x],k=await (null==c.getOffsetParent?void 0:c.getOffsetParent(f)),L=k?k[C]:0;L&&await (null==c.isElement?void 0:c.isElement(k))||(L=u.floating[C]||a.floating[E]);let P=L/2-S[E]/2-1,M=i(y[R?"top":"left"],P),W=i(y[R?"bottom":"right"],P),N=L-S[E]-W,O=L/2-S[E]/2+(A/2-T/2),D=l(M,i(O,N)),j=!s.arrow&&null!=h(o)&&O!==D&&a.reference[E]/2-(O<M?M:W)-S[E]/2<0,H=j?O<M?O-M:O-N:0;return{[x]:w[x]+H,data:{[x]:D,centerOffset:O-D-H,...j&&{alignmentOffset:H}},reset:j}}}),ec=(e,t,n)=>{let r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return S(e,t,{...o,platform:i})};var eu=n(4887),es="undefined"!=typeof document?r.useLayoutEffect:function(){};function ef(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ef(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ef(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ed(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return es(()=>{t.current=e}),t}let ev=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),em=(e,t)=>{var n;return{...(void 0===(n=e)&&(n=0),{name:"offset",options:n,async fn(e){var t,r;let{x:o,y:i,placement:l,middlewareData:a}=e,c=await T(e,n);return l===(null==(t=a.offset)?void 0:t.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+c.x,y:i+c.y,data:{...c,placement:l}}}}),options:[e,t]}},eg=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"shift",options:n,async fn(e){let{x:t,y:r,placement:o}=e,{mainAxis:a=!0,crossAxis:c=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=d(n,e),f={x:t,y:r},h=await R(e,s),m=g(p(o)),y=v(m),w=f[y],b=f[m];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+h[e],r=w-h[t];w=l(n,i(w,r))}if(c){let e="y"===m?"top":"left",t="y"===m?"bottom":"right",n=b+h[e],r=b-h[t];b=l(n,i(b,r))}let x=u.fn({...e,[y]:w,[m]:b});return{...x,data:{x:x.x-t,y:x.y-r,enabled:{[y]:a,[m]:c}}}}}),options:[e,t]}},ey=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{options:n,fn(e){let{x:t,y:r,placement:o,rects:i,middlewareData:l}=e,{offset:a=0,mainAxis:c=!0,crossAxis:u=!0}=d(n,e),s={x:t,y:r},f=g(o),h=v(f),m=s[h],y=s[f],w=d(a,e),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(c){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+b.mainAxis,n=i.reference[h]+i.reference[e]-b.mainAxis;m<t?m=t:m>n&&(m=n)}if(u){var x,E;let e="y"===h?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(x=l.offset)?void 0:x[f])||0)+(t?0:b.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[f])||0)-(t?b.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:m,[f]:y}}}),options:[e,t]}},ew=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"flip",options:n,async fn(e){var t,r,o,i,l;let{placement:a,middlewareData:c,rects:u,initialPlacement:s,platform:f,elements:b}=e,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:S,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:A="none",flipAlignment:T=!0,...k}=d(n,e);if(null!=(t=c.arrow)&&t.alignmentOffset)return{};let L=p(a),P=g(s),M=p(s)===s,W=await (null==f.isRTL?void 0:f.isRTL(b.floating)),N=S||(M||!T?[w(s)]:function(e){let t=w(e);return[y(e),t,y(t)]}(s)),O="none"!==A;!S&&O&&N.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(y)))),i}(s,T,A,W));let D=[s,...N],j=await R(e,k),H=[],_=(null==(r=c.flip)?void 0:r.overflows)||[];if(x&&H.push(j[L]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=v(g(e)),i=m(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=w(l)),[l,w(l)]}(a,u,W);H.push(j[e[0]],j[e[1]])}if(_=[..._,{placement:a,overflows:H}],!H.every(e=>e<=0)){let e=((null==(o=c.flip)?void 0:o.index)||0)+1,t=D[e];if(t&&(!("alignment"===E&&P!==g(t))||_.every(e=>e.overflows[0]>0&&g(e.placement)===P)))return{data:{index:e,overflows:_},reset:{placement:t}};let n=null==(i=_.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(C){case"bestFit":{let e=null==(l=_.filter(e=>{if(O){let t=g(e.placement);return t===P||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}),options:[e,t]}},eb=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"size",options:n,async fn(e){var t,r;let o,a;let{placement:c,rects:u,platform:s,elements:f}=e,{apply:v=()=>{},...m}=d(n,e),y=await R(e,m),w=p(c),b=h(c),x="y"===g(c),{width:E,height:S}=u.floating;"top"===w||"bottom"===w?(o=w,a=b===(await (null==s.isRTL?void 0:s.isRTL(f.floating))?"start":"end")?"left":"right"):(a=w,o="end"===b?"top":"bottom");let C=S-y.top-y.bottom,A=E-y.left-y.right,T=i(S-y[o],C),k=i(E-y[a],A),L=!e.middlewareData.shift,P=T,M=k;if(null!=(t=e.middlewareData.shift)&&t.enabled.x&&(M=A),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(P=C),L&&!b){let e=l(y.left,0),t=l(y.right,0),n=l(y.top,0),r=l(y.bottom,0);x?M=E-2*(0!==e||0!==t?e+t:l(y.left,y.right)):P=S-2*(0!==n||0!==r?n+r:l(y.top,y.bottom))}await v({...e,availableWidth:M,availableHeight:P});let W=await s.getDimensions(f.floating);return E!==W.width||S!==W.height?{reset:{rects:!0}}:{}}}),options:[e,t]}},ex=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"hide",options:n,async fn(e){let{rects:t}=e,{strategy:r="referenceHidden",...o}=d(n,e);switch(r){case"referenceHidden":{let n=C(await R(e,{...o,elementContext:"reference"}),t.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:A(n)}}}case"escaped":{let n=C(await R(e,{...o,altBoundary:!0}),t.floating);return{data:{escapedOffsets:n,escaped:A(n)}}}default:return{}}}}),options:[e,t]}},eE=(e,t)=>({...ev(e),options:[e,t]});var eS=n(9381),eR=n(7437),eC=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eR.jsx)(eS.WV.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eR.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eC.displayName="Arrow";var eA=n(2210),eT=n(6989),ek=n(6459),eL=n(5655),eP=n(4977),eM="Popper",[eW,eN]=(0,eT.b)(eM),[eO,eD]=eW(eM),ej=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eR.jsx)(eO,{scope:t,anchor:o,onAnchorChange:i,children:n})};ej.displayName=eM;var eH="PopperAnchor",e_=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,l=eD(eH,n),a=r.useRef(null),c=(0,eA.e)(t,a);return r.useEffect(()=>{l.onAnchorChange(o?.current||a.current)}),o?null:(0,eR.jsx)(eS.WV.div,{...i,ref:c})});e_.displayName=eH;var ez="PopperContent",[eZ,eF]=eW(ez),eI=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:a=0,align:u="center",alignOffset:s=0,arrowPadding:f=0,avoidCollisions:d=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:v="partial",hideWhenDetached:m=!1,updatePositionStrategy:g="optimized",onPlaced:y,...w}=e,b=eD(ez,n),[x,E]=r.useState(null),S=(0,eA.e)(t,e=>E(e)),[R,C]=r.useState(null),A=(0,eP.t)(R),T=A?.width??0,k=A?.height??0,L="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},P=Array.isArray(p)?p:[p],W=P.length>0,N={padding:L,boundary:P.filter(eX),altBoundary:W},{refs:O,floatingStyles:D,placement:j,isPositioned:H,middlewareData:_}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:l,floating:a}={},transform:c=!0,whileElementsMounted:u,open:s}=e,[f,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);ef(p,o)||h(o);let[v,m]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==S.current&&(S.current=e,m(e))},[]),b=r.useCallback(e=>{e!==R.current&&(R.current=e,y(e))},[]),x=l||v,E=a||g,S=r.useRef(null),R=r.useRef(null),C=r.useRef(f),A=null!=u,T=eh(u),k=eh(i),L=eh(s),P=r.useCallback(()=>{if(!S.current||!R.current)return;let e={placement:t,strategy:n,middleware:p};k.current&&(e.platform=k.current),ec(S.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==L.current};M.current&&!ef(C.current,t)&&(C.current=t,eu.flushSync(()=>{d(t)}))})},[p,t,n,k,L]);es(()=>{!1===s&&C.current.isPositioned&&(C.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);let M=r.useRef(!1);es(()=>(M.current=!0,()=>{M.current=!1}),[]),es(()=>{if(x&&(S.current=x),E&&(R.current=E),x&&E){if(T.current)return T.current(x,E,P);P()}},[x,E,P,T,A]);let W=r.useMemo(()=>({reference:S,floating:R,setReference:w,setFloating:b}),[w,b]),N=r.useMemo(()=>({reference:x,floating:E}),[x,E]),O=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!N.floating)return e;let t=ep(N.floating,f.x),r=ep(N.floating,f.y);return c?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(N.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,c,N.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:P,refs:W,elements:N,floatingStyles:O}),[f,P,W,N,O])}({strategy:"fixed",placement:o+("center"!==u?"-"+u:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=$(e),h=a||u?[...p?B(p):[],...B(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let v=p&&f?function(e,t){let n,r=null,o=M(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function u(s,f){void 0===s&&(s=!1),void 0===f&&(f=1),a();let d=e.getBoundingClientRect(),{left:p,top:h,width:v,height:m}=d;if(s||t(),!v||!m)return;let g=c(h),y=c(o.clientWidth-(p+v)),w={rootMargin:-g+"px "+-y+"px "+-c(o.clientHeight-(h+m))+"px "+-c(p)+"px",threshold:l(0,i(1,f))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==f){if(!b)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||el(d,e.getBoundingClientRect())||u(),b=!1}try{r=new IntersectionObserver(x,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),a}(p,n):null,m=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!d&&g.observe(p),g.observe(t));let y=d?G(e):null;return d&&function t(){let r=G(e);y&&!el(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==v||v(),null==(e=g)||e.disconnect(),g=null,d&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===g}),elements:{reference:b.anchor},middleware:[em({mainAxis:a+k,alignmentAxis:s}),d&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===v?ey():void 0,...N}),d&&ew({...N}),eb({...N,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),R&&eE({element:R,padding:f}),e$({arrowWidth:T,arrowHeight:k}),m&&ex({strategy:"referenceHidden",...N})]}),[z,Z]=eq(j),F=(0,ek.W)(y);(0,eL.b)(()=>{H&&F?.()},[H,F]);let I=_.arrow?.x,V=_.arrow?.y,Y=_.arrow?.centerOffset!==0,[X,q]=r.useState();return(0,eL.b)(()=>{x&&q(window.getComputedStyle(x).zIndex)},[x]),(0,eR.jsx)("div",{ref:O.setFloating,"data-radix-popper-content-wrapper":"",style:{...D,transform:H?D.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:X,"--radix-popper-transform-origin":[_.transformOrigin?.x,_.transformOrigin?.y].join(" "),..._.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eR.jsx)(eZ,{scope:n,placedSide:z,onArrowChange:C,arrowX:I,arrowY:V,shouldHideArrow:Y,children:(0,eR.jsx)(eS.WV.div,{"data-side":z,"data-align":Z,...w,ref:S,style:{...w.style,animation:H?void 0:"none"}})})})});eI.displayName=ez;var eV="PopperArrow",eB={top:"bottom",right:"left",bottom:"top",left:"right"},eY=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eF(eV,n),i=eB[o.placedSide];return(0,eR.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eR.jsx)(eC,{...r,ref:t,style:{...r.style,display:"block"}})})});function eX(e){return null!==e}eY.displayName=eV;var e$=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[c,u]=eq(n),s={start:"0%",center:"50%",end:"100%"}[u],f=(o.arrow?.x??0)+l/2,d=(o.arrow?.y??0)+a/2,p="",h="";return"bottom"===c?(p=i?s:`${f}px`,h=`${-a}px`):"top"===c?(p=i?s:`${f}px`,h=`${r.floating.height+a}px`):"right"===c?(p=`${-a}px`,h=i?s:`${d}px`):"left"===c&&(p=`${r.floating.width+a}px`,h=i?s:`${d}px`),{data:{x:p,y:h}}}});function eq(e){let[t,n="center"]=e.split("-");return[t,n]}var eU=ej,eK=e_,eG=eI,eJ=eY},5331:function(e,t,n){n.d(t,{Ns:function(){return U},fC:function(){return $},gb:function(){return S},l_:function(){return q},q4:function(){return O}});var r=n(2265),o=n(9381),i=n(5606),l=n(6989),a=n(2210),c=n(6459),u=n(5400),s=n(5655),f=n(760),d=n(5744),p=n(7437),h="ScrollArea",[v,m]=(0,l.b)(h),[g,y]=v(h),w=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,type:i="hover",dir:l,scrollHideDelay:c=600,...s}=e,[f,d]=r.useState(null),[h,v]=r.useState(null),[m,y]=r.useState(null),[w,b]=r.useState(null),[x,E]=r.useState(null),[S,R]=r.useState(0),[C,A]=r.useState(0),[T,k]=r.useState(!1),[L,P]=r.useState(!1),M=(0,a.e)(t,e=>d(e)),W=(0,u.gm)(l);return(0,p.jsx)(g,{scope:n,type:i,dir:W,scrollHideDelay:c,scrollArea:f,viewport:h,onViewportChange:v,content:m,onContentChange:y,scrollbarX:w,onScrollbarXChange:b,scrollbarXEnabled:T,onScrollbarXEnabledChange:k,scrollbarY:x,onScrollbarYChange:E,scrollbarYEnabled:L,onScrollbarYEnabledChange:P,onCornerWidthChange:R,onCornerHeightChange:A,children:(0,p.jsx)(o.WV.div,{dir:W,...s,ref:M,style:{position:"relative","--radix-scroll-area-corner-width":S+"px","--radix-scroll-area-corner-height":C+"px",...e.style}})})});w.displayName=h;var b="ScrollAreaViewport",x=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,children:i,nonce:l,...c}=e,u=y(b,n),s=r.useRef(null),f=(0,a.e)(t,s,u.onViewportChange);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,p.jsx)(o.WV.div,{"data-radix-scroll-area-viewport":"",...c,ref:f,style:{overflowX:u.scrollbarXEnabled?"scroll":"hidden",overflowY:u.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,p.jsx)("div",{ref:u.onContentChange,style:{minWidth:"100%",display:"table"},children:i})})]})});x.displayName=b;var E="ScrollAreaScrollbar",S=r.forwardRef((e,t)=>{let{forceMount:n,...o}=e,i=y(E,e.__scopeScrollArea),{onScrollbarXEnabledChange:l,onScrollbarYEnabledChange:a}=i,c="horizontal"===e.orientation;return r.useEffect(()=>(c?l(!0):a(!0),()=>{c?l(!1):a(!1)}),[c,l,a]),"hover"===i.type?(0,p.jsx)(R,{...o,ref:t,forceMount:n}):"scroll"===i.type?(0,p.jsx)(C,{...o,ref:t,forceMount:n}):"auto"===i.type?(0,p.jsx)(A,{...o,ref:t,forceMount:n}):"always"===i.type?(0,p.jsx)(T,{...o,ref:t}):null});S.displayName=E;var R=r.forwardRef((e,t)=>{let{forceMount:n,...o}=e,l=y(E,e.__scopeScrollArea),[a,c]=r.useState(!1);return r.useEffect(()=>{let e=l.scrollArea,t=0;if(e){let n=()=>{window.clearTimeout(t),c(!0)},r=()=>{t=window.setTimeout(()=>c(!1),l.scrollHideDelay)};return e.addEventListener("pointerenter",n),e.addEventListener("pointerleave",r),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",n),e.removeEventListener("pointerleave",r)}}},[l.scrollArea,l.scrollHideDelay]),(0,p.jsx)(i.z,{present:n||a,children:(0,p.jsx)(A,{"data-state":a?"visible":"hidden",...o,ref:t})})}),C=r.forwardRef((e,t)=>{var n;let{forceMount:o,...l}=e,a=y(E,e.__scopeScrollArea),c="horizontal"===e.orientation,u=Y(()=>f("SCROLL_END"),100),[s,f]=(n={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},r.useReducer((e,t)=>n[e][t]??e,"hidden"));return r.useEffect(()=>{if("idle"===s){let e=window.setTimeout(()=>f("HIDE"),a.scrollHideDelay);return()=>window.clearTimeout(e)}},[s,a.scrollHideDelay,f]),r.useEffect(()=>{let e=a.viewport,t=c?"scrollLeft":"scrollTop";if(e){let n=e[t],r=()=>{let r=e[t];n!==r&&(f("SCROLL"),u()),n=r};return e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[a.viewport,c,f,u]),(0,p.jsx)(i.z,{present:o||"hidden"!==s,children:(0,p.jsx)(T,{"data-state":"hidden"===s?"hidden":"visible",...l,ref:t,onPointerEnter:(0,d.M)(e.onPointerEnter,()=>f("POINTER_ENTER")),onPointerLeave:(0,d.M)(e.onPointerLeave,()=>f("POINTER_LEAVE"))})})}),A=r.forwardRef((e,t)=>{let n=y(E,e.__scopeScrollArea),{forceMount:o,...l}=e,[a,c]=r.useState(!1),u="horizontal"===e.orientation,s=Y(()=>{if(n.viewport){let e=n.viewport.offsetWidth<n.viewport.scrollWidth,t=n.viewport.offsetHeight<n.viewport.scrollHeight;c(u?e:t)}},10);return X(n.viewport,s),X(n.content,s),(0,p.jsx)(i.z,{present:o||a,children:(0,p.jsx)(T,{"data-state":a?"visible":"hidden",...l,ref:t})})}),T=r.forwardRef((e,t)=>{let{orientation:n="vertical",...o}=e,i=y(E,e.__scopeScrollArea),l=r.useRef(null),a=r.useRef(0),[c,u]=r.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),s=Z(c.viewport,c.content),f={...o,sizes:c,onSizesChange:u,hasThumb:!!(s>0&&s<1),onThumbChange:e=>l.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function d(e,t){return function(e,t,n,r="ltr"){let o=F(n),i=t||o/2,l=n.scrollbar.paddingStart+i,a=n.scrollbar.size-n.scrollbar.paddingEnd-(o-i),c=n.content-n.viewport;return V([l,a],"ltr"===r?[0,c]:[-1*c,0])(e)}(e,a.current,c,t)}return"horizontal"===n?(0,p.jsx)(k,{...f,ref:t,onThumbPositionChange:()=>{if(i.viewport&&l.current){let e=I(i.viewport.scrollLeft,c,i.dir);l.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollLeft=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollLeft=d(e,i.dir))}}):"vertical"===n?(0,p.jsx)(L,{...f,ref:t,onThumbPositionChange:()=>{if(i.viewport&&l.current){let e=I(i.viewport.scrollTop,c);l.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollTop=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollTop=d(e))}}):null}),k=r.forwardRef((e,t)=>{let{sizes:n,onSizesChange:o,...i}=e,l=y(E,e.__scopeScrollArea),[c,u]=r.useState(),s=r.useRef(null),f=(0,a.e)(t,s,l.onScrollbarXChange);return r.useEffect(()=>{s.current&&u(getComputedStyle(s.current))},[s]),(0,p.jsx)(W,{"data-orientation":"horizontal",...i,ref:f,sizes:n,style:{bottom:0,left:"rtl"===l.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===l.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":F(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,n)=>{if(l.viewport){let r=l.viewport.scrollLeft+t.deltaX;e.onWheelScroll(r),r>0&&r<n&&t.preventDefault()}},onResize:()=>{s.current&&l.viewport&&c&&o({content:l.viewport.scrollWidth,viewport:l.viewport.offsetWidth,scrollbar:{size:s.current.clientWidth,paddingStart:z(c.paddingLeft),paddingEnd:z(c.paddingRight)}})}})}),L=r.forwardRef((e,t)=>{let{sizes:n,onSizesChange:o,...i}=e,l=y(E,e.__scopeScrollArea),[c,u]=r.useState(),s=r.useRef(null),f=(0,a.e)(t,s,l.onScrollbarYChange);return r.useEffect(()=>{s.current&&u(getComputedStyle(s.current))},[s]),(0,p.jsx)(W,{"data-orientation":"vertical",...i,ref:f,sizes:n,style:{top:0,right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":F(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,n)=>{if(l.viewport){let r=l.viewport.scrollTop+t.deltaY;e.onWheelScroll(r),r>0&&r<n&&t.preventDefault()}},onResize:()=>{s.current&&l.viewport&&c&&o({content:l.viewport.scrollHeight,viewport:l.viewport.offsetHeight,scrollbar:{size:s.current.clientHeight,paddingStart:z(c.paddingTop),paddingEnd:z(c.paddingBottom)}})}})}),[P,M]=v(E),W=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,sizes:i,hasThumb:l,onThumbChange:u,onThumbPointerUp:s,onThumbPointerDown:f,onThumbPositionChange:h,onDragScroll:v,onWheelScroll:m,onResize:g,...w}=e,b=y(E,n),[x,S]=r.useState(null),R=(0,a.e)(t,e=>S(e)),C=r.useRef(null),A=r.useRef(""),T=b.viewport,k=i.content-i.viewport,L=(0,c.W)(m),M=(0,c.W)(h),W=Y(g,10);function N(e){C.current&&v({x:e.clientX-C.current.left,y:e.clientY-C.current.top})}return r.useEffect(()=>{let e=e=>{let t=e.target;x?.contains(t)&&L(e,k)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[T,x,k,L]),r.useEffect(M,[i,M]),X(x,W),X(b.content,W),(0,p.jsx)(P,{scope:n,scrollbar:x,hasThumb:l,onThumbChange:(0,c.W)(u),onThumbPointerUp:(0,c.W)(s),onThumbPositionChange:M,onThumbPointerDown:(0,c.W)(f),children:(0,p.jsx)(o.WV.div,{...w,ref:R,style:{position:"absolute",...w.style},onPointerDown:(0,d.M)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),C.current=x.getBoundingClientRect(),A.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",b.viewport&&(b.viewport.style.scrollBehavior="auto"),N(e))}),onPointerMove:(0,d.M)(e.onPointerMove,N),onPointerUp:(0,d.M)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=A.current,b.viewport&&(b.viewport.style.scrollBehavior=""),C.current=null})})})}),N="ScrollAreaThumb",O=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=M(N,e.__scopeScrollArea);return(0,p.jsx)(i.z,{present:n||o.hasThumb,children:(0,p.jsx)(D,{ref:t,...r})})}),D=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,style:i,...l}=e,c=y(N,n),u=M(N,n),{onThumbPositionChange:s}=u,f=(0,a.e)(t,e=>u.onThumbChange(e)),h=r.useRef(void 0),v=Y(()=>{h.current&&(h.current(),h.current=void 0)},100);return r.useEffect(()=>{let e=c.viewport;if(e){let t=()=>{if(v(),!h.current){let t=B(e,s);h.current=t,s()}};return s(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[c.viewport,v,s]),(0,p.jsx)(o.WV.div,{"data-state":u.hasThumb?"visible":"hidden",...l,ref:f,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:(0,d.M)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),n=e.clientX-t.left,r=e.clientY-t.top;u.onThumbPointerDown({x:n,y:r})}),onPointerUp:(0,d.M)(e.onPointerUp,u.onThumbPointerUp)})});O.displayName=N;var j="ScrollAreaCorner",H=r.forwardRef((e,t)=>{let n=y(j,e.__scopeScrollArea),r=!!(n.scrollbarX&&n.scrollbarY);return"scroll"!==n.type&&r?(0,p.jsx)(_,{...e,ref:t}):null});H.displayName=j;var _=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,...i}=e,l=y(j,n),[a,c]=r.useState(0),[u,s]=r.useState(0),f=!!(a&&u);return X(l.scrollbarX,()=>{let e=l.scrollbarX?.offsetHeight||0;l.onCornerHeightChange(e),s(e)}),X(l.scrollbarY,()=>{let e=l.scrollbarY?.offsetWidth||0;l.onCornerWidthChange(e),c(e)}),f?(0,p.jsx)(o.WV.div,{...i,ref:t,style:{width:a,height:u,position:"absolute",right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:0,...e.style}}):null});function z(e){return e?parseInt(e,10):0}function Z(e,t){let n=e/t;return isNaN(n)?0:n}function F(e){let t=Z(e.viewport,e.content),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-n)*t,18)}function I(e,t,n="ltr"){let r=F(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,i=t.scrollbar.size-o,l=t.content-t.viewport,a=(0,f.u)(e,"ltr"===n?[0,l]:[-1*l,0]);return V([0,l],[0,i-r])(a)}function V(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}var B=(e,t=()=>{})=>{let n={left:e.scrollLeft,top:e.scrollTop},r=0;return!function o(){let i={left:e.scrollLeft,top:e.scrollTop},l=n.left!==i.left,a=n.top!==i.top;(l||a)&&t(),n=i,r=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(r)};function Y(e,t){let n=(0,c.W)(e),o=r.useRef(0);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),r.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(n,t)},[n,t])}function X(e,t){let n=(0,c.W)(t);(0,s.b)(()=>{let t=0;if(e){let r=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(n)});return r.observe(e),()=>{window.cancelAnimationFrame(t),r.unobserve(e)}}},[e,n])}var $=w,q=x,U=H},4977:function(e,t,n){n.d(t,{t:function(){return i}});var r=n(2265),o=n(5655);function i(e){let[t,n]=r.useState(void 0);return(0,o.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}}}]);