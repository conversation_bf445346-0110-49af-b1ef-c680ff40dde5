"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[936],{5744:function(e,t,n){n.d(t,{M:function(){return r}});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},7733:function(e,t,n){n.d(t,{B:function(){return l}});var r=n(2265),o=n(6989),u=n(2210),i=n(7256),s=n(7437);function l(e){let t=e+"CollectionProvider",[n,l]=(0,o.b)(t),[a,c]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:n}=e,o=r.useRef(null),u=r.useRef(new Map).current;return(0,s.jsx)(a,{scope:t,itemMap:u,collectionRef:o,children:n})};d.displayName=t;let f=e+"CollectionSlot",m=(0,i.Z8)(f),v=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=c(f,n),i=(0,u.e)(t,o.collectionRef);return(0,s.jsx)(m,{ref:i,children:r})});v.displayName=f;let p=e+"CollectionItemSlot",E="data-radix-collection-item",y=(0,i.Z8)(p),b=r.forwardRef((e,t)=>{let{scope:n,children:o,...i}=e,l=r.useRef(null),a=(0,u.e)(t,l),d=c(p,n);return r.useEffect(()=>(d.itemMap.set(l,{ref:l,...i}),()=>void d.itemMap.delete(l))),(0,s.jsx)(y,{[E]:"",ref:a,children:o})});return b.displayName=p,[{Provider:d,Slot:v,ItemSlot:b},function(t){let n=c(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${E}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},l]}},6989:function(e,t,n){n.d(t,{b:function(){return u}});var r=n(2265),o=n(7437);function u(e,t=[]){let n=[],u=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return u.scopeName=e,[function(t,u){let i=r.createContext(u),s=n.length;n=[...n,u];let l=t=>{let{scope:n,children:u,...l}=t,a=n?.[e]?.[s]||i,c=r.useMemo(()=>l,Object.values(l));return(0,o.jsx)(a.Provider,{value:c,children:u})};return l.displayName=t+"Provider",[l,function(n,o){let l=o?.[e]?.[s]||i,a=r.useContext(l);if(a)return a;if(void 0!==u)return u;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(u,...t)]}},9249:function(e,t,n){n.d(t,{I0:function(){return y},XB:function(){return f},fC:function(){return E}});var r,o=n(2265),u=n(5744),i=n(9381),s=n(2210),l=n(6459),a=n(7437),c="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:f,onPointerDownOutside:m,onFocusOutside:E,onInteractOutside:y,onDismiss:b,...w}=e,N=o.useContext(d),[h,C]=o.useState(null),g=h?.ownerDocument??globalThis?.document,[,O]=o.useState({}),M=(0,s.e)(t,e=>C(e)),R=Array.from(N.layers),[P]=[...N.layersWithOutsidePointerEventsDisabled].slice(-1),T=R.indexOf(P),L=h?R.indexOf(h):-1,D=N.layersWithOutsidePointerEventsDisabled.size>0,S=L>=T,x=function(e,t=globalThis?.document){let n=(0,l.W)(e),r=o.useRef(!1),u=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){p("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",u.current),u.current=r,t.addEventListener("click",u.current,{once:!0})):r()}else t.removeEventListener("click",u.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",u.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...N.branches].some(e=>e.contains(t));!S||n||(m?.(e),y?.(e),e.defaultPrevented||b?.())},g),W=function(e,t=globalThis?.document){let n=(0,l.W)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&p("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;[...N.branches].some(e=>e.contains(t))||(E?.(e),y?.(e),e.defaultPrevented||b?.())},g);return!function(e,t=globalThis?.document){let n=(0,l.W)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{L!==N.layers.size-1||(f?.(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},g),o.useEffect(()=>{if(h)return n&&(0===N.layersWithOutsidePointerEventsDisabled.size&&(r=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),N.layersWithOutsidePointerEventsDisabled.add(h)),N.layers.add(h),v(),()=>{n&&1===N.layersWithOutsidePointerEventsDisabled.size&&(g.body.style.pointerEvents=r)}},[h,g,n,N]),o.useEffect(()=>()=>{h&&(N.layers.delete(h),N.layersWithOutsidePointerEventsDisabled.delete(h),v())},[h,N]),o.useEffect(()=>{let e=()=>O({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,a.jsx)(i.WV.div,{...w,ref:M,style:{pointerEvents:D?S?"auto":"none":void 0,...e.style},onFocusCapture:(0,u.M)(e.onFocusCapture,W.onFocusCapture),onBlurCapture:(0,u.M)(e.onBlurCapture,W.onBlurCapture),onPointerDownCapture:(0,u.M)(e.onPointerDownCapture,x.onPointerDownCapture)})});f.displayName="DismissableLayer";var m=o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),u=(0,s.e)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,a.jsx)(i.WV.div,{...e,ref:u})});function v(){let e=new CustomEvent(c);document.dispatchEvent(e)}function p(e,t,n,{discrete:r}){let o=n.originalEvent.target,u=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,i.jH)(o,u):o.dispatchEvent(u)}m.displayName="DismissableLayerBranch";var E=f,y=m},2730:function(e,t,n){n.d(t,{h:function(){return l}});var r=n(2265),o=n(4887),u=n(9381),i=n(5655),s=n(7437),l=r.forwardRef((e,t)=>{let{container:n,...l}=e,[a,c]=r.useState(!1);(0,i.b)(()=>c(!0),[]);let d=n||a&&globalThis?.document?.body;return d?o.createPortal((0,s.jsx)(u.WV.div,{...l,ref:t}),d):null});l.displayName="Portal"},5606:function(e,t,n){n.d(t,{z:function(){return i}});var r=n(2265),o=n(2210),u=n(5655),i=e=>{let t,n;let{present:i,children:l}=e,a=function(e){var t,n;let[o,i]=r.useState(),l=r.useRef(null),a=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=s(l.current);c.current="mounted"===d?e:"none"},[d]),(0,u.b)(()=>{let t=l.current,n=a.current;if(n!==e){let r=c.current,o=s(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),a.current=e}},[e,f]),(0,u.b)(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,n=n=>{let r=s(l.current).includes(n.animationName);if(n.target===o&&r&&(f("ANIMATION_END"),!a.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(c.current=s(l.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{l.current=e?getComputedStyle(e):null,i(e)},[])}}(i),c="function"==typeof l?l({present:a.isPresent}):r.Children.only(l),d=(0,o.e)(a.ref,(t=Object.getOwnPropertyDescriptor(c.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?c.ref:(t=Object.getOwnPropertyDescriptor(c,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?c.props.ref:c.props.ref||c.ref);return"function"==typeof l||a.isPresent?r.cloneElement(c,{ref:d}):null};function s(e){return e?.animationName||"none"}i.displayName="Presence"},6459:function(e,t,n){n.d(t,{W:function(){return o}});var r=n(2265);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},3763:function(e,t,n){n.d(t,{T:function(){return s}});var r,o=n(2265),u=n(5655),i=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||u.b;function s({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[u,s,l]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),u=o.useRef(n),s=o.useRef(t);return i(()=>{s.current=t},[t]),o.useEffect(()=>{u.current!==n&&(s.current?.(n),u.current=n)},[n,u]),[n,r,s]}({defaultProp:t,onChange:n}),a=void 0!==e,c=a?e:u;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==a){let t=a?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=a},[a,r])}return[c,o.useCallback(t=>{if(a){let n="function"==typeof t?t(e):t;n!==e&&l.current?.(n)}else s(t)},[a,e,s,l])]}Symbol("RADIX:SYNC_STATE")},5655:function(e,t,n){n.d(t,{b:function(){return o}});var r=n(2265),o=globalThis?.document?r.useLayoutEffect:()=>{}}}]);