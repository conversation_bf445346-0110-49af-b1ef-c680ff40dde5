(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[98],{8572:function(e,s,a){Promise.resolve().then(a.bind(a,7401))},7401:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return b}});var r=a(7437),l=a(2265),t=a(2749),n=a(4033),i=a(1396),c=a.n(i),d=a(575),o=a(2782),u=a(5671),m=a(5005),h=a(7257),x=a(9069),j=a(4907),p=a(1295),f=a(3298),N=a(7216),g=a(9670),v=a(519);function b(){let[e,s]=(0,l.useState)(""),[a,i]=(0,l.useState)(""),[b,y]=(0,l.useState)(!1),[w,k]=(0,l.useState)(!1),[S,C]=(0,l.useState)(""),Z=(0,n.useRouter)(),_=async s=>{s.preventDefault(),k(!0),C("");try{let s=await (0,t.signIn)("credentials",{email:e,password:a,redirect:!1});(null==s?void 0:s.error)?C("Invalid email or password"):Z.push("/dashboard")}catch(e){C("An error occurred. Please try again.")}finally{k(!1)}},E=async e=>{k(!0);try{await (0,t.signIn)(e,{callbackUrl:"/dashboard"})}catch(e){C("An error occurred. Please try again."),k(!1)}};return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:(0,r.jsxs)(u.Zb,{className:"w-full max-w-md",children:[(0,r.jsxs)(u.Ol,{className:"text-center",children:[(0,r.jsx)("div",{className:"flex justify-center mb-4",children:(0,r.jsx)("div",{className:"h-12 w-12 bg-primary rounded-lg flex items-center justify-center",children:(0,r.jsx)(j.Z,{className:"h-6 w-6 text-primary-foreground"})})}),(0,r.jsx)(u.ll,{className:"text-2xl font-bold",children:"Welcome back"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Sign in to your QueryCraft Studio account"})]}),(0,r.jsxs)(u.aY,{className:"space-y-4",children:[S&&(0,r.jsx)(x.bZ,{variant:"destructive",children:(0,r.jsx)(x.X,{children:S})}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)(d.z,{variant:"outline",className:"w-full",onClick:()=>E("google"),disabled:w,children:[(0,r.jsx)(p.Z,{className:"h-4 w-4 mr-2"}),"Continue with Google"]}),(0,r.jsxs)(d.z,{variant:"outline",className:"w-full",onClick:()=>E("github"),disabled:w,children:[(0,r.jsx)(f.Z,{className:"h-4 w-4 mr-2"}),"Continue with GitHub"]})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)(h.Z,{className:"w-full"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,r.jsx)("span",{className:"bg-background px-2 text-muted-foreground",children:"Or continue with email"})})]}),(0,r.jsxs)("form",{onSubmit:_,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(m._,{htmlFor:"email",children:"Email"}),(0,r.jsx)(o.I,{id:"email",type:"email",placeholder:"Enter your email",value:e,onChange:e=>s(e.target.value),required:!0,disabled:w})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(m._,{htmlFor:"password",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o.I,{id:"password",type:b?"text":"password",placeholder:"Enter your password",value:a,onChange:e=>i(e.target.value),required:!0,disabled:w}),(0,r.jsx)(d.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>y(!b),disabled:w,children:b?(0,r.jsx)(N.Z,{className:"h-4 w-4"}):(0,r.jsx)(g.Z,{className:"h-4 w-4"})})]})]}),(0,r.jsx)(d.z,{type:"submit",className:"w-full",disabled:w,children:w?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(v.Z,{className:"h-4 w-4 mr-2 animate-spin"}),"Signing in..."]}):"Sign in"})]}),(0,r.jsxs)("div",{className:"text-center text-sm",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Don't have an account? "}),(0,r.jsx)(c(),{href:"/auth/signup",className:"text-primary hover:underline",children:"Sign up"})]}),(0,r.jsx)("div",{className:"text-center text-sm",children:(0,r.jsx)(c(),{href:"/",className:"text-muted-foreground hover:underline",children:"← Back to home"})})]})]})})}}},function(e){e.O(0,[432,749,250,972,971,938,744],function(){return e(e.s=8572)}),_N_E=e.O()}]);