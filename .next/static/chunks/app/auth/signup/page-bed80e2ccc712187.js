(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[271],{2442:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(2898).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},3412:function(e,s,a){Promise.resolve().then(a.bind(a,9971))},9971:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return v}});var t=a(7437),r=a(2265),l=a(2749),n=a(4033),i=a(1396),c=a.n(i),d=a(575),o=a(2782),u=a(5671),m=a(5005),h=a(7257),x=a(9069),j=a(2442),f=a(519),p=a(4907),N=a(1295),g=a(3298),y=a(7216),b=a(9670);function v(){let[e,s]=(0,r.useState)(""),[a,i]=(0,r.useState)(""),[v,w]=(0,r.useState)(""),[C,k]=(0,r.useState)(""),[Z,S]=(0,r.useState)(!1),[_,P]=(0,r.useState)(!1),[E,F]=(0,r.useState)(""),[I,O]=(0,r.useState)(!1),z=(0,n.useRouter)(),A=async s=>{if(s.preventDefault(),P(!0),F(""),v!==C){F("Passwords do not match"),P(!1);return}try{let s=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e,email:a,password:v})}),t=await s.json();if(!s.ok){F(t.error||"An error occurred");return}O(!0),setTimeout(async()=>{let e=await (0,l.signIn)("credentials",{email:a,password:v,redirect:!1});(null==e?void 0:e.ok)&&z.push("/dashboard")},1500)}catch(e){F("An error occurred. Please try again.")}finally{P(!1)}},q=async e=>{P(!0);try{await (0,l.signIn)(e,{callbackUrl:"/dashboard"})}catch(e){F("An error occurred. Please try again."),P(!1)}};return I?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:(0,t.jsx)(u.Zb,{className:"w-full max-w-md",children:(0,t.jsxs)(u.aY,{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"flex justify-center mb-4",children:(0,t.jsx)("div",{className:"h-12 w-12 bg-green-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(j.Z,{className:"h-6 w-6 text-green-600"})})}),(0,t.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Account created successfully!"}),(0,t.jsx)("p",{className:"text-muted-foreground mb-4",children:"You're being signed in automatically..."}),(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)(f.Z,{className:"h-5 w-5 animate-spin"})})]})})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:(0,t.jsxs)(u.Zb,{className:"w-full max-w-md",children:[(0,t.jsxs)(u.Ol,{className:"text-center",children:[(0,t.jsx)("div",{className:"flex justify-center mb-4",children:(0,t.jsx)("div",{className:"h-12 w-12 bg-primary rounded-lg flex items-center justify-center",children:(0,t.jsx)(p.Z,{className:"h-6 w-6 text-primary-foreground"})})}),(0,t.jsx)(u.ll,{className:"text-2xl font-bold",children:"Create your account"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Get started with QueryCraft Studio"})]}),(0,t.jsxs)(u.aY,{className:"space-y-4",children:[E&&(0,t.jsx)(x.bZ,{variant:"destructive",children:(0,t.jsx)(x.X,{children:E})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(d.z,{variant:"outline",className:"w-full",onClick:()=>q("google"),disabled:_,children:[(0,t.jsx)(N.Z,{className:"h-4 w-4 mr-2"}),"Continue with Google"]}),(0,t.jsxs)(d.z,{variant:"outline",className:"w-full",onClick:()=>q("github"),disabled:_,children:[(0,t.jsx)(g.Z,{className:"h-4 w-4 mr-2"}),"Continue with GitHub"]})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,t.jsx)(h.Z,{className:"w-full"})}),(0,t.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,t.jsx)("span",{className:"bg-background px-2 text-muted-foreground",children:"Or continue with email"})})]}),(0,t.jsxs)("form",{onSubmit:A,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m._,{htmlFor:"name",children:"Full Name"}),(0,t.jsx)(o.I,{id:"name",type:"text",placeholder:"Enter your full name",value:e,onChange:e=>s(e.target.value),required:!0,disabled:_})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m._,{htmlFor:"email",children:"Email"}),(0,t.jsx)(o.I,{id:"email",type:"email",placeholder:"Enter your email",value:a,onChange:e=>i(e.target.value),required:!0,disabled:_})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m._,{htmlFor:"password",children:"Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(o.I,{id:"password",type:Z?"text":"password",placeholder:"Create a password",value:v,onChange:e=>w(e.target.value),required:!0,disabled:_,minLength:6}),(0,t.jsx)(d.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>S(!Z),disabled:_,children:Z?(0,t.jsx)(y.Z,{className:"h-4 w-4"}):(0,t.jsx)(b.Z,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m._,{htmlFor:"confirmPassword",children:"Confirm Password"}),(0,t.jsx)(o.I,{id:"confirmPassword",type:"password",placeholder:"Confirm your password",value:C,onChange:e=>k(e.target.value),required:!0,disabled:_})]}),(0,t.jsx)(d.z,{type:"submit",className:"w-full",disabled:_,children:_?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(f.Z,{className:"h-4 w-4 mr-2 animate-spin"}),"Creating account..."]}):"Create account"})]}),(0,t.jsxs)("div",{className:"text-center text-sm",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Already have an account? "}),(0,t.jsx)(c(),{href:"/auth/signin",className:"text-primary hover:underline",children:"Sign in"})]}),(0,t.jsx)("div",{className:"text-center text-sm",children:(0,t.jsx)(c(),{href:"/",className:"text-muted-foreground hover:underline",children:"← Back to home"})})]})]})})}}},function(e){e.O(0,[365,250,749,972,971,938,744],function(){return e(e.s=3412)}),_N_E=e.O()}]);