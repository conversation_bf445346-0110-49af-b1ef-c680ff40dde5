(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[702],{3100:function(e,s,a){Promise.resolve().then(a.bind(a,703)),Promise.resolve().then(a.bind(a,5624))},703:function(e,s,a){"use strict";a.r(s),a.d(s,{DashboardLayout:function(){return q}});var t=a(7437),r=a(2265),n=a(575),i=a(2749),l=a(1465),o=a(2169);let d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.fC,{ref:s,className:(0,o.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",a),...r})});d.displayName=l.fC.displayName;let c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.Ee,{ref:s,className:(0,o.cn)("aspect-square h-full w-full",a),...r})});c.displayName=l.Ee.displayName;let m=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.NY,{ref:s,className:(0,o.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",a),...r})});m.displayName=l.NY.displayName;var u=a(9352),x=a(7158),h=a(2442),f=a(6369);let p=u.fC,j=u.xz;u.ZA,u.Uv,u.Tr,u.Ee,r.forwardRef((e,s)=>{let{className:a,inset:r,children:n,...i}=e;return(0,t.jsxs)(u.fF,{ref:s,className:(0,o.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",r&&"pl-8",a),...i,children:[n,(0,t.jsx)(x.Z,{className:"ml-auto h-4 w-4"})]})}).displayName=u.fF.displayName,r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(u.tu,{ref:s,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...r})}).displayName=u.tu.displayName;let N=r.forwardRef((e,s)=>{let{className:a,sideOffset:r=4,...n}=e;return(0,t.jsx)(u.Uv,{children:(0,t.jsx)(u.VY,{ref:s,sideOffset:r,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...n})})});N.displayName=u.VY.displayName;let g=r.forwardRef((e,s)=>{let{className:a,inset:r,...n}=e;return(0,t.jsx)(u.ck,{ref:s,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r&&"pl-8",a),...n})});g.displayName=u.ck.displayName,r.forwardRef((e,s)=>{let{className:a,children:r,checked:n,...i}=e;return(0,t.jsxs)(u.oC,{ref:s,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:n,...i,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(u.wU,{children:(0,t.jsx)(h.Z,{className:"h-4 w-4"})})}),r]})}).displayName=u.oC.displayName,r.forwardRef((e,s)=>{let{className:a,children:r,...n}=e;return(0,t.jsxs)(u.Rk,{ref:s,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...n,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(u.wU,{children:(0,t.jsx)(f.Z,{className:"h-2 w-2 fill-current"})})}),r]})}).displayName=u.Rk.displayName;let y=r.forwardRef((e,s)=>{let{className:a,inset:r,...n}=e;return(0,t.jsx)(u.__,{ref:s,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",a),...n})});y.displayName=u.__.displayName;let b=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(u.Z0,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",a),...r})});b.displayName=u.Z0.displayName;var v=a(1097),w=a(7972),C=a(1738),S=a(9409),T=a(5883);function Z(){var e,s,a,r,l,o;let{data:u,status:x}=(0,i.useSession)();if("loading"===x)return(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsx)("div",{className:"h-8 w-8 bg-muted rounded-full animate-pulse"})});if(!u)return(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(n.z,{variant:"ghost",onClick:()=>(0,i.signIn)(),children:[(0,t.jsx)(v.Z,{className:"h-4 w-4 mr-2"}),"Sign In"]}),(0,t.jsx)(n.z,{onClick:()=>(0,i.signIn)(),children:"Get Started"})]});let h=(null===(s=u.user)||void 0===s?void 0:null===(e=s.name)||void 0===e?void 0:e.split(" ").map(e=>e[0]).join("").toUpperCase())||"U";return(0,t.jsxs)(p,{children:[(0,t.jsx)(j,{asChild:!0,children:(0,t.jsx)(n.z,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,t.jsxs)(d,{className:"h-8 w-8",children:[(0,t.jsx)(c,{src:(null===(a=u.user)||void 0===a?void 0:a.image)||"",alt:(null===(r=u.user)||void 0===r?void 0:r.name)||""}),(0,t.jsx)(m,{children:h})]})})}),(0,t.jsxs)(N,{className:"w-56",align:"end",forceMount:!0,children:[(0,t.jsx)(y,{className:"font-normal",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,t.jsx)("p",{className:"text-sm font-medium leading-none",children:(null===(l=u.user)||void 0===l?void 0:l.name)||"User"}),(0,t.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:null===(o=u.user)||void 0===o?void 0:o.email})]})}),(0,t.jsx)(b,{}),(0,t.jsxs)(g,{children:[(0,t.jsx)(w.Z,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:"Profile"})]}),(0,t.jsxs)(g,{children:[(0,t.jsx)(C.Z,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:"Billing"})]}),(0,t.jsxs)(g,{children:[(0,t.jsx)(S.Z,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:"Settings"})]}),(0,t.jsx)(b,{}),(0,t.jsxs)(g,{onClick:()=>(0,i.signOut)(),children:[(0,t.jsx)(T.Z,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:"Log out"})]})]})]})}var R=a(7332),E=a(8244),_=a(9883),I=a(9865),k=a(5750),z=a(5479),O=a(2549),L=a(8004),A=a(1396),D=a.n(A);function q(e){let{children:s}=e,[a,i]=(0,r.useState)(!0),l=[{name:"Query Workspace",href:"/dashboard",icon:R.Z,current:!0},{name:"Query History",href:"/dashboard/history",icon:E.Z,current:!1},{name:"Connections",href:"/dashboard/connections",icon:_.Z,current:!1},{name:"Documentation",href:"/dashboard/docs",icon:I.Z,current:!1},{name:"Team",href:"/dashboard/team",icon:k.Z,current:!1}],o=[{name:"Settings",href:"/dashboard/settings",icon:S.Z},{name:"Help",href:"/dashboard/help",icon:z.Z},{name:"Profile",href:"/dashboard/profile",icon:w.Z}];return(0,t.jsxs)("div",{className:"flex h-screen bg-background",children:[(0,t.jsx)("div",{className:"".concat(a?"w-64":"w-16"," transition-all duration-300 ease-in-out"),children:(0,t.jsxs)("div",{className:"flex flex-col h-full bg-card border-r border-border",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-border",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(R.Z,{className:"h-8 w-8 text-primary"}),a&&(0,t.jsx)("span",{className:"text-lg font-bold",children:"QueryCraft"})]}),(0,t.jsx)(n.z,{variant:"ghost",size:"icon",onClick:()=>i(!a),className:"h-8 w-8",children:a?(0,t.jsx)(O.Z,{className:"h-4 w-4"}):(0,t.jsx)(L.Z,{className:"h-4 w-4"})})]}),(0,t.jsx)("nav",{className:"flex-1 p-4 space-y-2",children:l.map(e=>{let s=e.icon;return(0,t.jsxs)(D(),{href:e.href,className:"flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ".concat(e.current?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-muted"),children:[(0,t.jsx)(s,{className:"h-5 w-5 flex-shrink-0"}),a&&(0,t.jsx)("span",{children:e.name})]},e.name)})}),(0,t.jsx)("div",{className:"p-4 border-t border-border space-y-2",children:o.map(e=>{let s=e.icon;return(0,t.jsxs)(D(),{href:e.href,className:"flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-muted transition-colors",children:[(0,t.jsx)(s,{className:"h-5 w-5 flex-shrink-0"}),a&&(0,t.jsx)("span",{children:e.name})]},e.name)})})]})}),(0,t.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,t.jsx)("header",{className:"bg-card border-b border-border px-6 py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-foreground",children:"SQL Workspace"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Build, debug, and optimize your SQL queries with AI assistance"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)(n.z,{variant:"outline",size:"sm",children:[(0,t.jsx)(_.Z,{className:"h-4 w-4 mr-2"}),"New Connection"]}),(0,t.jsx)(Z,{})]})]})}),(0,t.jsx)("main",{className:"flex-1 overflow-hidden",children:s})]})]})}},5624:function(e,s,a){"use strict";a.r(s),a.d(s,{QueryWorkspace:function(){return B}});var t=a(7437),r=a(2265),n=a(5671),i=a(575),l=a(6061),o=a(2169);let d=(0,l.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:s,variant:a,...r}=e;return(0,t.jsx)("div",{className:(0,o.cn)(d({variant:a}),s),...r})}var m=a(4907),u=a(2851),x=a(6224),h=a(9868),f=a(4658),p=a(7972),j=a(6020),N=a(4227);function g(e){let{onQueryGenerated:s}=e,{messages:a,isAILoading:l,generateQuery:d,setActiveTab:g,saveQueryToDatabase:y}=(0,N.l)(),[b,v]=(0,r.useState)(""),w=(0,r.useRef)(null),C=(0,r.useRef)(null),S=()=>{var e;null===(e=w.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})};(0,r.useEffect)(()=>{S()},[a]);let T=async()=>{b.trim()&&!l&&(v(""),await d(b))},Z=e=>{navigator.clipboard.writeText(e)},R=async(e,a,t)=>{if(s(e),g("editor"),a&&t)try{await y(a,e,t)}catch(e){console.error("Failed to save query to database:",e)}};return(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsxs)("div",{className:"flex-1 overflow-y-auto p-6 space-y-4",children:[a.map(e=>{var s;return(0,t.jsxs)("div",{className:"flex gap-3 ".concat("user"===e.role?"justify-end":"justify-start"),children:["assistant"===e.role&&(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"h-8 w-8 bg-primary rounded-full flex items-center justify-center",children:(0,t.jsx)(m.Z,{className:"h-4 w-4 text-primary-foreground"})})}),(0,t.jsx)("div",{className:"max-w-2xl ".concat("user"===e.role?"order-first":""),children:(0,t.jsxs)(n.Zb,{className:"p-4 ".concat("user"===e.role?"bg-primary text-primary-foreground":""),children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("div",{className:"text-sm",children:e.content}),"query"===e.type&&(null===(s=e.metadata)||void 0===s?void 0:s.sql)&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"bg-muted rounded-lg p-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsxs)(c,{variant:"secondary",className:"text-xs",children:[(0,t.jsx)(u.Z,{className:"h-3 w-3 mr-1"}),"Generated SQL"]}),(0,t.jsx)("div",{className:"flex gap-1",children:(0,t.jsx)(i.z,{variant:"ghost",size:"sm",onClick:()=>Z(e.metadata.sql),children:(0,t.jsx)(x.Z,{className:"h-3 w-3"})})})]}),(0,t.jsx)("pre",{className:"text-xs font-mono overflow-x-auto",children:(0,t.jsx)("code",{children:e.metadata.sql})})]}),e.metadata.explanation&&(0,t.jsxs)("div",{className:"text-sm text-muted-foreground",children:[(0,t.jsx)("strong",{children:"Explanation:"})," ",e.metadata.explanation]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(i.z,{size:"sm",onClick:()=>{var s,a;return R(e.metadata.sql,null===(s=e.metadata)||void 0===s?void 0:s.userInput,null===(a=e.metadata)||void 0===a?void 0:a.explanation)},children:"Use in Editor"}),(0,t.jsxs)(i.z,{variant:"outline",size:"sm",children:[(0,t.jsx)(h.Z,{className:"h-3 w-3 mr-1"}),"Good"]}),(0,t.jsxs)(i.z,{variant:"outline",size:"sm",children:[(0,t.jsx)(f.Z,{className:"h-3 w-3 mr-1"}),"Improve"]})]})]})]}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground mt-2",children:(0,o.SY)(e.timestamp)})]})}),"user"===e.role&&(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"h-8 w-8 bg-muted rounded-full flex items-center justify-center",children:(0,t.jsx)(p.Z,{className:"h-4 w-4 text-muted-foreground"})})})]},e.id)}),l&&(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"h-8 w-8 bg-primary rounded-full flex items-center justify-center",children:(0,t.jsx)(m.Z,{className:"h-4 w-4 text-primary-foreground animate-pulse"})})}),(0,t.jsx)(n.Zb,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"loading-spinner"}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"AI is thinking..."})]})})]}),(0,t.jsx)("div",{ref:w})]}),(0,t.jsxs)("div",{className:"border-t border-border p-4",children:[(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)("textarea",{ref:C,value:b,onChange:e=>v(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),T())},placeholder:"Describe the data you want to query... (e.g., 'Show me the top 10 customers by revenue this month')",className:"querycraft-textarea resize-none",rows:3,disabled:l})}),(0,t.jsx)(i.z,{onClick:T,disabled:!b.trim()||l,size:"lg",children:(0,t.jsx)(j.Z,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground mt-2",children:"Press Enter to send, Shift+Enter for new line"})]})]})}var y=a(837),b=a(3008),v=a(1981),w=a(4056),C=a(8940),S=a(2369);function T(e){let{value:s,onChange:a,language:l,readOnly:o=!1}=e,d=(0,r.useRef)(null),m=(0,r.useCallback)((e,s)=>{d.current=e,s.languages.registerCompletionItemProvider("sql",{provideCompletionItems:(e,a)=>({suggestions:[{label:"SELECT",kind:s.languages.CompletionItemKind.Keyword,insertText:"SELECT ",documentation:"SELECT statement"},{label:"FROM",kind:s.languages.CompletionItemKind.Keyword,insertText:"FROM ",documentation:"FROM clause"},{label:"WHERE",kind:s.languages.CompletionItemKind.Keyword,insertText:"WHERE ",documentation:"WHERE clause"},{label:"JOIN",kind:s.languages.CompletionItemKind.Keyword,insertText:"JOIN ",documentation:"JOIN clause"},{label:"GROUP BY",kind:s.languages.CompletionItemKind.Keyword,insertText:"GROUP BY ",documentation:"GROUP BY clause"},{label:"ORDER BY",kind:s.languages.CompletionItemKind.Keyword,insertText:"ORDER BY ",documentation:"ORDER BY clause"}]})}),s.editor.defineTheme("querycraft-theme",{base:"vs",inherit:!0,rules:[{token:"keyword.sql",foreground:"3b82f6",fontStyle:"bold"},{token:"string.sql",foreground:"22c55e"},{token:"comment.sql",foreground:"6b7280",fontStyle:"italic"}],colors:{"editor.background":"#ffffff","editor.foreground":"#1f2937","editor.lineHighlightBackground":"#f9fafb","editor.selectionBackground":"#dbeafe"}}),s.editor.setTheme("querycraft-theme")},[]),{errors:u,warnings:x}=(()=>{let e=[],a=[];return s.trim()&&(s.toLowerCase().includes("select")||s.toLowerCase().includes("insert")||s.toLowerCase().includes("update")||s.toLowerCase().includes("delete")||e.push("Query should contain a valid SQL statement"),s.toLowerCase().includes("select *")&&a.push("Consider specifying column names instead of using SELECT *"),s.toLowerCase().includes("where")&&!s.toLowerCase().includes("limit")&&a.push("Consider adding a LIMIT clause for large datasets")),{errors:e,warnings:a}})();return(0,t.jsxs)("div",{className:"flex flex-col h-full space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("h3",{className:"font-semibold",children:"SQL Editor"}),(0,t.jsx)(c,{variant:"outline",children:l.toUpperCase()}),0===u.length&&0===x.length&&s.trim()&&(0,t.jsxs)(c,{variant:"default",className:"bg-green-100 text-green-800",children:[(0,t.jsx)(b.Z,{className:"h-3 w-3 mr-1"}),"Valid"]}),u.length>0&&(0,t.jsxs)(c,{variant:"destructive",children:[(0,t.jsx)(v.Z,{className:"h-3 w-3 mr-1"}),u.length," Error",u.length>1?"s":""]}),x.length>0&&(0,t.jsxs)(c,{variant:"secondary",className:"bg-yellow-100 text-yellow-800",children:[(0,t.jsx)(w.Z,{className:"h-3 w-3 mr-1"}),x.length," Warning",x.length>1?"s":""]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>{d.current&&d.current.getAction("editor.action.formatDocument").run()},children:[(0,t.jsx)(C.Z,{className:"h-4 w-4 mr-2"}),"Format"]}),(0,t.jsxs)(i.z,{variant:"outline",size:"sm",children:[(0,t.jsx)(S.Z,{className:"h-4 w-4 mr-2"}),"Optimize"]})]})]}),(0,t.jsx)(n.Zb,{className:"flex-1 overflow-hidden sql-editor",children:(0,t.jsx)(y.ML,{height:"100%",language:"sql",value:s,onChange:e=>a(e||""),onMount:m,options:{readOnly:o,minimap:{enabled:!1},fontSize:14,lineNumbers:"on",roundedSelection:!1,scrollBeyondLastLine:!1,automaticLayout:!0,wordWrap:"on",folding:!0,lineDecorationsWidth:10,lineNumbersMinChars:3,glyphMargin:!1,contextmenu:!0,mouseWheelZoom:!0,smoothScrolling:!0,cursorBlinking:"blink",cursorStyle:"line",renderWhitespace:"selection",renderControlCharacters:!1,fontFamily:"JetBrains Mono, Fira Code, Consolas, monospace",fontLigatures:!0,suggest:{showKeywords:!0,showSnippets:!0,showFunctions:!0},quickSuggestions:{other:!0,comments:!1,strings:!1},parameterHints:{enabled:!0},acceptSuggestionOnCommitCharacter:!0,acceptSuggestionOnEnter:"on",accessibilitySupport:"auto"}})}),(u.length>0||x.length>0)&&(0,t.jsxs)("div",{className:"space-y-2",children:[u.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-red-600",children:[(0,t.jsx)(v.Z,{className:"h-4 w-4"}),e]},"error-".concat(s))),x.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-yellow-600",children:[(0,t.jsx)(w.Z,{className:"h-4 w-4"}),e]},"warning-".concat(s)))]})]})}var Z=a(7332),R=a(3523),E=a(7158),_=a(3223),I=a(774);function k(e){let{isConnected:s}=e,[a,l]=(0,r.useState)(new Set),o={database:"ecommerce_db",tables:[{name:"customers",rowCount:15420,columns:[{name:"customer_id",type:"INT",isPrimaryKey:!0,isNullable:!1},{name:"name",type:"VARCHAR(255)",isPrimaryKey:!1,isNullable:!1},{name:"email",type:"VARCHAR(255)",isPrimaryKey:!1,isNullable:!1},{name:"phone",type:"VARCHAR(20)",isPrimaryKey:!1,isNullable:!0},{name:"created_at",type:"TIMESTAMP",isPrimaryKey:!1,isNullable:!1}]},{name:"orders",rowCount:45230,columns:[{name:"order_id",type:"INT",isPrimaryKey:!0,isNullable:!1},{name:"customer_id",type:"INT",isPrimaryKey:!1,isNullable:!1},{name:"order_date",type:"TIMESTAMP",isPrimaryKey:!1,isNullable:!1},{name:"total_amount",type:"DECIMAL(10,2)",isPrimaryKey:!1,isNullable:!1},{name:"status",type:"VARCHAR(50)",isPrimaryKey:!1,isNullable:!1}]},{name:"order_items",rowCount:128450,columns:[{name:"item_id",type:"INT",isPrimaryKey:!0,isNullable:!1},{name:"order_id",type:"INT",isPrimaryKey:!1,isNullable:!1},{name:"product_id",type:"INT",isPrimaryKey:!1,isNullable:!1},{name:"quantity",type:"INT",isPrimaryKey:!1,isNullable:!1},{name:"unit_price",type:"DECIMAL(10,2)",isPrimaryKey:!1,isNullable:!1}]},{name:"products",rowCount:2340,columns:[{name:"product_id",type:"INT",isPrimaryKey:!0,isNullable:!1},{name:"name",type:"VARCHAR(255)",isPrimaryKey:!1,isNullable:!1},{name:"description",type:"TEXT",isPrimaryKey:!1,isNullable:!0},{name:"price",type:"DECIMAL(10,2)",isPrimaryKey:!1,isNullable:!1},{name:"category_id",type:"INT",isPrimaryKey:!1,isNullable:!1}]}]},d=e=>{let s=new Set(a);s.has(e)?s.delete(e):s.add(e),l(s)};return s?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(Z.Z,{className:"h-4 w-4 text-primary"}),(0,t.jsx)("span",{className:"font-medium",children:o.database})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:[o.tables.length," tables"]})]}),(0,t.jsx)("div",{className:"space-y-1",children:o.tables.map(e=>(0,t.jsxs)("div",{className:"schema-table",children:[(0,t.jsxs)("button",{onClick:()=>d(e.name),className:"schema-table-header w-full",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[a.has(e.name)?(0,t.jsx)(R.Z,{className:"h-4 w-4"}):(0,t.jsx)(E.Z,{className:"h-4 w-4"}),(0,t.jsx)(_.Z,{className:"h-4 w-4 text-primary"}),(0,t.jsx)("span",{className:"font-medium",children:e.name})]}),(0,t.jsx)("div",{className:"flex items-center gap-2",children:(0,t.jsxs)(c,{variant:"secondary",className:"text-xs",children:[e.rowCount.toLocaleString()," rows"]})})]}),a.has(e.name)&&(0,t.jsx)("div",{className:"schema-table-content",children:e.columns.map(e=>(0,t.jsxs)("div",{className:"schema-column",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 flex-1",children:[e.isPrimaryKey&&(0,t.jsx)(I.Z,{className:"h-3 w-3 text-yellow-500"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:e.name})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"schema-column-type text-xs",children:e.type}),!e.isNullable&&(0,t.jsx)(c,{variant:"outline",className:"text-xs px-1 py-0",children:"NOT NULL"})]})]},e.name))})]},e.name))}),(0,t.jsxs)(n.Zb,{className:"mx-4",children:[(0,t.jsx)(n.Ol,{className:"pb-3",children:(0,t.jsxs)(n.ll,{className:"text-sm flex items-center gap-2",children:[(0,t.jsx)(w.Z,{className:"h-4 w-4"}),"Schema Statistics"]})}),(0,t.jsxs)(n.aY,{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{children:"Total Tables:"}),(0,t.jsx)("span",{className:"font-medium",children:o.tables.length})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{children:"Total Columns:"}),(0,t.jsx)("span",{className:"font-medium",children:o.tables.reduce((e,s)=>e+s.columns.length,0)})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{children:"Total Rows:"}),(0,t.jsx)("span",{className:"font-medium",children:o.tables.reduce((e,s)=>e+s.rowCount,0).toLocaleString()})]})]})]})]}):(0,t.jsxs)("div",{className:"p-4 text-center",children:[(0,t.jsx)(Z.Z,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Connect to a database to explore its schema"}),(0,t.jsx)(i.z,{variant:"outline",size:"sm",children:"Connect Database"})]})}var z=a(5432),O=a(5817),L=a(6141),A=a(5790);function D(){let[e,s]=(0,r.useState)(!1),a={executionTime:245,rowsAffected:10,columns:[{name:"customer_id",type:"INT"},{name:"name",type:"VARCHAR"},{name:"email",type:"VARCHAR"},{name:"total_orders",type:"INT"},{name:"total_spent",type:"DECIMAL"}],data:[{customer_id:1,name:"John Doe",email:"<EMAIL>",total_orders:15,total_spent:2450.5},{customer_id:2,name:"Jane Smith",email:"<EMAIL>",total_orders:12,total_spent:1890.25},{customer_id:3,name:"Bob Johnson",email:"<EMAIL>",total_orders:8,total_spent:1234.75},{customer_id:4,name:"Alice Brown",email:"<EMAIL>",total_orders:6,total_spent:987.5},{customer_id:5,name:"Charlie Wilson",email:"<EMAIL>",total_orders:4,total_spent:567.25}]};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Query Results"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Last executed: 2 minutes ago"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>{s(!0),setTimeout(()=>s(!1),1e3)},disabled:e,children:[(0,t.jsx)(z.Z,{className:"h-4 w-4 mr-2 ".concat(e?"animate-spin":"")}),"Refresh"]}),(0,t.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>{console.log("Exporting results...")},children:[(0,t.jsx)(O.Z,{className:"h-4 w-4 mr-2"}),"Export"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsx)(n.Zb,{children:(0,t.jsx)(n.aY,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"p-2 bg-primary/10 rounded-lg",children:(0,t.jsx)(L.Z,{className:"h-4 w-4 text-primary"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Execution Time"}),(0,t.jsxs)("div",{className:"text-lg font-semibold",children:[a.executionTime,"ms"]})]})]})})}),(0,t.jsx)(n.Zb,{children:(0,t.jsx)(n.aY,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,t.jsx)(Z.Z,{className:"h-4 w-4 text-green-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Rows Returned"}),(0,t.jsx)("div",{className:"text-lg font-semibold",children:a.rowsAffected})]})]})})}),(0,t.jsx)(n.Zb,{children:(0,t.jsx)(n.aY,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,t.jsx)(A.Z,{className:"h-4 w-4 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Performance"}),(0,t.jsx)("div",{className:"text-lg font-semibold",children:(0,t.jsx)(c,{variant:"default",className:"bg-green-100 text-green-800",children:"Excellent"})})]})]})})})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsx)(n.Ol,{children:(0,t.jsx)(n.ll,{className:"text-base",children:"Data Results"})}),(0,t.jsx)(n.aY,{children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full border-collapse",children:[(0,t.jsx)("thead",{children:(0,t.jsx)("tr",{className:"border-b border-border",children:a.columns.map(e=>(0,t.jsx)("th",{className:"text-left p-3 font-medium text-sm text-muted-foreground",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.name,(0,t.jsx)(c,{variant:"outline",className:"text-xs",children:e.type})]})},e.name))})}),(0,t.jsx)("tbody",{children:a.data.map((e,s)=>(0,t.jsxs)("tr",{className:"border-b border-border hover:bg-muted/50",children:[(0,t.jsx)("td",{className:"p-3 text-sm",children:e.customer_id}),(0,t.jsx)("td",{className:"p-3 text-sm font-medium",children:e.name}),(0,t.jsx)("td",{className:"p-3 text-sm text-muted-foreground",children:e.email}),(0,t.jsx)("td",{className:"p-3 text-sm",children:e.total_orders}),(0,t.jsxs)("td",{className:"p-3 text-sm font-medium",children:["$",e.total_spent.toFixed(2)]})]},s))})]})})})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsx)(n.Ol,{children:(0,t.jsxs)(n.ll,{className:"text-base flex items-center gap-2",children:[(0,t.jsx)(A.Z,{className:"h-4 w-4"}),"Query Analysis"]})}),(0,t.jsx)(n.aY,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-sm mb-2",children:"Performance Insights"}),(0,t.jsxs)("ul",{className:"space-y-1 text-sm text-muted-foreground",children:[(0,t.jsxs)("li",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"h-2 w-2 bg-green-500 rounded-full"}),"Query executed efficiently"]}),(0,t.jsxs)("li",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"h-2 w-2 bg-green-500 rounded-full"}),"Proper index usage detected"]}),(0,t.jsxs)("li",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"h-2 w-2 bg-yellow-500 rounded-full"}),"Consider adding LIMIT clause"]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-sm mb-2",children:"Optimization Suggestions"}),(0,t.jsxs)("ul",{className:"space-y-1 text-sm text-muted-foreground",children:[(0,t.jsxs)("li",{className:"flex items-center gap-2",children:[(0,t.jsx)(v.Z,{className:"h-3 w-3 text-blue-500"}),"Add composite index on (customer_id, order_date)"]}),(0,t.jsxs)("li",{className:"flex items-center gap-2",children:[(0,t.jsx)(v.Z,{className:"h-3 w-3 text-blue-500"}),"Consider partitioning orders table by date"]})]})]})]})})]})]})}var q=a(7283);let P=r.forwardRef((e,s)=>{let{className:a,children:r,...n}=e;return(0,t.jsxs)(q.fC,{ref:s,className:(0,o.cn)("relative overflow-hidden",a),...n,children:[(0,t.jsx)(q.l_,{className:"h-full w-full rounded-[inherit]",children:r}),(0,t.jsx)(K,{}),(0,t.jsx)(q.Ns,{})]})});P.displayName=q.fC.displayName;let K=r.forwardRef((e,s)=>{let{className:a,orientation:r="vertical",...n}=e;return(0,t.jsx)(q.gb,{ref:s,orientation:r,className:(0,o.cn)("flex touch-none select-none transition-colors","vertical"===r&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===r&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",a),...n,children:(0,t.jsx)(q.q4,{className:"relative flex-1 rounded-full bg-border"})})});K.displayName=q.gb.displayName;var Q=a(8244),Y=a(4900);function M(e){let{userId:s}=e,[a,l]=(0,r.useState)([]),[d,m]=(0,r.useState)(!0),{setCurrentQuery:u,setActiveTab:f,currentUser:p}=(0,N.l)();(0,r.useEffect)(()=>{(async()=>{if(!s&&!p)return;let e=s||(null==p?void 0:p.id);if(e)try{m(!0);let s=await fetch("/api/queries?type=history&userId=".concat(e,"&limit=50"));if(s.ok){let e=await s.json();l(e)}}catch(e){console.error("Failed to load query history:",e)}finally{m(!1)}})()},[s,p]);let j=e=>{u(e),f("editor")},g=e=>{navigator.clipboard.writeText(e)},y=e=>{switch(e){case"EXECUTED":return"bg-green-100 text-green-800";case"FAILED":return"bg-red-100 text-red-800";case"GENERATED":return"bg-blue-100 text-blue-800";case"OPTIMIZED":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}};return d?(0,t.jsxs)(n.Zb,{children:[(0,t.jsx)(n.Ol,{children:(0,t.jsxs)(n.ll,{className:"flex items-center gap-2",children:[(0,t.jsx)(Q.Z,{className:"h-5 w-5"}),"Query History"]})}),(0,t.jsx)(n.aY,{children:(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)("div",{className:"loading-spinner"}),(0,t.jsx)("span",{className:"ml-2 text-sm text-muted-foreground",children:"Loading history..."})]})})]}):(0,t.jsxs)(n.Zb,{children:[(0,t.jsx)(n.Ol,{children:(0,t.jsxs)(n.ll,{className:"flex items-center gap-2",children:[(0,t.jsx)(Q.Z,{className:"h-5 w-5"}),"Query History",(0,t.jsxs)(c,{variant:"secondary",className:"ml-auto",children:[a.length," queries"]})]})}),(0,t.jsx)(n.aY,{children:(0,t.jsx)(P,{className:"h-[600px]",children:0===a.length?(0,t.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,t.jsx)(Q.Z,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,t.jsx)("p",{children:"No queries in history yet"}),(0,t.jsx)("p",{className:"text-sm",children:"Start generating queries to see them here"})]}):(0,t.jsx)("div",{className:"space-y-4",children:a.map(e=>(0,t.jsx)(n.Zb,{className:"border-l-4 border-l-primary/20",children:(0,t.jsx)(n.aY,{className:"p-4",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("div",{className:"flex items-start justify-between",children:(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-foreground",children:e.userInput}),(0,t.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,t.jsx)(c,{className:y(e.status),children:e.status}),(0,t.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[(0,t.jsx)(Z.Z,{className:"h-3 w-3"}),e.databaseType]}),(0,t.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[(0,t.jsx)(L.Z,{className:"h-3 w-3"}),(0,o.SY)(new Date(e.createdAt))]})]})]})}),(0,t.jsx)("div",{className:"bg-muted rounded-lg p-3",children:(0,t.jsx)("pre",{className:"text-xs font-mono overflow-x-auto whitespace-pre-wrap",children:(0,t.jsx)("code",{children:e.generatedSQL})})}),e.explanation&&(0,t.jsxs)("div",{className:"text-sm text-muted-foreground",children:[(0,t.jsx)("strong",{children:"Explanation:"})," ",e.explanation]}),e.executionTime&&(0,t.jsxs)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[(0,t.jsxs)("span",{children:["Execution: ",e.executionTime,"ms"]}),e.rowsAffected&&(0,t.jsxs)("span",{children:["Rows: ",e.rowsAffected]}),e.userFeedback&&(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)("span",{children:"Rating:"}),(0,t.jsx)("div",{className:"flex",children:[void 0,void 0,void 0,void 0,void 0].map((s,a)=>(0,t.jsx)(h.Z,{className:"h-3 w-3 ".concat(a<e.userFeedback?"text-yellow-500 fill-current":"text-gray-300")},a))})]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>j(e.generatedSQL),children:[(0,t.jsx)(Y.Z,{className:"h-3 w-3 mr-1"}),"Use"]}),(0,t.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>g(e.generatedSQL),children:[(0,t.jsx)(x.Z,{className:"h-3 w-3 mr-1"}),"Copy"]})]})]})})},e.id))})})})]})}var H=a(6245),U=a(1274);function B(){let{activeTab:e,setActiveTab:s,currentQuery:a,setCurrentQuery:l,currentDatabase:o}=(0,N.l)(),[d,u]=(0,r.useState)(!1),x=[{id:"chat",label:"AI Assistant",icon:m.Z},{id:"editor",label:"SQL Editor",icon:Z.Z},{id:"results",label:"Results",icon:A.Z},{id:"history",label:"History",icon:Q.Z}];return(0,t.jsxs)("div",{className:"flex h-full",children:[(0,t.jsxs)("div",{className:"w-80 border-r border-border bg-card",children:[(0,t.jsxs)("div",{className:"p-4 border-b border-border",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"font-semibold",children:"Database Schema"}),(0,t.jsx)(c,{variant:d?"default":"secondary",children:d?"Connected":"Not Connected"})]}),!d&&(0,t.jsxs)(i.z,{variant:"outline",size:"sm",className:"w-full",onClick:()=>u(!0),children:[(0,t.jsx)(Z.Z,{className:"h-4 w-4 mr-2"}),"Connect Database"]})]}),(0,t.jsx)("div",{className:"flex-1 overflow-auto",children:(0,t.jsx)(k,{isConnected:d})})]}),(0,t.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,t.jsx)("div",{className:"border-b border-border bg-card",children:(0,t.jsxs)("div",{className:"flex items-center justify-between px-6 py-3",children:[(0,t.jsx)("div",{className:"flex space-x-1",children:x.map(a=>{let r=a.icon;return(0,t.jsxs)("button",{onClick:()=>s(a.id),className:"flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ".concat(e===a.id?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-muted"),children:[(0,t.jsx)(r,{className:"h-4 w-4"}),a.label]},a.id)})}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(i.z,{variant:"outline",size:"sm",children:[(0,t.jsx)(Q.Z,{className:"h-4 w-4 mr-2"}),"History"]}),(0,t.jsxs)(i.z,{variant:"outline",size:"sm",children:[(0,t.jsx)(H.Z,{className:"h-4 w-4 mr-2"}),"Save"]}),(0,t.jsxs)(i.z,{variant:"outline",size:"sm",children:[(0,t.jsx)(U.Z,{className:"h-4 w-4 mr-2"}),"Share"]}),"editor"===e&&(0,t.jsxs)(i.z,{size:"sm",children:[(0,t.jsx)(Y.Z,{className:"h-4 w-4 mr-2"}),"Run Query"]})]})]})}),(0,t.jsxs)("div",{className:"flex-1 overflow-hidden",children:["chat"===e&&(0,t.jsx)(g,{onQueryGenerated:e=>{l(e)}}),"editor"===e&&(0,t.jsx)("div",{className:"h-full flex flex-col",children:(0,t.jsx)("div",{className:"flex-1 p-6",children:(0,t.jsx)(T,{value:a,onChange:l,language:(null==o?void 0:o.databaseType)||"postgresql"})})}),"results"===e&&(0,t.jsx)("div",{className:"h-full p-6",children:(0,t.jsx)(D,{})}),"history"===e&&(0,t.jsx)("div",{className:"h-full p-6",children:(0,t.jsx)(M,{})})]})]}),(0,t.jsxs)("div",{className:"w-80 border-l border-border bg-card",children:[(0,t.jsx)("div",{className:"p-4 border-b border-border",children:(0,t.jsxs)("h3",{className:"font-semibold flex items-center gap-2",children:[(0,t.jsx)(S.Z,{className:"h-4 w-4"}),"AI Insights"]})}),(0,t.jsxs)("div",{className:"p-4 space-y-4",children:[(0,t.jsxs)(n.Zb,{children:[(0,t.jsx)(n.Ol,{className:"pb-3",children:(0,t.jsx)(n.ll,{className:"text-sm",children:"Query Suggestions"})}),(0,t.jsx)(n.aY,{className:"space-y-2",children:(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"No active query to analyze. Start by describing what you want to query in the AI Assistant."})})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsx)(n.Ol,{className:"pb-3",children:(0,t.jsx)(n.ll,{className:"text-sm",children:"Performance Tips"})}),(0,t.jsx)(n.aY,{className:"space-y-2",children:(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Connect to a database to get performance recommendations."})})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsx)(n.Ol,{className:"pb-3",children:(0,t.jsx)(n.ll,{className:"text-sm",children:"Recent Activity"})}),(0,t.jsx)(n.aY,{className:"space-y-2",children:(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"No recent queries found."})})]})]})]})]})}},575:function(e,s,a){"use strict";a.d(s,{z:function(){return d}});var t=a(7437),r=a(2265),n=a(7256),i=a(6061),l=a(2169);let o=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,s)=>{let{className:a,variant:r,size:i,asChild:d=!1,...c}=e,m=d?n.g7:"button";return(0,t.jsx)(m,{className:(0,l.cn)(o({variant:r,size:i,className:a})),ref:s,...c})});d.displayName="Button"},5671:function(e,s,a){"use strict";a.d(s,{Ol:function(){return l},Zb:function(){return i},aY:function(){return d},ll:function(){return o}});var t=a(7437),r=a(2265),n=a(2169);let i=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...r})});i.displayName="Card";let l=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",a),...r})});l.displayName="CardHeader";let o=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",a),...r})});o.displayName="CardTitle",r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",a),...r})}).displayName="CardDescription";let d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",a),...r})});d.displayName="CardContent",r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",a),...r})}).displayName="CardFooter"},2169:function(e,s,a){"use strict";a.d(s,{SY:function(){return i},cn:function(){return n}});var t=a(7042),r=a(4769);function n(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,r.m6)((0,t.W)(s))}function i(e){let s=new Date,a=new Date(e),t=Math.floor((s.getTime()-a.getTime())/1e3);if(t<60)return"just now";let r=Math.floor(t/60);if(r<60)return"".concat(r," minute").concat(1===r?"":"s"," ago");let n=Math.floor(r/60);if(n<24)return"".concat(n," hour").concat(1===n?"":"s"," ago");let i=Math.floor(n/24);return i<7?"".concat(i," day").concat(1===i?"":"s"," ago"):new Intl.DateTimeFormat("en-US",{month:"long",day:"numeric",year:"numeric"}).format(new Date(e))}},4227:function(e,s,a){"use strict";a.d(s,{l:function(){return n}});var t=a(4660),r=a(4810);let n=(0,t.Ue)()((0,r.mW)((0,r.tJ)((e,s)=>({currentSession:null,currentQuery:"",currentDatabase:null,messages:[{id:"1",role:"assistant",content:"Hello! I'm your AI SQL assistant. I can help you write, debug, and optimize SQL queries. What would you like to work on today?",timestamp:new Date,type:"text"}],isAILoading:!1,queryHistory:[],connections:[],activeTab:"chat",sidebarOpen:!0,currentUser:null,setCurrentQuery:s=>e({currentQuery:s}),setCurrentDatabase:s=>e({currentDatabase:s}),addMessage:s=>e(e=>({messages:[...e.messages,s]})),setAILoading:s=>e({isAILoading:s}),addToHistory:s=>e(e=>({queryHistory:[s,...e.queryHistory.slice(0,49)]})),setActiveTab:s=>e({activeTab:s}),setSidebarOpen:s=>e({sidebarOpen:s}),addConnection:s=>e(e=>({connections:[...e.connections,s]})),removeConnection:s=>e(e=>({connections:e.connections.filter(e=>e.id!==s)})),clearMessages:()=>e({messages:[{id:"1",role:"assistant",content:"Hello! I'm your AI SQL assistant. I can help you write, debug, and optimize SQL queries. What would you like to work on today?",timestamp:new Date,type:"text"}]}),generateQuery:async e=>{let{addMessage:a,setAILoading:t,setCurrentQuery:r,setActiveTab:n,currentDatabase:l}=s();a({id:Date.now().toString(),role:"user",content:e,timestamp:new Date,type:"text"}),t(!0);try{await new Promise(e=>setTimeout(e,1500));let t=function(e,s){let a=e.toLowerCase();return a.includes("customer")&&a.includes("revenue")?"SELECT \n  c.customer_id,\n  c.name,\n  c.email,\n  COUNT(o.order_id) as total_orders,\n  SUM(o.total_amount) as total_revenue\nFROM customers c\nLEFT JOIN orders o ON c.customer_id = o.customer_id\nWHERE o.order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)\nGROUP BY c.customer_id, c.name, c.email\nORDER BY total_revenue DESC\nLIMIT 10;":a.includes("product")&&a.includes("sales")?"SELECT \n  p.product_id,\n  p.name as product_name,\n  COUNT(oi.item_id) as items_sold,\n  SUM(oi.quantity * oi.unit_price) as total_sales\nFROM products p\nJOIN order_items oi ON p.product_id = oi.product_id\nJOIN orders o ON oi.order_id = o.order_id\nWHERE o.order_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)\nGROUP BY p.product_id, p.name\nORDER BY total_sales DESC;":a.includes("order")&&a.includes("status")?"SELECT \n  status,\n  COUNT(*) as order_count,\n  AVG(total_amount) as avg_order_value\nFROM orders\nWHERE order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)\nGROUP BY status\nORDER BY order_count DESC;":"SELECT *\nFROM your_table\nWHERE condition = 'value'\nORDER BY created_at DESC\nLIMIT 10;"}(e,null==l||l.databaseType),n={id:(Date.now()+1).toString(),role:"assistant",content:"I've generated a SQL query based on your request. Here's what I came up with:",timestamp:new Date,type:"query",metadata:{sql:t,explanation:i(e),userInput:e,suggestions:["Consider adding appropriate indexes for better performance","Add error handling for edge cases","Test with sample data before running on production"]}};a(n),r(t);let o={id:Date.now().toString(),sessionId:"current",userInput:e,generatedSQL:t,explanation:i(e),createdAt:new Date};s().addToHistory(o)}catch(e){a({id:(Date.now()+1).toString(),role:"assistant",content:"I apologize, but I encountered an error while generating your query. Please try again or rephrase your request.",timestamp:new Date,type:"text"})}finally{t(!1)}},optimizeQuery:async e=>(await new Promise(e=>setTimeout(e,1e3)),"-- Optimized version\n".concat(e,"\n-- Added index hints and optimizations")),explainQuery:async e=>(await new Promise(e=>setTimeout(e,800)),"This query performs the following operations:\n1. Selects data from the specified tables\n2. Applies filtering conditions\n3. Groups and aggregates results\n4. Orders the output for better readability"),setCurrentUser:s=>e({currentUser:s}),loadUserSessions:async e=>{try{let s=await fetch("/api/queries?type=sessions&userId=".concat(e));if(s.ok){let e=await s.json();console.log("Loaded sessions:",e)}}catch(e){console.error("Failed to load user sessions:",e)}},loadUserConnections:async s=>{try{let a=await fetch("/api/connections?userId=".concat(s));if(a.ok){let s=await a.json();e({connections:s})}}catch(e){console.error("Failed to load user connections:",e)}},createNewSession:async(s,a)=>{try{let t=await fetch("/api/queries?type=session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:s,databaseConnectionId:a,name:"Session ".concat(new Date().toLocaleString())})});if(t.ok){let s=await t.json();e({currentSession:s}),console.log("Created new session:",s)}}catch(e){console.error("Failed to create new session:",e)}},saveQueryToDatabase:async(e,a,t)=>{let{currentSession:r,currentUser:n,currentDatabase:i}=s();if(!r||!n||!i){console.warn("Cannot save query: missing session, user, or database");return}try{let s=await fetch("/api/queries?type=query",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sessionId:r.id,userId:n.id,userInput:e,generatedSQL:a,explanation:t,databaseType:i.databaseType})});if(s.ok){let e=await s.json();console.log("Saved query to database:",e)}}catch(e){console.error("Failed to save query to database:",e)}}}),{name:"querycraft-store",partialize:e=>({queryHistory:e.queryHistory,connections:e.connections,sidebarOpen:e.sidebarOpen,currentDatabase:e.currentDatabase})}),{name:"querycraft-store"}));function i(e){let s=e.toLowerCase();return s.includes("customer")&&s.includes("revenue")?"This query retrieves the top customers by revenue in the last 30 days, including their contact information and order statistics.":s.includes("product")&&s.includes("sales")?"This query analyzes product sales performance over the last 7 days, showing which products are selling best.":s.includes("order")&&s.includes("status")?"This query provides an overview of order statuses and their distribution over the last 30 days.":"This query retrieves data based on your specified criteria with appropriate filtering and sorting."}}},function(e){e.O(0,[570,250,82,217,971,938,744],function(){return e(e.s=3100)}),_N_E=e.O()}]);