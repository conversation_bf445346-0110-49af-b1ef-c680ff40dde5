(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[809],{6836:function(e,s,t){Promise.resolve().then(t.bind(t,3071))},3071:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return e_}});var a=t(7437),n=t(2265),r=t(5671),i=t(575),l=t(2169);let c=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...n})});c.displayName="Textarea";var o=t(8010),d=t(3523),u=t(9224),m=t(2442);let x=o.fC;o.ZA;let h=o.B4,f=n.forwardRef((e,s)=>{let{className:t,children:n,...r}=e;return(0,a.jsxs)(o.xz,{ref:s,className:(0,l.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...r,children:[n,(0,a.jsx)(o.JO,{asChild:!0,children:(0,a.jsx)(d.Z,{className:"h-4 w-4 opacity-50"})})]})});f.displayName=o.xz.displayName;let p=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(o.u_,{ref:s,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",t),...n,children:(0,a.jsx)(u.Z,{className:"h-4 w-4"})})});p.displayName=o.u_.displayName;let j=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(o.$G,{ref:s,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",t),...n,children:(0,a.jsx)(d.Z,{className:"h-4 w-4"})})});j.displayName=o.$G.displayName;let g=n.forwardRef((e,s)=>{let{className:t,children:n,position:r="popper",...i}=e;return(0,a.jsx)(o.h_,{children:(0,a.jsxs)(o.VY,{ref:s,className:(0,l.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,...i,children:[(0,a.jsx)(p,{}),(0,a.jsx)(o.l_,{className:(0,l.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:n}),(0,a.jsx)(j,{})]})})});g.displayName=o.VY.displayName,n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(o.__,{ref:s,className:(0,l.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...n})}).displayName=o.__.displayName;let N=n.forwardRef((e,s)=>{let{className:t,children:n,...r}=e;return(0,a.jsxs)(o.ck,{ref:s,className:(0,l.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...r,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(o.wU,{children:(0,a.jsx)(m.Z,{className:"h-4 w-4"})})}),(0,a.jsx)(o.eT,{children:n})]})});N.displayName=o.ck.displayName,n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(o.Z0,{ref:s,className:(0,l.cn)("-mx-1 my-1 h-px bg-muted",t),...n})}).displayName=o.Z0.displayName;var y=t(3277),v=t(9069),b=t(6141),w=t(3008),C=t(2104),S=t(7332),k=t(6357),Z=t(4900),I=t(4056),E=t(2894);function T(e){var s,t;let{sql:l,connectionId:c,onExecutionComplete:o}=e,[d,u]=(0,n.useState)(!1),[m,x]=(0,n.useState)(null),[h,f]=(0,n.useState)(null),[p,j]=(0,n.useState)(!1),g=async()=>{if(c&&l.trim()){u(!0),x(null);try{let e=await fetch("/api/queries/execute",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({connectionId:c,sql:l.trim(),maxRows:1e3,timeout:3e4,saveToHistory:!0})}),s=await e.json();x(s),null==o||o(s)}catch(s){let e={success:!1,error:s instanceof Error?s.message:"Unknown error occurred",metadata:{connectionId:c,executedAt:new Date().toISOString(),executionTime:0,rowsReturned:0,queryHash:""}};x(e),null==o||o(e)}finally{u(!1)}}},N=async()=>{if(c&&l.trim()){j(!0),f(null);try{let e=await fetch("/api/queries/execute/validate?connectionId=".concat(c,"&sql=").concat(encodeURIComponent(l.trim())));if(e.ok){let s=await e.json();f(s.validation)}}catch(e){console.error("Failed to validate query:",e)}finally{j(!1)}}},T=c&&l.trim()&&!d;return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[d?(0,a.jsx)(b.Z,{className:"h-4 w-4 animate-spin"}):(null==m?void 0:m.success)?(0,a.jsx)(w.Z,{className:"h-4 w-4 text-green-500"}):m&&!m.success?(0,a.jsx)(C.Z,{className:"h-4 w-4 text-red-500"}):(0,a.jsx)(S.Z,{className:"h-4 w-4"}),"Query Execution",m&&(0,a.jsx)(y.C,{variant:m.success?"default":"destructive",children:m.success?"Success":"Failed"})]})}),(0,a.jsxs)(r.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(i.z,{onClick:g,disabled:!T,className:"flex items-center gap-2",children:d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(k.Z,{className:"h-4 w-4"}),"Executing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(Z.Z,{className:"h-4 w-4"}),"Execute Query"]})}),(0,a.jsx)(i.z,{variant:"outline",onClick:N,disabled:!c||!l.trim()||p,className:"flex items-center gap-2",children:p?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.Z,{className:"h-4 w-4 animate-spin"}),"Validating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(w.Z,{className:"h-4 w-4"}),"Validate"]})})]}),!c&&(0,a.jsxs)(v.bZ,{children:[(0,a.jsx)(I.Z,{className:"h-4 w-4"}),(0,a.jsx)(v.X,{children:"Please select a database connection to execute queries."})]})]})]}),h&&(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(w.Z,{className:"h-4 w-4"}),"Query Validation",(t=h.estimatedComplexity,(0,a.jsxs)(y.C,{variant:{LOW:"default",MEDIUM:"secondary",HIGH:"destructive"}[t]||"default",children:[t," Complexity"]}))]})}),(0,a.jsxs)(r.aY,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[(0,a.jsx)(y.C,{variant:"outline",children:h.queryType}),h.isReadOnly&&(0,a.jsx)(y.C,{variant:"secondary",children:"Read-Only"})]}),h.errors.length>0&&(0,a.jsxs)(v.bZ,{className:"border-red-200 bg-red-50",children:[(0,a.jsx)(C.Z,{className:"h-4 w-4 text-red-500"}),(0,a.jsxs)(v.X,{children:[(0,a.jsx)("div",{className:"font-medium text-red-700 mb-1",children:"Validation Errors:"}),(0,a.jsx)("ul",{className:"list-disc list-inside text-red-600 text-sm",children:h.errors.map((e,s)=>(0,a.jsx)("li",{children:e},s))})]})]}),h.warnings.length>0&&(0,a.jsxs)(v.bZ,{className:"border-yellow-200 bg-yellow-50",children:[(0,a.jsx)(E.Z,{className:"h-4 w-4 text-yellow-500"}),(0,a.jsxs)(v.X,{children:[(0,a.jsx)("div",{className:"font-medium text-yellow-700 mb-1",children:"Warnings:"}),(0,a.jsx)("ul",{className:"list-disc list-inside text-yellow-600 text-sm",children:h.warnings.map((e,s)=>(0,a.jsx)("li",{children:e},s))})]})]}),h.isValid&&0===h.errors.length&&(0,a.jsxs)(v.bZ,{className:"border-green-200 bg-green-50",children:[(0,a.jsx)(w.Z,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)(v.X,{className:"text-green-700",children:"Query validation passed. Ready to execute."})]})]})]}),m&&(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[m.success?(0,a.jsx)(w.Z,{className:"h-4 w-4 text-green-500"}):(0,a.jsx)(C.Z,{className:"h-4 w-4 text-red-500"}),"Execution Results"]})}),(0,a.jsxs)(r.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-muted-foreground",children:"Execution Time"}),(0,a.jsxs)("div",{className:"font-medium",children:[m.metadata.executionTime,"ms"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-muted-foreground",children:"Rows Returned"}),(0,a.jsx)("div",{className:"font-medium",children:m.metadata.rowsReturned})]}),(null===(s=m.data)||void 0===s?void 0:s.affectedRows)!==void 0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-muted-foreground",children:"Rows Affected"}),(0,a.jsx)("div",{className:"font-medium",children:m.data.affectedRows})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-muted-foreground",children:"Executed At"}),(0,a.jsx)("div",{className:"font-medium",children:new Date(m.metadata.executedAt).toLocaleTimeString()})]})]}),m.error&&(0,a.jsxs)(v.bZ,{className:"border-red-200 bg-red-50",children:[(0,a.jsx)(C.Z,{className:"h-4 w-4 text-red-500"}),(0,a.jsxs)(v.X,{children:[(0,a.jsx)("div",{className:"font-medium text-red-700 mb-1",children:"Execution Error:"}),(0,a.jsx)("div",{className:"text-red-600 text-sm",children:m.error})]})]}),m.warnings&&m.warnings.length>0&&(0,a.jsxs)(v.bZ,{className:"border-yellow-200 bg-yellow-50",children:[(0,a.jsx)(E.Z,{className:"h-4 w-4 text-yellow-500"}),(0,a.jsxs)(v.X,{children:[(0,a.jsx)("div",{className:"font-medium text-yellow-700 mb-1",children:"Warnings:"}),(0,a.jsx)("ul",{className:"list-disc list-inside text-yellow-600 text-sm",children:m.warnings.map((e,s)=>(0,a.jsx)("li",{children:e},s))})]})]}),m.success&&m.data&&(0,a.jsxs)(v.bZ,{className:"border-green-200 bg-green-50",children:[(0,a.jsx)(w.Z,{className:"h-4 w-4 text-green-500"}),(0,a.jsxs)(v.X,{className:"text-green-700",children:["Query executed successfully!",m.data.rowCount>0&&(0,a.jsxs)("span",{children:[" Returned ",m.data.rowCount," rows."]}),void 0!==m.data.affectedRows&&m.data.affectedRows>0&&(0,a.jsxs)("span",{children:[" Affected ",m.data.affectedRows," rows."]})]})]})]})]})]})}var R=t(2782);let O=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:s,className:(0,l.cn)("w-full caption-bottom text-sm",t),...n})})});O.displayName="Table";let _=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("thead",{ref:s,className:(0,l.cn)("[&_tr]:border-b",t),...n})});_.displayName="TableHeader";let L=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("tbody",{ref:s,className:(0,l.cn)("[&_tr:last-child]:border-0",t),...n})});L.displayName="TableBody",n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("tfoot",{ref:s,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...n})}).displayName="TableFooter";let q=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("tr",{ref:s,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...n})});q.displayName="TableRow";let F=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("th",{ref:s,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...n})});F.displayName="TableHead";let U=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("td",{ref:s,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...n})});U.displayName="TableCell",n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("caption",{ref:s,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",t),...n})}).displayName="TableCaption";var D=t(1813),z=t(5817),M=t(6637),P=t(1827),Q=t(1291),A=t(7158);function Y(e){let{data:s,metadata:t}=e,[l,c]=(0,n.useState)(""),[o,d]=(0,n.useState)(1),[u]=(0,n.useState)(50),m=(0,n.useMemo)(()=>l?s.rows.filter(e=>Object.values(e).some(e=>String(e).toLowerCase().includes(l.toLowerCase()))):s.rows,[s.rows,l]),x=(0,n.useMemo)(()=>{let e=(o-1)*u;return m.slice(e,e+u)},[m,o,u]),h=Math.ceil(m.length/u),f=(e,s)=>{if(null==e)return(0,a.jsx)("span",{className:"text-muted-foreground italic",children:"NULL"});if(s.includes("json")||s.includes("jsonb"))try{return(0,a.jsx)("pre",{className:"text-xs",children:JSON.stringify(JSON.parse(e),null,2)})}catch(s){return String(e)}if(s.includes("bool"))return(0,a.jsx)(y.C,{variant:e?"default":"secondary",children:e?"TRUE":"FALSE"});if(s.includes("date")||s.includes("time"))try{return new Date(e).toLocaleString()}catch(s){return String(e)}let t=String(e);return t.length>100?(0,a.jsxs)("span",{title:t,children:[t.substring(0,100),"..."]}):t},p=e=>e.includes("int")||e.includes("number")||e.includes("decimal")?"\uD83D\uDD22":e.includes("text")||e.includes("varchar")||e.includes("char")?"\uD83D\uDCDD":e.includes("date")||e.includes("time")?"\uD83D\uDCC5":e.includes("bool")?"✅":e.includes("json")?"\uD83D\uDCCB":"\uD83D\uDCC4";return s.rows.length?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(D.Z,{className:"h-5 w-5"}),"Query Results",(0,a.jsxs)(y.C,{variant:"outline",children:[s.rowCount," rows"]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>{if(!s.rows.length)return;let e=[s.fields.map(e=>e.name).join(","),...s.rows.map(e=>s.fields.map(s=>{let t=e[s.name];return"string"==typeof t&&(t.includes(",")||t.includes('"'))?'"'.concat(t.replace(/"/g,'""'),'"'):null!=t?t:""}).join(","))].join("\n"),a=new Blob([e],{type:"text/csv;charset=utf-8;"}),n=document.createElement("a"),r=URL.createObjectURL(a);n.setAttribute("href",r),n.setAttribute("download","query_results_".concat(t.queryHash,".csv")),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n)},className:"flex items-center gap-2",children:[(0,a.jsx)(z.Z,{className:"h-4 w-4"}),"CSV"]}),(0,a.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>{if(!s.rows.length)return;let e=JSON.stringify(s.rows,null,2),a=new Blob([e],{type:"application/json;charset=utf-8;"}),n=document.createElement("a"),r=URL.createObjectURL(a);n.setAttribute("href",r),n.setAttribute("download","query_results_".concat(t.queryHash,".json")),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n)},className:"flex items-center gap-2",children:[(0,a.jsx)(M.Z,{className:"h-4 w-4"}),"JSON"]})]})]})}),(0,a.jsx)(r.aY,{children:(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[(0,a.jsxs)("span",{children:["Execution time: ",t.executionTime,"ms"]}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:["Rows returned: ",t.rowsReturned]}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:["Executed at: ",new Date(t.executedAt).toLocaleString()]})]})})]}),(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(P.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(R.I,{placeholder:"Search in results...",value:l,onChange:e=>{c(e.target.value),d(1)},className:"pl-10"})]}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:[m.length," of ",s.rows.length," rows"]})]})})}),(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"p-0",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)(O,{children:[(0,a.jsx)(_,{children:(0,a.jsx)(q,{children:s.fields.map(e=>(0,a.jsx)(F,{className:"whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{children:p(e.type)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[e.type,!e.nullable&&(0,a.jsx)("span",{className:"ml-1",children:"NOT NULL"})]})]})]})},e.name))})}),(0,a.jsx)(L,{children:x.map((e,t)=>(0,a.jsx)(q,{children:s.fields.map(s=>(0,a.jsx)(U,{className:"max-w-xs",children:f(e[s.name],s.type)},s.name))},t))})]})})})}),h>1&&(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Page ",o," of ",h]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>d(e=>Math.max(1,e-1)),disabled:1===o,className:"flex items-center gap-2",children:[(0,a.jsx)(Q.Z,{className:"h-4 w-4"}),"Previous"]}),(0,a.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>d(e=>Math.min(h,e+1)),disabled:o===h,className:"flex items-center gap-2",children:["Next",(0,a.jsx)(A.Z,{className:"h-4 w-4"})]})]})]})})})]}):(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"flex items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(S.Z,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"No data returned"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Query executed successfully but returned no rows"})]})})})}var H=t(5005),J=t(6062);let V=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(J.fC,{ref:s,className:(0,l.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...n,children:(0,a.jsx)(J.z$,{className:(0,l.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(m.Z,{className:"h-4 w-4"})})})});V.displayName=J.fC.displayName;var X=t(6264),B=t(4280);let K={async getUser(e){let s=await fetch("/api/users?id=".concat(e));if(!s.ok)throw Error("Failed to fetch user");return s.json()},async createUser(e){let s=await fetch("/api/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to create user");return s.json()},async updateUser(e,s){let t=await fetch("/api/users?id=".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!t.ok)throw Error("Failed to update user");return t.json()},async getUserConnections(e){let s=await fetch("/api/connections?userId=".concat(e));if(!s.ok)throw Error("Failed to fetch connections");return s.json()},async createConnection(e){let s=await fetch("/api/connections",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to create connection");return s.json()},async updateConnection(e,s){let t=await fetch("/api/connections?id=".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!t.ok)throw Error("Failed to update connection");return t.json()},async deleteConnection(e){if(!(await fetch("/api/connections?id=".concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete connection")},async getUserSessions(e){let s=await fetch("/api/queries?type=sessions&userId=".concat(e));if(!s.ok)throw Error("Failed to fetch sessions");return s.json()},async getSession(e){let s=await fetch("/api/queries?sessionId=".concat(e));if(!s.ok)throw Error("Failed to fetch session");return s.json()},async createSession(e){let s=await fetch("/api/queries?type=session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to create session");return s.json()},async createQuery(e){let s=await fetch("/api/queries?type=query",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to create query");return s.json()},async getUserQueryHistory(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,t=await fetch("/api/queries?type=history&userId=".concat(e,"&limit=").concat(s));if(!t.ok)throw Error("Failed to fetch query history");return t.json()},async testConnection(e,s){let t=await fetch("/api/connections/test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e?{connectionId:e}:s)});if(!t.ok)throw Error((await t.json()).error||"Failed to test connection");return t.json()},async getConnectionSchema(e){let s=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=await fetch("/api/connections/schema?connectionId=".concat(e,"&refresh=").concat(s));if(!t.ok)throw Error((await t.json()).error||"Failed to fetch schema");return t.json()},async refreshConnectionSchema(e){let s=await fetch("/api/connections/schema",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({connectionId:e})});if(!s.ok)throw Error((await s.json()).error||"Failed to refresh schema");return s.json()},async executeQuery(e){let s=await fetch("/api/queries/execute",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error((await s.json()).error||"Failed to execute query");return s.json()},async validateQuery(e,s){let t=await fetch("/api/queries/execute/validate?connectionId=".concat(e,"&sql=").concat(encodeURIComponent(s)));if(!t.ok)throw Error((await t.json()).error||"Failed to validate query");return t.json()},async updateQueryFeedback(e,s){let t=await fetch("/api/queries?type=query&id=".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({userFeedback:s})});if(!t.ok)throw Error("Failed to update query feedback");return t.json()}},G=()=>K;function W(e){let{connectionId:s,onConnectionTested:t}=e,[l,c]=(0,n.useState)(!1),[o,d]=(0,n.useState)(null),[u,m]=(0,n.useState)({databaseType:"POSTGRESQL",connectionString:"",host:"",port:5432,database:"",username:"",password:"",ssl:!1}),p=G(),j=async()=>{c(!0),d(null);try{let e;e=s?await p.testConnection(s):await p.testConnection(void 0,u),d(e),null==t||t(e)}catch(s){let e={success:!1,connected:!1,error:s instanceof Error?s.message:"Unknown error",timestamp:new Date().toISOString()};d(e),null==t||t(e)}finally{c(!1)}},b=(e,s)=>{m(t=>({...t,[e]:s})),d(null)};return(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[l?(0,a.jsx)(X.Z,{className:"h-4 w-4 animate-spin"}):(null==o?void 0:o.connected)?(0,a.jsx)(w.Z,{className:"h-4 w-4 text-green-500"}):o&&!o.connected?(0,a.jsx)(C.Z,{className:"h-4 w-4 text-red-500"}):(0,a.jsx)(S.Z,{className:"h-4 w-4"}),s?"Test Connection":"Test Database Connection",l?(0,a.jsx)(y.C,{variant:"secondary",children:"Testing..."}):(null==o?void 0:o.connected)?(0,a.jsx)(y.C,{variant:"default",className:"bg-green-100 text-green-800",children:"Connected"}):o&&!o.connected?(0,a.jsx)(y.C,{variant:"destructive",children:"Failed"}):null]})}),(0,a.jsxs)(r.aY,{className:"space-y-4",children:[!s&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(H._,{htmlFor:"databaseType",children:"Database Type"}),(0,a.jsxs)(x,{value:u.databaseType,onValueChange:e=>b("databaseType",e),children:[(0,a.jsx)(f,{children:(0,a.jsx)(h,{})}),(0,a.jsxs)(g,{children:[(0,a.jsx)(N,{value:"POSTGRESQL",children:"PostgreSQL"}),(0,a.jsx)(N,{value:"MYSQL",children:"MySQL"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(H._,{htmlFor:"connectionString",children:"Connection String (Optional)"}),(0,a.jsx)(R.I,{id:"connectionString",type:"text",placeholder:"postgresql://user:password@host:port/database",value:u.connectionString,onChange:e=>b("connectionString",e.target.value)}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Leave empty to use individual connection fields below"})]}),!u.connectionString&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(H._,{htmlFor:"host",children:"Host"}),(0,a.jsx)(R.I,{id:"host",type:"text",placeholder:"localhost",value:u.host,onChange:e=>b("host",e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(H._,{htmlFor:"port",children:"Port"}),(0,a.jsx)(R.I,{id:"port",type:"number",placeholder:"MYSQL"===u.databaseType?"3306":"5432",value:u.port,onChange:e=>b("port",parseInt(e.target.value)||5432)})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(H._,{htmlFor:"database",children:"Database Name"}),(0,a.jsx)(R.I,{id:"database",type:"text",placeholder:"my_database",value:u.database,onChange:e=>b("database",e.target.value)})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(H._,{htmlFor:"username",children:"Username"}),(0,a.jsx)(R.I,{id:"username",type:"text",placeholder:"username",value:u.username,onChange:e=>b("username",e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(H._,{htmlFor:"password",children:"Password"}),(0,a.jsx)(R.I,{id:"password",type:"password",placeholder:"password",value:u.password,onChange:e=>b("password",e.target.value)})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(V,{id:"ssl",checked:u.ssl,onCheckedChange:e=>b("ssl",e)}),(0,a.jsx)(H._,{htmlFor:"ssl",children:"Use SSL"})]})]})]}),(0,a.jsx)(i.z,{onClick:j,disabled:l||!s&&!u.connectionString&&(!u.host||!u.database||!u.username),className:"w-full",children:l?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(X.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Testing Connection..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(B.Z,{className:"mr-2 h-4 w-4"}),"Test Connection"]})}),o&&(0,a.jsx)(v.bZ,{className:o.connected?"border-green-200 bg-green-50":"border-red-200 bg-red-50",children:(0,a.jsxs)(v.X,{children:[o.connected?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(w.Z,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"text-green-700",children:"Connection successful!"})]}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(C.Z,{className:"h-4 w-4 text-red-500"}),(0,a.jsx)("span",{className:"text-red-700",children:"Connection failed"})]}),o.error&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:o.error})]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-2",children:["Tested at ",new Date(o.timestamp).toLocaleString()]})]})})]})]})}var $=t(1927);let ee=$.fC,es=$.wy,et=$.Fw;var ea=t(774),en=t(7706),er=t(3223),ei=t(4689),el=t(9670);function ec(e){let{connectionId:s}=e,[t,l]=(0,n.useState)(null),[c,o]=(0,n.useState)(!1),[u,m]=(0,n.useState)(null),[x,h]=(0,n.useState)(null),[f,p]=(0,n.useState)(!1),[j,g]=(0,n.useState)(new Set),[N,b]=(0,n.useState)(new Set),w=G(),C=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];o(!0),m(null);try{let t=await w.getConnectionSchema(s,e);l(t.schema),h(t.lastUpdated),p(t.cached)}catch(e){m(e instanceof Error?e.message:"Failed to load schema")}finally{o(!1)}},k=async()=>{o(!0),m(null);try{let e=await w.refreshConnectionSchema(s);l(e.schema),h(e.lastUpdated),p(!1)}catch(e){m(e instanceof Error?e.message:"Failed to refresh schema")}finally{o(!1)}};(0,n.useEffect)(()=>{C()},[s]);let Z=e=>{let s=new Set(j);s.has(e)?s.delete(e):s.add(e),g(s)},I=e=>{let s=new Set(N);s.has(e)?s.delete(e):s.add(e),b(s)},E=e=>e.isPrimaryKey?(0,a.jsx)(ea.Z,{className:"h-3 w-3 text-yellow-500"}):e.isForeignKey?(0,a.jsx)(ea.Z,{className:"h-3 w-3 text-blue-500"}):(0,a.jsx)(en.Z,{className:"h-3 w-3 text-gray-400"}),T=e=>{let s=[];return e.isPrimaryKey&&s.push((0,a.jsx)(y.C,{variant:"secondary",className:"text-xs",children:"PK"},"pk")),e.isForeignKey&&s.push((0,a.jsx)(y.C,{variant:"outline",className:"text-xs",children:"FK"},"fk")),e.nullable||s.push((0,a.jsx)(y.C,{variant:"outline",className:"text-xs",children:"NOT NULL"},"nn")),s};return c&&!t?(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"flex items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(X.Z,{className:"h-4 w-4 animate-spin"}),(0,a.jsx)("span",{children:"Loading database schema..."})]})})}):u?(0,a.jsx)(r.Zb,{children:(0,a.jsxs)(r.aY,{className:"p-6",children:[(0,a.jsx)(v.bZ,{className:"border-red-200 bg-red-50",children:(0,a.jsx)(v.X,{className:"text-red-700",children:u})}),(0,a.jsxs)(i.z,{onClick:()=>C(),className:"mt-4",children:[(0,a.jsx)(B.Z,{className:"mr-2 h-4 w-4"}),"Retry"]})]})}):t?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(r.Zb,{children:(0,a.jsxs)(r.Ol,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(S.Z,{className:"h-5 w-5"}),"Database Schema",f&&(0,a.jsx)(y.C,{variant:"secondary",children:"Cached"})]}),(0,a.jsxs)(i.z,{onClick:k,disabled:c,variant:"outline",size:"sm",children:[c?(0,a.jsx)(X.Z,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(B.Z,{className:"mr-2 h-4 w-4"}),"Refresh"]})]}),x&&(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Last updated: ",new Date(x).toLocaleString()]})]})}),t.tables&&t.tables.length>0&&(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(er.Z,{className:"h-4 w-4"}),"Tables (",t.tables.length,")"]})}),(0,a.jsx)(r.aY,{className:"space-y-2",children:t.tables.map(e=>(0,a.jsxs)(ee,{open:j.has(e.name),onOpenChange:()=>Z(e.name),children:[(0,a.jsxs)(es,{className:"flex items-center gap-2 w-full p-2 hover:bg-muted rounded",children:[j.has(e.name)?(0,a.jsx)(d.Z,{className:"h-4 w-4"}):(0,a.jsx)(A.Z,{className:"h-4 w-4"}),(0,a.jsx)(er.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium",children:e.name}),(0,a.jsxs)(y.C,{variant:"outline",className:"text-xs",children:[e.columns.length," columns"]}),e.indexes.length>0&&(0,a.jsxs)(y.C,{variant:"outline",className:"text-xs",children:[e.indexes.length," indexes"]})]}),(0,a.jsxs)(et,{className:"ml-6 mt-2 space-y-2",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Columns"}),e.columns.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-muted/50 rounded text-sm",children:[E(e),(0,a.jsx)("span",{className:"font-mono",children:e.name}),(0,a.jsx)(y.C,{variant:"outline",className:"text-xs",children:e.type}),(0,a.jsx)("div",{className:"flex gap-1",children:T(e)}),e.isForeignKey&&e.referencedTable&&(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:["→ ",e.referencedTable,".",e.referencedColumn]})]},e.name))]}),e.indexes.length>0&&(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Indexes"}),e.indexes.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-muted/50 rounded text-sm",children:[(0,a.jsx)(ei.Z,{className:"h-3 w-3 text-gray-400"}),(0,a.jsx)("span",{className:"font-mono",children:e.name}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:["(",e.columns.join(", "),")"]}),e.isUnique&&(0,a.jsx)(y.C,{variant:"outline",className:"text-xs",children:"UNIQUE"}),e.isPrimary&&(0,a.jsx)(y.C,{variant:"secondary",className:"text-xs",children:"PRIMARY"})]},e.name))]})]})]},"".concat(e.schema,".").concat(e.name)))})]}),t.views&&t.views.length>0&&(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(el.Z,{className:"h-4 w-4"}),"Views (",t.views.length,")"]})}),(0,a.jsx)(r.aY,{className:"space-y-2",children:t.views.map(e=>(0,a.jsxs)(ee,{open:N.has(e.name),onOpenChange:()=>I(e.name),children:[(0,a.jsxs)(es,{className:"flex items-center gap-2 w-full p-2 hover:bg-muted rounded",children:[N.has(e.name)?(0,a.jsx)(d.Z,{className:"h-4 w-4"}):(0,a.jsx)(A.Z,{className:"h-4 w-4"}),(0,a.jsx)(el.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium",children:e.name})]}),(0,a.jsx)(et,{className:"ml-6 mt-2",children:(0,a.jsx)("div",{className:"p-3 bg-muted/50 rounded",children:(0,a.jsx)("pre",{className:"text-xs overflow-x-auto whitespace-pre-wrap",children:e.definition})})})]},"".concat(e.schema,".").concat(e.name)))})]}),(!t.tables||0===t.tables.length)&&(!t.views||0===t.views.length)&&(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"flex items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(S.Z,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"No tables or views found in this database"})]})})})]}):(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"flex items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(S.Z,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"No schema data available"}),(0,a.jsxs)(i.z,{onClick:()=>C(!0),className:"mt-4",children:[(0,a.jsx)(B.Z,{className:"mr-2 h-4 w-4"}),"Load Schema"]})]})})})}var eo=t(261),ed=t(8784),eu=t(5750),em=t(6224),ex=t(3480),eh=t(1274),ef=t(4527),ep=t(4337);class ej{connect(){this.socket=(0,ep.io)({path:"/api/socket",autoConnect:!1}),this.setupEventHandlers()}setupEventHandlers(){this.socket&&(this.socket.on("connect",()=>{console.log("Connected to collaboration server"),this.isConnected=!0}),this.socket.on("disconnect",()=>{console.log("Disconnected from collaboration server"),this.isConnected=!1,this.currentSessionId=null,this.currentUser=null}),Object.keys(this.eventHandlers).forEach(e=>{this.socket.on(e,s=>{let t=this.eventHandlers[e];t&&t(s)})}))}async authenticate(e){return new Promise(s=>{if(!this.socket){s(!1);return}this.socket.connect(),this.socket.once("authenticated",e=>{e.success&&e.user?(this.currentUser=e.user,s(!0)):(console.error("Authentication failed:",e.error),s(!1))}),this.socket.emit("authenticate",e)})}joinSession(e,s,t){if(!this.socket||!this.isConnected){console.error("Not connected to collaboration server");return}this.currentSessionId=e,this.socket.emit("join_session",{sessionId:e,connectionId:s,sessionName:t})}leaveSession(){this.socket&&this.currentSessionId&&(this.socket.emit("leave_session",this.currentSessionId),this.currentSessionId=null)}updateSqlContent(e,s){this.socket&&this.currentSessionId&&this.socket.emit("sql_change",{sessionId:this.currentSessionId,content:e,operation:s})}updateCursorPosition(e){this.socket&&this.currentSessionId&&this.socket.emit("cursor_update",{sessionId:this.currentSessionId,position:e})}sendChatMessage(e){this.socket&&this.currentSessionId&&this.socket.emit("chat_message",{sessionId:this.currentSessionId,message:e})}notifyQueryExecutionStart(e){this.socket&&this.currentSessionId&&this.socket.emit("query_execution_start",{sessionId:this.currentSessionId,sql:e})}notifyQueryExecutionComplete(e){this.socket&&this.currentSessionId&&this.socket.emit("query_execution_complete",{sessionId:this.currentSessionId,result:e})}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null),this.isConnected=!1,this.currentSessionId=null,this.currentUser=null}on(e,s){this.eventHandlers[e]=s,this.socket&&this.socket.on(e,s)}off(e){delete this.eventHandlers[e],this.socket&&this.socket.off(e)}get connected(){return this.isConnected}get sessionId(){return this.currentSessionId}get user(){return this.currentUser}constructor(){this.socket=null,this.eventHandlers={},this.isConnected=!1,this.currentSessionId=null,this.currentUser=null,this.connect()}}let eg=null;function eN(){var e;let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,n.useRef)(null),[a,r]=(0,n.useState)({isConnected:!1,isAuthenticated:!1,currentUser:null,participants:[],cursors:new Map,chatMessages:[],sqlContent:"",isInSession:!1});(0,n.useEffect)(()=>{t.current||(t.current=(eg||(eg=new ej),eg));let e=t.current;e.on("authenticated",e=>{r(s=>({...s,isAuthenticated:e.success,currentUser:e.user||null}))}),e.on("session_joined",e=>{r(s=>({...s,isInSession:!0,participants:e.participants,currentUser:e.user}))}),e.on("user_joined",e=>{r(s=>({...s,participants:e.participants}))}),e.on("user_left",e=>{r(s=>{let t=new Map(s.cursors);return t.delete(e.userId),{...s,participants:e.participants,cursors:t}})}),e.on("sql_sync",e=>{r(s=>({...s,sqlContent:e.content}))}),e.on("sql_change",e=>{r(s=>({...s,sqlContent:e.content}))}),e.on("cursor_update",e=>{r(s=>{let t=new Map(s.cursors);return t.set(e.user.id,{...e.position,user:e.user}),{...s,cursors:t}})}),e.on("chat_message",e=>{r(s=>({...s,chatMessages:[...s.chatMessages,e]}))}),e.on("query_execution_start",e=>{console.log("".concat(e.userName," started executing a query"))}),e.on("query_execution_complete",e=>{console.log("".concat(e.userName," completed query execution"))}),e.on("error",e=>{console.error("Collaboration error:",e.message)});let s=setInterval(()=>{r(s=>({...s,isConnected:e.connected}))},1e3);return()=>{clearInterval(s)}},[]),(0,n.useEffect)(()=>{s.autoConnect&&t.current&&!a.isAuthenticated&&i()},[s.autoConnect]),(0,n.useEffect)(()=>{s.sessionId&&s.connectionId&&a.isAuthenticated&&!a.isInSession&&l(s.sessionId,s.connectionId,s.sessionName)},[s.sessionId,s.connectionId,a.isAuthenticated,a.isInSession]);let i=(0,n.useCallback)(async()=>{if(!t.current)return!1;try{return await t.current.authenticate("demo-token")}catch(e){return console.error("Authentication failed:",e),!1}},[]),l=(0,n.useCallback)((e,s,a)=>{t.current&&t.current.joinSession(e,s,a)},[]),c=(0,n.useCallback)(()=>{t.current&&(t.current.leaveSession(),r(e=>({...e,isInSession:!1,participants:[],cursors:new Map,chatMessages:[],sqlContent:""})))},[]),o=(0,n.useCallback)((e,s)=>{t.current&&t.current.updateSqlContent(e,s)},[]),d=(0,n.useCallback)(e=>{t.current&&t.current.updateCursorPosition(e)},[]),u=(0,n.useCallback)(e=>{t.current&&t.current.sendChatMessage(e)},[]),m=(0,n.useCallback)(e=>{t.current&&t.current.notifyQueryExecutionStart(e)},[]),x=(0,n.useCallback)(e=>{t.current&&t.current.notifyQueryExecutionComplete(e)},[]),h=(0,n.useCallback)(()=>{r(e=>({...e,chatMessages:[]}))},[]),f=(0,n.useCallback)(()=>{t.current&&(t.current.disconnect(),r({isConnected:!1,isAuthenticated:!1,currentUser:null,participants:[],cursors:new Map,chatMessages:[],sqlContent:"",isInSession:!1}))},[]);return{...a,authenticate:i,joinSession:l,leaveSession:c,updateSqlContent:o,updateCursorPosition:d,sendChatMessage:u,notifyQueryExecutionStart:m,notifyQueryExecutionComplete:x,clearChatMessages:h,disconnect:f,participantCount:a.participants.length,otherParticipants:a.participants.filter(e=>{var s;return e.id!==(null===(s=a.currentUser)||void 0===s?void 0:s.id)}),canCollaborate:a.isConnected&&a.isAuthenticated&&a.isInSession,sessionId:(null===(e=t.current)||void 0===e?void 0:e.sessionId)||null}}function ey(e){let{connectionId:s,onSessionJoined:t,onSessionLeft:l}=e,[c,o]=(0,n.useState)(""),[d,u]=(0,n.useState)(""),[m,x]=(0,n.useState)(!1),[h,f]=(0,n.useState)(""),p=eN({autoConnect:!0});(0,n.useEffect)(()=>{p.isInSession&&p.currentUser?(f("".concat(window.location.origin,"/dashboard/query-editor?session=").concat(p.sessionId,"&connection=").concat(s)),null==t||t(p.sessionId||"")):(f(""),null==l||l())},[p.isInSession,p.sessionId,s,t,l]);let j=async()=>{p.isAuthenticated||await p.authenticate(),x(!0);try{let e="session_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),t=d.trim()||"Collaboration Session";p.joinSession(e,s,t),u("")}catch(e){console.error("Failed to create session:",e)}finally{x(!1)}},g=async()=>{if(c.trim()){p.isAuthenticated||await p.authenticate();try{p.joinSession(c.trim(),s),o("")}catch(e){console.error("Failed to join session:",e)}}},N=async()=>{if(h)try{await navigator.clipboard.writeText(h)}catch(e){console.error("Failed to copy URL:",e)}};return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[p.isConnected?(0,a.jsx)(eo.Z,{className:"h-4 w-4 text-green-500"}):(0,a.jsx)(ed.Z,{className:"h-4 w-4 text-red-500"}),"Collaboration Status",(0,a.jsx)(y.C,{variant:p.isConnected?"default":"destructive",children:p.isConnected?p.isAuthenticated?p.isInSession?"In Session":"Connected":"Connecting...":"Disconnected"})]})}),(0,a.jsxs)(r.aY,{children:[!p.isConnected&&(0,a.jsxs)(v.bZ,{children:[(0,a.jsx)(ed.Z,{className:"h-4 w-4"}),(0,a.jsx)(v.X,{children:"Not connected to collaboration server. Real-time features are unavailable."})]}),p.isConnected&&!p.isAuthenticated&&(0,a.jsx)(v.bZ,{children:(0,a.jsx)(v.X,{children:"Authenticating with collaboration server..."})})]})]}),p.isInSession&&(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(eu.Z,{className:"h-5 w-5"}),"Active Session",(0,a.jsxs)(y.C,{variant:"default",children:[p.participantCount," participants"]})]})}),(0,a.jsxs)(r.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Participants"}),(0,a.jsx)("div",{className:"space-y-1",children:p.participants.map(e=>{var s;return(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-muted rounded",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color}}),(0,a.jsx)("span",{className:"text-sm",children:e.name}),e.id===(null===(s=p.currentUser)||void 0===s?void 0:s.id)&&(0,a.jsx)(y.C,{variant:"secondary",className:"text-xs",children:"You"})]},e.id)})})]}),h&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Share Session"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(R.I,{value:h,readOnly:!0,className:"text-xs"}),(0,a.jsxs)(i.z,{variant:"outline",size:"sm",onClick:N,className:"flex items-center gap-2",children:[(0,a.jsx)(em.Z,{className:"h-4 w-4"}),"Copy"]})]})]}),(0,a.jsxs)(i.z,{variant:"outline",onClick:()=>{p.leaveSession()},className:"w-full flex items-center gap-2",children:[(0,a.jsx)(ex.Z,{className:"h-4 w-4"}),"Leave Session"]})]})]}),p.isAuthenticated&&!p.isInSession&&(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(eh.Z,{className:"h-5 w-5"}),"Start Collaboration"]})}),(0,a.jsxs)(r.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Create New Session"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(R.I,{placeholder:"Session name (optional)",value:d,onChange:e=>u(e.target.value)}),(0,a.jsxs)(i.z,{onClick:j,disabled:m,className:"flex items-center gap-2",children:[(0,a.jsx)(ef.Z,{className:"h-4 w-4"}),"Create"]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Join Existing Session"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(R.I,{placeholder:"Enter session ID",value:c,onChange:e=>o(e.target.value)}),(0,a.jsxs)(i.z,{onClick:g,disabled:!c.trim(),variant:"outline",className:"flex items-center gap-2",children:[(0,a.jsx)(eu.Z,{className:"h-4 w-4"}),"Join"]})]})]})]})]}),p.isConnected&&!p.isAuthenticated&&(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"pt-6",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)(i.z,{onClick:p.authenticate,className:"flex items-center gap-2",children:[(0,a.jsx)(eu.Z,{className:"h-4 w-4"}),"Connect to Collaboration"]})})})})]})}var ev=t(9102),eb=t.n(ev);function ew(e){var s;let{editorRef:t,onCursorUpdate:r}=e,i=eN(),[l,c]=(0,n.useState)([]);return(0,n.useEffect)(()=>{let e=[];i.cursors.forEach((s,t)=>{var a;t!==(null===(a=i.currentUser)||void 0===a?void 0:a.id)&&e.push({id:t,user:s.user,position:{line:s.line,column:s.column,selection:s.selection}})}),c(e)},[i.cursors,null===(s=i.currentUser)||void 0===s?void 0:s.id]),(0,n.useEffect)(()=>{if(!(null==t?void 0:t.current))return;let e=t.current,s=()=>{var s;let t=e.selectionStart,a=e.selectionEnd,n=e.value.substring(0,t).split("\n"),l=n.length-1,c=(null===(s=n[n.length-1])||void 0===s?void 0:s.length)||0;i.canCollaborate&&i.updateCursorPosition({line:l,column:c,selection:t!==a?{startLine:l,startColumn:c,endLine:l,endColumn:c+(a-t)}:void 0}),null==r||r(l,c)};return e.addEventListener("selectionchange",s),e.addEventListener("keyup",s),e.addEventListener("mouseup",s),()=>{e.removeEventListener("selectionchange",s),e.removeEventListener("keyup",s),e.removeEventListener("mouseup",s)}},[t,i,r]),(0,a.jsxs)("div",{className:"relative",children:[(null==t?void 0:t.current)&&0!==l.length?l.map(e=>(0,a.jsx)(eC,{cursor:e,editorRef:t},e.id)):null,i.canCollaborate&&i.otherParticipants.length>0&&(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"Active collaborators:"}),i.otherParticipants.map(e=>(0,a.jsxs)(y.C,{variant:"outline",className:"text-xs flex items-center gap-1",style:{borderColor:e.color},children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full",style:{backgroundColor:e.color}}),e.name]},e.id))]})]})}function eC(e){let{cursor:s,editorRef:t}=e,[r,i]=(0,n.useState)(null);return((0,n.useEffect)(()=>{var e,a;if(!(null==t?void 0:t.current))return;let n=t.current,r=n.value,l=r.split("\n"),c=0;for(let e=0;e<s.position.line&&e<l.length;e++)c+=((null===(a=l[e])||void 0===a?void 0:a.length)||0)+1;c+=Math.min(s.position.column,(null===(e=l[s.position.line])||void 0===e?void 0:e.length)||0);let o=document.createElement("textarea");o.style.position="absolute",o.style.visibility="hidden",o.style.whiteSpace="pre",o.style.font=window.getComputedStyle(n).font,o.style.fontSize=window.getComputedStyle(n).fontSize,o.style.fontFamily=window.getComputedStyle(n).fontFamily,o.style.lineHeight=window.getComputedStyle(n).lineHeight,o.style.padding=window.getComputedStyle(n).padding,o.style.border=window.getComputedStyle(n).border,o.style.width=n.offsetWidth+"px",o.value=r.substring(0,c),document.body.appendChild(o);let d=n.scrollTop,u=n.scrollLeft,m=parseInt(window.getComputedStyle(n).lineHeight)||20,x=n.getBoundingClientRect();i({top:x.top+s.position.line*m-d,left:x.left+8*s.position.column-u}),document.body.removeChild(o)},[s.position,t]),r)?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{style:{top:r.top,left:r.left,backgroundColor:s.user.color,animation:"blink 1s infinite"},className:"jsx-a58dbfef7be8da39 absolute w-0.5 h-5 z-10 pointer-events-none"}),(0,a.jsx)("div",{style:{top:r.top-25,left:r.left,transform:"translateX(-50%)"},className:"jsx-a58dbfef7be8da39 absolute z-20 pointer-events-none",children:(0,a.jsx)(y.C,{className:"text-xs px-2 py-1 text-white",style:{backgroundColor:s.user.color},children:s.user.name})}),s.position.selection&&(0,a.jsx)("div",{style:{top:r.top,left:r.left,width:(s.position.selection.endColumn-s.position.selection.startColumn)*8,height:20,backgroundColor:s.user.color,opacity:.2},className:"jsx-a58dbfef7be8da39 absolute pointer-events-none z-5"}),(0,a.jsx)(eb(),{id:"a58dbfef7be8da39",children:"@-webkit-keyframes blink{0%,50%{opacity:1}51%,100%{opacity:0}}@-moz-keyframes blink{0%,50%{opacity:1}51%,100%{opacity:0}}@-o-keyframes blink{0%,50%{opacity:1}51%,100%{opacity:0}}@keyframes blink{0%,50%{opacity:1}51%,100%{opacity:0}}"})]}):null}var eS=t(2302),ek=t(3966),eZ=t(7972),eI=t(2176),eE=t(6020);function eT(e){let{className:s,maxHeight:t="400px"}=e,[l,c]=(0,n.useState)(""),[o,d]=(0,n.useState)(!1),u=(0,n.useRef)(null),m=eN();(0,n.useEffect)(()=>{var e;null===(e=u.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})},[m.chatMessages]);let x=()=>{l.trim()&&m.canCollaborate&&(m.sendChatMessage(l.trim()),c(""))},h=e=>new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),f=e=>{switch(e){case"system":return(0,a.jsx)(ek.Z,{className:"h-3 w-3"});case"query_execution":return(0,a.jsx)(Z.Z,{className:"h-3 w-3"});default:return(0,a.jsx)(eZ.Z,{className:"h-3 w-3"})}},p=e=>{var s;let t=e.userId===(null===(s=m.currentUser)||void 0===s?void 0:s.id);return"system"===e.type||"query_execution"===e.type?{container:"bg-muted/50 border-l-2 border-muted-foreground/20",text:"text-muted-foreground text-sm italic"}:t?{container:"bg-primary/10 border-l-2 border-primary/30 ml-4",text:"text-foreground"}:{container:"bg-background border-l-2 border-muted/30",text:"text-foreground"}};return m.canCollaborate?(0,a.jsxs)(r.Zb,{className:s,children:[(0,a.jsx)(r.Ol,{className:"cursor-pointer",onClick:()=>d(!o),children:(0,a.jsxs)(r.ll,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(eI.Z,{className:"h-5 w-5"}),"Chat",m.chatMessages.length>0&&(0,a.jsx)(y.C,{variant:"secondary",className:"text-xs",children:m.chatMessages.length})]}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:m.otherParticipants.length>0&&(0,a.jsxs)(y.C,{variant:"outline",className:"text-xs",children:[m.participantCount," online"]})})]})}),o&&(0,a.jsxs)(r.aY,{className:"space-y-4",children:[(0,a.jsx)(eS.x,{style:{height:t},children:(0,a.jsxs)("div",{className:"space-y-3 pr-4",children:[0===m.chatMessages.length?(0,a.jsxs)("div",{className:"text-center text-muted-foreground text-sm py-8",children:[(0,a.jsx)(eI.Z,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No messages yet. Start the conversation!"})]}):m.chatMessages.map(e=>{var s;let t=p(e),n=e.userId===(null===(s=m.currentUser)||void 0===s?void 0:s.id);return(0,a.jsx)("div",{className:"p-3 rounded-lg ".concat(t.container),children:(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:f(e.type)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:n?"You":e.userName}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:h(e.timestamp)}),"message"!==e.type&&(0,a.jsx)(y.C,{variant:"outline",className:"text-xs",children:"system"===e.type?"System":"Query"})]}),(0,a.jsx)("p",{className:"text-sm ".concat(t.text," break-words"),children:e.message})]})]})},e.id)}),(0,a.jsx)("div",{ref:u})]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(R.I,{value:l,onChange:e=>c(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),x())},placeholder:"Type a message...",className:"flex-1",maxLength:500}),(0,a.jsx)(i.z,{onClick:x,disabled:!l.trim(),size:"sm",className:"flex items-center gap-2",children:(0,a.jsx)(eE.Z,{className:"h-4 w-4"})})]})]})]}):(0,a.jsx)(r.Zb,{className:s,children:(0,a.jsxs)(r.aY,{className:"p-6 text-center",children:[(0,a.jsx)(eI.Z,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-muted-foreground text-sm",children:"Join a collaboration session to start chatting"})]})})}var eR=t(6104),eO=t(9409);function e_(){var e,s;let[t,l]=(0,n.useState)("-- Welcome to QueryCraft Studio Query Editor\n-- Try executing this sample query:\n\nSELECT\n  customer_id,\n  name,\n  email,\n  COUNT(order_id) as total_orders,\n  SUM(order_total) as total_spent\nFROM customers c\nLEFT JOIN orders o ON c.customer_id = o.customer_id\nGROUP BY customer_id, name, email\nORDER BY total_spent DESC\nLIMIT 10;"),[o,d]=(0,n.useState)(null),[u,m]=(0,n.useState)("editor"),[p,j]=(0,n.useState)(null),[v]=(0,n.useState)(n.createRef()),b=eN({connectionId:o||void 0,autoConnect:!0}),w=[{id:"conn1",name:"Production DB (PostgreSQL)",type:"POSTGRESQL"},{id:"conn2",name:"Analytics DB (MySQL)",type:"MYSQL"},{id:"conn3",name:"Development DB (PostgreSQL)",type:"POSTGRESQL"}],C=e=>{l(e),b.canCollaborate&&e!==b.sqlContent&&b.updateSqlContent(e)};n.useEffect(()=>{b.sqlContent&&b.sqlContent!==t&&l(b.sqlContent)},[b.sqlContent]);let k=[{id:"editor",label:"Query Editor",icon:M.Z},{id:"results",label:"Results",icon:Z.Z,disabled:!p},{id:"schema",label:"Schema",icon:el.Z,disabled:!o},{id:"test",label:"Test Connection",icon:eR.Z},{id:"collaborate",label:"Collaborate",icon:eu.Z,badge:b.participantCount>1?b.participantCount:void 0}];return(0,a.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Query Editor"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Execute SQL queries against your connected databases"})]}),(0,a.jsx)("div",{className:"flex items-center gap-4",children:(0,a.jsxs)(x,{value:o||"",onValueChange:d,children:[(0,a.jsx)(f,{className:"w-64",children:(0,a.jsx)(h,{placeholder:"Select a database connection"})}),(0,a.jsx)(g,{children:w.map(e=>(0,a.jsx)(N,{value:e.id,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(S.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:e.name}),(0,a.jsx)(y.C,{variant:"outline",className:"text-xs",children:e.type})]})},e.id))})]})})]}),o&&(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(S.Z,{className:"h-4 w-4 text-green-500"}),(0,a.jsxs)("span",{className:"text-sm",children:["Connected to: ",null===(e=w.find(e=>e.id===o))||void 0===e?void 0:e.name]}),(0,a.jsx)(y.C,{variant:"default",className:"text-xs",children:null===(s=w.find(e=>e.id===o))||void 0===s?void 0:s.type})]})})}),(0,a.jsx)("div",{className:"flex space-x-1 bg-muted p-1 rounded-lg",children:k.map(e=>{let s=e.icon;return(0,a.jsxs)(i.z,{variant:u===e.id?"default":"ghost",size:"sm",onClick:()=>m(e.id),disabled:e.disabled,className:"flex items-center gap-2",children:[(0,a.jsx)(s,{className:"h-4 w-4"}),e.label,e.badge&&(0,a.jsx)(y.C,{variant:"secondary",className:"text-xs",children:e.badge})]},e.id)})}),(0,a.jsxs)("div",{className:"space-y-6",children:["editor"===u&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(M.Z,{className:"h-5 w-5"}),"SQL Editor",b.canCollaborate&&(0,a.jsx)(y.C,{variant:"outline",className:"text-xs",children:"Live"})]}),b.participantCount>1&&(0,a.jsxs)(y.C,{variant:"secondary",className:"text-xs",children:[b.participantCount," collaborators"]})]})}),(0,a.jsxs)(r.aY,{className:"relative",children:[(0,a.jsx)(ew,{editorRef:v,onCursorUpdate:(e,s)=>{}}),(0,a.jsx)(c,{ref:v,value:t,onChange:e=>C(e.target.value),placeholder:"Enter your SQL query here...",className:"min-h-[400px] font-mono text-sm"})]})]}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)(T,{sql:t,connectionId:o,onExecutionComplete:e=>{j(e),e.success&&m("results"),b.canCollaborate&&b.notifyQueryExecutionComplete(e)}})})]}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)(eT,{maxHeight:"300px"})})]}),"results"===u&&p&&(0,a.jsx)("div",{children:p.success&&p.data?(0,a.jsx)(Y,{data:p.data,metadata:p.metadata}):(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"p-8 text-center",children:(0,a.jsx)("div",{className:"text-muted-foreground",children:p.error?(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-red-600 mb-2",children:"Query Failed"}),(0,a.jsx)("p",{className:"text-sm",children:p.error})]}):(0,a.jsx)("p",{children:"No results to display"})})})})}),"schema"===u&&o&&(0,a.jsx)(ec,{connectionId:o}),"test"===u&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[o&&(0,a.jsx)(W,{connectionId:o,onConnectionTested:e=>{console.log("Connection test result:",e)}}),(0,a.jsx)(W,{onConnectionTested:e=>{console.log("New connection test result:",e)}})]}),"collaborate"===u&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)(ey,{connectionId:o||"",onSessionJoined:e=>{console.log("Joined session:",e)},onSessionLeft:()=>{console.log("Left session")}})}),(0,a.jsx)("div",{children:(0,a.jsx)(eT,{maxHeight:"500px"})})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(eO.Z,{className:"h-5 w-5"}),"Quick Actions"]})}),(0,a.jsx)(r.aY,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)(i.z,{variant:"outline",onClick:()=>l("SELECT * FROM users LIMIT 10;"),className:"h-auto p-4 flex flex-col items-center gap-2",children:[(0,a.jsx)(S.Z,{className:"h-6 w-6"}),(0,a.jsx)("span",{className:"text-sm",children:"Sample Query"})]}),(0,a.jsxs)(i.z,{variant:"outline",onClick:()=>l(""),className:"h-auto p-4 flex flex-col items-center gap-2",children:[(0,a.jsx)(M.Z,{className:"h-6 w-6"}),(0,a.jsx)("span",{className:"text-sm",children:"Clear Editor"})]}),(0,a.jsxs)(i.z,{variant:"outline",onClick:()=>m("schema"),disabled:!o,className:"h-auto p-4 flex flex-col items-center gap-2",children:[(0,a.jsx)(el.Z,{className:"h-6 w-6"}),(0,a.jsx)("span",{className:"text-sm",children:"View Schema"})]}),(0,a.jsxs)(i.z,{variant:"outline",onClick:()=>m("test"),className:"h-auto p-4 flex flex-col items-center gap-2",children:[(0,a.jsx)(eR.Z,{className:"h-6 w-6"}),(0,a.jsx)("span",{className:"text-sm",children:"Test Connection"})]})]})})]})]})}},9069:function(e,s,t){"use strict";t.d(s,{X:function(){return o},bZ:function(){return c}});var a=t(7437),n=t(2265),r=t(6061),i=t(2169);let l=(0,r.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=n.forwardRef((e,s)=>{let{className:t,variant:n,...r}=e;return(0,a.jsx)("div",{ref:s,role:"alert",className:(0,i.cn)(l({variant:n}),t),...r})});c.displayName="Alert",n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("h5",{ref:s,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",t),...n})}).displayName="AlertTitle";let o=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",t),...n})});o.displayName="AlertDescription"},3277:function(e,s,t){"use strict";t.d(s,{C:function(){return l}});var a=t(7437);t(2265);var n=t(6061),r=t(2169);let i=(0,n.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,...n}=e;return(0,a.jsx)("div",{className:(0,r.cn)(i({variant:t}),s),...n})}},575:function(e,s,t){"use strict";t.d(s,{z:function(){return o}});var a=t(7437),n=t(2265),r=t(7256),i=t(6061),l=t(2169);let c=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=n.forwardRef((e,s)=>{let{className:t,variant:n,size:i,asChild:o=!1,...d}=e,u=o?r.g7:"button";return(0,a.jsx)(u,{className:(0,l.cn)(c({variant:n,size:i,className:t})),ref:s,...d})});o.displayName="Button"},5671:function(e,s,t){"use strict";t.d(s,{Ol:function(){return l},Zb:function(){return i},aY:function(){return o},ll:function(){return c}});var a=t(7437),n=t(2265),r=t(2169);let i=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...n})});i.displayName="Card";let l=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",t),...n})});l.displayName="CardHeader";let c=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("h3",{ref:s,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",t),...n})});c.displayName="CardTitle",n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("p",{ref:s,className:(0,r.cn)("text-sm text-muted-foreground",t),...n})}).displayName="CardDescription";let o=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.cn)("p-6 pt-0",t),...n})});o.displayName="CardContent",n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.cn)("flex items-center p-6 pt-0",t),...n})}).displayName="CardFooter"},2782:function(e,s,t){"use strict";t.d(s,{I:function(){return i}});var a=t(7437),n=t(2265),r=t(2169);let i=n.forwardRef((e,s)=>{let{className:t,type:n,...i}=e;return(0,a.jsx)("input",{type:n,className:(0,r.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...i})});i.displayName="Input"},5005:function(e,s,t){"use strict";t.d(s,{_:function(){return o}});var a=t(7437),n=t(2265),r=t(6743),i=t(6061),l=t(2169);let c=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(r.f,{ref:s,className:(0,l.cn)(c(),t),...n})});o.displayName=r.f.displayName},2302:function(e,s,t){"use strict";t.d(s,{x:function(){return l}});var a=t(7437),n=t(2265),r=t(5331),i=t(2169);let l=n.forwardRef((e,s)=>{let{className:t,children:n,...l}=e;return(0,a.jsxs)(r.fC,{ref:s,className:(0,i.cn)("relative overflow-hidden",t),...l,children:[(0,a.jsx)(r.l_,{className:"h-full w-full rounded-[inherit]",children:n}),(0,a.jsx)(c,{}),(0,a.jsx)(r.Ns,{})]})});l.displayName=r.fC.displayName;let c=n.forwardRef((e,s)=>{let{className:t,orientation:n="vertical",...l}=e;return(0,a.jsx)(r.gb,{ref:s,orientation:n,className:(0,i.cn)("flex touch-none select-none transition-colors","vertical"===n&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===n&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",t),...l,children:(0,a.jsx)(r.q4,{className:"relative flex-1 rounded-full bg-border"})})});c.displayName=r.gb.displayName},2169:function(e,s,t){"use strict";t.d(s,{SY:function(){return i},cn:function(){return r}});var a=t(7042),n=t(4769);function r(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,n.m6)((0,a.W)(s))}function i(e){let s=new Date,t=new Date(e),a=Math.floor((s.getTime()-t.getTime())/1e3);if(a<60)return"just now";let n=Math.floor(a/60);if(n<60)return"".concat(n," minute").concat(1===n?"":"s"," ago");let r=Math.floor(n/60);if(r<24)return"".concat(r," hour").concat(1===r?"":"s"," ago");let i=Math.floor(r/24);return i<7?"".concat(i," day").concat(1===i?"":"s"," ago"):new Intl.DateTimeFormat("en-US",{month:"long",day:"numeric",year:"numeric"}).format(new Date(e))}}},function(e){e.O(0,[365,936,932,247,971,938,744],function(){return e(e.s=6836)}),_N_E=e.O()}]);