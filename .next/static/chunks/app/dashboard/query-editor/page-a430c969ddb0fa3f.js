(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[809],{6836:function(e,s,t){Promise.resolve().then(t.bind(t,9472))},9472:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return em}});var a=t(7437),n=t(2265),r=t(5671),l=t(575),i=t(2169);let c=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...n})});c.displayName="Textarea";var d=t(8010),o=t(3523),m=t(9224),u=t(2442);let x=d.fC;d.ZA;let h=d.B4,f=n.forwardRef((e,s)=>{let{className:t,children:n,...r}=e;return(0,a.jsxs)(d.xz,{ref:s,className:(0,i.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...r,children:[n,(0,a.jsx)(d.JO,{asChild:!0,children:(0,a.jsx)(o.Z,{className:"h-4 w-4 opacity-50"})})]})});f.displayName=d.xz.displayName;let j=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(d.u_,{ref:s,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...n,children:(0,a.jsx)(m.Z,{className:"h-4 w-4"})})});j.displayName=d.u_.displayName;let p=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(d.$G,{ref:s,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...n,children:(0,a.jsx)(o.Z,{className:"h-4 w-4"})})});p.displayName=d.$G.displayName;let g=n.forwardRef((e,s)=>{let{className:t,children:n,position:r="popper",...l}=e;return(0,a.jsx)(d.h_,{children:(0,a.jsxs)(d.VY,{ref:s,className:(0,i.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,...l,children:[(0,a.jsx)(j,{}),(0,a.jsx)(d.l_,{className:(0,i.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:n}),(0,a.jsx)(p,{})]})})});g.displayName=d.VY.displayName,n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(d.__,{ref:s,className:(0,i.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...n})}).displayName=d.__.displayName;let N=n.forwardRef((e,s)=>{let{className:t,children:n,...r}=e;return(0,a.jsxs)(d.ck,{ref:s,className:(0,i.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...r,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(d.wU,{children:(0,a.jsx)(u.Z,{className:"h-4 w-4"})})}),(0,a.jsx)(d.eT,{children:n})]})});N.displayName=d.ck.displayName,n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(d.Z0,{ref:s,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",t),...n})}).displayName=d.Z0.displayName;var y=t(3277),b=t(9069),w=t(6141),v=t(3008),C=t(2104),S=t(7332),Z=t(6357),T=t(4900),k=t(4056),R=t(2894);function E(e){var s,t;let{sql:i,connectionId:c,onExecutionComplete:d}=e,[o,m]=(0,n.useState)(!1),[u,x]=(0,n.useState)(null),[h,f]=(0,n.useState)(null),[j,p]=(0,n.useState)(!1),g=async()=>{if(c&&i.trim()){m(!0),x(null);try{let e=await fetch("/api/queries/execute",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({connectionId:c,sql:i.trim(),maxRows:1e3,timeout:3e4,saveToHistory:!0})}),s=await e.json();x(s),null==d||d(s)}catch(s){let e={success:!1,error:s instanceof Error?s.message:"Unknown error occurred",metadata:{connectionId:c,executedAt:new Date().toISOString(),executionTime:0,rowsReturned:0,queryHash:""}};x(e),null==d||d(e)}finally{m(!1)}}},N=async()=>{if(c&&i.trim()){p(!0),f(null);try{let e=await fetch("/api/queries/execute/validate?connectionId=".concat(c,"&sql=").concat(encodeURIComponent(i.trim())));if(e.ok){let s=await e.json();f(s.validation)}}catch(e){console.error("Failed to validate query:",e)}finally{p(!1)}}},E=c&&i.trim()&&!o;return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[o?(0,a.jsx)(w.Z,{className:"h-4 w-4 animate-spin"}):(null==u?void 0:u.success)?(0,a.jsx)(v.Z,{className:"h-4 w-4 text-green-500"}):u&&!u.success?(0,a.jsx)(C.Z,{className:"h-4 w-4 text-red-500"}):(0,a.jsx)(S.Z,{className:"h-4 w-4"}),"Query Execution",u&&(0,a.jsx)(y.C,{variant:u.success?"default":"destructive",children:u.success?"Success":"Failed"})]})}),(0,a.jsxs)(r.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(l.z,{onClick:g,disabled:!E,className:"flex items-center gap-2",children:o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(Z.Z,{className:"h-4 w-4"}),"Executing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(T.Z,{className:"h-4 w-4"}),"Execute Query"]})}),(0,a.jsx)(l.z,{variant:"outline",onClick:N,disabled:!c||!i.trim()||j,className:"flex items-center gap-2",children:j?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(w.Z,{className:"h-4 w-4 animate-spin"}),"Validating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.Z,{className:"h-4 w-4"}),"Validate"]})})]}),!c&&(0,a.jsxs)(b.bZ,{children:[(0,a.jsx)(k.Z,{className:"h-4 w-4"}),(0,a.jsx)(b.X,{children:"Please select a database connection to execute queries."})]})]})]}),h&&(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(v.Z,{className:"h-4 w-4"}),"Query Validation",(t=h.estimatedComplexity,(0,a.jsxs)(y.C,{variant:{LOW:"default",MEDIUM:"secondary",HIGH:"destructive"}[t]||"default",children:[t," Complexity"]}))]})}),(0,a.jsxs)(r.aY,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[(0,a.jsx)(y.C,{variant:"outline",children:h.queryType}),h.isReadOnly&&(0,a.jsx)(y.C,{variant:"secondary",children:"Read-Only"})]}),h.errors.length>0&&(0,a.jsxs)(b.bZ,{className:"border-red-200 bg-red-50",children:[(0,a.jsx)(C.Z,{className:"h-4 w-4 text-red-500"}),(0,a.jsxs)(b.X,{children:[(0,a.jsx)("div",{className:"font-medium text-red-700 mb-1",children:"Validation Errors:"}),(0,a.jsx)("ul",{className:"list-disc list-inside text-red-600 text-sm",children:h.errors.map((e,s)=>(0,a.jsx)("li",{children:e},s))})]})]}),h.warnings.length>0&&(0,a.jsxs)(b.bZ,{className:"border-yellow-200 bg-yellow-50",children:[(0,a.jsx)(R.Z,{className:"h-4 w-4 text-yellow-500"}),(0,a.jsxs)(b.X,{children:[(0,a.jsx)("div",{className:"font-medium text-yellow-700 mb-1",children:"Warnings:"}),(0,a.jsx)("ul",{className:"list-disc list-inside text-yellow-600 text-sm",children:h.warnings.map((e,s)=>(0,a.jsx)("li",{children:e},s))})]})]}),h.isValid&&0===h.errors.length&&(0,a.jsxs)(b.bZ,{className:"border-green-200 bg-green-50",children:[(0,a.jsx)(v.Z,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)(b.X,{className:"text-green-700",children:"Query validation passed. Ready to execute."})]})]})]}),u&&(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[u.success?(0,a.jsx)(v.Z,{className:"h-4 w-4 text-green-500"}):(0,a.jsx)(C.Z,{className:"h-4 w-4 text-red-500"}),"Execution Results"]})}),(0,a.jsxs)(r.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-muted-foreground",children:"Execution Time"}),(0,a.jsxs)("div",{className:"font-medium",children:[u.metadata.executionTime,"ms"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-muted-foreground",children:"Rows Returned"}),(0,a.jsx)("div",{className:"font-medium",children:u.metadata.rowsReturned})]}),(null===(s=u.data)||void 0===s?void 0:s.affectedRows)!==void 0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-muted-foreground",children:"Rows Affected"}),(0,a.jsx)("div",{className:"font-medium",children:u.data.affectedRows})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-muted-foreground",children:"Executed At"}),(0,a.jsx)("div",{className:"font-medium",children:new Date(u.metadata.executedAt).toLocaleTimeString()})]})]}),u.error&&(0,a.jsxs)(b.bZ,{className:"border-red-200 bg-red-50",children:[(0,a.jsx)(C.Z,{className:"h-4 w-4 text-red-500"}),(0,a.jsxs)(b.X,{children:[(0,a.jsx)("div",{className:"font-medium text-red-700 mb-1",children:"Execution Error:"}),(0,a.jsx)("div",{className:"text-red-600 text-sm",children:u.error})]})]}),u.warnings&&u.warnings.length>0&&(0,a.jsxs)(b.bZ,{className:"border-yellow-200 bg-yellow-50",children:[(0,a.jsx)(R.Z,{className:"h-4 w-4 text-yellow-500"}),(0,a.jsxs)(b.X,{children:[(0,a.jsx)("div",{className:"font-medium text-yellow-700 mb-1",children:"Warnings:"}),(0,a.jsx)("ul",{className:"list-disc list-inside text-yellow-600 text-sm",children:u.warnings.map((e,s)=>(0,a.jsx)("li",{children:e},s))})]})]}),u.success&&u.data&&(0,a.jsxs)(b.bZ,{className:"border-green-200 bg-green-50",children:[(0,a.jsx)(v.Z,{className:"h-4 w-4 text-green-500"}),(0,a.jsxs)(b.X,{className:"text-green-700",children:["Query executed successfully!",u.data.rowCount>0&&(0,a.jsxs)("span",{children:[" Returned ",u.data.rowCount," rows."]}),void 0!==u.data.affectedRows&&u.data.affectedRows>0&&(0,a.jsxs)("span",{children:[" Affected ",u.data.affectedRows," rows."]})]})]})]})]})]})}var O=t(2782);let F=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:s,className:(0,i.cn)("w-full caption-bottom text-sm",t),...n})})});F.displayName="Table";let L=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("thead",{ref:s,className:(0,i.cn)("[&_tr]:border-b",t),...n})});L.displayName="TableHeader";let D=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("tbody",{ref:s,className:(0,i.cn)("[&_tr:last-child]:border-0",t),...n})});D.displayName="TableBody",n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("tfoot",{ref:s,className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...n})}).displayName="TableFooter";let _=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("tr",{ref:s,className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...n})});_.displayName="TableRow";let I=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("th",{ref:s,className:(0,i.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...n})});I.displayName="TableHead";let Q=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("td",{ref:s,className:(0,i.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...n})});Q.displayName="TableCell",n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("caption",{ref:s,className:(0,i.cn)("mt-4 text-sm text-muted-foreground",t),...n})}).displayName="TableCaption";var q=t(1813),U=t(5817),P=t(6637),z=t(1827),Y=t(1291),M=t(7158);function A(e){let{data:s,metadata:t}=e,[i,c]=(0,n.useState)(""),[d,o]=(0,n.useState)(1),[m]=(0,n.useState)(50),u=(0,n.useMemo)(()=>i?s.rows.filter(e=>Object.values(e).some(e=>String(e).toLowerCase().includes(i.toLowerCase()))):s.rows,[s.rows,i]),x=(0,n.useMemo)(()=>{let e=(d-1)*m;return u.slice(e,e+m)},[u,d,m]),h=Math.ceil(u.length/m),f=(e,s)=>{if(null==e)return(0,a.jsx)("span",{className:"text-muted-foreground italic",children:"NULL"});if(s.includes("json")||s.includes("jsonb"))try{return(0,a.jsx)("pre",{className:"text-xs",children:JSON.stringify(JSON.parse(e),null,2)})}catch(s){return String(e)}if(s.includes("bool"))return(0,a.jsx)(y.C,{variant:e?"default":"secondary",children:e?"TRUE":"FALSE"});if(s.includes("date")||s.includes("time"))try{return new Date(e).toLocaleString()}catch(s){return String(e)}let t=String(e);return t.length>100?(0,a.jsxs)("span",{title:t,children:[t.substring(0,100),"..."]}):t},j=e=>e.includes("int")||e.includes("number")||e.includes("decimal")?"\uD83D\uDD22":e.includes("text")||e.includes("varchar")||e.includes("char")?"\uD83D\uDCDD":e.includes("date")||e.includes("time")?"\uD83D\uDCC5":e.includes("bool")?"✅":e.includes("json")?"\uD83D\uDCCB":"\uD83D\uDCC4";return s.rows.length?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(q.Z,{className:"h-5 w-5"}),"Query Results",(0,a.jsxs)(y.C,{variant:"outline",children:[s.rowCount," rows"]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(l.z,{variant:"outline",size:"sm",onClick:()=>{if(!s.rows.length)return;let e=[s.fields.map(e=>e.name).join(","),...s.rows.map(e=>s.fields.map(s=>{let t=e[s.name];return"string"==typeof t&&(t.includes(",")||t.includes('"'))?'"'.concat(t.replace(/"/g,'""'),'"'):null!=t?t:""}).join(","))].join("\n"),a=new Blob([e],{type:"text/csv;charset=utf-8;"}),n=document.createElement("a"),r=URL.createObjectURL(a);n.setAttribute("href",r),n.setAttribute("download","query_results_".concat(t.queryHash,".csv")),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n)},className:"flex items-center gap-2",children:[(0,a.jsx)(U.Z,{className:"h-4 w-4"}),"CSV"]}),(0,a.jsxs)(l.z,{variant:"outline",size:"sm",onClick:()=>{if(!s.rows.length)return;let e=JSON.stringify(s.rows,null,2),a=new Blob([e],{type:"application/json;charset=utf-8;"}),n=document.createElement("a"),r=URL.createObjectURL(a);n.setAttribute("href",r),n.setAttribute("download","query_results_".concat(t.queryHash,".json")),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n)},className:"flex items-center gap-2",children:[(0,a.jsx)(P.Z,{className:"h-4 w-4"}),"JSON"]})]})]})}),(0,a.jsx)(r.aY,{children:(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[(0,a.jsxs)("span",{children:["Execution time: ",t.executionTime,"ms"]}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:["Rows returned: ",t.rowsReturned]}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:["Executed at: ",new Date(t.executedAt).toLocaleString()]})]})})]}),(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(z.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(O.I,{placeholder:"Search in results...",value:i,onChange:e=>{c(e.target.value),o(1)},className:"pl-10"})]}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:[u.length," of ",s.rows.length," rows"]})]})})}),(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"p-0",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)(F,{children:[(0,a.jsx)(L,{children:(0,a.jsx)(_,{children:s.fields.map(e=>(0,a.jsx)(I,{className:"whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{children:j(e.type)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[e.type,!e.nullable&&(0,a.jsx)("span",{className:"ml-1",children:"NOT NULL"})]})]})]})},e.name))})}),(0,a.jsx)(D,{children:x.map((e,t)=>(0,a.jsx)(_,{children:s.fields.map(s=>(0,a.jsx)(Q,{className:"max-w-xs",children:f(e[s.name],s.type)},s.name))},t))})]})})})}),h>1&&(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Page ",d," of ",h]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(l.z,{variant:"outline",size:"sm",onClick:()=>o(e=>Math.max(1,e-1)),disabled:1===d,className:"flex items-center gap-2",children:[(0,a.jsx)(Y.Z,{className:"h-4 w-4"}),"Previous"]}),(0,a.jsxs)(l.z,{variant:"outline",size:"sm",onClick:()=>o(e=>Math.min(h,e+1)),disabled:d===h,className:"flex items-center gap-2",children:["Next",(0,a.jsx)(M.Z,{className:"h-4 w-4"})]})]})]})})})]}):(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"flex items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(S.Z,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"No data returned"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Query executed successfully but returned no rows"})]})})})}var J=t(5005),V=t(6062);let B=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(V.fC,{ref:s,className:(0,i.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...n,children:(0,a.jsx)(V.z$,{className:(0,i.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(u.Z,{className:"h-4 w-4"})})})});B.displayName=V.fC.displayName;var H=t(6264),X=t(4280);let G={async getUser(e){let s=await fetch("/api/users?id=".concat(e));if(!s.ok)throw Error("Failed to fetch user");return s.json()},async createUser(e){let s=await fetch("/api/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to create user");return s.json()},async updateUser(e,s){let t=await fetch("/api/users?id=".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!t.ok)throw Error("Failed to update user");return t.json()},async getUserConnections(e){let s=await fetch("/api/connections?userId=".concat(e));if(!s.ok)throw Error("Failed to fetch connections");return s.json()},async createConnection(e){let s=await fetch("/api/connections",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to create connection");return s.json()},async updateConnection(e,s){let t=await fetch("/api/connections?id=".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!t.ok)throw Error("Failed to update connection");return t.json()},async deleteConnection(e){if(!(await fetch("/api/connections?id=".concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete connection")},async getUserSessions(e){let s=await fetch("/api/queries?type=sessions&userId=".concat(e));if(!s.ok)throw Error("Failed to fetch sessions");return s.json()},async getSession(e){let s=await fetch("/api/queries?sessionId=".concat(e));if(!s.ok)throw Error("Failed to fetch session");return s.json()},async createSession(e){let s=await fetch("/api/queries?type=session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to create session");return s.json()},async createQuery(e){let s=await fetch("/api/queries?type=query",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to create query");return s.json()},async getUserQueryHistory(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,t=await fetch("/api/queries?type=history&userId=".concat(e,"&limit=").concat(s));if(!t.ok)throw Error("Failed to fetch query history");return t.json()},async testConnection(e,s){let t=await fetch("/api/connections/test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e?{connectionId:e}:s)});if(!t.ok)throw Error((await t.json()).error||"Failed to test connection");return t.json()},async getConnectionSchema(e){let s=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=await fetch("/api/connections/schema?connectionId=".concat(e,"&refresh=").concat(s));if(!t.ok)throw Error((await t.json()).error||"Failed to fetch schema");return t.json()},async refreshConnectionSchema(e){let s=await fetch("/api/connections/schema",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({connectionId:e})});if(!s.ok)throw Error((await s.json()).error||"Failed to refresh schema");return s.json()},async executeQuery(e){let s=await fetch("/api/queries/execute",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error((await s.json()).error||"Failed to execute query");return s.json()},async validateQuery(e,s){let t=await fetch("/api/queries/execute/validate?connectionId=".concat(e,"&sql=").concat(encodeURIComponent(s)));if(!t.ok)throw Error((await t.json()).error||"Failed to validate query");return t.json()},async updateQueryFeedback(e,s){let t=await fetch("/api/queries?type=query&id=".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({userFeedback:s})});if(!t.ok)throw Error("Failed to update query feedback");return t.json()}},K=()=>G;function W(e){let{connectionId:s,onConnectionTested:t}=e,[i,c]=(0,n.useState)(!1),[d,o]=(0,n.useState)(null),[m,u]=(0,n.useState)({databaseType:"POSTGRESQL",connectionString:"",host:"",port:5432,database:"",username:"",password:"",ssl:!1}),j=K(),p=async()=>{c(!0),o(null);try{let e;e=s?await j.testConnection(s):await j.testConnection(void 0,m),o(e),null==t||t(e)}catch(s){let e={success:!1,connected:!1,error:s instanceof Error?s.message:"Unknown error",timestamp:new Date().toISOString()};o(e),null==t||t(e)}finally{c(!1)}},w=(e,s)=>{u(t=>({...t,[e]:s})),o(null)};return(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[i?(0,a.jsx)(H.Z,{className:"h-4 w-4 animate-spin"}):(null==d?void 0:d.connected)?(0,a.jsx)(v.Z,{className:"h-4 w-4 text-green-500"}):d&&!d.connected?(0,a.jsx)(C.Z,{className:"h-4 w-4 text-red-500"}):(0,a.jsx)(S.Z,{className:"h-4 w-4"}),s?"Test Connection":"Test Database Connection",i?(0,a.jsx)(y.C,{variant:"secondary",children:"Testing..."}):(null==d?void 0:d.connected)?(0,a.jsx)(y.C,{variant:"default",className:"bg-green-100 text-green-800",children:"Connected"}):d&&!d.connected?(0,a.jsx)(y.C,{variant:"destructive",children:"Failed"}):null]})}),(0,a.jsxs)(r.aY,{className:"space-y-4",children:[!s&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(J._,{htmlFor:"databaseType",children:"Database Type"}),(0,a.jsxs)(x,{value:m.databaseType,onValueChange:e=>w("databaseType",e),children:[(0,a.jsx)(f,{children:(0,a.jsx)(h,{})}),(0,a.jsxs)(g,{children:[(0,a.jsx)(N,{value:"POSTGRESQL",children:"PostgreSQL"}),(0,a.jsx)(N,{value:"MYSQL",children:"MySQL"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(J._,{htmlFor:"connectionString",children:"Connection String (Optional)"}),(0,a.jsx)(O.I,{id:"connectionString",type:"text",placeholder:"postgresql://user:password@host:port/database",value:m.connectionString,onChange:e=>w("connectionString",e.target.value)}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Leave empty to use individual connection fields below"})]}),!m.connectionString&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(J._,{htmlFor:"host",children:"Host"}),(0,a.jsx)(O.I,{id:"host",type:"text",placeholder:"localhost",value:m.host,onChange:e=>w("host",e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(J._,{htmlFor:"port",children:"Port"}),(0,a.jsx)(O.I,{id:"port",type:"number",placeholder:"MYSQL"===m.databaseType?"3306":"5432",value:m.port,onChange:e=>w("port",parseInt(e.target.value)||5432)})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(J._,{htmlFor:"database",children:"Database Name"}),(0,a.jsx)(O.I,{id:"database",type:"text",placeholder:"my_database",value:m.database,onChange:e=>w("database",e.target.value)})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(J._,{htmlFor:"username",children:"Username"}),(0,a.jsx)(O.I,{id:"username",type:"text",placeholder:"username",value:m.username,onChange:e=>w("username",e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(J._,{htmlFor:"password",children:"Password"}),(0,a.jsx)(O.I,{id:"password",type:"password",placeholder:"password",value:m.password,onChange:e=>w("password",e.target.value)})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(B,{id:"ssl",checked:m.ssl,onCheckedChange:e=>w("ssl",e)}),(0,a.jsx)(J._,{htmlFor:"ssl",children:"Use SSL"})]})]})]}),(0,a.jsx)(l.z,{onClick:p,disabled:i||!s&&!m.connectionString&&(!m.host||!m.database||!m.username),className:"w-full",children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(H.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Testing Connection..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(X.Z,{className:"mr-2 h-4 w-4"}),"Test Connection"]})}),d&&(0,a.jsx)(b.bZ,{className:d.connected?"border-green-200 bg-green-50":"border-red-200 bg-red-50",children:(0,a.jsxs)(b.X,{children:[d.connected?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(v.Z,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"text-green-700",children:"Connection successful!"})]}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(C.Z,{className:"h-4 w-4 text-red-500"}),(0,a.jsx)("span",{className:"text-red-700",children:"Connection failed"})]}),d.error&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:d.error})]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-2",children:["Tested at ",new Date(d.timestamp).toLocaleString()]})]})})]})]})}var $=t(1927);let ee=$.fC,es=$.wy,et=$.Fw;var ea=t(774),en=t(7706),er=t(3223),el=t(4689),ei=t(9670);function ec(e){let{connectionId:s}=e,[t,i]=(0,n.useState)(null),[c,d]=(0,n.useState)(!1),[m,u]=(0,n.useState)(null),[x,h]=(0,n.useState)(null),[f,j]=(0,n.useState)(!1),[p,g]=(0,n.useState)(new Set),[N,w]=(0,n.useState)(new Set),v=K(),C=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];d(!0),u(null);try{let t=await v.getConnectionSchema(s,e);i(t.schema),h(t.lastUpdated),j(t.cached)}catch(e){u(e instanceof Error?e.message:"Failed to load schema")}finally{d(!1)}},Z=async()=>{d(!0),u(null);try{let e=await v.refreshConnectionSchema(s);i(e.schema),h(e.lastUpdated),j(!1)}catch(e){u(e instanceof Error?e.message:"Failed to refresh schema")}finally{d(!1)}};(0,n.useEffect)(()=>{C()},[s]);let T=e=>{let s=new Set(p);s.has(e)?s.delete(e):s.add(e),g(s)},k=e=>{let s=new Set(N);s.has(e)?s.delete(e):s.add(e),w(s)},R=e=>e.isPrimaryKey?(0,a.jsx)(ea.Z,{className:"h-3 w-3 text-yellow-500"}):e.isForeignKey?(0,a.jsx)(ea.Z,{className:"h-3 w-3 text-blue-500"}):(0,a.jsx)(en.Z,{className:"h-3 w-3 text-gray-400"}),E=e=>{let s=[];return e.isPrimaryKey&&s.push((0,a.jsx)(y.C,{variant:"secondary",className:"text-xs",children:"PK"},"pk")),e.isForeignKey&&s.push((0,a.jsx)(y.C,{variant:"outline",className:"text-xs",children:"FK"},"fk")),e.nullable||s.push((0,a.jsx)(y.C,{variant:"outline",className:"text-xs",children:"NOT NULL"},"nn")),s};return c&&!t?(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"flex items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(H.Z,{className:"h-4 w-4 animate-spin"}),(0,a.jsx)("span",{children:"Loading database schema..."})]})})}):m?(0,a.jsx)(r.Zb,{children:(0,a.jsxs)(r.aY,{className:"p-6",children:[(0,a.jsx)(b.bZ,{className:"border-red-200 bg-red-50",children:(0,a.jsx)(b.X,{className:"text-red-700",children:m})}),(0,a.jsxs)(l.z,{onClick:()=>C(),className:"mt-4",children:[(0,a.jsx)(X.Z,{className:"mr-2 h-4 w-4"}),"Retry"]})]})}):t?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(r.Zb,{children:(0,a.jsxs)(r.Ol,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(S.Z,{className:"h-5 w-5"}),"Database Schema",f&&(0,a.jsx)(y.C,{variant:"secondary",children:"Cached"})]}),(0,a.jsxs)(l.z,{onClick:Z,disabled:c,variant:"outline",size:"sm",children:[c?(0,a.jsx)(H.Z,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(X.Z,{className:"mr-2 h-4 w-4"}),"Refresh"]})]}),x&&(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Last updated: ",new Date(x).toLocaleString()]})]})}),t.tables&&t.tables.length>0&&(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(er.Z,{className:"h-4 w-4"}),"Tables (",t.tables.length,")"]})}),(0,a.jsx)(r.aY,{className:"space-y-2",children:t.tables.map(e=>(0,a.jsxs)(ee,{open:p.has(e.name),onOpenChange:()=>T(e.name),children:[(0,a.jsxs)(es,{className:"flex items-center gap-2 w-full p-2 hover:bg-muted rounded",children:[p.has(e.name)?(0,a.jsx)(o.Z,{className:"h-4 w-4"}):(0,a.jsx)(M.Z,{className:"h-4 w-4"}),(0,a.jsx)(er.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium",children:e.name}),(0,a.jsxs)(y.C,{variant:"outline",className:"text-xs",children:[e.columns.length," columns"]}),e.indexes.length>0&&(0,a.jsxs)(y.C,{variant:"outline",className:"text-xs",children:[e.indexes.length," indexes"]})]}),(0,a.jsxs)(et,{className:"ml-6 mt-2 space-y-2",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Columns"}),e.columns.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-muted/50 rounded text-sm",children:[R(e),(0,a.jsx)("span",{className:"font-mono",children:e.name}),(0,a.jsx)(y.C,{variant:"outline",className:"text-xs",children:e.type}),(0,a.jsx)("div",{className:"flex gap-1",children:E(e)}),e.isForeignKey&&e.referencedTable&&(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:["→ ",e.referencedTable,".",e.referencedColumn]})]},e.name))]}),e.indexes.length>0&&(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Indexes"}),e.indexes.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-muted/50 rounded text-sm",children:[(0,a.jsx)(el.Z,{className:"h-3 w-3 text-gray-400"}),(0,a.jsx)("span",{className:"font-mono",children:e.name}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:["(",e.columns.join(", "),")"]}),e.isUnique&&(0,a.jsx)(y.C,{variant:"outline",className:"text-xs",children:"UNIQUE"}),e.isPrimary&&(0,a.jsx)(y.C,{variant:"secondary",className:"text-xs",children:"PRIMARY"})]},e.name))]})]})]},"".concat(e.schema,".").concat(e.name)))})]}),t.views&&t.views.length>0&&(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(ei.Z,{className:"h-4 w-4"}),"Views (",t.views.length,")"]})}),(0,a.jsx)(r.aY,{className:"space-y-2",children:t.views.map(e=>(0,a.jsxs)(ee,{open:N.has(e.name),onOpenChange:()=>k(e.name),children:[(0,a.jsxs)(es,{className:"flex items-center gap-2 w-full p-2 hover:bg-muted rounded",children:[N.has(e.name)?(0,a.jsx)(o.Z,{className:"h-4 w-4"}):(0,a.jsx)(M.Z,{className:"h-4 w-4"}),(0,a.jsx)(ei.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium",children:e.name})]}),(0,a.jsx)(et,{className:"ml-6 mt-2",children:(0,a.jsx)("div",{className:"p-3 bg-muted/50 rounded",children:(0,a.jsx)("pre",{className:"text-xs overflow-x-auto whitespace-pre-wrap",children:e.definition})})})]},"".concat(e.schema,".").concat(e.name)))})]}),(!t.tables||0===t.tables.length)&&(!t.views||0===t.views.length)&&(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"flex items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(S.Z,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"No tables or views found in this database"})]})})})]}):(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"flex items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(S.Z,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"No schema data available"}),(0,a.jsxs)(l.z,{onClick:()=>C(!0),className:"mt-4",children:[(0,a.jsx)(X.Z,{className:"mr-2 h-4 w-4"}),"Load Schema"]})]})})})}var ed=t(6104),eo=t(9409);function em(){var e,s;let[t,i]=(0,n.useState)("-- Welcome to QueryCraft Studio Query Editor\n-- Try executing this sample query:\n\nSELECT \n  customer_id,\n  name,\n  email,\n  COUNT(order_id) as total_orders,\n  SUM(order_total) as total_spent\nFROM customers c\nLEFT JOIN orders o ON c.customer_id = o.customer_id\nGROUP BY customer_id, name, email\nORDER BY total_spent DESC\nLIMIT 10;"),[d,o]=(0,n.useState)(null),[m,u]=(0,n.useState)("editor"),[j,p]=(0,n.useState)(null),b=[{id:"conn1",name:"Production DB (PostgreSQL)",type:"POSTGRESQL"},{id:"conn2",name:"Analytics DB (MySQL)",type:"MYSQL"},{id:"conn3",name:"Development DB (PostgreSQL)",type:"POSTGRESQL"}],w=[{id:"editor",label:"Query Editor",icon:P.Z},{id:"results",label:"Results",icon:T.Z,disabled:!j},{id:"schema",label:"Schema",icon:ei.Z,disabled:!d},{id:"test",label:"Test Connection",icon:ed.Z}];return(0,a.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Query Editor"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Execute SQL queries against your connected databases"})]}),(0,a.jsx)("div",{className:"flex items-center gap-4",children:(0,a.jsxs)(x,{value:d||"",onValueChange:o,children:[(0,a.jsx)(f,{className:"w-64",children:(0,a.jsx)(h,{placeholder:"Select a database connection"})}),(0,a.jsx)(g,{children:b.map(e=>(0,a.jsx)(N,{value:e.id,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(S.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:e.name}),(0,a.jsx)(y.C,{variant:"outline",className:"text-xs",children:e.type})]})},e.id))})]})})]}),d&&(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(S.Z,{className:"h-4 w-4 text-green-500"}),(0,a.jsxs)("span",{className:"text-sm",children:["Connected to: ",null===(e=b.find(e=>e.id===d))||void 0===e?void 0:e.name]}),(0,a.jsx)(y.C,{variant:"default",className:"text-xs",children:null===(s=b.find(e=>e.id===d))||void 0===s?void 0:s.type})]})})}),(0,a.jsx)("div",{className:"flex space-x-1 bg-muted p-1 rounded-lg",children:w.map(e=>{let s=e.icon;return(0,a.jsxs)(l.z,{variant:m===e.id?"default":"ghost",size:"sm",onClick:()=>u(e.id),disabled:e.disabled,className:"flex items-center gap-2",children:[(0,a.jsx)(s,{className:"h-4 w-4"}),e.label]},e.id)})}),(0,a.jsxs)("div",{className:"space-y-6",children:["editor"===m&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(P.Z,{className:"h-5 w-5"}),"SQL Editor"]})}),(0,a.jsx)(r.aY,{children:(0,a.jsx)(c,{value:t,onChange:e=>i(e.target.value),placeholder:"Enter your SQL query here...",className:"min-h-[400px] font-mono text-sm"})})]}),(0,a.jsx)("div",{children:(0,a.jsx)(E,{sql:t,connectionId:d,onExecutionComplete:e=>{p(e),e.success&&u("results")}})})]}),"results"===m&&j&&(0,a.jsx)("div",{children:j.success&&j.data?(0,a.jsx)(A,{data:j.data,metadata:j.metadata}):(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"p-8 text-center",children:(0,a.jsx)("div",{className:"text-muted-foreground",children:j.error?(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-red-600 mb-2",children:"Query Failed"}),(0,a.jsx)("p",{className:"text-sm",children:j.error})]}):(0,a.jsx)("p",{children:"No results to display"})})})})}),"schema"===m&&d&&(0,a.jsx)(ec,{connectionId:d}),"test"===m&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[d&&(0,a.jsx)(W,{connectionId:d,onConnectionTested:e=>{console.log("Connection test result:",e)}}),(0,a.jsx)(W,{onConnectionTested:e=>{console.log("New connection test result:",e)}})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(eo.Z,{className:"h-5 w-5"}),"Quick Actions"]})}),(0,a.jsx)(r.aY,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)(l.z,{variant:"outline",onClick:()=>i("SELECT * FROM users LIMIT 10;"),className:"h-auto p-4 flex flex-col items-center gap-2",children:[(0,a.jsx)(S.Z,{className:"h-6 w-6"}),(0,a.jsx)("span",{className:"text-sm",children:"Sample Query"})]}),(0,a.jsxs)(l.z,{variant:"outline",onClick:()=>i(""),className:"h-auto p-4 flex flex-col items-center gap-2",children:[(0,a.jsx)(P.Z,{className:"h-6 w-6"}),(0,a.jsx)("span",{className:"text-sm",children:"Clear Editor"})]}),(0,a.jsxs)(l.z,{variant:"outline",onClick:()=>u("schema"),disabled:!d,className:"h-auto p-4 flex flex-col items-center gap-2",children:[(0,a.jsx)(ei.Z,{className:"h-6 w-6"}),(0,a.jsx)("span",{className:"text-sm",children:"View Schema"})]}),(0,a.jsxs)(l.z,{variant:"outline",onClick:()=>u("test"),className:"h-auto p-4 flex flex-col items-center gap-2",children:[(0,a.jsx)(ed.Z,{className:"h-6 w-6"}),(0,a.jsx)("span",{className:"text-sm",children:"Test Connection"})]})]})})]})]})}},9069:function(e,s,t){"use strict";t.d(s,{X:function(){return d},bZ:function(){return c}});var a=t(7437),n=t(2265),r=t(6061),l=t(2169);let i=(0,r.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=n.forwardRef((e,s)=>{let{className:t,variant:n,...r}=e;return(0,a.jsx)("div",{ref:s,role:"alert",className:(0,l.cn)(i({variant:n}),t),...r})});c.displayName="Alert",n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("h5",{ref:s,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",t),...n})}).displayName="AlertTitle";let d=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",t),...n})});d.displayName="AlertDescription"},3277:function(e,s,t){"use strict";t.d(s,{C:function(){return i}});var a=t(7437);t(2265);var n=t(6061),r=t(2169);let l=(0,n.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:t,...n}=e;return(0,a.jsx)("div",{className:(0,r.cn)(l({variant:t}),s),...n})}},575:function(e,s,t){"use strict";t.d(s,{z:function(){return d}});var a=t(7437),n=t(2265),r=t(7256),l=t(6061),i=t(2169);let c=(0,l.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,s)=>{let{className:t,variant:n,size:l,asChild:d=!1,...o}=e,m=d?r.g7:"button";return(0,a.jsx)(m,{className:(0,i.cn)(c({variant:n,size:l,className:t})),ref:s,...o})});d.displayName="Button"},5671:function(e,s,t){"use strict";t.d(s,{Ol:function(){return i},Zb:function(){return l},aY:function(){return d},ll:function(){return c}});var a=t(7437),n=t(2265),r=t(2169);let l=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...n})});l.displayName="Card";let i=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",t),...n})});i.displayName="CardHeader";let c=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("h3",{ref:s,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",t),...n})});c.displayName="CardTitle",n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("p",{ref:s,className:(0,r.cn)("text-sm text-muted-foreground",t),...n})}).displayName="CardDescription";let d=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.cn)("p-6 pt-0",t),...n})});d.displayName="CardContent",n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.cn)("flex items-center p-6 pt-0",t),...n})}).displayName="CardFooter"},2782:function(e,s,t){"use strict";t.d(s,{I:function(){return l}});var a=t(7437),n=t(2265),r=t(2169);let l=n.forwardRef((e,s)=>{let{className:t,type:n,...l}=e;return(0,a.jsx)("input",{type:n,className:(0,r.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...l})});l.displayName="Input"},5005:function(e,s,t){"use strict";t.d(s,{_:function(){return d}});var a=t(7437),n=t(2265),r=t(6743),l=t(6061),i=t(2169);let c=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(r.f,{ref:s,className:(0,i.cn)(c(),t),...n})});d.displayName=r.f.displayName},2169:function(e,s,t){"use strict";t.d(s,{SY:function(){return l},cn:function(){return r}});var a=t(7042),n=t(4769);function r(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,n.m6)((0,a.W)(s))}function l(e){let s=new Date,t=new Date(e),a=Math.floor((s.getTime()-t.getTime())/1e3);if(a<60)return"just now";let n=Math.floor(a/60);if(n<60)return"".concat(n," minute").concat(1===n?"":"s"," ago");let r=Math.floor(n/60);if(r<24)return"".concat(r," hour").concat(1===r?"":"s"," ago");let l=Math.floor(r/24);return l<7?"".concat(l," day").concat(1===l?"":"s"," ago"):new Intl.DateTimeFormat("en-US",{month:"long",day:"numeric",year:"numeric"}).format(new Date(e))}}},function(e){e.O(0,[432,936,150,428,971,938,744],function(){return e(e.s=6836)}),_N_E=e.O()}]);