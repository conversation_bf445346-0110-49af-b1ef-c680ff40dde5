(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{6615:function(e,t,s){Promise.resolve().then(s.t.bind(s,7005,23)),Promise.resolve().then(s.t.bind(s,6518,23)),Promise.resolve().then(s.t.bind(s,3385,23)),Promise.resolve().then(s.bind(s,7518)),Promise.resolve().then(s.bind(s,7451))},7518:function(e,t,s){"use strict";s.r(t),s.d(t,{Providers:function(){return c}});var r=s(7437),n=s(9715),a=s(8038),o=s(2749),i=s(2265),d=s(4227);function u(e){let{children:t}=e,{data:s,status:n}=(0,o.useSession)(),{setCurrentUser:a,loadUserConnections:u,loadUserSessions:c,currentUser:l,connections:p}=(0,d.l)();return(0,i.useEffect)(()=>{let e=async()=>{if("authenticated"===n&&(null==s?void 0:s.user)&&!l)try{a({id:s.user.id,name:s.user.name||"",email:s.user.email||""}),await u(s.user.id),await c(s.user.id);let e=d.l.getState();e.connections.length>0&&e.connections[0]&&d.l.getState().setCurrentDatabase(e.connections[0])}catch(e){console.error("Failed to initialize authenticated user:",e)}else if("unauthenticated"===n&&!l)try{let e=await fetch("/api/users?email=<EMAIL>");if(e.ok){let t=await e.json();a({id:t.id,name:t.name,email:t.email}),await u(t.id),await c(t.id);let s=d.l.getState();s.connections.length>0&&s.connections[0]&&d.l.getState().setCurrentDatabase(s.connections[0])}else console.error("Demo user not found in database")}catch(e){console.error("Failed to initialize demo user:",e)}};"loading"!==n&&e()},[s,n,l,a,u,c]),(0,r.jsx)(r.Fragment,{children:t})}function c(e){let{children:t}=e,[s]=(0,i.useState)(()=>new n.S({defaultOptions:{queries:{staleTime:6e4,retry:(e,t)=>(!((null==t?void 0:t.status)>=400)||!((null==t?void 0:t.status)<500))&&e<3},mutations:{retry:!1}}}));return(0,r.jsx)(o.SessionProvider,{children:(0,r.jsx)(a.aH,{client:s,children:(0,r.jsx)(u,{children:t})})})}},7451:function(e,t,s){"use strict";s.r(t),s.d(t,{Toaster:function(){return _}});var r=s(7437),n=s(2265);let a=0,o=new Map,i=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),l({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},d=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?i(s):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},u=[],c={toasts:[]};function l(e){c=d(c,e),u.forEach(e=>{e(c)})}function p(e){let{...t}=e,s=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>l({type:"DISMISS_TOAST",toastId:s});return l({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||r()}}}),{id:s,dismiss:r,update:e=>l({type:"UPDATE_TOAST",toast:{...e,id:s}})}}var m=s(3022),f=s(6061),y=s(2549),g=s(2169);let h=m.zt,v=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)(m.l_,{ref:t,className:(0,g.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",s),...n})});v.displayName=m.l_.displayName;let w=(0,f.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),b=n.forwardRef((e,t)=>{let{className:s,variant:n,...a}=e;return(0,r.jsx)(m.fC,{ref:t,className:(0,g.cn)(w({variant:n}),s),...a})});b.displayName=m.fC.displayName,n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)(m.aU,{ref:t,className:(0,g.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",s),...n})}).displayName=m.aU.displayName;let S=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)(m.x8,{ref:t,className:(0,g.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",s),"toast-close":"",...n,children:(0,r.jsx)(y.Z,{className:"h-4 w-4"})})});S.displayName=m.x8.displayName;let T=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)(m.Dx,{ref:t,className:(0,g.cn)("text-sm font-semibold",s),...n})});T.displayName=m.Dx.displayName;let x=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)(m.dk,{ref:t,className:(0,g.cn)("text-sm opacity-90",s),...n})});function _(){let{toasts:e}=function(){let[e,t]=n.useState(c);return n.useEffect(()=>(u.push(t),()=>{let e=u.indexOf(t);e>-1&&u.splice(e,1)}),[e]),{...e,toast:p,dismiss:e=>l({type:"DISMISS_TOAST",toastId:e})}}();return(0,r.jsxs)(h,{children:[e.map(function(e){let{id:t,title:s,description:n,action:a,...o}=e;return(0,r.jsxs)(b,{...o,children:[(0,r.jsxs)("div",{className:"grid gap-1",children:[s&&(0,r.jsx)(T,{children:s}),n&&(0,r.jsx)(x,{children:n})]}),a,(0,r.jsx)(S,{})]},t)}),(0,r.jsx)(v,{})]})}x.displayName=m.dk.displayName},2169:function(e,t,s){"use strict";s.d(t,{SY:function(){return o},cn:function(){return a}});var r=s(7042),n=s(4769);function a(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,n.m6)((0,r.W)(t))}function o(e){let t=new Date,s=new Date(e),r=Math.floor((t.getTime()-s.getTime())/1e3);if(r<60)return"just now";let n=Math.floor(r/60);if(n<60)return"".concat(n," minute").concat(1===n?"":"s"," ago");let a=Math.floor(n/60);if(a<24)return"".concat(a," hour").concat(1===a?"":"s"," ago");let o=Math.floor(a/24);return o<7?"".concat(o," day").concat(1===o?"":"s"," ago"):new Intl.DateTimeFormat("en-US",{month:"long",day:"numeric",year:"numeric"}).format(new Date(e))}},4227:function(e,t,s){"use strict";s.d(t,{l:function(){return a}});var r=s(4660),n=s(4810);let a=(0,r.Ue)()((0,n.mW)((0,n.tJ)((e,t)=>({currentSession:null,currentQuery:"",currentDatabase:null,messages:[{id:"1",role:"assistant",content:"Hello! I'm your AI SQL assistant. I can help you write, debug, and optimize SQL queries. What would you like to work on today?",timestamp:new Date,type:"text"}],isAILoading:!1,queryHistory:[],connections:[],activeTab:"chat",sidebarOpen:!0,currentUser:null,setCurrentQuery:t=>e({currentQuery:t}),setCurrentDatabase:t=>e({currentDatabase:t}),addMessage:t=>e(e=>({messages:[...e.messages,t]})),setAILoading:t=>e({isAILoading:t}),addToHistory:t=>e(e=>({queryHistory:[t,...e.queryHistory.slice(0,49)]})),setActiveTab:t=>e({activeTab:t}),setSidebarOpen:t=>e({sidebarOpen:t}),addConnection:t=>e(e=>({connections:[...e.connections,t]})),removeConnection:t=>e(e=>({connections:e.connections.filter(e=>e.id!==t)})),clearMessages:()=>e({messages:[{id:"1",role:"assistant",content:"Hello! I'm your AI SQL assistant. I can help you write, debug, and optimize SQL queries. What would you like to work on today?",timestamp:new Date,type:"text"}]}),generateQuery:async e=>{let{addMessage:s,setAILoading:r,setCurrentQuery:n,setActiveTab:a,currentDatabase:i}=t();s({id:Date.now().toString(),role:"user",content:e,timestamp:new Date,type:"text"}),r(!0);try{await new Promise(e=>setTimeout(e,1500));let r=function(e,t){let s=e.toLowerCase();return s.includes("customer")&&s.includes("revenue")?"SELECT \n  c.customer_id,\n  c.name,\n  c.email,\n  COUNT(o.order_id) as total_orders,\n  SUM(o.total_amount) as total_revenue\nFROM customers c\nLEFT JOIN orders o ON c.customer_id = o.customer_id\nWHERE o.order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)\nGROUP BY c.customer_id, c.name, c.email\nORDER BY total_revenue DESC\nLIMIT 10;":s.includes("product")&&s.includes("sales")?"SELECT \n  p.product_id,\n  p.name as product_name,\n  COUNT(oi.item_id) as items_sold,\n  SUM(oi.quantity * oi.unit_price) as total_sales\nFROM products p\nJOIN order_items oi ON p.product_id = oi.product_id\nJOIN orders o ON oi.order_id = o.order_id\nWHERE o.order_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)\nGROUP BY p.product_id, p.name\nORDER BY total_sales DESC;":s.includes("order")&&s.includes("status")?"SELECT \n  status,\n  COUNT(*) as order_count,\n  AVG(total_amount) as avg_order_value\nFROM orders\nWHERE order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)\nGROUP BY status\nORDER BY order_count DESC;":"SELECT *\nFROM your_table\nWHERE condition = 'value'\nORDER BY created_at DESC\nLIMIT 10;"}(e,null==i||i.databaseType),a={id:(Date.now()+1).toString(),role:"assistant",content:"I've generated a SQL query based on your request. Here's what I came up with:",timestamp:new Date,type:"query",metadata:{sql:r,explanation:o(e),userInput:e,suggestions:["Consider adding appropriate indexes for better performance","Add error handling for edge cases","Test with sample data before running on production"]}};s(a),n(r);let d={id:Date.now().toString(),sessionId:"current",userInput:e,generatedSQL:r,explanation:o(e),createdAt:new Date};t().addToHistory(d)}catch(e){s({id:(Date.now()+1).toString(),role:"assistant",content:"I apologize, but I encountered an error while generating your query. Please try again or rephrase your request.",timestamp:new Date,type:"text"})}finally{r(!1)}},optimizeQuery:async e=>(await new Promise(e=>setTimeout(e,1e3)),"-- Optimized version\n".concat(e,"\n-- Added index hints and optimizations")),explainQuery:async e=>(await new Promise(e=>setTimeout(e,800)),"This query performs the following operations:\n1. Selects data from the specified tables\n2. Applies filtering conditions\n3. Groups and aggregates results\n4. Orders the output for better readability"),setCurrentUser:t=>e({currentUser:t}),loadUserSessions:async e=>{try{let t=await fetch("/api/queries?type=sessions&userId=".concat(e));if(t.ok){let e=await t.json();console.log("Loaded sessions:",e)}}catch(e){console.error("Failed to load user sessions:",e)}},loadUserConnections:async t=>{try{let s=await fetch("/api/connections?userId=".concat(t));if(s.ok){let t=await s.json();e({connections:t})}}catch(e){console.error("Failed to load user connections:",e)}},createNewSession:async(t,s)=>{try{let r=await fetch("/api/queries?type=session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:t,databaseConnectionId:s,name:"Session ".concat(new Date().toLocaleString())})});if(r.ok){let t=await r.json();e({currentSession:t}),console.log("Created new session:",t)}}catch(e){console.error("Failed to create new session:",e)}},saveQueryToDatabase:async(e,s,r)=>{let{currentSession:n,currentUser:a,currentDatabase:o}=t();if(!n||!a||!o){console.warn("Cannot save query: missing session, user, or database");return}try{let t=await fetch("/api/queries?type=query",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sessionId:n.id,userId:a.id,userInput:e,generatedSQL:s,explanation:r,databaseType:o.databaseType})});if(t.ok){let e=await t.json();console.log("Saved query to database:",e)}}catch(e){console.error("Failed to save query to database:",e)}}}),{name:"querycraft-store",partialize:e=>({queryHistory:e.queryHistory,connections:e.connections,sidebarOpen:e.sidebarOpen,currentDatabase:e.currentDatabase})}),{name:"querycraft-store"}));function o(e){let t=e.toLowerCase();return t.includes("customer")&&t.includes("revenue")?"This query retrieves the top customers by revenue in the last 30 days, including their contact information and order statistics.":t.includes("product")&&t.includes("sales")?"This query analyzes product sales performance over the last 7 days, showing which products are selling best.":t.includes("order")&&t.includes("status")?"This query provides an overview of order statuses and their distribution over the last 30 days.":"This query retrieves data based on your specified criteria with appropriate filtering and sorting."}},3385:function(){}},function(e){e.O(0,[570,82,422,971,938,744],function(){return e(e.s=6615)}),_N_E=e.O()}]);