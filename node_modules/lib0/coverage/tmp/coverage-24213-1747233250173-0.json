{"result": [{"scriptId": "3", "url": "node:internal/per_context/primordials", "functions": [{"functionName": "SafeIterator", "ranges": [{"startOffset": 9162, "endOffset": 9233, "count": 2261}], "isBlockCoverage": false}, {"functionName": "next", "ranges": [{"startOffset": 9238, "endOffset": 9287, "count": 3987}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9292, "endOffset": 9337, "count": 1780}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10902, "endOffset": 10969, "count": 524}], "isBlockCoverage": false}, {"functionName": "SafeMap", "ranges": [{"startOffset": 11627, "endOffset": 11655, "count": 157}], "isBlockCoverage": false}, {"functionName": "SafeSet", "ranges": [{"startOffset": 11956, "endOffset": 11984, "count": 146}], "isBlockCoverage": false}, {"functionName": "SafeFinalizationRegistry", "ranges": [{"startOffset": 12408, "endOffset": 12464, "count": 3}], "isBlockCoverage": false}, {"functionName": "SafeWeakRef", "ranges": [{"startOffset": 12617, "endOffset": 12655, "count": 12}], "isBlockCoverage": false}, {"functionName": "SafePromise", "ranges": [{"startOffset": 12803, "endOffset": 12845, "count": 576}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13350, "endOffset": 13624, "count": 96}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13495, "endOffset": 13619, "count": 96}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13525, "endOffset": 13574, "count": 96}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14944, "endOffset": 15500, "count": 98}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14979, "endOffset": 15499, "count": 98}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15370, "endOffset": 15479, "count": 305}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15842, "endOffset": 16295, "count": 99}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15877, "endOffset": 16294, "count": 99}], "isBlockCoverage": false}, {"functionName": "onFulfilled", "ranges": [{"startOffset": 16009, "endOffset": 16086, "count": 305}], "isBlockCoverage": false}]}, {"scriptId": "4", "url": "node:internal/per_context/domexception", "functions": [{"functionName": "DOMException", "ranges": [{"startOffset": 1102, "endOffset": 1728, "count": 2}], "isBlockCoverage": false}]}, {"scriptId": "6", "url": "node:internal/bootstrap/realm", "functions": [{"functionName": "internalBinding", "ranges": [{"startOffset": 6003, "endOffset": 6265, "count": 76}], "isBlockCoverage": false}, {"functionName": "getOwn", "ranges": [{"startOffset": 6531, "endOffset": 6685, "count": 78}], "isBlockCoverage": false}, {"functionName": "allowRequireByUsers", "ranges": [{"startOffset": 8452, "endOffset": 8813, "count": 1}], "isBlockCoverage": false}, {"functionName": "canBeRequiredByUsers", "ranges": [{"startOffset": 9498, "endOffset": 9573, "count": 4}], "isBlockCoverage": false}, {"functionName": "canBeRequiredWithoutScheme", "ranges": [{"startOffset": 9584, "endOffset": 9678, "count": 15}], "isBlockCoverage": false}, {"functionName": "getCanBeRequiredByUsersWithoutSchemeList", "ranges": [{"startOffset": 10297, "endOffset": 10406, "count": 1}], "isBlockCoverage": false}, {"functionName": "compileForPublicLoader", "ranges": [{"startOffset": 10564, "endOffset": 11227, "count": 2}], "isBlockCoverage": false}, {"functionName": "getESMFacade", "ranges": [{"startOffset": 11231, "endOffset": 11865, "count": 2}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11602, "endOffset": 11705, "count": 2}], "isBlockCoverage": false}, {"functionName": "syncExports", "ranges": [{"startOffset": 12161, "endOffset": 12505, "count": 2}], "isBlockCoverage": false}, {"functionName": "compileForInternalLoader", "ranges": [{"startOffset": 12509, "endOffset": 13331, "count": 3106}], "isBlockCoverage": false}, {"functionName": "requireBuiltin", "ranges": [{"startOffset": 13526, "endOffset": 13891, "count": 3107}], "isBlockCoverage": false}]}, {"scriptId": "7", "url": "node:internal/errors", "functions": [{"functionName": "setInternalPrepareStackTrace", "ranges": [{"startOffset": 2792, "endOffset": 2883, "count": 1}], "isBlockCoverage": false}, {"functionName": "isErrorStackTraceLimitWritable", "ranges": [{"startOffset": 6863, "endOffset": 7370, "count": 2}], "isBlockCoverage": false}, {"functionName": "hideStackFrames", "ranges": [{"startOffset": 14678, "endOffset": 14975, "count": 3}], "isBlockCoverage": false}, {"functionName": "wrappedFn", "ranges": [{"startOffset": 14711, "endOffset": 14917, "count": 8211}], "isBlockCoverage": false}]}, {"scriptId": "8", "url": "node:internal/assert", "functions": [{"functionName": "assert", "ranges": [{"startOffset": 128, "endOffset": 278, "count": 710}], "isBlockCoverage": false}]}, {"scriptId": "9", "url": "node:internal/bootstrap/node", "functions": [{"functionName": "get", "ranges": [{"startOffset": 3830, "endOffset": 3880, "count": 46}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 3886, "endOffset": 3944, "count": 2}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4086, "endOffset": 4122, "count": 2}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 4128, "endOffset": 4547, "count": 2}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 14768, "endOffset": 14804, "count": 12}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 15674, "endOffset": 15709, "count": 3018}], "isBlockCoverage": false}]}, {"scriptId": "10", "url": "node:internal/timers", "functions": [{"functionName": "", "ranges": [{"startOffset": 4385, "endOffset": 4410, "count": 1}], "isBlockCoverage": false}, {"functionName": "initAsyncResource", "ranges": [{"startOffset": 5890, "endOffset": 6231, "count": 255}], "isBlockCoverage": false}, {"functionName": "Timeout", "ranges": [{"startOffset": 6337, "endOffset": 7383, "count": 255}], "isBlockCoverage": false}, {"functionName": "TimersList", "ranges": [{"startOffset": 8125, "endOffset": 8431, "count": 53}], "isBlockCoverage": false}, {"functionName": "incRefCount", "ranges": [{"startOffset": 9808, "endOffset": 9974, "count": 255}], "isBlockCoverage": false}, {"functionName": "decRefCount", "ranges": [{"startOffset": 9976, "endOffset": 10143, "count": 15}], "isBlockCoverage": false}, {"functionName": "insert", "ranges": [{"startOffset": 11292, "endOffset": 12031, "count": 584}], "isBlockCoverage": false}, {"functionName": "compareTimersLists", "ranges": [{"startOffset": 12888, "endOffset": 13093, "count": 137}], "isBlockCoverage": false}, {"functionName": "setPosition", "ranges": [{"startOffset": 13095, "endOffset": 13166, "count": 414}], "isBlockCoverage": false}, {"functionName": "processTimers", "ranges": [{"startOffset": 15568, "endOffset": 16058, "count": 367}], "isBlockCoverage": false}, {"functionName": "listOnTimeout", "ranges": [{"startOffset": 16062, "endOffset": 18946, "count": 372}], "isBlockCoverage": false}]}, {"scriptId": "11", "url": "node:internal/async_hooks", "functions": [{"functionName": "newAsyncId", "ranges": [{"startOffset": 14549, "endOffset": 14619, "count": 300}], "isBlockCoverage": false}, {"functionName": "getDefaultTriggerAsyncId", "ranges": [{"startOffset": 14994, "endOffset": 15284, "count": 300}], "isBlockCoverage": false}, {"functionName": "hasHooks", "ranges": [{"startOffset": 16096, "endOffset": 16159, "count": 2449}], "isBlockCoverage": false}, {"functionName": "enabledHooksExist", "ranges": [{"startOffset": 16161, "endOffset": 16220, "count": 617}], "isBlockCoverage": false}, {"functionName": "initHooksExist", "ranges": [{"startOffset": 16222, "endOffset": 16277, "count": 300}], "isBlockCoverage": false}, {"functionName": "destroyHooksExist", "ranges": [{"startOffset": 16338, "endOffset": 16399, "count": 298}], "isBlockCoverage": false}, {"functionName": "emitBeforeScript", "ranges": [{"startOffset": 16890, "endOffset": 17066, "count": 617}], "isBlockCoverage": false}, {"functionName": "emitAfterScript", "ranges": [{"startOffset": 17069, "endOffset": 17189, "count": 617}], "isBlockCoverage": false}, {"functionName": "pushAsyncContext", "ranges": [{"startOffset": 17534, "endOffset": 18104, "count": 617}], "isBlockCoverage": false}, {"functionName": "popAsyncContext", "ranges": [{"startOffset": 18169, "endOffset": 18780, "count": 617}], "isBlockCoverage": false}]}, {"scriptId": "12", "url": "node:internal/validators", "functions": [{"functionName": "parseFileMode", "ranges": [{"startOffset": 1881, "endOffset": 2192, "count": 96}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2438, "endOffset": 2819, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3063, "endOffset": 3526, "count": 18}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3757, "endOffset": 4189, "count": 104}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4381, "endOffset": 4493, "count": 3323}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4735, "endOffset": 5181, "count": 3069}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5898, "endOffset": 6012, "count": 3}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6594, "endOffset": 7613, "count": 1105}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8567, "endOffset": 8845, "count": 11}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12692, "endOffset": 12909, "count": 8}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13109, "endOffset": 13225, "count": 2655}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14978, "endOffset": 15188, "count": 3064}], "isBlockCoverage": false}]}, {"scriptId": "13", "url": "node:internal/util", "functions": [{"functionName": "isError", "ranges": [{"startOffset": 2012, "endOffset": 2262, "count": 1}], "isBlockCoverage": false}, {"functionName": "getDeprecationWarningEmitter", "ranges": [{"startOffset": 2419, "endOffset": 3171, "count": 2}], "isBlockCoverage": false}, {"functionName": "shouldEmitW<PERSON>ning", "ranges": [{"startOffset": 2518, "endOffset": 2528, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2565, "endOffset": 3168, "count": 0}], "isBlockCoverage": false}, {"functionName": "deprecate", "ranges": [{"startOffset": 3920, "endOffset": 4974, "count": 2}], "isBlockCoverage": false}, {"functionName": "deprecated", "ranges": [{"startOffset": 4284, "endOffset": 4586, "count": 0}], "isBlockCoverage": false}, {"functionName": "assertCrypto", "ranges": [{"startOffset": 5244, "endOffset": 5318, "count": 1}], "isBlockCoverage": false}, {"functionName": "cachedResult", "ranges": [{"startOffset": 8556, "endOffset": 8712, "count": 3}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8607, "endOffset": 8709, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCWDURL", "ranges": [{"startOffset": 10679, "endOffset": 11189, "count": 1}], "isBlockCoverage": false}, {"functionName": "promisify", "ranges": [{"startOffset": 11636, "endOffset": 13691, "count": 8}], "isBlockCoverage": false}, {"functionName": "fn", "ranges": [{"startOffset": 12376, "endOffset": 13089, "count": 16}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12422, "endOffset": 13083, "count": 16}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12476, "endOffset": 12839, "count": 16}], "isBlockCoverage": false}, {"functionName": "join", "ranges": [{"startOffset": 13786, "endOffset": 14108, "count": 13}], "isBlockCoverage": false}, {"functionName": "exposeInterface", "ranges": [{"startOffset": 16386, "endOffset": 16606, "count": 1}], "isBlockCoverage": false}, {"functionName": "defineLazyProperties", "ranges": [{"startOffset": 17105, "endOffset": 18038, "count": 2}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 17324, "endOffset": 17467, "count": 3}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17570, "endOffset": 17766, "count": 3}], "isBlockCoverage": false}, {"functionName": "defineReplaceableLazyAttribute", "ranges": [{"startOffset": 18040, "endOffset": 18940, "count": 2}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 18252, "endOffset": 18488, "count": 2}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 18593, "endOffset": 18664, "count": 0}], "isBlockCoverage": false}, {"functionName": "exposeLazyInterfaces", "ranges": [{"startOffset": 18942, "endOffset": 19042, "count": 2}], "isBlockCoverage": false}, {"functionName": "lazyDOMException", "ranges": [{"startOffset": 19217, "endOffset": 19613, "count": 2}], "isBlockCoverage": false}, {"functionName": "setOwnProperty", "ranges": [{"startOffset": 20237, "endOffset": 20427, "count": 2593}], "isBlockCoverage": false}, {"functionName": "getLazy", "ranges": [{"startOffset": 22294, "endOffset": 22506, "count": 2}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 22375, "endOffset": 22503, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupCoverageHooks", "ranges": [{"startOffset": 22631, "endOffset": 23308, "count": 1}], "isBlockCoverage": false}, {"functionName": "guessHandleType", "ranges": [{"startOffset": 23381, "endOffset": 23478, "count": 4}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 23500, "endOffset": 24086, "count": 12}], "isBlockCoverage": false}, {"functionName": "WeakReference", "ranges": [{"startOffset": 23617, "endOffset": 23684, "count": 12}], "isBlockCoverage": false}, {"functionName": "incRef", "ranges": [{"startOffset": 23688, "endOffset": 23906, "count": 0}], "isBlockCoverage": false}, {"functionName": "decRef", "ranges": [{"startOffset": 23910, "endOffset": 24038, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 24042, "endOffset": 24084, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "14", "url": "node:internal/options", "functions": [{"functionName": "getCLIOptionsFromBinding", "ranges": [{"startOffset": 495, "endOffset": 582, "count": 75}], "isBlockCoverage": false}, {"functionName": "getEmbedderOptions", "ranges": [{"startOffset": 671, "endOffset": 766, "count": 6}], "isBlockCoverage": false}, {"functionName": "refreshOptions", "ranges": [{"startOffset": 768, "endOffset": 824, "count": 1}], "isBlockCoverage": false}, {"functionName": "getOptionValue", "ranges": [{"startOffset": 826, "endOffset": 914, "count": 75}], "isBlockCoverage": false}]}, {"scriptId": "15", "url": "node:internal/util/types", "functions": [{"functionName": "isTypedArray", "ranges": [{"startOffset": 131, "endOffset": 234, "count": 3101}], "isBlockCoverage": false}, {"functionName": "isUint8Array", "ranges": [{"startOffset": 236, "endOffset": 342, "count": 3312}], "isBlockCoverage": false}, {"functionName": "isFloat32Array", "ranges": [{"startOffset": 1008, "endOffset": 1118, "count": 3087}], "isBlockCoverage": false}, {"functionName": "isFloat64Array", "ranges": [{"startOffset": 1120, "endOffset": 1230, "count": 3101}], "isBlockCoverage": false}]}, {"scriptId": "16", "url": "node:internal/linkedlist", "functions": [{"functionName": "peek", "ranges": [{"startOffset": 133, "endOffset": 225, "count": 945}], "isBlockCoverage": false}, {"functionName": "remove", "ranges": [{"startOffset": 260, "endOffset": 487, "count": 843}], "isBlockCoverage": false}, {"functionName": "append", "ranges": [{"startOffset": 543, "endOffset": 995, "count": 584}], "isBlockCoverage": false}, {"functionName": "isEmpty", "ranges": [{"startOffset": 997, "endOffset": 1057, "count": 15}], "isBlockCoverage": false}]}, {"scriptId": "17", "url": "node:internal/priority_queue", "functions": [{"functionName": "insert", "ranges": [{"startOffset": 692, "endOffset": 875, "count": 53}], "isBlockCoverage": false}, {"functionName": "peek", "ranges": [{"startOffset": 879, "endOffset": 917, "count": 739}], "isBlockCoverage": false}, {"functionName": "percolateDown", "ranges": [{"startOffset": 978, "endOffset": 1642, "count": 347}], "isBlockCoverage": false}, {"functionName": "percolateUp", "ranges": [{"startOffset": 1646, "endOffset": 2134, "count": 53}], "isBlockCoverage": false}, {"functionName": "removeAt", "ranges": [{"startOffset": 2138, "endOffset": 2468, "count": 52}], "isBlockCoverage": false}, {"functionName": "shift", "ranges": [{"startOffset": 2472, "endOffset": 2626, "count": 38}], "isBlockCoverage": false}]}, {"scriptId": "18", "url": "node:internal/util/inspect", "functions": [{"functionName": "isURL", "ranges": [{"startOffset": 3653, "endOffset": 3798, "count": 1}], "isBlockCoverage": false}, {"functionName": "isUndetectableObject", "ranges": [{"startOffset": 4064, "endOffset": 4114, "count": 15}], "isBlockCoverage": false}, {"functionName": "inspect", "ranges": [{"startOffset": 9465, "endOffset": 11491, "count": 17}], "isBlockCoverage": false}, {"functionName": "addQuotes", "ranges": [{"startOffset": 14766, "endOffset": 14924, "count": 6}], "isBlockCoverage": false}, {"functionName": "strEscape", "ranges": [{"startOffset": 15213, "endOffset": 17367, "count": 6}], "isBlockCoverage": false}, {"functionName": "stylizeNoColor", "ranges": [{"startOffset": 17637, "endOffset": 17683, "count": 64}], "isBlockCoverage": false}, {"functionName": "getEmptyFormatArray", "ranges": [{"startOffset": 17762, "endOffset": 17809, "count": 8}], "isBlockCoverage": false}, {"functionName": "isInstanceof", "ranges": [{"startOffset": 17811, "endOffset": 17929, "count": 6}], "isBlockCoverage": false}, {"functionName": "getConstructorName", "ranges": [{"startOffset": 18668, "endOffset": 20421, "count": 15}], "isBlockCoverage": false}, {"functionName": "getPrefix", "ranges": [{"startOffset": 22536, "endOffset": 22923, "count": 6}], "isBlockCoverage": false}, {"functionName": "get<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 22960, "endOffset": 23951, "count": 10}], "isBlockCoverage": false}, {"functionName": "filter", "ranges": [{"startOffset": 23791, "endOffset": 23847, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCtxStyle", "ranges": [{"startOffset": 23953, "endOffset": 24216, "count": 1}], "isBlockCoverage": false}, {"functionName": "formatValue", "ranges": [{"startOffset": 24850, "endOffset": 27650, "count": 30}], "isBlockCoverage": false}, {"functionName": "formatRaw", "ranges": [{"startOffset": 27652, "endOffset": 38746, "count": 15}], "isBlockCoverage": false}, {"functionName": "groupArrayElements", "ranges": [{"startOffset": 49729, "endOffset": 54068, "count": 2}], "isBlockCoverage": false}, {"functionName": "formatNumber", "ranges": [{"startOffset": 55157, "endOffset": 55945, "count": 47}], "isBlockCoverage": false}, {"functionName": "formatPrimitive", "ranges": [{"startOffset": 56165, "endOffset": 57570, "count": 15}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 56954, "endOffset": 56993, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatTypedArray", "ranges": [{"startOffset": 61284, "endOffset": 62313, "count": 5}], "isBlockCoverage": false}, {"functionName": "formatProperty", "ranges": [{"startOffset": 66868, "endOffset": 69435, "count": 13}], "isBlockCoverage": false}, {"functionName": "isBelowBreak<PERSON>ength", "ranges": [{"startOffset": 69437, "endOffset": 70395, "count": 11}], "isBlockCoverage": false}, {"functionName": "reduceToSingleString", "ranges": [{"startOffset": 70397, "endOffset": 73298, "count": 13}], "isBlockCoverage": false}, {"functionName": "formatWithOptions", "ranges": [{"startOffset": 75226, "endOffset": 75418, "count": 776}], "isBlockCoverage": false}, {"functionName": "formatWithOptionsInternal", "ranges": [{"startOffset": 75788, "endOffset": 79812, "count": 776}], "isBlockCoverage": false}, {"functionName": "getStringWidth", "ranges": [{"startOffset": 80817, "endOffset": 81408, "count": 26}], "isBlockCoverage": false}]}, {"scriptId": "19", "url": "node:internal/util/debuglog", "functions": [{"functionName": "initializeDebugEnv", "ranges": [{"startOffset": 832, "endOffset": 1305, "count": 1}], "isBlockCoverage": false}, {"functionName": "testEnabled", "ranges": [{"startOffset": 1199, "endOffset": 1256, "count": 0}], "isBlockCoverage": false}, {"functionName": "testEnabled", "ranges": [{"startOffset": 1287, "endOffset": 1298, "count": 6}], "isBlockCoverage": false}, {"functionName": "noop", "ranges": [{"startOffset": 1733, "endOffset": 1741, "count": 1990}], "isBlockCoverage": false}, {"functionName": "debuglogImpl", "ranges": [{"startOffset": 1862, "endOffset": 2426, "count": 6}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 2051, "endOffset": 2344, "count": 0}], "isBlockCoverage": false}, {"functionName": "debuglog", "ranges": [{"startOffset": 2646, "endOffset": 3672, "count": 6}], "isBlockCoverage": false}, {"functionName": "init", "ranges": [{"startOffset": 2677, "endOffset": 2773, "count": 6}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 2788, "endOffset": 3182, "count": 6}], "isBlockCoverage": false}, {"functionName": "test", "ranges": [{"startOffset": 3212, "endOffset": 3281, "count": 0}], "isBlockCoverage": false}, {"functionName": "logger", "ranges": [{"startOffset": 3300, "endOffset": 3495, "count": 6}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3566, "endOffset": 3600, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "21", "url": "node:internal/async_context_frame", "functions": [{"functionName": "current", "ranges": [{"startOffset": 1224, "endOffset": 1236, "count": 311}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 1246, "endOffset": 1259, "count": 617}], "isBlockCoverage": false}, {"functionName": "exchange", "ranges": [{"startOffset": 1269, "endOffset": 1287, "count": 617}], "isBlockCoverage": false}]}, {"scriptId": "22", "url": "node:events", "functions": [{"functionName": "EventEmitter", "ranges": [{"startOffset": 6589, "endOffset": 6658, "count": 98}], "isBlockCoverage": false}, {"functionName": "checkListener", "ranges": [{"startOffset": 8109, "endOffset": 8187, "count": 2331}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8295, "endOffset": 8343, "count": 5}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9985, "endOffset": 10633, "count": 98}], "isBlockCoverage": false}, {"functionName": "emit", "ranges": [{"startOffset": 13430, "endOffset": 15722, "count": 98}], "isBlockCoverage": false}, {"functionName": "_addListener", "ranges": [{"startOffset": 15725, "endOffset": 17641, "count": 779}], "isBlockCoverage": false}, {"functionName": "addListener", "ranges": [{"startOffset": 17819, "endOffset": 17911, "count": 779}], "isBlockCoverage": false}, {"functionName": "_onceWrap", "ranges": [{"startOffset": 18570, "endOffset": 18811, "count": 776}], "isBlockCoverage": false}, {"functionName": "once", "ranges": [{"startOffset": 19002, "endOffset": 19128, "count": 776}], "isBlockCoverage": false}, {"functionName": "removeListener", "ranges": [{"startOffset": 19738, "endOffset": 21044, "count": 776}], "isBlockCoverage": false}, {"functionName": "listenerCount", "ranges": [{"startOffset": 24355, "endOffset": 25060, "count": 776}], "isBlockCoverage": false}, {"functionName": "arrayClone", "ranges": [{"startOffset": 25312, "endOffset": 25785, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "24", "url": "node:buffer", "functions": [{"functionName": "createPool", "ranges": [{"startOffset": 3962, "endOffset": 4150, "count": 44}], "isBlockCoverage": false}, {"functionName": "alignPool", "ranges": [{"startOffset": 4166, "endOffset": 4288, "count": 3075}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8399, "endOffset": 8427, "count": 1}], "isBlockCoverage": false}, {"functionName": "from", "ranges": [{"startOffset": 8685, "endOffset": 9650, "count": 7492}], "isBlockCoverage": false}, {"functionName": "allocUnsafeSlow", "ranges": [{"startOffset": 12263, "endOffset": 12379, "count": 96}], "isBlockCoverage": false}, {"functionName": "fromStringFast", "ranges": [{"startOffset": 13034, "endOffset": 13686, "count": 3700}], "isBlockCoverage": false}, {"functionName": "fromString", "ranges": [{"startOffset": 13935, "endOffset": 14295, "count": 3766}], "isBlockCoverage": false}, {"functionName": "fromArrayBuffer", "ranges": [{"startOffset": 14297, "endOffset": 14959, "count": 3554}], "isBlockCoverage": false}, {"functionName": "write", "ranges": [{"startOffset": 19900, "endOffset": 19966, "count": 3545}], "isBlockCoverage": false}, {"functionName": "slice", "ranges": [{"startOffset": 19979, "endOffset": 20027, "count": 3544}], "isBlockCoverage": false}, {"functionName": "getEncodingOps", "ranges": [{"startOffset": 21286, "endOffset": 23110, "count": 4743}], "isBlockCoverage": false}, {"functionName": "toString", "ranges": [{"startOffset": 24737, "endOffset": 25352, "count": 3614}], "isBlockCoverage": false}]}, {"scriptId": "25", "url": "node:internal/buffer", "functions": [{"functionName": "FastBuffer", "ranges": [{"startOffset": 26383, "endOffset": 26483, "count": 3050}], "isBlockCoverage": false}, {"functionName": "markAsUntransferable", "ranges": [{"startOffset": 30673, "endOffset": 30922, "count": 44}], "isBlockCoverage": false}, {"functionName": "createUnsafeBuffer", "ranges": [{"startOffset": 31508, "endOffset": 31644, "count": 140}], "isBlockCoverage": false}, {"functionName": "reconnectZeroFillToggle", "ranges": [{"startOffset": 31899, "endOffset": 31971, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "26", "url": "node:internal/worker/js_transferable", "functions": [{"functionName": "markTransferMode", "ranges": [{"startOffset": 2750, "endOffset": 3148, "count": 1507}], "isBlockCoverage": false}]}, {"scriptId": "27", "url": "node:internal/webidl", "functions": [{"functionName": "DOMString", "ranges": [{"startOffset": 5132, "endOffset": 5262, "count": 12}], "isBlockCoverage": false}, {"functionName": "createEnumConverter", "ranges": [{"startOffset": 5875, "endOffset": 6236, "count": 2}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5963, "endOffset": 6233, "count": 119}], "isBlockCoverage": false}, {"functionName": "type", "ranges": [{"startOffset": 6303, "endOffset": 7049, "count": 56}], "isBlockCoverage": false}, {"functionName": "createSequenceConverter", "ranges": [{"startOffset": 8828, "endOffset": 9670, "count": 3}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8883, "endOffset": 9667, "count": 56}], "isBlockCoverage": false}]}, {"scriptId": "28", "url": "node:internal/process/per_thread", "functions": [{"functionName": "exit", "ranges": [{"startOffset": 4767, "endOffset": 5763, "count": 1}], "isBlockCoverage": false}, {"functionName": "toggleTraceCategoryState", "ranges": [{"startOffset": 12288, "endOffset": 12571, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "29", "url": "node:internal/process/task_queues", "functions": [{"functionName": "hasTickScheduled", "ranges": [{"startOffset": 1119, "endOffset": 1194, "count": 415}], "isBlockCoverage": false}, {"functionName": "setHasTickScheduled", "ranges": [{"startOffset": 1196, "endOffset": 1282, "count": 88}], "isBlockCoverage": false}, {"functionName": "runNextTicks", "ranges": [{"startOffset": 1384, "endOffset": 1580, "count": 208}], "isBlockCoverage": false}, {"functionName": "processTicksAndRejections", "ranges": [{"startOffset": 1582, "endOffset": 2706, "count": 43}], "isBlockCoverage": false}, {"functionName": "nextTick", "ranges": [{"startOffset": 2850, "endOffset": 3780, "count": 45}], "isBlockCoverage": false}]}, {"scriptId": "30", "url": "node:internal/process/promises", "functions": [{"functionName": "", "ranges": [{"startOffset": 1335, "endOffset": 1447, "count": 1}], "isBlockCoverage": false}, {"functionName": "setHasRejectionToWarn", "ranges": [{"startOffset": 4165, "endOffset": 4255, "count": 54}], "isBlockCoverage": false}, {"functionName": "hasRejectionToWarn", "ranges": [{"startOffset": 4287, "endOffset": 4366, "count": 409}], "isBlockCoverage": false}, {"functionName": "promiseRejectHandler", "ranges": [{"startOffset": 4664, "endOffset": 5347, "count": 22}], "isBlockCoverage": false}, {"functionName": "unhandledRejection", "ranges": [{"startOffset": 6776, "endOffset": 7037, "count": 11}], "isBlockCoverage": false}, {"functionName": "handledRejection", "ranges": [{"startOffset": 7075, "endOffset": 7656, "count": 11}], "isBlockCoverage": false}, {"functionName": "getUnhandledRejectionsMode", "ranges": [{"startOffset": 11428, "endOffset": 11979, "count": 1}], "isBlockCoverage": false}, {"functionName": "processPromiseRejections", "ranges": [{"startOffset": 12152, "endOffset": 13663, "count": 43}], "isBlockCoverage": false}]}, {"scriptId": "31", "url": "node:internal/fixed_queue", "functions": [{"functionName": "isEmpty", "ranges": [{"startOffset": 3130, "endOffset": 3182, "count": 177}], "isBlockCoverage": false}, {"functionName": "isFull", "ranges": [{"startOffset": 3186, "endOffset": 3253, "count": 45}], "isBlockCoverage": false}, {"functionName": "push", "ranges": [{"startOffset": 3257, "endOffset": 3344, "count": 45}], "isBlockCoverage": false}, {"functionName": "shift", "ranges": [{"startOffset": 3348, "endOffset": 3563, "count": 88}], "isBlockCoverage": false}, {"functionName": "isEmpty", "ranges": [{"startOffset": 3683, "endOffset": 3730, "count": 89}], "isBlockCoverage": false}, {"functionName": "push", "ranges": [{"startOffset": 3734, "endOffset": 3998, "count": 45}], "isBlockCoverage": false}, {"functionName": "shift", "ranges": [{"startOffset": 4002, "endOffset": 4257, "count": 88}], "isBlockCoverage": false}]}, {"scriptId": "33", "url": "node:timers", "functions": [{"functionName": "", "ranges": [{"startOffset": 1886, "endOffset": 1911, "count": 1}], "isBlockCoverage": false}, {"functionName": "unenroll", "ranges": [{"startOffset": 2207, "endOffset": 3391, "count": 15}], "isBlockCoverage": false}, {"functionName": "setTimeout", "ranges": [{"startOffset": 4093, "endOffset": 4762, "count": 248}], "isBlockCoverage": false}, {"functionName": "clearTimeout", "ranges": [{"startOffset": 5057, "endOffset": 5415, "count": 18}], "isBlockCoverage": false}, {"functionName": "setInterval", "ranges": [{"startOffset": 5652, "endOffset": 6323, "count": 7}], "isBlockCoverage": false}, {"functionName": "clearInterval", "ranges": [{"startOffset": 6420, "endOffset": 6718, "count": 6}], "isBlockCoverage": false}]}, {"scriptId": "34", "url": "node:internal/process/execution", "functions": [{"functionName": "tryGetCwd", "ranges": [{"startOffset": 1136, "endOffset": 1466, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "35", "url": "node:path", "functions": [{"functionName": "isPosixPathSeparator", "ranges": [{"startOffset": 2070, "endOffset": 2147, "count": 3848}], "isBlockCoverage": false}, {"functionName": "normalizeString", "ranges": [{"startOffset": 2382, "endOffset": 4330, "count": 98}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 36500, "endOffset": 36519, "count": 1}], "isBlockCoverage": false}, {"functionName": "resolve", "ranges": [{"startOffset": 36644, "endOffset": 37748, "count": 635}], "isBlockCoverage": false}, {"functionName": "isAbsolute", "ranges": [{"startOffset": 38494, "endOffset": 38650, "count": 1}], "isBlockCoverage": false}, {"functionName": "toNamespacedPath", "ranges": [{"startOffset": 41599, "endOffset": 41675, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "36", "url": "node:internal/url", "functions": [{"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3563, "endOffset": 4335, "count": 1366}], "isBlockCoverage": false}, {"functionName": "get hasPort", "ranges": [{"startOffset": 4114, "endOffset": 4179, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hasSearch", "ranges": [{"startOffset": 4183, "endOffset": 4258, "count": 1100}], "isBlockCoverage": false}, {"functionName": "get hasHash", "ranges": [{"startOffset": 4262, "endOffset": 4333, "count": 1100}], "isBlockCoverage": false}, {"functionName": "isURL", "ranges": [{"startOffset": 21832, "endOffset": 21957, "count": 802}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 22444, "endOffset": 33908, "count": 1366}], "isBlockCoverage": false}, {"functionName": "setURLSearchParamsModified", "ranges": [{"startOffset": 22564, "endOffset": 23052, "count": 0}], "isBlockCoverage": false}, {"functionName": "URL", "ranges": [{"startOffset": 23061, "endOffset": 23966, "count": 1366}], "isBlockCoverage": false}, {"functionName": "parse", "ranges": [{"startOffset": 23977, "endOffset": 24219, "count": 613}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 24223, "endOffset": 24943, "count": 0}], "isBlockCoverage": false}, {"functionName": "#getSearchFromContext", "ranges": [{"startOffset": 24947, "endOffset": 25281, "count": 304}], "isBlockCoverage": false}, {"functionName": "#getSearchFromParams", "ranges": [{"startOffset": 25285, "endOffset": 25396, "count": 0}], "isBlockCoverage": false}, {"functionName": "#ensureSearchParamsUpdated", "ranges": [{"startOffset": 25400, "endOffset": 25839, "count": 2741}], "isBlockCoverage": false}, {"functionName": "#updateContext", "ranges": [{"startOffset": 26087, "endOffset": 27732, "count": 1944}], "isBlockCoverage": false}, {"functionName": "toString", "ranges": [{"startOffset": 27736, "endOffset": 27921, "count": 927}], "isBlockCoverage": false}, {"functionName": "get href", "ranges": [{"startOffset": 27925, "endOffset": 28110, "count": 1510}], "isBlockCoverage": false}, {"functionName": "set href", "ranges": [{"startOffset": 28114, "endOffset": 28333, "count": 0}], "isBlockCoverage": false}, {"functionName": "get origin", "ranges": [{"startOffset": 28351, "endOffset": 29220, "count": 0}], "isBlockCoverage": false}, {"functionName": "get protocol", "ranges": [{"startOffset": 29224, "endOffset": 29328, "count": 2088}], "isBlockCoverage": false}, {"functionName": "set protocol", "ranges": [{"startOffset": 29332, "endOffset": 29505, "count": 0}], "isBlockCoverage": false}, {"functionName": "get username", "ranges": [{"startOffset": 29509, "endOffset": 29736, "count": 0}], "isBlockCoverage": false}, {"functionName": "set username", "ranges": [{"startOffset": 29740, "endOffset": 29913, "count": 0}], "isBlockCoverage": false}, {"functionName": "get password", "ranges": [{"startOffset": 29917, "endOffset": 30140, "count": 0}], "isBlockCoverage": false}, {"functionName": "set password", "ranges": [{"startOffset": 30144, "endOffset": 30317, "count": 0}], "isBlockCoverage": false}, {"functionName": "get host", "ranges": [{"startOffset": 30321, "endOffset": 30752, "count": 0}], "isBlockCoverage": false}, {"functionName": "set host", "ranges": [{"startOffset": 30756, "endOffset": 30921, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hostname", "ranges": [{"startOffset": 30925, "endOffset": 31207, "count": 158}], "isBlockCoverage": false}, {"functionName": "set hostname", "ranges": [{"startOffset": 31211, "endOffset": 31384, "count": 0}], "isBlockCoverage": false}, {"functionName": "get port", "ranges": [{"startOffset": 31388, "endOffset": 31496, "count": 0}], "isBlockCoverage": false}, {"functionName": "set port", "ranges": [{"startOffset": 31500, "endOffset": 31665, "count": 0}], "isBlockCoverage": false}, {"functionName": "get pathname", "ranges": [{"startOffset": 31669, "endOffset": 31961, "count": 796}], "isBlockCoverage": false}, {"functionName": "set pathname", "ranges": [{"startOffset": 31965, "endOffset": 32138, "count": 0}], "isBlockCoverage": false}, {"functionName": "get search", "ranges": [{"startOffset": 32142, "endOffset": 32339, "count": 304}], "isBlockCoverage": false}, {"functionName": "set search", "ranges": [{"startOffset": 32343, "endOffset": 32547, "count": 304}], "isBlockCoverage": false}, {"functionName": "get searchParams", "ranges": [{"startOffset": 32565, "endOffset": 32923, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hash", "ranges": [{"startOffset": 32927, "endOffset": 33142, "count": 304}], "isBlockCoverage": false}, {"functionName": "set hash", "ranges": [{"startOffset": 33146, "endOffset": 33311, "count": 304}], "isBlockCoverage": false}, {"functionName": "toJSON", "ranges": [{"startOffset": 33315, "endOffset": 33498, "count": 0}], "isBlockCoverage": false}, {"functionName": "canParse", "ranges": [{"startOffset": 33509, "endOffset": 33906, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPathFromURLPosix", "ranges": [{"startOffset": 43190, "endOffset": 43711, "count": 158}], "isBlockCoverage": false}, {"functionName": "fileURLToPath", "ranges": [{"startOffset": 43713, "endOffset": 44125, "count": 400}], "isBlockCoverage": false}, {"functionName": "pathToFileURL", "ranges": [{"startOffset": 44127, "endOffset": 45978, "count": 321}], "isBlockCoverage": false}, {"functionName": "toPathIfFileURL", "ranges": [{"startOffset": 45980, "endOffset": 46117, "count": 402}], "isBlockCoverage": false}]}, {"scriptId": "42", "url": "node:internal/source_map/source_map_cache", "functions": [{"functionName": "setSourceMapsSupport", "ranges": [{"startOffset": 2067, "endOffset": 2836, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "43", "url": "node:internal/modules/helpers", "functions": [{"functionName": "", "ranges": [{"startOffset": 1339, "endOffset": 1364, "count": 1}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1833, "endOffset": 1965, "count": 2}], "isBlockCoverage": false}, {"functionName": "initializeCjsConditions", "ranges": [{"startOffset": 2081, "endOffset": 2572, "count": 1}], "isBlockCoverage": false}, {"functionName": "loadBuiltinModule", "ranges": [{"startOffset": 2993, "endOffset": 3373, "count": 2}], "isBlockCoverage": false}, {"functionName": "lazyTypes", "ranges": [{"startOffset": 11074, "endOffset": 11190, "count": 192}], "isBlockCoverage": false}, {"functionName": "assertBufferSource", "ranges": [{"startOffset": 11672, "endOffset": 12093, "count": 192}], "isBlockCoverage": false}, {"functionName": "stringify", "ranges": [{"startOffset": 12338, "endOffset": 12605, "count": 96}], "isBlockCoverage": false}, {"functionName": "hasStartedUserCJSExecution", "ranges": [{"startOffset": 12928, "endOffset": 13002, "count": 1}], "isBlockCoverage": false}, {"functionName": "hasStartedUserESMExecution", "ranges": [{"startOffset": 13087, "endOffset": 13161, "count": 1}], "isBlockCoverage": false}, {"functionName": "setHasStartedUserESMExecution", "ranges": [{"startOffset": 13165, "endOffset": 13242, "count": 3}], "isBlockCoverage": false}]}, {"scriptId": "44", "url": "node:fs", "functions": [{"functionName": "isFileType", "ranges": [{"startOffset": 5150, "endOffset": 5410, "count": 892}], "isBlockCoverage": false}, {"functionName": "splitRoot", "ranges": [{"startOffset": 69505, "endOffset": 69715, "count": 97}], "isBlockCoverage": false}, {"functionName": "encodeRealpathResult", "ranges": [{"startOffset": 69720, "endOffset": 70007, "count": 97}], "isBlockCoverage": false}, {"functionName": "nextPart", "ranges": [{"startOffset": 70413, "endOffset": 70488, "count": 500}], "isBlockCoverage": false}, {"functionName": "realpathSync", "ranges": [{"startOffset": 70662, "endOffset": 74353, "count": 306}], "isBlockCoverage": false}]}, {"scriptId": "46", "url": "node:internal/encoding", "functions": [{"functionName": "validate<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1355, "endOffset": 1480, "count": 4586}], "isBlockCoverage": false}, {"functionName": "validateDecoder", "ranges": [{"startOffset": 1482, "endOffset": 1607, "count": 3914}], "isBlockCoverage": false}, {"functionName": "getEncodingFromLabel", "ranges": [{"startOffset": 9404, "endOffset": 9583, "count": 2}], "isBlockCoverage": false}, {"functionName": "TextEncoder", "ranges": [{"startOffset": 9607, "endOffset": 9653, "count": 1}], "isBlockCoverage": false}, {"functionName": "encode", "ranges": [{"startOffset": 9728, "endOffset": 9820, "count": 1061}], "isBlockCoverage": false}, {"functionName": "encodeInto", "ranges": [{"startOffset": 9824, "endOffset": 10230, "count": 7296}], "isBlockCoverage": false}, {"functionName": "TextDecoder", "ranges": [{"startOffset": 11141, "endOffset": 12104, "count": 2}], "isBlockCoverage": false}, {"functionName": "decode", "ranges": [{"startOffset": 12381, "endOffset": 13076, "count": 11027}], "isBlockCoverage": false}]}, {"scriptId": "51", "url": "node:internal/fs/utils", "functions": [{"functionName": "assertEncoding", "ranges": [{"startOffset": 3561, "endOffset": 3761, "count": 306}], "isBlockCoverage": false}, {"functionName": "getOptions", "ranges": [{"startOffset": 7395, "endOffset": 8021, "count": 402}], "isBlockCoverage": false}, {"functionName": "stringToFlags", "ranges": [{"startOffset": 15724, "endOffset": 16980, "count": 96}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 19007, "endOffset": 19719, "count": 402}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 19764, "endOffset": 19899, "count": 96}], "isBlockCoverage": false}]}, {"scriptId": "55", "url": "node:internal/console/constructor", "functions": [{"functionName": "lazyUtilColors", "ranges": [{"startOffset": 1918, "endOffset": 2018, "count": 776}], "isBlockCoverage": false}, {"functionName": "value", "ranges": [{"startOffset": 5822, "endOffset": 6401, "count": 1}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6031, "endOffset": 6095, "count": 2317}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6107, "endOffset": 6137, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6267, "endOffset": 6331, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6343, "endOffset": 6373, "count": 0}], "isBlockCoverage": false}, {"functionName": "value", "ranges": [{"startOffset": 7911, "endOffset": 9383, "count": 776}], "isBlockCoverage": false}, {"functionName": "value", "ranges": [{"startOffset": 9478, "endOffset": 9888, "count": 776}], "isBlockCoverage": false}, {"functionName": "value", "ranges": [{"startOffset": 9981, "endOffset": 10161, "count": 776}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10592, "endOffset": 11410, "count": 765}], "isBlockCoverage": false}, {"functionName": "log", "ranges": [{"startOffset": 11675, "endOffset": 11827, "count": 776}], "isBlockCoverage": false}, {"functionName": "group", "ranges": [{"startOffset": 15247, "endOffset": 15555, "count": 197}], "isBlockCoverage": false}, {"functionName": "groupEnd", "ranges": [{"startOffset": 15560, "endOffset": 15857, "count": 197}], "isBlockCoverage": false}, {"functionName": "initializeGlobalConsole", "ranges": [{"startOffset": 19660, "endOffset": 20650, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20545, "endOffset": 20646, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "56", "url": "node:diagnostics_channel", "functions": [{"functionName": "set", "ranges": [{"startOffset": 930, "endOffset": 1047, "count": 12}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1051, "endOffset": 1099, "count": 12}], "isBlockCoverage": false}, {"functionName": "Channel", "ranges": [{"startOffset": 4074, "endOffset": 4215, "count": 12}], "isBlockCoverage": false}, {"functionName": "get hasSubscribers", "ranges": [{"startOffset": 4683, "endOffset": 4727, "count": 791}], "isBlockCoverage": false}, {"functionName": "channel", "ranges": [{"startOffset": 4874, "endOffset": 5140, "count": 12}], "isBlockCoverage": false}, {"functionName": "tracingChannelFrom", "ranges": [{"startOffset": 5698, "endOffset": 6223, "count": 10}], "isBlockCoverage": false}, {"functionName": "TracingChannel", "ranges": [{"startOffset": 6250, "endOffset": 6523, "count": 2}], "isBlockCoverage": false}, {"functionName": "get hasSubscribers", "ranges": [{"startOffset": 6527, "endOffset": 6742, "count": 3}], "isBlockCoverage": false}, {"functionName": "tracePromise", "ranges": [{"startOffset": 7750, "endOffset": 8936, "count": 3}], "isBlockCoverage": false}, {"functionName": "reject", "ranges": [{"startOffset": 7953, "endOffset": 8212, "count": 0}], "isBlockCoverage": false}, {"functionName": "resolve", "ranges": [{"startOffset": 8218, "endOffset": 8443, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8481, "endOffset": 8930, "count": 0}], "isBlockCoverage": false}, {"functionName": "tracingChannel", "ranges": [{"startOffset": 10037, "endOffset": 10125, "count": 2}], "isBlockCoverage": false}]}, {"scriptId": "58", "url": "node:internal/event_target", "functions": [{"functionName": "isEvent", "ranges": [{"startOffset": 2221, "endOffset": 2293, "count": 2}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2307, "endOffset": 7167, "count": 1}], "isBlockCoverage": false}, {"functionName": "Event", "ranges": [{"startOffset": 2611, "endOffset": 3089, "count": 1}], "isBlockCoverage": false}, {"functionName": "initEvent", "ranges": [{"startOffset": 3198, "endOffset": 3484, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3488, "endOffset": 4003, "count": 0}], "isBlockCoverage": false}, {"functionName": "stopImmediatePropagation", "ranges": [{"startOffset": 4007, "endOffset": 4366, "count": 0}], "isBlockCoverage": false}, {"functionName": "preventDefault", "ranges": [{"startOffset": 4370, "endOffset": 4494, "count": 0}], "isBlockCoverage": false}, {"functionName": "get target", "ranges": [{"startOffset": 4535, "endOffset": 4646, "count": 0}], "isBlockCoverage": false}, {"functionName": "get currentTarget", "ranges": [{"startOffset": 4687, "endOffset": 4839, "count": 0}], "isBlockCoverage": false}, {"functionName": "get srcElement", "ranges": [{"startOffset": 4880, "endOffset": 4995, "count": 0}], "isBlockCoverage": false}, {"functionName": "get type", "ranges": [{"startOffset": 5031, "endOffset": 5138, "count": 1}], "isBlockCoverage": false}, {"functionName": "get cancelable", "ranges": [{"startOffset": 5175, "endOffset": 5293, "count": 0}], "isBlockCoverage": false}, {"functionName": "get defaultPrevented", "ranges": [{"startOffset": 5330, "endOffset": 5480, "count": 1}], "isBlockCoverage": false}, {"functionName": "get timeStamp", "ranges": [{"startOffset": 5516, "endOffset": 5632, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 5850, "endOffset": 5997, "count": 0}], "isBlockCoverage": false}, {"functionName": "get returnValue", "ranges": [{"startOffset": 6034, "endOffset": 6181, "count": 0}], "isBlockCoverage": false}, {"functionName": "get bubbles", "ranges": [{"startOffset": 6218, "endOffset": 6330, "count": 0}], "isBlockCoverage": false}, {"functionName": "get composed", "ranges": [{"startOffset": 6367, "endOffset": 6481, "count": 0}], "isBlockCoverage": false}, {"functionName": "get eventPhase", "ranges": [{"startOffset": 6517, "endOffset": 6674, "count": 0}], "isBlockCoverage": false}, {"functionName": "get cancelBubble", "ranges": [{"startOffset": 6711, "endOffset": 6839, "count": 0}], "isBlockCoverage": false}, {"functionName": "set cancelBubble", "ranges": [{"startOffset": 6876, "endOffset": 7034, "count": 0}], "isBlockCoverage": false}, {"functionName": "stopPropagation", "ranges": [{"startOffset": 7038, "endOffset": 7165, "count": 0}], "isBlockCoverage": false}, {"functionName": "Listener", "ranges": [{"startOffset": 10827, "endOffset": 12291, "count": 10}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12118, "endOffset": 12248, "count": 0}], "isBlockCoverage": false}, {"functionName": "get capture", "ranges": [{"startOffset": 12358, "endOffset": 12424, "count": 2}], "isBlockCoverage": false}, {"functionName": "get weak", "ranges": [{"startOffset": 12579, "endOffset": 12639, "count": 14}], "isBlockCoverage": false}, {"functionName": "set removed", "ranges": [{"startOffset": 12808, "endOffset": 12925, "count": 12}], "isBlockCoverage": false}, {"functionName": "same", "ranges": [{"startOffset": 12929, "endOffset": 13096, "count": 2}], "isBlockCoverage": false}, {"functionName": "remove", "ranges": [{"startOffset": 13100, "endOffset": 13357, "count": 2}], "isBlockCoverage": false}, {"functionName": "initEventTarget", "ranges": [{"startOffset": 13361, "endOffset": 13580, "count": 5}], "isBlockCoverage": false}, {"functionName": "EventTarget", "ranges": [{"startOffset": 13823, "endOffset": 13869, "count": 2}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13873, "endOffset": 14729, "count": 10}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14732, "endOffset": 14783, "count": 2}], "isBlockCoverage": false}, {"functionName": "addEventListener", "ranges": [{"startOffset": 15172, "endOffset": 18279, "count": 10}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 16350, "endOffset": 16428, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeEventListener", "ranges": [{"startOffset": 18437, "endOffset": 19278, "count": 2}], "isBlockCoverage": false}, {"functionName": "dispatchEvent", "ranges": [{"startOffset": 19945, "endOffset": 20406, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20410, "endOffset": 22706, "count": 1}], "isBlockCoverage": false}, {"functionName": "createEvent", "ranges": [{"startOffset": 20478, "endOffset": 20678, "count": 0}], "isBlockCoverage": false}, {"functionName": "iterationCondition", "ranges": [{"startOffset": 21061, "endOffset": 21226, "count": 0}], "isBlockCoverage": false}, {"functionName": "initNodeEventTarget", "ranges": [{"startOffset": 23511, "endOffset": 23574, "count": 2}], "isBlockCoverage": false}, {"functionName": "off", "ranges": [{"startOffset": 24798, "endOffset": 24991, "count": 2}], "isBlockCoverage": false}, {"functionName": "on", "ranges": [{"startOffset": 25516, "endOffset": 25721, "count": 8}], "isBlockCoverage": false}, {"functionName": "validateEventListener", "ranges": [{"startOffset": 27586, "endOffset": 27958, "count": 12}], "isBlockCoverage": false}, {"functionName": "validateEventListenerOptions", "ranges": [{"startOffset": 27960, "endOffset": 28518, "count": 8}], "isBlockCoverage": false}, {"functionName": "isEventTarget", "ranges": [{"startOffset": 28823, "endOffset": 28899, "count": 13}], "isBlockCoverage": false}, {"functionName": "isNodeEventTarget", "ranges": [{"startOffset": 28901, "endOffset": 28985, "count": 10}], "isBlockCoverage": false}, {"functionName": "makeEventHandler", "ranges": [{"startOffset": 29386, "endOffset": 29795, "count": 2}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 29567, "endOffset": 29736, "count": 0}], "isBlockCoverage": false}, {"functionName": "defineEventHandler", "ranges": [{"startOffset": 29797, "endOffset": 31263, "count": 6}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 29957, "endOffset": 30093, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 30192, "endOffset": 31030, "count": 2}], "isBlockCoverage": false}]}, {"scriptId": "61", "url": "node:internal/bootstrap/switches/is_main_thread", "functions": [{"functionName": "createWritableStdioStream", "ranges": [{"startOffset": 1428, "endOffset": 3030, "count": 2}], "isBlockCoverage": false}, {"functionName": "write", "ranges": [{"startOffset": 2845, "endOffset": 2892, "count": 0}], "isBlockCoverage": false}, {"functionName": "addCleanup", "ranges": [{"startOffset": 3643, "endOffset": 3734, "count": 2}], "isBlockCoverage": false}, {"functionName": "getStdout", "ranges": [{"startOffset": 3736, "endOffset": 4428, "count": 2}], "isBlockCoverage": false}, {"functionName": "cleanupStdout", "ranges": [{"startOffset": 4094, "endOffset": 4272, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStderr", "ranges": [{"startOffset": 4430, "endOffset": 5121, "count": 1}], "isBlockCoverage": false}, {"functionName": "cleanupStderr", "ranges": [{"startOffset": 4787, "endOffset": 4965, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "62", "url": "node:internal/v8/startup_snapshot", "functions": [{"functionName": "isBuildingSnapshot", "ranges": [{"startOffset": 433, "endOffset": 504, "count": 9}], "isBlockCoverage": false}, {"functionName": "runDeserializeCallbacks", "ranges": [{"startOffset": 831, "endOffset": 1004, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "63", "url": "node:internal/process/signal", "functions": [{"functionName": "isSignal", "ranges": [{"startOffset": 238, "endOffset": 334, "count": 1}], "isBlockCoverage": false}, {"functionName": "startListeningIfSignal", "ranges": [{"startOffset": 398, "endOffset": 908, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "65", "url": "node:internal/modules/cjs/loader", "functions": [{"functionName": "stat", "ranges": [{"startOffset": 6459, "endOffset": 6929, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializeCJS", "ranges": [{"startOffset": 11366, "endOffset": 12170, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 19825, "endOffset": 23264, "count": 1}], "isBlockCoverage": false}, {"functionName": "isRelative", "ranges": [{"startOffset": 58614, "endOffset": 58955, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 59075, "endOffset": 60040, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "66", "url": "node:internal/modules/package_json_reader", "functions": [{"functionName": "deserializePackageJSON", "ranges": [{"startOffset": 1121, "endOffset": 2426, "count": 334}], "isBlockCoverage": false}, {"functionName": "get imports", "ranges": [{"startOffset": 1844, "endOffset": 2075, "count": 0}], "isBlockCoverage": false}, {"functionName": "get exports", "ranges": [{"startOffset": 2131, "endOffset": 2362, "count": 334}], "isBlockCoverage": false}, {"functionName": "requiresJSONParse", "ranges": [{"startOffset": 2677, "endOffset": 2751, "count": 334}], "isBlockCoverage": false}, {"functionName": "read", "ranges": [{"startOffset": 2986, "endOffset": 3532, "count": 15}], "isBlockCoverage": false}, {"functionName": "getPackageScopeConfig", "ranges": [{"startOffset": 4617, "endOffset": 5137, "count": 319}], "isBlockCoverage": false}, {"functionName": "parsePackageName", "ranges": [{"startOffset": 5602, "endOffset": 6660, "count": 15}], "isBlockCoverage": false}, {"functionName": "getPackageJSONURL", "ranges": [{"startOffset": 6662, "endOffset": 8032, "count": 15}], "isBlockCoverage": false}]}, {"scriptId": "67", "url": "node:internal/modules/esm/utils", "functions": [{"functionName": "getDefaultConditions", "ranges": [{"startOffset": 1284, "endOffset": 1390, "count": 305}], "isBlockCoverage": false}, {"functionName": "getDefaultConditionsSet", "ranges": [{"startOffset": 1520, "endOffset": 1635, "count": 304}], "isBlockCoverage": false}, {"functionName": "initializeDefaultConditions", "ranges": [{"startOffset": 1785, "endOffset": 2290, "count": 1}], "isBlockCoverage": false}, {"functionName": "getConditionsSet", "ranges": [{"startOffset": 2360, "endOffset": 2719, "count": 304}], "isBlockCoverage": false}, {"functionName": "defaultImportModuleDynamicallyForModule", "ranges": [{"startOffset": 7219, "endOffset": 7474, "count": 2}], "isBlockCoverage": false}, {"functionName": "importModuleDynamicallyCallback", "ranges": [{"startOffset": 8813, "endOffset": 10290, "count": 2}], "isBlockCoverage": false}, {"functionName": "initializeESM", "ranges": [{"startOffset": 10602, "endOffset": 10988, "count": 1}], "isBlockCoverage": false}, {"functionName": "forceDefaultLoader", "ranges": [{"startOffset": 11116, "endOffset": 11179, "count": 1}], "isBlockCoverage": false}, {"functionName": "compileSourceTextModule", "ranges": [{"startOffset": 12724, "endOffset": 13185, "count": 96}], "isBlockCoverage": false}]}, {"scriptId": "68", "url": "node:internal/process/pre_execution", "functions": [{"functionName": "prepareMainThreadExecution", "ranges": [{"startOffset": 1056, "endOffset": 1240, "count": 1}], "isBlockCoverage": false}, {"functionName": "prepareExecution", "ranges": [{"startOffset": 2420, "endOffset": 4520, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupSymbolDisposePolyfill", "ranges": [{"startOffset": 4522, "endOffset": 5257, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupUserModules", "ranges": [{"startOffset": 5259, "endOffset": 6007, "count": 1}], "isBlockCoverage": false}, {"functionName": "refreshRuntimeOptions", "ranges": [{"startOffset": 6009, "endOffset": 6065, "count": 1}], "isBlockCoverage": false}, {"functionName": "patchProcessObject", "ranges": [{"startOffset": 6492, "endOffset": 8829, "count": 1}], "isBlockCoverage": false}, {"functionName": "addReadOnlyProcessAlias", "ranges": [{"startOffset": 8831, "endOffset": 9112, "count": 13}], "isBlockCoverage": false}, {"functionName": "setupWarningHandler", "ranges": [{"startOffset": 9114, "endOffset": 9634, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9518, "endOffset": 9620, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 9709, "endOffset": 10038, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupEventsource", "ranges": [{"startOffset": 10106, "endOffset": 10231, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupNavigator", "ranges": [{"startOffset": 10335, "endOffset": 10736, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupWebCrypto", "ranges": [{"startOffset": 10840, "endOffset": 11792, "count": 1}], "isBlockCoverage": false}, {"functionName": "cryptoThisCheck", "ranges": [{"startOffset": 11166, "endOffset": 11342, "count": 0}], "isBlockCoverage": false}, {"functionName": "get crypto", "ranges": [{"startOffset": 11643, "endOffset": 11742, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupSQLite", "ranges": [{"startOffset": 11794, "endOffset": 12001, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupWebStorage", "ranges": [{"startOffset": 12003, "endOffset": 12408, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupCodeCoverage", "ranges": [{"startOffset": 12410, "endOffset": 12931, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupCustomEvent", "ranges": [{"startOffset": 13037, "endOffset": 13314, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupStacktracePrinterOnSigint", "ranges": [{"startOffset": 13316, "endOffset": 13541, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializeReport", "ranges": [{"startOffset": 13543, "endOffset": 13792, "count": 1}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 13688, "endOffset": 13783, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupDebugEnv", "ranges": [{"startOffset": 13794, "endOffset": 14025, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializeReportSignalHandlers", "ranges": [{"startOffset": 14087, "endOffset": 14275, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializeHeapSnapshotSignalHandlers", "ranges": [{"startOffset": 14277, "endOffset": 15011, "count": 1}], "isBlockCoverage": false}, {"functionName": "doWriteHeapSnapshot", "ranges": [{"startOffset": 14585, "endOffset": 14739, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14931, "endOffset": 15003, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupTraceCategoryState", "ranges": [{"startOffset": 15013, "endOffset": 15272, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupInspectorHooks", "ranges": [{"startOffset": 15274, "endOffset": 15856, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupNetworkInspection", "ranges": [{"startOffset": 15858, "endOffset": 16171, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializeDeprecations", "ranges": [{"startOffset": 16366, "endOffset": 18268, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupChildProcessIpcChannel", "ranges": [{"startOffset": 18270, "endOffset": 18787, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializeClusterIPC", "ranges": [{"startOffset": 18789, "endOffset": 19059, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializePermission", "ranges": [{"startOffset": 19061, "endOffset": 20965, "count": 1}], "isBlockCoverage": false}, {"functionName": "binding", "ranges": [{"startOffset": 19190, "endOffset": 19277, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20810, "endOffset": 20957, "count": 6}], "isBlockCoverage": false}, {"functionName": "initializeCJSLoader", "ranges": [{"startOffset": 20967, "endOffset": 21088, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializeESMLoader", "ranges": [{"startOffset": 21090, "endOffset": 21673, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializeSourceMapsHandlers", "ranges": [{"startOffset": 21675, "endOffset": 22181, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializeFrozenIntrinsics", "ranges": [{"startOffset": 22183, "endOffset": 22370, "count": 1}], "isBlockCoverage": false}, {"functionName": "loadPreloadModules", "ranges": [{"startOffset": 22477, "endOffset": 22825, "count": 1}], "isBlockCoverage": false}, {"functionName": "markBootstrapComplete", "ranges": [{"startOffset": 22827, "endOffset": 22921, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "70", "url": "node:internal/modules/run_main", "functions": [{"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 718, "endOffset": 1821, "count": 1}], "isBlockCoverage": false}, {"functionName": "shouldUseESMLoader", "ranges": [{"startOffset": 1982, "endOffset": 3503, "count": 1}], "isBlockCoverage": false}, {"functionName": "asyncRunEntryPointWithESMLoader", "ranges": [{"startOffset": 3578, "endOffset": 4309, "count": 2}], "isBlockCoverage": false}, {"functionName": "runEntryPointWithESMLoader", "ranges": [{"startOffset": 4693, "endOffset": 5106, "count": 1}], "isBlockCoverage": false}, {"functionName": "executeUserEntryPoint", "ranges": [{"startOffset": 5874, "endOffset": 6989, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6742, "endOffset": 6981, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "71", "url": "node:internal/dns/utils", "functions": [{"functionName": "initializeDns", "ranges": [{"startOffset": 5465, "endOffset": 5946, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5886, "endOffset": 5942, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "73", "url": "node:internal/bootstrap/switches/does_own_process_state", "functions": [{"functionName": "wrappedCwd", "ranges": [{"startOffset": 3731, "endOffset": 3834, "count": 3}], "isBlockCoverage": false}]}, {"scriptId": "74", "url": "node:internal/main/run_main_module", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1367, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "75", "url": "node:internal/modules/esm/loader", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 36514, "count": 1}], "isBlockCoverage": false}, {"functionName": "newResolveCache", "ranges": [{"startOffset": 1705, "endOffset": 1835, "count": 1}], "isBlockCoverage": true}, {"functionName": "newLoadCache", "ranges": [{"startOffset": 1989, "endOffset": 2110, "count": 1}], "isBlockCoverage": true}, {"functionName": "getTranslators", "ranges": [{"startOffset": 2272, "endOffset": 2394, "count": 99}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3167, "endOffset": 29227, "count": 1}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 4258, "endOffset": 4335, "count": 1}], "isBlockCoverage": true}, {"functionName": "setCustomizations", "ranges": [{"startOffset": 5891, "endOffset": 6135, "count": 1}, {"startOffset": 5994, "endOffset": 6076, "count": 0}], "isBlockCoverage": true}, {"functionName": "createModuleWrap", "ranges": [{"startOffset": 6302, "endOffset": 6392, "count": 0}], "isBlockCoverage": false}, {"functionName": "executeModuleJob", "ranges": [{"startOffset": 6628, "endOffset": 7202, "count": 0}], "isBlockCoverage": false}, {"functionName": "eval", "ranges": [{"startOffset": 7447, "endOffset": 7604, "count": 0}], "isBlockCoverage": false}, {"functionName": "getModuleJobForImport", "ranges": [{"startOffset": 8232, "endOffset": 8484, "count": 308}], "isBlockCoverage": true}, {"functionName": "getModuleJobForRequireInImportedCJS", "ranges": [{"startOffset": 8985, "endOffset": 9242, "count": 0}], "isBlockCoverage": false}, {"functionName": "#getJobFromResolveResult", "ranges": [{"startOffset": 9824, "endOffset": 10300, "count": 308}, {"startOffset": 10160, "endOffset": 10279, "count": 98}], "isBlockCoverage": true}, {"functionName": "importSyncForRequire", "ranges": [{"startOffset": 10835, "endOffset": 12893, "count": 0}], "isBlockCoverage": false}, {"functionName": "getModuleJobForRequire", "ranges": [{"startOffset": 13317, "endOffset": 16761, "count": 0}], "isBlockCoverage": false}, {"functionName": "#translate", "ranges": [{"startOffset": 17331, "endOffset": 17631, "count": 98}, {"startOffset": 17490, "endOffset": 17553, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadAndTranslateForRequireInImportedCJS", "ranges": [{"startOffset": 18048, "endOffset": 18968, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadAndTranslate", "ranges": [{"startOffset": 19330, "endOffset": 19507, "count": 98}], "isBlockCoverage": true}, {"functionName": "#createModuleJob", "ranges": [{"startOffset": 20319, "endOffset": 21296, "count": 98}, {"startOffset": 20568, "endOffset": 20673, "count": 0}, {"startOffset": 20801, "endOffset": 20841, "count": 1}, {"startOffset": 20896, "endOffset": 20911, "count": 0}, {"startOffset": 20913, "endOffset": 20967, "count": 0}], "isBlockCoverage": true}, {"functionName": "import", "ranges": [{"startOffset": 21758, "endOffset": 22158, "count": 3}], "isBlockCoverage": true}, {"functionName": "onImport.tracePromise.__proto__", "ranges": [{"startOffset": 21868, "endOffset": 22081, "count": 3}], "isBlockCoverage": true}, {"functionName": "register", "ranges": [{"startOffset": 22224, "endOffset": 22771, "count": 0}], "isBlockCoverage": false}, {"functionName": "resolve", "ranges": [{"startOffset": 23497, "endOffset": 23818, "count": 308}, {"startOffset": 23609, "endOffset": 23735, "count": 0}], "isBlockCoverage": true}, {"functionName": "#cachedDefaultResolve", "ranges": [{"startOffset": 24180, "endOffset": 24627, "count": 308}, {"startOffset": 24431, "endOffset": 24465, "count": 1}, {"startOffset": 24465, "endOffset": 24626, "count": 307}], "isBlockCoverage": true}, {"functionName": "#resolveAndMaybeBlockOnLoaderThread", "ranges": [{"startOffset": 25052, "endOffset": 25349, "count": 0}], "isBlockCoverage": false}, {"functionName": "resolveSync", "ranges": [{"startOffset": 26060, "endOffset": 26241, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultResolve", "ranges": [{"startOffset": 26453, "endOffset": 26793, "count": 307}], "isBlockCoverage": true}, {"functionName": "load", "ranges": [{"startOffset": 27108, "endOffset": 27339, "count": 98}, {"startOffset": 27165, "endOffset": 27226, "count": 0}], "isBlockCoverage": true}, {"functionName": "#loadAndMaybeBlockOnLoaderThread", "ranges": [{"startOffset": 27699, "endOffset": 27967, "count": 0}], "isBlockCoverage": false}, {"functionName": "#loadSync", "ranges": [{"startOffset": 28511, "endOffset": 28604, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateLoadResult", "ranges": [{"startOffset": 28608, "endOffset": 28759, "count": 98}, {"startOffset": 28666, "endOffset": 28755, "count": 0}], "isBlockCoverage": true}, {"functionName": "importMetaInitialize", "ranges": [{"startOffset": 28763, "endOffset": 29098, "count": 0}], "isBlockCoverage": false}, {"functionName": "forceLoadHooks", "ranges": [{"startOffset": 29159, "endOffset": 29225, "count": 1}, {"startOffset": 29202, "endOffset": 29218, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 29310, "endOffset": 31908, "count": 0}], "isBlockCoverage": false}, {"functionName": "createModuleLoader", "ranges": [{"startOffset": 32292, "endOffset": 33916, "count": 1}, {"startOffset": 32699, "endOffset": 33866, "count": 0}], "isBlockCoverage": true}, {"functionName": "readableURIEncode", "ranges": [{"startOffset": 32774, "endOffset": 33269, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 33589, "endOffset": 33677, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHooksProxy", "ranges": [{"startOffset": 34029, "endOffset": 34205, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOrInitializeCascadedLoader", "ranges": [{"startOffset": 34707, "endOffset": 34819, "count": 3}], "isBlockCoverage": true}, {"functionName": "register", "ranges": [{"startOffset": 36061, "endOffset": 36406, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "76", "url": "node:internal/modules/esm/assert", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3875, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1301, "endOffset": 1342, "count": 5}], "isBlockCoverage": true}, {"functionName": "validateAttributes", "ranges": [{"startOffset": 1763, "endOffset": 3145, "count": 98}, {"startOffset": 1959, "endOffset": 2086, "count": 0}, {"startOffset": 2158, "endOffset": 2303, "count": 0}, {"startOffset": 2571, "endOffset": 2631, "count": 0}, {"startOffset": 2637, "endOffset": 2746, "count": 0}, {"startOffset": 2752, "endOffset": 3139, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleInvalidType", "ranges": [{"startOffset": 3379, "endOffset": 3802, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "77", "url": "node:internal/modules/esm/module_map", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4046, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 284, "endOffset": 309, "count": 1}], "isBlockCoverage": true}, {"functionName": "ResolveCache", "ranges": [{"startOffset": 853, "endOffset": 881, "count": 1}], "isBlockCoverage": true}, {"functionName": "serialize<PERSON>ey", "ranges": [{"startOffset": 1227, "endOffset": 1839, "count": 308}, {"startOffset": 1636, "endOffset": 1838, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1755, "endOffset": 1821, "count": 0}], "isBlockCoverage": false}, {"functionName": "#getModuleCachedImports", "ranges": [{"startOffset": 1843, "endOffset": 2059, "count": 615}, {"startOffset": 1957, "endOffset": 2029, "count": 84}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2234, "endOffset": 2336, "count": 308}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 2476, "endOffset": 2605, "count": 307}], "isBlockCoverage": true}, {"functionName": "has", "ranges": [{"startOffset": 2609, "endOffset": 2713, "count": 0}], "isBlockCoverage": false}, {"functionName": "Load<PERSON>ache", "ranges": [{"startOffset": 2847, "endOffset": 2875, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2924, "endOffset": 3070, "count": 308}, {"startOffset": 3057, "endOffset": 3065, "count": 210}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 3073, "endOffset": 3686, "count": 98}, {"startOffset": 3307, "endOffset": 3343, "count": 0}, {"startOffset": 3345, "endOffset": 3415, "count": 0}, {"startOffset": 3502, "endOffset": 3508, "count": 0}], "isBlockCoverage": true}, {"functionName": "has", "ranges": [{"startOffset": 3689, "endOffset": 3849, "count": 0}], "isBlockCoverage": false}, {"functionName": "delete", "ranges": [{"startOffset": 3852, "endOffset": 3991, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "78", "url": "node:internal/modules/esm/translators", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16925, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1153, "endOffset": 1178, "count": 1}], "isBlockCoverage": true}, {"functionName": "getSource", "ranges": [{"startOffset": 1736, "endOffset": 1864, "count": 0}], "isBlockCoverage": false}, {"functionName": "initCJSParseSync", "ranges": [{"startOffset": 2165, "endOffset": 2302, "count": 0}], "isBlockCoverage": false}, {"functionName": "err<PERSON><PERSON>", "ranges": [{"startOffset": 2493, "endOffset": 2638, "count": 0}], "isBlockCoverage": false}, {"functionName": "moduleStrategy", "ranges": [{"startOffset": 2720, "endOffset": 3043, "count": 96}], "isBlockCoverage": true}, {"functionName": "loadCJSModule", "ranges": [{"startOffset": 3703, "endOffset": 5757, "count": 0}], "isBlockCoverage": false}, {"functionName": "createCJSModuleWrap", "ranges": [{"startOffset": 6241, "endOffset": 7683, "count": 0}], "isBlockCoverage": false}, {"functionName": "requireCommonJS", "ranges": [{"startOffset": 7718, "endOffset": 7986, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8148, "endOffset": 8262, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8435, "endOffset": 8591, "count": 0}], "isBlockCoverage": false}, {"functionName": "commonjsStrategy", "ranges": [{"startOffset": 8779, "endOffset": 9522, "count": 0}], "isBlockCoverage": false}, {"functionName": "cjsPreparseModuleExports", "ranges": [{"startOffset": 9711, "endOffset": 12256, "count": 0}], "isBlockCoverage": false}, {"functionName": "builtinStrategy", "ranges": [{"startOffset": 12380, "endOffset": 12786, "count": 2}, {"startOffset": 12659, "endOffset": 12711, "count": 0}], "isBlockCoverage": true}, {"functionName": "jsonStrategy", "ranges": [{"startOffset": 12850, "endOffset": 15053, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15119, "endOffset": 16212, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 16308, "endOffset": 16571, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 16661, "endOffset": 16922, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "79", "url": "node:internal/modules/esm/resolve", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 39032, "count": 1}], "isBlockCoverage": false}, {"functionName": "emitTrailingSlashPatternDeprecation", "ranges": [{"startOffset": 2488, "endOffset": 3121, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitInvalidSegmentDeprecation", "ranges": [{"startOffset": 3634, "endOffset": 4376, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitLegacyIndexDeprecation", "ranges": [{"startOffset": 4893, "endOffset": 5974, "count": 0}], "isBlockCoverage": false}, {"functionName": "legacyMainResolve", "ranges": [{"startOffset": 7107, "endOffset": 7825, "count": 0}], "isBlockCoverage": false}, {"functionName": "finalizeResolution", "ranges": [{"startOffset": 8609, "endOffset": 10355, "count": 304}, {"startOffset": 8745, "endOffset": 8996, "count": 0}, {"startOffset": 9058, "endOffset": 9185, "count": 0}, {"startOffset": 9322, "endOffset": 9354, "count": 0}, {"startOffset": 9423, "endOffset": 9613, "count": 0}, {"startOffset": 9636, "endOffset": 10014, "count": 0}, {"startOffset": 10260, "endOffset": 10265, "count": 0}], "isBlockCoverage": true}, {"functionName": "importNotDefined", "ranges": [{"startOffset": 10769, "endOffset": 10980, "count": 0}], "isBlockCoverage": false}, {"functionName": "exportsNotFound", "ranges": [{"startOffset": 11376, "endOffset": 11571, "count": 0}], "isBlockCoverage": false}, {"functionName": "throwInvalidSubpath", "ranges": [{"startOffset": 12209, "endOffset": 12592, "count": 0}], "isBlockCoverage": false}, {"functionName": "invalidPackageTarget", "ranges": [{"startOffset": 13048, "endOffset": 13426, "count": 0}], "isBlockCoverage": false}, {"functionName": "resolvePackageTargetString", "ranges": [{"startOffset": 14738, "endOffset": 17477, "count": 15}, {"startOffset": 14907, "endOffset": 14918, "count": 0}, {"startOffset": 14919, "endOffset": 14955, "count": 0}, {"startOffset": 14957, "endOffset": 15041, "count": 0}, {"startOffset": 15091, "endOffset": 15634, "count": 0}, {"startOffset": 15726, "endOffset": 16336, "count": 0}, {"startOffset": 16555, "endOffset": 16639, "count": 0}, {"startOffset": 16683, "endOffset": 17476, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15409, "endOffset": 15422, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15935, "endOffset": 15948, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 16081, "endOffset": 16094, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 16818, "endOffset": 16831, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 17058, "endOffset": 17071, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 17411, "endOffset": 17424, "count": 0}], "isBlockCoverage": false}, {"functionName": "isArrayIndex", "ranges": [{"startOffset": 17669, "endOffset": 17813, "count": 122}, {"startOffset": 17765, "endOffset": 17810, "count": 0}], "isBlockCoverage": true}, {"functionName": "resolvePackageTarget", "ranges": [{"startOffset": 18608, "endOffset": 20826, "count": 41}, {"startOffset": 18801, "endOffset": 18955, "count": 15}, {"startOffset": 18955, "endOffset": 20709, "count": 26}, {"startOffset": 18987, "endOffset": 19801, "count": 0}, {"startOffset": 19954, "endOffset": 20182, "count": 122}, {"startOffset": 20012, "endOffset": 20176, "count": 0}, {"startOffset": 20225, "endOffset": 20634, "count": 107}, {"startOffset": 20282, "endOffset": 20304, "count": 96}, {"startOffset": 20306, "endOffset": 20628, "count": 26}, {"startOffset": 20577, "endOffset": 20590, "count": 0}, {"startOffset": 20634, "endOffset": 20709, "count": 0}, {"startOffset": 20709, "endOffset": 20825, "count": 0}], "isBlockCoverage": true}, {"functionName": "isConditionalExportsMainSugar", "ranges": [{"startOffset": 21110, "endOffset": 22053, "count": 15}, {"startOffset": 21242, "endOffset": 21258, "count": 0}, {"startOffset": 21314, "endOffset": 21331, "count": 0}, {"startOffset": 21471, "endOffset": 22022, "count": 2205}, {"startOffset": 21581, "endOffset": 21638, "count": 15}, {"startOffset": 21638, "endOffset": 22018, "count": 2190}, {"startOffset": 21694, "endOffset": 22018, "count": 0}], "isBlockCoverage": true}, {"functionName": "packageExportsResolve", "ranges": [{"startOffset": 22550, "endOffset": 25121, "count": 15}, {"startOffset": 22754, "endOffset": 22791, "count": 0}, {"startOffset": 23193, "endOffset": 23267, "count": 0}, {"startOffset": 23298, "endOffset": 25120, "count": 0}], "isBlockCoverage": true}, {"functionName": "patternKeyCompare", "ranges": [{"startOffset": 25495, "endOffset": 26050, "count": 0}], "isBlockCoverage": false}, {"functionName": "packageImportsResolve", "ranges": [{"startOffset": 26537, "endOffset": 29012, "count": 0}], "isBlockCoverage": false}, {"functionName": "packageResolve", "ranges": [{"startOffset": 29337, "endOffset": 30132, "count": 15}, {"startOffset": 29501, "endOffset": 29547, "count": 0}, {"startOffset": 29951, "endOffset": 30131, "count": 0}], "isBlockCoverage": true}, {"functionName": "isBareSpecifier", "ranges": [{"startOffset": 30243, "endOffset": 30353, "count": 0}], "isBlockCoverage": false}, {"functionName": "isRelativeSpecifier", "ranges": [{"startOffset": 30472, "endOffset": 30751, "count": 611}, {"startOffset": 30542, "endOffset": 30733, "count": 576}, {"startOffset": 30600, "endOffset": 30616, "count": 506}, {"startOffset": 30616, "endOffset": 30729, "count": 70}, {"startOffset": 30733, "endOffset": 30750, "count": 35}], "isBlockCoverage": true}, {"functionName": "shouldBeTreatedAsRelativeOrAbsolutePath", "ranges": [{"startOffset": 30900, "endOffset": 31091, "count": 611}, {"startOffset": 30986, "endOffset": 31003, "count": 0}, {"startOffset": 31032, "endOffset": 31048, "count": 0}], "isBlockCoverage": true}, {"functionName": "moduleResolve", "ranges": [{"startOffset": 31454, "endOffset": 32736, "count": 304}, {"startOffset": 31645, "endOffset": 31664, "count": 0}, {"startOffset": 31879, "endOffset": 32103, "count": 288}, {"startOffset": 31940, "endOffset": 32099, "count": 0}, {"startOffset": 32103, "endOffset": 32607, "count": 16}, {"startOffset": 32159, "endOffset": 32231, "count": 0}, {"startOffset": 32292, "endOffset": 32603, "count": 15}, {"startOffset": 32325, "endOffset": 32380, "count": 0}, {"startOffset": 32382, "endOffset": 32535, "count": 0}, {"startOffset": 32645, "endOffset": 32671, "count": 0}], "isBlockCoverage": true}, {"functionName": "resolveAsCommonJS", "ranges": [{"startOffset": 32936, "endOffset": 34762, "count": 0}], "isBlockCoverage": false}, {"functionName": "throwIfInvalidParentURL", "ranges": [{"startOffset": 34902, "endOffset": 35175, "count": 307}, {"startOffset": 34979, "endOffset": 35030, "count": 1}, {"startOffset": 35030, "endOffset": 35067, "count": 306}, {"startOffset": 35067, "endOffset": 35087, "count": 0}, {"startOffset": 35089, "endOffset": 35173, "count": 0}], "isBlockCoverage": true}, {"functionName": "defaultResolve", "ranges": [{"startOffset": 35681, "endOffset": 37846, "count": 307}, {"startOffset": 35854, "endOffset": 35902, "count": 306}, {"startOffset": 35986, "endOffset": 36042, "count": 288}, {"startOffset": 36042, "endOffset": 36087, "count": 19}, {"startOffset": 36111, "endOffset": 36311, "count": 292}, {"startOffset": 36248, "endOffset": 36307, "count": 0}, {"startOffset": 36334, "endOffset": 36344, "count": 0}, {"startOffset": 36374, "endOffset": 36421, "count": 3}, {"startOffset": 36421, "endOffset": 36480, "count": 304}, {"startOffset": 36480, "endOffset": 36987, "count": 1}, {"startOffset": 36940, "endOffset": 36983, "count": 0}, {"startOffset": 36987, "endOffset": 37143, "count": 304}, {"startOffset": 37143, "endOffset": 37165, "count": 1}, {"startOffset": 37166, "endOffset": 37184, "count": 303}, {"startOffset": 37197, "endOffset": 37620, "count": 0}, {"startOffset": 37620, "endOffset": 37845, "count": 304}], "isBlockCoverage": true}, {"functionName": "decorateErrorWithCommonJSHints", "ranges": [{"startOffset": 38109, "endOffset": 38722, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "80", "url": "node:internal/modules/esm/get_format", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9499, "count": 1}], "isBlockCoverage": false}, {"functionName": "node:", "ranges": [{"startOffset": 854, "endOffset": 885, "count": 0}], "isBlockCoverage": false}, {"functionName": "detectModuleFormat", "ranges": [{"startOffset": 1053, "endOffset": 1294, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDataProtocolModuleFormat", "ranges": [{"startOffset": 1355, "endOffset": 1571, "count": 0}], "isBlockCoverage": false}, {"functionName": "extname", "ranges": [{"startOffset": 1846, "endOffset": 2205, "count": 304}, {"startOffset": 1946, "endOffset": 2190, "count": 912}, {"startOffset": 2008, "endOffset": 2043, "count": 0}, {"startOffset": 2051, "endOffset": 2180, "count": 304}, {"startOffset": 2139, "endOffset": 2143, "count": 0}, {"startOffset": 2190, "endOffset": 2204, "count": 0}], "isBlockCoverage": true}, {"functionName": "underNodeModules", "ranges": [{"startOffset": 2435, "endOffset": 2656, "count": 0}], "isBlockCoverage": false}, {"functionName": "warnTypelessPackageJsonFile", "ranges": [{"startOffset": 2699, "endOffset": 3326, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFileProtocolModuleFormat", "ranges": [{"startOffset": 3467, "endOffset": 8641, "count": 304}, {"startOffset": 3802, "endOffset": 4294, "count": 0}, {"startOffset": 4301, "endOffset": 4424, "count": 0}, {"startOffset": 4431, "endOffset": 5209, "count": 0}, {"startOffset": 5219, "endOffset": 5287, "count": 0}, {"startOffset": 5289, "endOffset": 8640, "count": 0}], "isBlockCoverage": true}, {"functionName": "defaultGetFormatWithoutErrors", "ranges": [{"startOffset": 8786, "endOffset": 9021, "count": 304}, {"startOffset": 8940, "endOffset": 8962, "count": 0}], "isBlockCoverage": true}, {"functionName": "defaultGetFormat", "ranges": [{"startOffset": 9166, "endOffset": 9389, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "81", "url": "node:internal/modules/esm/formats", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1947, "count": 1}], "isBlockCoverage": false}, {"functionName": "mimeToFormat", "ranges": [{"startOffset": 840, "endOffset": 1178, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFormatOfExtensionlessFile", "ranges": [{"startOffset": 1548, "endOffset": 1853, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "82", "url": "node:internal/modules/esm/load", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6309, "count": 1}], "isBlockCoverage": false}, {"functionName": "getSource", "ranges": [{"startOffset": 941, "endOffset": 1598, "count": 96}, {"startOffset": 1211, "endOffset": 1545, "count": 0}], "isBlockCoverage": true}, {"functionName": "getSourceSync", "ranges": [{"startOffset": 1781, "endOffset": 2342, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultLoad", "ranges": [{"startOffset": 2463, "endOffset": 3843, "count": 98}, {"startOffset": 2647, "endOffset": 2682, "count": 0}, {"startOffset": 2683, "endOffset": 2715, "count": 0}, {"startOffset": 2717, "endOffset": 2924, "count": 0}, {"startOffset": 3048, "endOffset": 3098, "count": 2}, {"startOffset": 3098, "endOffset": 3708, "count": 96}, {"startOffset": 3130, "endOffset": 3157, "count": 0}, {"startOffset": 3339, "endOffset": 3704, "count": 0}], "isBlockCoverage": true}, {"functionName": "defaultLoadSync", "ranges": [{"startOffset": 4356, "endOffset": 4996, "count": 0}], "isBlockCoverage": false}, {"functionName": "throwIfUnsupportedURLScheme", "ranges": [{"startOffset": 5137, "endOffset": 5588, "count": 98}, {"startOffset": 5339, "endOffset": 5366, "count": 2}, {"startOffset": 5367, "endOffset": 5394, "count": 2}, {"startOffset": 5395, "endOffset": 5467, "count": 0}, {"startOffset": 5472, "endOffset": 5586, "count": 0}], "isBlockCoverage": true}, {"functionName": "throwUnknownModuleFormat", "ranges": [{"startOffset": 5967, "endOffset": 6205, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "83", "url": "node:internal/data_url", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10177, "count": 1}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 389, "endOffset": 560, "count": 0}], "isBlockCoverage": false}, {"functionName": "dataURLProcessor", "ranges": [{"startOffset": 742, "endOffset": 3845, "count": 0}], "isBlockCoverage": false}, {"functionName": "URLSerializer", "ranges": [{"startOffset": 3966, "endOffset": 4370, "count": 0}], "isBlockCoverage": false}, {"functionName": "collectASequenceOfCodePointsFast", "ranges": [{"startOffset": 4563, "endOffset": 4938, "count": 0}], "isBlockCoverage": false}, {"functionName": "stringPercentDecode", "ranges": [{"startOffset": 5023, "endOffset": 5235, "count": 0}], "isBlockCoverage": false}, {"functionName": "isHexCharByte", "ranges": [{"startOffset": 5269, "endOffset": 5427, "count": 0}], "isBlockCoverage": false}, {"functionName": "hexByteToNumber", "ranges": [{"startOffset": 5461, "endOffset": 5665, "count": 0}], "isBlockCoverage": false}, {"functionName": "percentDecode", "ranges": [{"startOffset": 5747, "endOffset": 7023, "count": 0}], "isBlockCoverage": false}, {"functionName": "forgivingBase64", "ranges": [{"startOffset": 7111, "endOffset": 8245, "count": 0}], "isBlockCoverage": false}, {"functionName": "isASCIIWhitespace", "ranges": [{"startOffset": 8335, "endOffset": 8485, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeASCIIWhitespace", "ranges": [{"startOffset": 8672, "endOffset": 8809, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeChars", "ranges": [{"startOffset": 8951, "endOffset": 9374, "count": 0}], "isBlockCoverage": false}, {"functionName": "isomorphicDecode", "ranges": [{"startOffset": 9491, "endOffset": 10133, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "84", "url": "node:internal/mime", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11171, "count": 1}], "isBlockCoverage": false}, {"functionName": "toASCIILower", "ranges": [{"startOffset": 587, "endOffset": 820, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseTypeAndSubtype", "ranges": [{"startOffset": 867, "endOffset": 2486, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeBackslashes", "ranges": [{"startOffset": 2603, "endOffset": 3018, "count": 0}], "isBlockCoverage": false}, {"functionName": "escapeQuote<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3021, "endOffset": 3233, "count": 0}], "isBlockCoverage": false}, {"functionName": "encode", "ranges": [{"startOffset": 3250, "endOffset": 3489, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3509, "endOffset": 9197, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 9672, "endOffset": 10967, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "85", "url": "node:internal/fs/promises", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33465, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyLoadCpPromises", "ranges": [{"startOffset": 3219, "endOffset": 3311, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyFsStreams", "ranges": [{"startOffset": 3373, "endOffset": 3456, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3485, "endOffset": 3535, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleErrorFromBinding", "ranges": [{"startOffset": 3820, "endOffset": 3950, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3990, "endOffset": 10090, "count": 96}], "isBlockCoverage": false}, {"functionName": "FileHandle", "ranges": [{"startOffset": 4072, "endOffset": 4290, "count": 96}, {"startOffset": 4227, "endOffset": 4231, "count": 0}], "isBlockCoverage": true}, {"functionName": "getAsyncId", "ranges": [{"startOffset": 4294, "endOffset": 4351, "count": 0}], "isBlockCoverage": false}, {"functionName": "get fd", "ranges": [{"startOffset": 4355, "endOffset": 4391, "count": 192}], "isBlockCoverage": true}, {"functionName": "appendFile", "ranges": [{"startOffset": 4395, "endOffset": 4477, "count": 0}], "isBlockCoverage": false}, {"functionName": "chmod", "ranges": [{"startOffset": 4481, "endOffset": 4537, "count": 0}], "isBlockCoverage": false}, {"functionName": "chown", "ranges": [{"startOffset": 4541, "endOffset": 4605, "count": 0}], "isBlockCoverage": false}, {"functionName": "datasync", "ranges": [{"startOffset": 4609, "endOffset": 4661, "count": 0}], "isBlockCoverage": false}, {"functionName": "sync", "ranges": [{"startOffset": 4665, "endOffset": 4709, "count": 0}], "isBlockCoverage": false}, {"functionName": "read", "ranges": [{"startOffset": 4713, "endOffset": 4822, "count": 0}], "isBlockCoverage": false}, {"functionName": "readv", "ranges": [{"startOffset": 4826, "endOffset": 4907, "count": 0}], "isBlockCoverage": false}, {"functionName": "readFile", "ranges": [{"startOffset": 4911, "endOffset": 4978, "count": 0}], "isBlockCoverage": false}, {"functionName": "readLines", "ranges": [{"startOffset": 4982, "endOffset": 5125, "count": 0}], "isBlockCoverage": false}, {"functionName": "stat", "ranges": [{"startOffset": 5129, "endOffset": 5189, "count": 0}], "isBlockCoverage": false}, {"functionName": "truncate", "ranges": [{"startOffset": 5193, "endOffset": 5257, "count": 0}], "isBlockCoverage": false}, {"functionName": "utimes", "ranges": [{"startOffset": 5261, "endOffset": 5335, "count": 0}], "isBlockCoverage": false}, {"functionName": "write", "ranges": [{"startOffset": 5339, "endOffset": 5450, "count": 0}], "isBlockCoverage": false}, {"functionName": "writev", "ranges": [{"startOffset": 5454, "endOffset": 5537, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeFile", "ranges": [{"startOffset": 5541, "endOffset": 5622, "count": 0}], "isBlockCoverage": false}, {"functionName": "close", "ranges": [{"startOffset": 5634, "endOffset": 6426, "count": 96}, {"startOffset": 5668, "endOffset": 5706, "count": 0}, {"startOffset": 5737, "endOffset": 5778, "count": 0}, {"startOffset": 6004, "endOffset": 6365, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5946, "endOffset": 5988, "count": 96}], "isBlockCoverage": true}, {"functionName": "<computed>.SafePromisePrototypeFinally.<computed>", "ranges": [{"startOffset": 6089, "endOffset": 6201, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6204, "endOffset": 6349, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6431, "endOffset": 6490, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableWebStream", "ranges": [{"startOffset": 6685, "endOffset": 8371, "count": 0}], "isBlockCoverage": false}, {"functionName": "createReadStream", "ranges": [{"startOffset": 8669, "endOffset": 8820, "count": 0}], "isBlockCoverage": false}, {"functionName": "createWriteStream", "ranges": [{"startOffset": 9124, "endOffset": 9278, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9282, "endOffset": 9682, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9686, "endOffset": 9739, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9743, "endOffset": 9830, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9834, "endOffset": 9867, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9871, "endOffset": 10088, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleFdClose", "ranges": [{"startOffset": 10092, "endOffset": 10463, "count": 96}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10203, "endOffset": 10262, "count": 96}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10249, "endOffset": 10261, "count": 96}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10268, "endOffset": 10455, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleFdSync", "ranges": [{"startOffset": 10465, "endOffset": 10748, "count": 0}], "isBlockCoverage": false}, {"functionName": "fsCall", "ranges": [{"startOffset": 10750, "endOffset": 11210, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkAborted", "ranges": [{"startOffset": 11212, "endOffset": 11331, "count": 384}, {"startOffset": 11256, "endOffset": 11265, "count": 0}, {"startOffset": 11271, "endOffset": 11329, "count": 0}], "isBlockCoverage": true}, {"functionName": "writeFileHandle", "ranges": [{"startOffset": 11333, "endOffset": 12475, "count": 0}], "isBlockCoverage": false}, {"functionName": "readFileHandle", "ranges": [{"startOffset": 12477, "endOffset": 14878, "count": 96}, {"startOffset": 12629, "endOffset": 12659, "count": 0}, {"startOffset": 13013, "endOffset": 13051, "count": 0}, {"startOffset": 13084, "endOffset": 13132, "count": 0}, {"startOffset": 13165, "endOffset": 13203, "count": 0}, {"startOffset": 13474, "endOffset": 13546, "count": 0}, {"startOffset": 13730, "endOffset": 13734, "count": 0}, {"startOffset": 13819, "endOffset": 13886, "count": 0}, {"startOffset": 13980, "endOffset": 13991, "count": 0}, {"startOffset": 14048, "endOffset": 14108, "count": 0}, {"startOffset": 14152, "endOffset": 14166, "count": 0}, {"startOffset": 14168, "endOffset": 14278, "count": 0}, {"startOffset": 14309, "endOffset": 14537, "count": 0}, {"startOffset": 14538, "endOffset": 14552, "count": 0}, {"startOffset": 14572, "endOffset": 14872, "count": 0}], "isBlockCoverage": true}, {"functionName": "access", "ranges": [{"startOffset": 15031, "endOffset": 15224, "count": 0}], "isBlockCoverage": false}, {"functionName": "cp", "ranges": [{"startOffset": 15226, "endOffset": 15437, "count": 0}], "isBlockCoverage": false}, {"functionName": "copyFile", "ranges": [{"startOffset": 15439, "endOffset": 15703, "count": 0}], "isBlockCoverage": false}, {"functionName": "open", "ranges": [{"startOffset": 15823, "endOffset": 16155, "count": 96}], "isBlockCoverage": true}, {"functionName": "read", "ranges": [{"startOffset": 16157, "endOffset": 17653, "count": 0}], "isBlockCoverage": false}, {"functionName": "readv", "ranges": [{"startOffset": 17655, "endOffset": 18019, "count": 0}], "isBlockCoverage": false}, {"functionName": "write", "ranges": [{"startOffset": 18021, "endOffset": 19379, "count": 0}], "isBlockCoverage": false}, {"functionName": "writev", "ranges": [{"startOffset": 19381, "endOffset": 19846, "count": 0}], "isBlockCoverage": false}, {"functionName": "rename", "ranges": [{"startOffset": 19848, "endOffset": 20128, "count": 0}], "isBlockCoverage": false}, {"functionName": "truncate", "ranges": [{"startOffset": 20130, "endOffset": 20263, "count": 0}], "isBlockCoverage": false}, {"functionName": "ftrun<PERSON>", "ranges": [{"startOffset": 20265, "endOffset": 20504, "count": 0}], "isBlockCoverage": false}, {"functionName": "rm", "ranges": [{"startOffset": 20506, "endOffset": 20679, "count": 0}], "isBlockCoverage": false}, {"functionName": "rmdir", "ranges": [{"startOffset": 20681, "endOffset": 21101, "count": 0}], "isBlockCoverage": false}, {"functionName": "fdatasync", "ranges": [{"startOffset": 21103, "endOffset": 21272, "count": 0}], "isBlockCoverage": false}, {"functionName": "fsync", "ranges": [{"startOffset": 21274, "endOffset": 21435, "count": 0}], "isBlockCoverage": false}, {"functionName": "mkdir", "ranges": [{"startOffset": 21437, "endOffset": 21950, "count": 0}], "isBlockCoverage": false}, {"functionName": "readdirRecursive", "ranges": [{"startOffset": 21952, "endOffset": 23925, "count": 0}], "isBlockCoverage": false}, {"functionName": "readdir", "ranges": [{"startOffset": 23927, "endOffset": 24518, "count": 0}], "isBlockCoverage": false}, {"functionName": "readlink", "ranges": [{"startOffset": 24520, "endOffset": 24784, "count": 0}], "isBlockCoverage": false}, {"functionName": "symlink", "ranges": [{"startOffset": 24786, "endOffset": 26028, "count": 0}], "isBlockCoverage": false}, {"functionName": "fstat", "ranges": [{"startOffset": 26030, "endOffset": 26282, "count": 0}], "isBlockCoverage": false}, {"functionName": "lstat", "ranges": [{"startOffset": 26284, "endOffset": 26547, "count": 0}], "isBlockCoverage": false}, {"functionName": "stat", "ranges": [{"startOffset": 26549, "endOffset": 26810, "count": 0}], "isBlockCoverage": false}, {"functionName": "statfs", "ranges": [{"startOffset": 26812, "endOffset": 27060, "count": 0}], "isBlockCoverage": false}, {"functionName": "link", "ranges": [{"startOffset": 27062, "endOffset": 27363, "count": 0}], "isBlockCoverage": false}, {"functionName": "unlink", "ranges": [{"startOffset": 27365, "endOffset": 27539, "count": 0}], "isBlockCoverage": false}, {"functionName": "fchmod", "ranges": [{"startOffset": 27541, "endOffset": 27754, "count": 0}], "isBlockCoverage": false}, {"functionName": "chmod", "ranges": [{"startOffset": 27756, "endOffset": 27993, "count": 0}], "isBlockCoverage": false}, {"functionName": "lchmod", "ranges": [{"startOffset": 27995, "endOffset": 28223, "count": 0}], "isBlockCoverage": false}, {"functionName": "lchown", "ranges": [{"startOffset": 28225, "endOffset": 28528, "count": 0}], "isBlockCoverage": false}, {"functionName": "fchown", "ranges": [{"startOffset": 28530, "endOffset": 28807, "count": 0}], "isBlockCoverage": false}, {"functionName": "chown", "ranges": [{"startOffset": 28809, "endOffset": 29110, "count": 0}], "isBlockCoverage": false}, {"functionName": "utimes", "ranges": [{"startOffset": 29112, "endOffset": 29394, "count": 0}], "isBlockCoverage": false}, {"functionName": "futimes", "ranges": [{"startOffset": 29396, "endOffset": 29675, "count": 0}], "isBlockCoverage": false}, {"functionName": "lutimes", "ranges": [{"startOffset": 29677, "endOffset": 29946, "count": 0}], "isBlockCoverage": false}, {"functionName": "realpath", "ranges": [{"startOffset": 29948, "endOffset": 30186, "count": 0}], "isBlockCoverage": false}, {"functionName": "mkdtemp", "ranges": [{"startOffset": 30188, "endOffset": 30496, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeFile", "ranges": [{"startOffset": 30498, "endOffset": 31372, "count": 0}], "isBlockCoverage": false}, {"functionName": "isCustomIterable", "ranges": [{"startOffset": 31374, "endOffset": 31488, "count": 0}], "isBlockCoverage": false}, {"functionName": "appendFile", "ranges": [{"startOffset": 31490, "endOffset": 31717, "count": 0}], "isBlockCoverage": false}, {"functionName": "readFile", "ranges": [{"startOffset": 31719, "endOffset": 32063, "count": 96}, {"startOffset": 31836, "endOffset": 31842, "count": 0}, {"startOffset": 31883, "endOffset": 31920, "count": 0}], "isBlockCoverage": true}, {"functionName": "_watch", "ranges": [{"startOffset": 32065, "endOffset": 32726, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 32753, "endOffset": 32791, "count": 0}], "isBlockCoverage": false}, {"functionName": "glob", "ranges": [{"startOffset": 32794, "endOffset": 32942, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "86", "url": "node:internal/fs/dir", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7361, "count": 1}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 693, "endOffset": 6356, "count": 0}], "isBlockCoverage": false}, {"functionName": "opendir", "ranges": [{"startOffset": 6534, "endOffset": 7085, "count": 0}], "isBlockCoverage": false}, {"functionName": "opendirSync", "ranges": [{"startOffset": 7087, "endOffset": 7304, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "87", "url": "node:string_decoder", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5141, "count": 1}], "isBlockCoverage": false}, {"functionName": "normalizeEncoding", "ranges": [{"startOffset": 2172, "endOffset": 2422, "count": 0}], "isBlockCoverage": false}, {"functionName": "StringDecoder", "ranges": [{"startOffset": 2633, "endOffset": 2832, "count": 0}], "isBlockCoverage": false}, {"functionName": "write", "ranges": [{"startOffset": 3155, "endOffset": 3540, "count": 0}], "isBlockCoverage": false}, {"functionName": "end", "ranges": [{"startOffset": 3817, "endOffset": 4008, "count": 0}], "isBlockCoverage": false}, {"functionName": "text", "ranges": [{"startOffset": 4219, "endOffset": 4376, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4514, "endOffset": 4727, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4819, "endOffset": 4882, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4975, "endOffset": 5090, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "88", "url": "node:internal/fs/watchers", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11258, "count": 1}], "isBlockCoverage": false}, {"functionName": "emitStop", "ranges": [{"startOffset": 1419, "endOffset": 1467, "count": 0}], "isBlockCoverage": false}, {"functionName": "StatWatcher", "ranges": [{"startOffset": 1469, "endOffset": 1699, "count": 0}], "isBlockCoverage": false}, {"functionName": "onchange", "ranges": [{"startOffset": 1819, "endOffset": 2176, "count": 0}], "isBlockCoverage": false}, {"functionName": "StatWatcher.<computed>", "ranges": [{"startOffset": 2564, "endOffset": 3402, "count": 0}], "isBlockCoverage": false}, {"functionName": "StatWatcher.start", "ranges": [{"startOffset": 3623, "endOffset": 3631, "count": 0}], "isBlockCoverage": false}, {"functionName": "StatWatcher.stop", "ranges": [{"startOffset": 3877, "endOffset": 4158, "count": 0}], "isBlockCoverage": false}, {"functionName": "StatWatcher.<computed>", "ranges": [{"startOffset": 4247, "endOffset": 4674, "count": 0}], "isBlockCoverage": false}, {"functionName": "StatWatcher.ref", "ranges": [{"startOffset": 4705, "endOffset": 4987, "count": 0}], "isBlockCoverage": false}, {"functionName": "StatWatcher.unref", "ranges": [{"startOffset": 5020, "endOffset": 5270, "count": 0}], "isBlockCoverage": false}, {"functionName": "FSWatcher", "ranges": [{"startOffset": 5274, "endOffset": 6246, "count": 0}], "isBlockCoverage": false}, {"functionName": "FSWatcher.<computed>", "ranges": [{"startOffset": 6783, "endOffset": 7697, "count": 0}], "isBlockCoverage": false}, {"functionName": "FSWatcher.start", "ranges": [{"startOffset": 7914, "endOffset": 7922, "count": 0}], "isBlockCoverage": false}, {"functionName": "FSWatcher.close", "ranges": [{"startOffset": 8045, "endOffset": 8382, "count": 0}], "isBlockCoverage": false}, {"functionName": "FSWatcher.ref", "ranges": [{"startOffset": 8411, "endOffset": 8480, "count": 0}], "isBlockCoverage": false}, {"functionName": "FSWatcher.unref", "ranges": [{"startOffset": 8511, "endOffset": 8582, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitCloseNT", "ranges": [{"startOffset": 8585, "endOffset": 8637, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8861, "endOffset": 8897, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 8901, "endOffset": 8942, "count": 0}], "isBlockCoverage": false}, {"functionName": "watch", "ranges": [{"startOffset": 8978, "endOffset": 11126, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "89", "url": "node:internal/fs/recursive_watch", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7216, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyLoadFsSync", "ranges": [{"startOffset": 716, "endOffset": 802, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 870, "endOffset": 7162, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "90", "url": "node:internal/readline/interface", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 39048, "count": 1}], "isBlockCoverage": false}, {"functionName": "InterfaceConstructor", "ranges": [{"startOffset": 3837, "endOffset": 9323, "count": 0}], "isBlockCoverage": false}, {"functionName": "Interface", "ranges": [{"startOffset": 9564, "endOffset": 9664, "count": 0}], "isBlockCoverage": false}, {"functionName": "get columns", "ranges": [{"startOffset": 9667, "endOffset": 9765, "count": 0}], "isBlockCoverage": false}, {"functionName": "setPrompt", "ranges": [{"startOffset": 9874, "endOffset": 9925, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPrompt", "ranges": [{"startOffset": 10019, "endOffset": 10062, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10066, "endOffset": 10257, "count": 0}], "isBlockCoverage": false}, {"functionName": "prompt", "ranges": [{"startOffset": 10396, "endOffset": 10650, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10654, "endOffset": 10961, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10965, "endOffset": 11207, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11211, "endOffset": 11298, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11302, "endOffset": 11470, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11474, "endOffset": 11671, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11675, "endOffset": 12853, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12857, "endOffset": 13862, "count": 0}], "isBlockCoverage": false}, {"functionName": "close", "ranges": [{"startOffset": 13946, "endOffset": 14117, "count": 0}], "isBlockCoverage": false}, {"functionName": "pause", "ranges": [{"startOffset": 14198, "endOffset": 14329, "count": 0}], "isBlockCoverage": false}, {"functionName": "resume", "ranges": [{"startOffset": 14421, "endOffset": 14557, "count": 0}], "isBlockCoverage": false}, {"functionName": "write", "ranges": [{"startOffset": 14828, "endOffset": 14987, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14991, "endOffset": 16725, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 16729, "endOffset": 17398, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 17402, "endOffset": 17801, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 17805, "endOffset": 20014, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20018, "endOffset": 20479, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20483, "endOffset": 20741, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20745, "endOffset": 21227, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 21231, "endOffset": 21712, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 21716, "endOffset": 22476, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 22480, "endOffset": 22907, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 22911, "endOffset": 23189, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 23193, "endOffset": 23451, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 23455, "endOffset": 23715, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 23719, "endOffset": 23874, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 23878, "endOffset": 24553, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearLine", "ranges": [{"startOffset": 24557, "endOffset": 24706, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 24710, "endOffset": 24864, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 24868, "endOffset": 25055, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 25059, "endOffset": 25367, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 25371, "endOffset": 25679, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26114, "endOffset": 26748, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26752, "endOffset": 27460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27535, "endOffset": 28475, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCursorPos", "ranges": [{"startOffset": 28652, "endOffset": 28821, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 28940, "endOffset": 29497, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 29534, "endOffset": 37779, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 38087, "endOffset": 38467, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "91", "url": "node:internal/readline/utils", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12360, "count": 1}], "isBlockCoverage": false}, {"functionName": "CSI", "ranges": [{"startOffset": 366, "endOffset": 560, "count": 4}, {"startOffset": 525, "endOffset": 540, "count": 0}], "isBlockCoverage": true}, {"functionName": "charLengthLeft", "ranges": [{"startOffset": 939, "endOffset": 1201, "count": 0}], "isBlockCoverage": false}, {"functionName": "charLengthAt", "ranges": [{"startOffset": 1203, "endOffset": 1465, "count": 0}], "isBlockCoverage": false}, {"functionName": "emit<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2194, "endOffset": 11821, "count": 0}], "isBlockCoverage": false}, {"functionName": "commonPrefix", "ranges": [{"startOffset": 11851, "endOffset": 12247, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "92", "url": "node:internal/readline/callbacks", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2754, "count": 1}], "isBlockCoverage": false}, {"functionName": "cursorTo", "ranges": [{"startOffset": 460, "endOffset": 1146, "count": 0}], "isBlockCoverage": false}, {"functionName": "moveCursor", "ranges": [{"startOffset": 1210, "endOffset": 1722, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearLine", "ranges": [{"startOffset": 1869, "endOffset": 2272, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearScreenDown", "ranges": [{"startOffset": 2349, "endOffset": 2672, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "93", "url": "node:internal/modules/esm/module_job", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15447, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 463, "endOffset": 488, "count": 1}], "isBlockCoverage": true}, {"functionName": "isCommonJSGlobalLikeNotDefinedError", "ranges": [{"startOffset": 1317, "endOffset": 1448, "count": 0}], "isBlockCoverage": false}, {"functionName": "ModuleJobBase", "ranges": [{"startOffset": 1475, "endOffset": 1662, "count": 98}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1815, "endOffset": 10949, "count": 98}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2524, "endOffset": 3479, "count": 98}, {"startOffset": 2872, "endOffset": 3027, "count": 0}], "isBlockCoverage": true}, {"functionName": "_link", "ranges": [{"startOffset": 3617, "endOffset": 5242, "count": 98}, {"startOffset": 4600, "endOffset": 5167, "count": 305}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4856, "endOffset": 5007, "count": 305}], "isBlockCoverage": true}, {"functionName": "instantiate", "ranges": [{"startOffset": 5246, "endOffset": 5391, "count": 3}, {"startOffset": 5303, "endOffset": 5357, "count": 2}], "isBlockCoverage": true}, {"functionName": "_instantiate", "ranges": [{"startOffset": 5395, "endOffset": 9132, "count": 2}, {"startOffset": 5928, "endOffset": 6105, "count": 0}, {"startOffset": 6162, "endOffset": 8885, "count": 0}, {"startOffset": 8932, "endOffset": 9128, "count": 99}], "isBlockCoverage": true}, {"functionName": "addJobsToDependencyGraph", "ranges": [{"startOffset": 5494, "endOffset": 5827, "count": 307}, {"startOffset": 5628, "endOffset": 5653, "count": 208}, {"startOffset": 5653, "endOffset": 5826, "count": 99}], "isBlockCoverage": true}, {"functionName": "runSync", "ranges": [{"startOffset": 9136, "endOffset": 9573, "count": 0}], "isBlockCoverage": false}, {"functionName": "run", "ranges": [{"startOffset": 9577, "endOffset": 10947, "count": 3}, {"startOffset": 9663, "endOffset": 9737, "count": 1}, {"startOffset": 9906, "endOffset": 10890, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 11566, "endOffset": 15327, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "94", "url": "file:///home/<USER>/ylabs/lib0/test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2365, "count": 1}, {"startOffset": 1827, "endOffset": 1866, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2272, "endOffset": 2363, "count": 1}, {"startOffset": 2353, "endOffset": 2356, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "95", "url": "file:///home/<USER>/ylabs/lib0/testing.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27153, "count": 1}, {"startOffset": 1868, "endOffset": 1914, "count": 0}, {"startOffset": 3148, "endOffset": 3178, "count": 0}, {"startOffset": 3253, "endOffset": 3277, "count": 0}], "isBlockCoverage": true}, {"functionName": "TestCase", "ranges": [{"startOffset": 2023, "endOffset": 2395, "count": 151}], "isBlockCoverage": true}, {"functionName": "resetSeed", "ranges": [{"startOffset": 2399, "endOffset": 2461, "count": 149065}], "isBlockCoverage": true}, {"functionName": "get seed", "ranges": [{"startOffset": 2520, "endOffset": 2712, "count": 125116}, {"startOffset": 2671, "endOffset": 2680, "count": 0}], "isBlockCoverage": true}, {"functionName": "get prng", "ranges": [{"startOffset": 2858, "endOffset": 3001, "count": 159123}, {"startOffset": 2926, "endOffset": 2975, "count": 125113}], "isBlockCoverage": true}, {"functionName": "run", "ranges": [{"startOffset": 3521, "endOffset": 6229, "count": 151}, {"startOffset": 3779, "endOffset": 3800, "count": 0}, {"startOffset": 4107, "endOffset": 4146, "count": 0}, {"startOffset": 4310, "endOffset": 4673, "count": 149216}, {"startOffset": 4376, "endOffset": 4401, "count": 35}, {"startOffset": 4393, "endOffset": 4401, "count": 29}, {"startOffset": 4408, "endOffset": 4445, "count": 8}, {"startOffset": 4560, "endOffset": 4575, "count": 149084}, {"startOffset": 4576, "endOffset": 4614, "count": 149084}, {"startOffset": 4616, "endOffset": 4644, "count": 149065}, {"startOffset": 4644, "endOffset": 4669, "count": 151}, {"startOffset": 4694, "endOffset": 4732, "count": 149065}, {"startOffset": 4812, "endOffset": 4844, "count": 8}, {"startOffset": 4846, "endOffset": 4875, "count": 0}, {"startOffset": 5106, "endOffset": 5234, "count": 0}, {"startOffset": 5312, "endOffset": 5316, "count": 114}, {"startOffset": 5317, "endOffset": 5339, "count": 37}, {"startOffset": 5369, "endOffset": 5384, "count": 19}, {"startOffset": 5390, "endOffset": 5679, "count": 19}, {"startOffset": 5684, "endOffset": 5726, "count": 132}, {"startOffset": 5747, "endOffset": 6079, "count": 8}, {"startOffset": 5895, "endOffset": 6050, "count": 0}, {"startOffset": 6079, "endOffset": 6210, "count": 143}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5023, "endOffset": 5038, "count": 1767663}], "isBlockCoverage": true}, {"functionName": "describe", "ranges": [{"startOffset": 6583, "endOffset": 6664, "count": 149}], "isBlockCoverage": true}, {"functionName": "info", "ranges": [{"startOffset": 6932, "endOffset": 6958, "count": 77}], "isBlockCoverage": true}, {"functionName": "group", "ranges": [{"startOffset": 7518, "endOffset": 7629, "count": 31}], "isBlockCoverage": true}, {"functionName": "groupAsync", "ranges": [{"startOffset": 8126, "endOffset": 8249, "count": 15}], "isBlockCoverage": true}, {"functionName": "measureTime", "ranges": [{"startOffset": 8747, "endOffset": 8992, "count": 172}], "isBlockCoverage": true}, {"functionName": "measureTimeAsync", "ranges": [{"startOffset": 9529, "endOffset": 9786, "count": 17}], "isBlockCoverage": true}, {"functionName": "compareArrays", "ranges": [{"startOffset": 9932, "endOffset": 10121, "count": 73}, {"startOffset": 9997, "endOffset": 10014, "count": 1}, {"startOffset": 10014, "endOffset": 10053, "count": 72}, {"startOffset": 10053, "endOffset": 10105, "count": 606811}, {"startOffset": 10080, "endOffset": 10101, "count": 1}, {"startOffset": 10105, "endOffset": 10120, "count": 71}], "isBlockCoverage": true}, {"functionName": "compareStrings", "ranges": [{"startOffset": 10271, "endOffset": 10538, "count": 28907}, {"startOffset": 10319, "endOffset": 10536, "count": 1}], "isBlockCoverage": true}, {"functionName": "compareObjects", "ranges": [{"startOffset": 10714, "endOffset": 10782, "count": 13}, {"startOffset": 10770, "endOffset": 10780, "count": 3}], "isBlockCoverage": true}, {"functionName": "compareValues", "ranges": [{"startOffset": 10926, "endOffset": 11081, "count": 198979}, {"startOffset": 10973, "endOffset": 11065, "count": 3}, {"startOffset": 11065, "endOffset": 11080, "count": 198976}], "isBlockCoverage": true}, {"functionName": "_failMessage", "ranges": [{"startOffset": 11213, "endOffset": 11331, "count": 12}, {"startOffset": 11269, "endOffset": 11290, "count": 6}, {"startOffset": 11295, "endOffset": 11329, "count": 6}], "isBlockCoverage": true}, {"functionName": "_compare", "ranges": [{"startOffset": 11513, "endOffset": 14297, "count": 241883}, {"startOffset": 11684, "endOffset": 11696, "count": 240503}, {"startOffset": 11698, "endOffset": 11746, "count": 1381}, {"startOffset": 11746, "endOffset": 11792, "count": 240502}, {"startOffset": 11792, "endOffset": 11943, "count": 2}, {"startOffset": 11836, "endOffset": 11939, "count": 1}, {"startOffset": 11943, "endOffset": 11983, "count": 240500}, {"startOffset": 11983, "endOffset": 12049, "count": 1}, {"startOffset": 12049, "endOffset": 12102, "count": 240499}, {"startOffset": 12102, "endOffset": 12175, "count": 1}, {"startOffset": 12227, "endOffset": 12484, "count": 29465}, {"startOffset": 12287, "endOffset": 12361, "count": 2}, {"startOffset": 12361, "endOffset": 12392, "count": 29463}, {"startOffset": 12392, "endOffset": 12407, "count": 5215963}, {"startOffset": 12414, "endOffset": 12466, "count": 5186500}, {"startOffset": 12466, "endOffset": 12484, "count": 29463}, {"startOffset": 12489, "endOffset": 12802, "count": 3}, {"startOffset": 12530, "endOffset": 12619, "count": 1}, {"startOffset": 12619, "endOffset": 12802, "count": 2}, {"startOffset": 12807, "endOffset": 13238, "count": 5}, {"startOffset": 12848, "endOffset": 12937, "count": 1}, {"startOffset": 12937, "endOffset": 13238, "count": 4}, {"startOffset": 13243, "endOffset": 13258, "count": 1}, {"startOffset": 13318, "endOffset": 13759, "count": 791}, {"startOffset": 13380, "endOffset": 13474, "count": 1}, {"startOffset": 13474, "endOffset": 13759, "count": 790}, {"startOffset": 13764, "endOffset": 14028, "count": 12637}, {"startOffset": 13809, "endOffset": 13902, "count": 1}, {"startOffset": 13902, "endOffset": 14028, "count": 12636}, {"startOffset": 14060, "endOffset": 14250, "count": 197598}, {"startOffset": 14137, "endOffset": 14250, "count": 0}, {"startOffset": 14254, "endOffset": 14296, "count": 240485}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12656, "endOffset": 12783, "count": 3}, {"startOffset": 12694, "endOffset": 12775, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12974, "endOffset": 13219, "count": 8}, {"startOffset": 13017, "endOffset": 13130, "count": 1}, {"startOffset": 13130, "endOffset": 13218, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13499, "endOffset": 13746, "count": 956}, {"startOffset": 13558, "endOffset": 13661, "count": 2}, {"startOffset": 13661, "endOffset": 13745, "count": 954}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13939, "endOffset": 14015, "count": 132115}], "isBlockCoverage": true}, {"functionName": "compare", "ranges": [{"startOffset": 14472, "endOffset": 14574, "count": 108807}], "isBlockCoverage": true}, {"functionName": "assert", "ranges": [{"startOffset": 14766, "endOffset": 14877, "count": 979324}, {"startOffset": 14807, "endOffset": 14875, "count": 1}, {"startOffset": 14851, "endOffset": 14867, "count": 0}], "isBlockCoverage": true}, {"functionName": "promiseRejected", "ranges": [{"startOffset": 14962, "endOffset": 15066, "count": 4}, {"startOffset": 14996, "endOffset": 15001, "count": 1}, {"startOffset": 15001, "endOffset": 15029, "count": 3}, {"startOffset": 15029, "endOffset": 15065, "count": 1}], "isBlockCoverage": true}, {"functionName": "fails", "ranges": [{"startOffset": 15156, "endOffset": 15300, "count": 32}, {"startOffset": 15183, "endOffset": 15266, "count": 31}, {"startOffset": 15266, "endOffset": 15299, "count": 1}], "isBlockCoverage": true}, {"functionName": "failsAsync", "ranges": [{"startOffset": 15403, "endOffset": 15559, "count": 5}, {"startOffset": 15437, "endOffset": 15442, "count": 1}, {"startOffset": 15442, "endOffset": 15525, "count": 4}, {"startOffset": 15525, "endOffset": 15558, "count": 1}], "isBlockCoverage": true}, {"functionName": "runTests", "ranges": [{"startOffset": 15680, "endOffset": 17127, "count": 1}, {"startOffset": 16121, "endOffset": 16611, "count": 38}, {"startOffset": 16183, "endOffset": 16607, "count": 152}, {"startOffset": 16273, "endOffset": 16601, "count": 151}, {"startOffset": 16367, "endOffset": 16388, "count": 302}, {"startOffset": 16924, "endOffset": 17085, "count": 0}], "isBlockCoverage": true}, {"functionName": "filterTest", "ranges": [{"startOffset": 15760, "endOffset": 15835, "count": 304}, {"startOffset": 15800, "endOffset": 15835, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15878, "endOffset": 15987, "count": 38}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15901, "endOffset": 15966, "count": 152}, {"startOffset": 15959, "endOffset": 15962, "count": 151}, {"startOffset": 15963, "endOffset": 15966, "count": 1}], "isBlockCoverage": true}, {"functionName": "fail", "ranges": [{"startOffset": 17240, "endOffset": 17347, "count": 26}], "isBlockCoverage": true}, {"functionName": "skip", "ranges": [{"startOffset": 17495, "endOffset": 17571, "count": 18}, {"startOffset": 17526, "endOffset": 17569, "count": 8}], "isBlockCoverage": true}]}, {"scriptId": "96", "url": "file:///home/<USER>/ylabs/lib0/broadcastchannel.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 666, "count": 1}], "isBlockCoverage": true}, {"functionName": "testBroadcastChannel", "ranges": [{"startOffset": 149, "endOffset": 665, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 277, "endOffset": 336, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "97", "url": "file:///home/<USER>/ylabs/lib0/array.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3889, "count": 1}], "isBlockCoverage": true}, {"functionName": "testIsarrayPerformance", "ranges": [{"startOffset": 178, "endOffset": 994, "count": 1}, {"startOffset": 293, "endOffset": 390, "count": 100000}, {"startOffset": 316, "endOffset": 386, "count": 50000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 452, "endOffset": 640, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 508, "endOffset": 595, "count": 100000}, {"startOffset": 554, "endOffset": 589, "count": 50000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 695, "endOffset": 876, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 751, "endOffset": 831, "count": 100000}, {"startOffset": 790, "endOffset": 825, "count": 50000}], "isBlockCoverage": true}, {"functionName": "testAppend", "ranges": [{"startOffset": 1057, "endOffset": 1173, "count": 1}], "isBlockCoverage": true}, {"functionName": "testBasic", "ranges": [{"startOffset": 1235, "endOffset": 1347, "count": 1}], "isBlockCoverage": true}, {"functionName": "testflatten", "ranges": [{"startOffset": 1411, "endOffset": 1504, "count": 1}], "isBlockCoverage": true}, {"functionName": "testFolding", "ranges": [{"startOffset": 1568, "endOffset": 2017, "count": 1}], "isBlockCoverage": true}, {"functionName": "testcase", "ranges": [{"startOffset": 1631, "endOffset": 1971, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1778, "endOffset": 1784, "count": 101}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1790, "endOffset": 1939, "count": 101}], "isBlockCoverage": true}, {"functionName": "testEvery", "ranges": [{"startOffset": 2079, "endOffset": 2283, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2140, "endOffset": 2151, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2183, "endOffset": 2193, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2223, "endOffset": 2235, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2266, "endOffset": 2279, "count": 3}], "isBlockCoverage": true}, {"functionName": "testIsArray", "ranges": [{"startOffset": 2347, "endOffset": 2561, "count": 1}], "isBlockCoverage": true}, {"functionName": "testUnique", "ranges": [{"startOffset": 2624, "endOffset": 2842, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2785, "endOffset": 2794, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2832, "endOffset": 2838, "count": 0}], "isBlockCoverage": false}, {"functionName": "testBubblesortItemEdgeCases", "ranges": [{"startOffset": 2921, "endOffset": 3102, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2982, "endOffset": 2997, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3033, "endOffset": 3048, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3084, "endOffset": 3099, "count": 1}], "isBlockCoverage": true}, {"functionName": "testRepeatBubblesortItem", "ranges": [{"startOffset": 3178, "endOffset": 3534, "count": 8105}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3252, "endOffset": 3267, "count": 185805}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3434, "endOffset": 3449, "count": 33912}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3484, "endOffset": 3499, "count": 7518}], "isBlockCoverage": true}, {"functionName": "testRepeatBubblesort", "ranges": [{"startOffset": 3606, "endOffset": 3888, "count": 3799}, {"startOffset": 3771, "endOffset": 3854, "count": 37990}, {"startOffset": 3837, "endOffset": 3850, "count": 32816}, {"startOffset": 3854, "endOffset": 3887, "count": 37990}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3710, "endOffset": 3725, "count": 87189}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3813, "endOffset": 3828, "count": 195780}], "isBlockCoverage": true}]}, {"scriptId": "98", "url": "file:///home/<USER>/ylabs/lib0/crypto.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14615, "count": 1}], "isBlockCoverage": true}, {"functionName": "testJwt", "ranges": [{"startOffset": 365, "endOffset": 1736, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1668, "endOffset": 1733, "count": 1}, {"startOffset": 1729, "endOffset": 1732, "count": 0}], "isBlockCoverage": true}, {"functionName": "testEncryption", "ranges": [{"startOffset": 1802, "endOffset": 3083, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1965, "endOffset": 2146, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2176, "endOffset": 2384, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2429, "endOffset": 2722, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2466, "endOffset": 2717, "count": 1}, {"startOffset": 2684, "endOffset": 2716, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2768, "endOffset": 3080, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2805, "endOffset": 3075, "count": 1}, {"startOffset": 2981, "endOffset": 3074, "count": 0}], "isBlockCoverage": true}, {"functionName": "testReapeatEncryption", "ranges": [{"startOffset": 3156, "endOffset": 3880, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3476, "endOffset": 3537, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3580, "endOffset": 3642, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3781, "endOffset": 3848, "count": 1}], "isBlockCoverage": true}, {"functionName": "testImportExport", "ranges": [{"startOffset": 3948, "endOffset": 6327, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4078, "endOffset": 4351, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4392, "endOffset": 4665, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4704, "endOffset": 5290, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5329, "endOffset": 5710, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5752, "endOffset": 6324, "count": 1}], "isBlockCoverage": true}, {"functionName": "testEncryptionPerformance", "ranges": [{"startOffset": 6404, "endOffset": 7430, "count": 1}, {"startOffset": 6776, "endOffset": 6827, "count": 1000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6622, "endOffset": 6683, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6976, "endOffset": 7101, "count": 1}, {"startOffset": 7032, "endOffset": 7097, "count": 1000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7251, "endOffset": 7394, "count": 1}, {"startOffset": 7316, "endOffset": 7390, "count": 1000}], "isBlockCoverage": true}, {"functionName": "testConsistentKeyGeneration", "ranges": [{"startOffset": 7510, "endOffset": 13906, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7571, "endOffset": 8015, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8065, "endOffset": 9247, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9295, "endOffset": 13903, "count": 1}], "isBlockCoverage": true}, {"functionName": "testSigning", "ranges": [{"startOffset": 13969, "endOffset": 14614, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14055, "endOffset": 14611, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "99", "url": "file:///home/<USER>/ylabs/lib0/hash/rabin.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6821, "count": 1}], "isBlockCoverage": true}, {"functionName": "testPolynomialBasics", "ranges": [{"startOffset": 430, "endOffset": 629, "count": 1}], "isBlockCoverage": true}, {"functionName": "testIrreducibleInput", "ranges": [{"startOffset": 702, "endOffset": 1186, "count": 1}], "isBlockCoverage": true}, {"functionName": "testIrreducibleSpread", "ranges": [{"startOffset": 1260, "endOffset": 1576, "count": 1}], "isBlockCoverage": true}, {"functionName": "getSpreadAverage", "ranges": [{"startOffset": 1662, "endOffset": 2037, "count": 1}, {"startOffset": 1762, "endOffset": 1985, "count": 29084}, {"startOffset": 1887, "endOffset": 1981, "count": 1000}], "isBlockCoverage": true}, {"functionName": "testGenerateIrreducibles", "ranges": [{"startOffset": 2114, "endOffset": 2990, "count": 1}], "isBlockCoverage": true}, {"functionName": "testIrreducibleGen", "ranges": [{"startOffset": 2193, "endOffset": 2492, "count": 5}], "isBlockCoverage": true}, {"functionName": "_testFingerprintCompatiblityK", "ranges": [{"startOffset": 3085, "endOffset": 5634, "count": 5}, {"startOffset": 3548, "endOffset": 3607, "count": 1500}, {"startOffset": 3890, "endOffset": 3911, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3408, "endOffset": 3516, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3713, "endOffset": 3808, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3757, "endOffset": 3803, "count": 1500}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4023, "endOffset": 4281, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4067, "endOffset": 4276, "count": 1500}, {"startOffset": 4195, "endOffset": 4232, "count": 195000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4447, "endOffset": 4705, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4491, "endOffset": 4700, "count": 1500}, {"startOffset": 4619, "endOffset": 4656, "count": 195000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5034, "endOffset": 5290, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5078, "endOffset": 5285, "count": 1500}, {"startOffset": 5204, "endOffset": 5241, "count": 195000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5465, "endOffset": 5589, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5509, "endOffset": 5584, "count": 1500}], "isBlockCoverage": true}, {"functionName": "testFingerprintCompatiblity", "ranges": [{"startOffset": 5713, "endOffset": 5922, "count": 1}], "isBlockCoverage": true}, {"functionName": "testConflicts", "ranges": [{"startOffset": 5987, "endOffset": 6820, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6152, "endOffset": 6274, "count": 1}, {"startOffset": 6192, "endOffset": 6270, "count": 100}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6396, "endOffset": 6575, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6421, "endOffset": 6570, "count": 100}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6524, "endOffset": 6539, "count": 85}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6625, "endOffset": 6644, "count": 85}], "isBlockCoverage": true}]}, {"scriptId": "100", "url": "file:///home/<USER>/ylabs/lib0/hash/sha256.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5855, "count": 1}], "isBlockCoverage": true}, {"functionName": "testSelfReferencingHash", "ranges": [{"startOffset": 460, "endOffset": 661, "count": 1}], "isBlockCoverage": true}, {"functionName": "testSha256Basics", "ranges": [{"startOffset": 730, "endOffset": 2682, "count": 1}], "isBlockCoverage": true}, {"functionName": "test", "ranges": [{"startOffset": 904, "endOffset": 1306, "count": 8}, {"startOffset": 966, "endOffset": 994, "count": 1}, {"startOffset": 995, "endOffset": 1001, "count": 7}], "isBlockCoverage": true}, {"functionName": "testLarge<PERSON><PERSON>ue", "ranges": [{"startOffset": 2827, "endOffset": 3848, "count": 1}, {"startOffset": 3081, "endOffset": 3847, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3194, "endOffset": 3506, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3565, "endOffset": 3610, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3681, "endOffset": 3780, "count": 0}], "isBlockCoverage": false}, {"functionName": "testRepeatSha256Hashing", "ranges": [{"startOffset": 3923, "endOffset": 4257, "count": 5}, {"startOffset": 3970, "endOffset": 4000, "count": 4}, {"startOffset": 4001, "endOffset": 4035, "count": 1}], "isBlockCoverage": true}, {"functionName": "testBenchmarkSha256", "ranges": [{"startOffset": 4329, "endOffset": 5854, "count": 1}, {"startOffset": 5725, "endOffset": 5853, "count": 0}], "isBlockCoverage": true}, {"functionName": "bench", "ranges": [{"startOffset": 4419, "endOffset": 5614, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4484, "endOffset": 5613, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4564, "endOffset": 4594, "count": 20000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4634, "endOffset": 4775, "count": 2}, {"startOffset": 4676, "endOffset": 4769, "count": 20000}, {"startOffset": 4744, "endOffset": 4761, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4890, "endOffset": 5042, "count": 2}, {"startOffset": 4934, "endOffset": 5034, "count": 20000}, {"startOffset": 5007, "endOffset": 5024, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5105, "endOffset": 5279, "count": 2}, {"startOffset": 5153, "endOffset": 5273, "count": 20000}, {"startOffset": 5248, "endOffset": 5265, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5334, "endOffset": 5608, "count": 2}, {"startOffset": 5453, "endOffset": 5524, "count": 20000}, {"startOffset": 5585, "endOffset": 5602, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "101", "url": "file:///home/<USER>/ylabs/lib0/logging.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1216, "count": 1}], "isBlockCoverage": true}, {"functionName": "testLogging", "ranges": [{"startOffset": 64, "endOffset": 829, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 712, "endOffset": 781, "count": 1}], "isBlockCoverage": true}, {"functionName": "testModuleLogger", "ranges": [{"startOffset": 863, "endOffset": 1215, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1064, "endOffset": 1114, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1163, "endOffset": 1212, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "102", "url": "file:///home/<USER>/ylabs/lib0/string.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2748, "count": 1}], "isBlockCoverage": true}, {"functionName": "testUtilities", "ranges": [{"startOffset": 171, "endOffset": 308, "count": 1}], "isBlockCoverage": true}, {"functionName": "testLowercaseTransformation", "ranges": [{"startOffset": 388, "endOffset": 693, "count": 1}], "isBlockCoverage": true}, {"functionName": "testRepeatStringUtf8Encoding", "ranges": [{"startOffset": 773, "endOffset": 1195, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 946, "endOffset": 1006, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1052, "endOffset": 1118, "count": 2}], "isBlockCoverage": true}, {"functionName": "testRepeatStringUtf8Decoding", "ranges": [{"startOffset": 1275, "endOffset": 1716, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1467, "endOffset": 1527, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1573, "endOffset": 1639, "count": 1}], "isBlockCoverage": true}, {"functionName": "testBomEncodingDecoding", "ranges": [{"startOffset": 1792, "endOffset": 2198, "count": 1}], "isBlockCoverage": true}, {"functionName": "testSplice", "ranges": [{"startOffset": 2261, "endOffset": 2409, "count": 1}], "isBlockCoverage": true}, {"functionName": "testHtmlEscape", "ranges": [{"startOffset": 2476, "endOffset": 2747, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2644, "endOffset": 2744, "count": 3}], "isBlockCoverage": true}]}, {"scriptId": "103", "url": "file:///home/<USER>/ylabs/lib0/diff.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5061, "count": 1}], "isBlockCoverage": true}, {"functionName": "runDiffTest", "ranges": [{"startOffset": 344, "endOffset": 923, "count": 13}], "isBlockCoverage": true}, {"functionName": "testDiffing", "ranges": [{"startOffset": 986, "endOffset": 2202, "count": 1}], "isBlockCoverage": true}, {"functionName": "testRepeatDiffing", "ranges": [{"startOffset": 2271, "endOffset": 2495, "count": 10054}], "isBlockCoverage": true}, {"functionName": "testSimpleDiffWithCursor", "ranges": [{"startOffset": 2571, "endOffset": 4638, "count": 1}], "isBlockCoverage": true}, {"functionName": "testArrayDiffing", "ranges": [{"startOffset": 4706, "endOffset": 5060, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "104", "url": "file:///home/<USER>/ylabs/lib0/encoding.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 29920, "count": 1}, {"startOffset": 1276, "endOffset": 1328, "count": 0}], "isBlockCoverage": true}, {"functionName": "genAnyLookupTable.val", "ranges": [{"startOffset": 454, "endOffset": 534, "count": 677}], "isBlockCoverage": true}, {"functionName": "genAnyLookupTable.val", "ranges": [{"startOffset": 550, "endOffset": 567, "count": 2716}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 583, "endOffset": 595, "count": 2761}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 611, "endOffset": 683, "count": 2655}], "isBlockCoverage": true}, {"functionName": "genAnyLookupTable.val", "ranges": [{"startOffset": 699, "endOffset": 722, "count": 2771}], "isBlockCoverage": true}, {"functionName": "genAnyLookupTable.val", "ranges": [{"startOffset": 746, "endOffset": 758, "count": 2752}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 774, "endOffset": 787, "count": 2782}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 803, "endOffset": 831, "count": 2755}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 847, "endOffset": 932, "count": 2741}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 948, "endOffset": 1089, "count": 2764}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1042, "endOffset": 1088, "count": 19414}], "isBlockCoverage": true}, {"functionName": "genAnyLookupTable", "ranges": [{"startOffset": 1105, "endOffset": 1157, "count": 2794}], "isBlockCoverage": true}, {"functionName": "genAny", "ranges": [{"startOffset": 1428, "endOffset": 1593, "count": 28168}, {"startOffset": 1508, "endOffset": 1541, "count": 20685}, {"startOffset": 1542, "endOffset": 1561, "count": 7483}], "isBlockCoverage": true}, {"functionName": "testGolangBinaryEncodingCompatibility", "ranges": [{"startOffset": 1854, "endOffset": 3062, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2686, "endOffset": 3059, "count": 20}, {"startOffset": 3006, "endOffset": 3055, "count": 58}], "isBlockCoverage": true}, {"functionName": "test", "ranges": [{"startOffset": 3258, "endOffset": 3892, "count": 73985}, {"startOffset": 3695, "endOffset": 3809, "count": 15}], "isBlockCoverage": true}, {"functionName": "testVarString", "ranges": [{"startOffset": 3945, "endOffset": 4221, "count": 9407}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4009, "endOffset": 4059, "count": 9407}], "isBlockCoverage": true}, {"functionName": "testVerifyLen", "ranges": [{"startOffset": 4252, "endOffset": 4541, "count": 1}], "isBlockCoverage": true}, {"functionName": "testStringEncodingPerformanceNativeVsPolyfill", "ranges": [{"startOffset": 4604, "endOffset": 6397, "count": 1}, {"startOffset": 4694, "endOffset": 4715, "count": 10000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4786, "endOffset": 5006, "count": 1}, {"startOffset": 4886, "endOffset": 5002, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5077, "endOffset": 5293, "count": 1}, {"startOffset": 5177, "endOffset": 5289, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5511, "endOffset": 5784, "count": 1}, {"startOffset": 5611, "endOffset": 5780, "count": 100000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5860, "endOffset": 6127, "count": 1}, {"startOffset": 5960, "endOffset": 6123, "count": 100000}], "isBlockCoverage": true}, {"functionName": "testDecodingPerformanceNativeVsPolyfill", "ranges": [{"startOffset": 6454, "endOffset": 8369, "count": 1}, {"startOffset": 6577, "endOffset": 6598, "count": 10000}, {"startOffset": 6734, "endOffset": 6868, "count": 10000}, {"startOffset": 6913, "endOffset": 6968, "count": 1000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7144, "endOffset": 7297, "count": 1}, {"startOffset": 7241, "endOffset": 7293, "count": 30000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7378, "endOffset": 7524, "count": 1}, {"startOffset": 7475, "endOffset": 7520, "count": 30000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7601, "endOffset": 7759, "count": 1}, {"startOffset": 7703, "endOffset": 7755, "count": 1000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7840, "endOffset": 8000, "count": 1}, {"startOffset": 7942, "endOffset": 7996, "count": 1000}], "isBlockCoverage": true}, {"functionName": "testStringDecodingPerformance", "ranges": [{"startOffset": 8416, "endOffset": 10517, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8710, "endOffset": 9191, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8821, "endOffset": 8924, "count": 1}, {"startOffset": 8863, "endOffset": 8918, "count": 2000000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9058, "endOffset": 9186, "count": 1}, {"startOffset": 9121, "endOffset": 9180, "count": 2000000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9277, "endOffset": 10300, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9481, "endOffset": 9840, "count": 1}, {"startOffset": 9540, "endOffset": 9743, "count": 2000000}, {"startOffset": 9661, "endOffset": 9735, "count": 100000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10046, "endOffset": 10295, "count": 1}, {"startOffset": 10163, "endOffset": 10289, "count": 2000000}], "isBlockCoverage": true}, {"functionName": "testAnyEncodeUnknowns", "ranges": [{"startOffset": 10591, "endOffset": 11010, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10770, "endOffset": 10778, "count": 0}], "isBlockCoverage": false}, {"functionName": "testAnyEncodeDate", "ranges": [{"startOffset": 11080, "endOffset": 11179, "count": 1}], "isBlockCoverage": true}, {"functionName": "testEncodeMax32bitUint", "ranges": [{"startOffset": 11254, "endOffset": 11349, "count": 1}], "isBlockCoverage": true}, {"functionName": "testVarUintEncoding", "ranges": [{"startOffset": 11421, "endOffset": 11969, "count": 1}], "isBlockCoverage": true}, {"functionName": "testVarIntEncoding", "ranges": [{"startOffset": 12040, "endOffset": 12676, "count": 1}], "isBlockCoverage": true}, {"functionName": "testRepeatVarUintEncoding", "ranges": [{"startOffset": 12753, "endOffset": 12894, "count": 13020}], "isBlockCoverage": true}, {"functionName": "testRepeatVarUintEncoding53bit", "ranges": [{"startOffset": 12976, "endOffset": 13127, "count": 12545}], "isBlockCoverage": true}, {"functionName": "testRepeatVarIntEncoding", "ranges": [{"startOffset": 13203, "endOffset": 13358, "count": 12602}], "isBlockCoverage": true}, {"functionName": "testRepeatVarIntEncoding53bit", "ranges": [{"startOffset": 13439, "endOffset": 13608, "count": 11548}], "isBlockCoverage": true}, {"functionName": "testRepeanntAnyEncoding", "ranges": [{"startOffset": 13683, "endOffset": 13790, "count": 1}], "isBlockCoverage": true}, {"functionName": "testRepeatPeekVarUintEncoding", "ranges": [{"startOffset": 13871, "endOffset": 14011, "count": 13579}], "isBlockCoverage": true}, {"functionName": "testRepeatPeekVarIntEncoding", "ranges": [{"startOffset": 14091, "endOffset": 14260, "count": 10675}], "isBlockCoverage": true}, {"functionName": "testAnyVsJsonEncoding", "ranges": [{"startOffset": 14333, "endOffset": 15025, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14386, "endOffset": 14416, "count": 5000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14455, "endOffset": 14696, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14741, "endOffset": 15022, "count": 1}], "isBlockCoverage": true}, {"functionName": "testStringEncoding", "ranges": [{"startOffset": 15096, "endOffset": 15331, "count": 1}], "isBlockCoverage": true}, {"functionName": "testRepeatStringEncoding", "ranges": [{"startOffset": 15407, "endOffset": 15455, "count": 9399}], "isBlockCoverage": true}, {"functionName": "testSetMethods", "ranges": [{"startOffset": 15522, "endOffset": 16118, "count": 1}], "isBlockCoverage": true}, {"functionName": "strictComparison", "ranges": [{"startOffset": 16251, "endOffset": 16268, "count": 7005}], "isBlockCoverage": true}, {"functionName": "read", "ranges": [{"startOffset": 16700, "endOffset": 16751, "count": 993}], "isBlockCoverage": true}, {"functionName": "gen", "ranges": [{"startOffset": 16791, "endOffset": 16826, "count": 993}], "isBlockCoverage": true}, {"functionName": "gen", "ranges": [{"startOffset": 16952, "endOffset": 17008, "count": 990}], "isBlockCoverage": true}, {"functionName": "gen", "ranges": [{"startOffset": 17110, "endOffset": 17150, "count": 1000}], "isBlockCoverage": true}, {"functionName": "gen", "ranges": [{"startOffset": 17262, "endOffset": 17303, "count": 1002}], "isBlockCoverage": true}, {"functionName": "gen", "ranges": [{"startOffset": 17415, "endOffset": 17456, "count": 1000}], "isBlockCoverage": true}, {"functionName": "gen", "ranges": [{"startOffset": 17595, "endOffset": 17636, "count": 1012}], "isBlockCoverage": true}, {"functionName": "gen", "ranges": [{"startOffset": 17757, "endOffset": 17814, "count": 965}], "isBlockCoverage": true}, {"functionName": "gen", "ranges": [{"startOffset": 17929, "endOffset": 17980, "count": 995}], "isBlockCoverage": true}, {"functionName": "gen", "ranges": [{"startOffset": 18092, "endOffset": 18164, "count": 1031}], "isBlockCoverage": true}, {"functionName": "testRepeatRandomWrites", "ranges": [{"startOffset": 18371, "endOffset": 19277, "count": 1}, {"startOffset": 18591, "endOffset": 18810, "count": 10000}, {"startOffset": 19111, "endOffset": 19213, "count": 10000}], "isBlockCoverage": true}, {"functionName": "testWriteUint8ArrayOverflow", "ranges": [{"startOffset": 19357, "endOffset": 19861, "count": 1}, {"startOffset": 19554, "endOffset": 19574, "count": 400}, {"startOffset": 19780, "endOffset": 19820, "count": 399}], "isBlockCoverage": true}, {"functionName": "testSetOnOverflow", "ranges": [{"startOffset": 19931, "endOffset": 20851, "count": 1}, {"startOffset": 20402, "endOffset": 20436, "count": 98}], "isBlockCoverage": true}, {"functionName": "testCloneDecoder", "ranges": [{"startOffset": 20920, "endOffset": 21451, "count": 1}], "isBlockCoverage": true}, {"functionName": "testWriteBinaryEncoder", "ranges": [{"startOffset": 21526, "endOffset": 21941, "count": 1}], "isBlockCoverage": true}, {"functionName": "testOverflowStringDecoding", "ranges": [{"startOffset": 22019, "endOffset": 22387, "count": 1}], "isBlockCoverage": true}, {"functionName": "testRleEncoder", "ranges": [{"startOffset": 22454, "endOffset": 22964, "count": 1}, {"startOffset": 22574, "endOffset": 22691, "count": 100}, {"startOffset": 22629, "endOffset": 22687, "count": 4950}, {"startOffset": 22818, "endOffset": 22962, "count": 100}, {"startOffset": 22887, "endOffset": 22958, "count": 4950}], "isBlockCoverage": true}, {"functionName": "testRleIntDiffEncoder", "ranges": [{"startOffset": 23038, "endOffset": 23525, "count": 1}, {"startOffset": 23146, "endOffset": 23263, "count": 200}, {"startOffset": 23201, "endOffset": 23259, "count": 4950}, {"startOffset": 23379, "endOffset": 23523, "count": 200}, {"startOffset": 23448, "endOffset": 23519, "count": 4950}], "isBlockCoverage": true}, {"functionName": "testUintOptRleEncoder", "ranges": [{"startOffset": 23599, "endOffset": 24072, "count": 1}, {"startOffset": 23705, "endOffset": 23822, "count": 100}, {"startOffset": 23760, "endOffset": 23818, "count": 4950}, {"startOffset": 23926, "endOffset": 24070, "count": 100}, {"startOffset": 23995, "endOffset": 24066, "count": 4950}], "isBlockCoverage": true}, {"functionName": "testIncUintOptRleEncoder", "ranges": [{"startOffset": 24149, "endOffset": 24628, "count": 1}, {"startOffset": 24258, "endOffset": 24375, "count": 100}, {"startOffset": 24313, "endOffset": 24371, "count": 4950}, {"startOffset": 24482, "endOffset": 24626, "count": 100}, {"startOffset": 24551, "endOffset": 24622, "count": 4950}], "isBlockCoverage": true}, {"functionName": "testIntDiffRleEncoder", "ranges": [{"startOffset": 24702, "endOffset": 25183, "count": 1}, {"startOffset": 24812, "endOffset": 24929, "count": 200}, {"startOffset": 24867, "endOffset": 24925, "count": 4950}, {"startOffset": 25037, "endOffset": 25181, "count": 200}, {"startOffset": 25106, "endOffset": 25177, "count": 4950}], "isBlockCoverage": true}, {"functionName": "testIntEncoders", "ranges": [{"startOffset": 25250, "endOffset": 26531, "count": 1}, {"startOffset": 25395, "endOffset": 25602, "count": 10000}, {"startOffset": 25421, "endOffset": 25543, "count": 4975}, {"startOffset": 25543, "endOffset": 25598, "count": 5025}], "isBlockCoverage": true}, {"functionName": "read", "ranges": [{"startOffset": 25759, "endOffset": 25827, "count": 1}], "isBlockCoverage": true}, {"functionName": "read", "ranges": [{"startOffset": 25884, "endOffset": 25957, "count": 1}], "isBlockCoverage": true}, {"functionName": "read", "ranges": [{"startOffset": 26015, "endOffset": 26089, "count": 1}], "isBlockCoverage": true}, {"functionName": "read", "ranges": [{"startOffset": 26149, "endOffset": 26225, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26254, "endOffset": 26528, "count": 4}, {"startOffset": 26455, "endOffset": 26494, "count": 40000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26296, "endOffset": 26317, "count": 40000}], "isBlockCoverage": true}, {"functionName": "testIntDiffEncoder", "ranges": [{"startOffset": 26602, "endOffset": 26888, "count": 1}, {"startOffset": 26707, "endOffset": 26733, "count": 200}, {"startOffset": 26846, "endOffset": 26886, "count": 200}], "isBlockCoverage": true}, {"functionName": "testStringDecoder", "ranges": [{"startOffset": 26957, "endOffset": 27577, "count": 1}, {"startOffset": 27053, "endOffset": 27295, "count": 1000}, {"startOffset": 27116, "endOffset": 27215, "count": 10}, {"startOffset": 27239, "endOffset": 27291, "count": 10}, {"startOffset": 27384, "endOffset": 27417, "count": 1030}, {"startOffset": 27528, "endOffset": 27575, "count": 1030}], "isBlockCoverage": true}, {"functionName": "testLargeNumberEncoding", "ranges": [{"startOffset": 27652, "endOffset": 28624, "count": 1}], "isBlockCoverage": true}, {"functionName": "testInvalidVarIntEncoding", "ranges": [{"startOffset": 28702, "endOffset": 28948, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28826, "endOffset": 28870, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28900, "endOffset": 28945, "count": 1}], "isBlockCoverage": true}, {"functionName": "testTerminatedEncodering", "ranges": [{"startOffset": 29025, "endOffset": 29919, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "105", "url": "file:///home/<USER>/ylabs/lib0/testing.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4941, "count": 1}], "isBlockCoverage": true}, {"functionName": "nottestingNotTested", "ranges": [{"startOffset": 271, "endOffset": 376, "count": 0}], "isBlockCoverage": false}, {"functionName": "testAssertTyping", "ranges": [{"startOffset": 410, "endOffset": 677, "count": 1}, {"startOffset": 466, "endOffset": 472, "count": 0}], "isBlockCoverage": true}, {"functionName": "testComparing", "ranges": [{"startOffset": 743, "endOffset": 3951, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1296, "endOffset": 1365, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1377, "endOffset": 1446, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1458, "endOffset": 1523, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1535, "endOffset": 1624, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1636, "endOffset": 1728, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1740, "endOffset": 1832, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1844, "endOffset": 1976, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1988, "endOffset": 2134, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2146, "endOffset": 2225, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2289, "endOffset": 2373, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2385, "endOffset": 2468, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2540, "endOffset": 2613, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2625, "endOffset": 2720, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2732, "endOffset": 2824, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2836, "endOffset": 2935, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2973, "endOffset": 3144, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3156, "endOffset": 3327, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3339, "endOffset": 3493, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3531, "endOffset": 3641, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3653, "endOffset": 3760, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3823, "endOffset": 3948, "count": 1}], "isBlockCoverage": true}, {"functionName": "testFailing", "ranges": [{"startOffset": 3980, "endOffset": 4346, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4004, "endOffset": 4051, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4079, "endOffset": 4135, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4147, "endOffset": 4180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4167, "endOffset": 4175, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4203, "endOffset": 4260, "count": 1}, {"startOffset": 4256, "endOffset": 4259, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4240, "endOffset": 4255, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4288, "endOffset": 4340, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4316, "endOffset": 4339, "count": 1}], "isBlockCoverage": true}, {"functionName": "testSkipping", "ranges": [{"startOffset": 4376, "endOffset": 4484, "count": 1}], "isBlockCoverage": true}, {"functionName": "testAsync", "ranges": [{"startOffset": 4511, "endOffset": 4751, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4560, "endOffset": 4600, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4581, "endOffset": 4599, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4643, "endOffset": 4664, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4692, "endOffset": 4748, "count": 1}], "isBlockCoverage": true}, {"functionName": "testRepeatRepetition", "ranges": [{"startOffset": 4789, "endOffset": 4940, "count": 23987}, {"startOffset": 4861, "endOffset": 4882, "count": 2398700}], "isBlockCoverage": true}]}, {"scriptId": "106", "url": "file:///home/<USER>/ylabs/lib0/indexeddb.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3326, "count": 1}], "isBlockCoverage": true}, {"functionName": "initTestDB", "ranges": [{"startOffset": 193, "endOffset": 256, "count": 0}], "isBlockCoverage": false}, {"functionName": "createTransaction", "ranges": [{"startOffset": 370, "endOffset": 413, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStore", "ranges": [{"startOffset": 518, "endOffset": 546, "count": 0}], "isBlockCoverage": false}, {"functionName": "testRetrieveElements", "ranges": [{"startOffset": 605, "endOffset": 2915, "count": 1}, {"startOffset": 719, "endOffset": 2914, "count": 0}], "isBlockCoverage": true}, {"functionName": "iterateTests", "ranges": [{"startOffset": 1558, "endOffset": 2085, "count": 0}], "isBlockCoverage": false}, {"functionName": "testBlocked", "ranges": [{"startOffset": 2965, "endOffset": 3325, "count": 1}, {"startOffset": 3069, "endOffset": 3324, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "107", "url": "file:///home/<USER>/ylabs/lib0/indexeddbV2.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3802, "count": 1}], "isBlockCoverage": true}, {"functionName": "initTestDB", "ranges": [{"startOffset": 233, "endOffset": 296, "count": 0}], "isBlockCoverage": false}, {"functionName": "createTransaction", "ranges": [{"startOffset": 425, "endOffset": 525, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStore", "ranges": [{"startOffset": 668, "endOffset": 751, "count": 0}], "isBlockCoverage": false}, {"functionName": "testRetrieveElements", "ranges": [{"startOffset": 810, "endOffset": 3284, "count": 1}, {"startOffset": 934, "endOffset": 3283, "count": 0}], "isBlockCoverage": true}, {"functionName": "iterateTests", "ranges": [{"startOffset": 1817, "endOffset": 2364, "count": 0}], "isBlockCoverage": false}, {"functionName": "testBlocked", "ranges": [{"startOffset": 3334, "endOffset": 3801, "count": 1}, {"startOffset": 3799, "endOffset": 3800, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3443, "endOffset": 3714, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3720, "endOffset": 3788, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "108", "url": "file:///home/<USER>/ylabs/lib0/logging.node.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3901, "count": 1}, {"startOffset": 1849, "endOffset": 1873, "count": 0}], "isBlockCoverage": true}, {"functionName": "computeNodeLoggingArgs", "ranges": [{"startOffset": 774, "endOffset": 1757, "count": 0}], "isBlockCoverage": false}, {"functionName": "print", "ranges": [{"startOffset": 2024, "endOffset": 2083, "count": 568}], "isBlockCoverage": true}, {"functionName": "warn", "ranges": [{"startOffset": 2187, "endOffset": 2247, "count": 0}], "isBlockCoverage": false}, {"functionName": "printError", "ranges": [{"startOffset": 2348, "endOffset": 2381, "count": 0}], "isBlockCoverage": false}, {"functionName": "printImg", "ranges": [{"startOffset": 2553, "endOffset": 2675, "count": 1}], "isBlockCoverage": true}, {"functionName": "printImgBase64", "ranges": [{"startOffset": 2811, "endOffset": 2884, "count": 1}], "isBlockCoverage": true}, {"functionName": "group", "ranges": [{"startOffset": 2990, "endOffset": 3051, "count": 46}], "isBlockCoverage": true}, {"functionName": "groupCollapsed", "ranges": [{"startOffset": 3166, "endOffset": 3236, "count": 151}], "isBlockCoverage": true}, {"functionName": "groupEnd", "ranges": [{"startOffset": 3285, "endOffset": 3315, "count": 197}], "isBlockCoverage": true}, {"functionName": "printDom", "ranges": [{"startOffset": 3412, "endOffset": 3431, "count": 0}], "isBlockCoverage": false}, {"functionName": "printCanvas", "ranges": [{"startOffset": 3554, "endOffset": 3612, "count": 0}], "isBlockCoverage": false}, {"functionName": "createVConsole", "ranges": [{"startOffset": 3698, "endOffset": 3710, "count": 0}], "isBlockCoverage": false}, {"functionName": "createModuleLogger", "ranges": [{"startOffset": 3840, "endOffset": 3900, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "109", "url": "file:///home/<USER>/ylabs/lib0/prng.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7184, "count": 1}], "isBlockCoverage": true}, {"functionName": "runGenTest", "ranges": [{"startOffset": 539, "endOffset": 5873, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 596, "endOffset": 879, "count": 3}, {"startOffset": 662, "endOffset": 789, "count": 15000}, {"startOffset": 715, "endOffset": 765, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 928, "endOffset": 1350, "count": 3}, {"startOffset": 1029, "endOffset": 1129, "count": 15000}, {"startOffset": 1069, "endOffset": 1093, "count": 7542}, {"startOffset": 1093, "endOffset": 1123, "count": 7458}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1400, "endOffset": 1773, "count": 3}, {"startOffset": 1475, "endOffset": 1522, "count": 15000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1828, "endOffset": 2552, "count": 3}, {"startOffset": 1940, "endOffset": 2137, "count": 15000}, {"startOffset": 2032, "endOffset": 2066, "count": 22}, {"startOffset": 2096, "endOffset": 2131, "count": 28}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2617, "endOffset": 2969, "count": 3}, {"startOffset": 2704, "endOffset": 2816, "count": 15000}, {"startOffset": 2780, "endOffset": 2810, "count": 30}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3029, "endOffset": 3761, "count": 3}, {"startOffset": 3141, "endOffset": 3357, "count": 15000}, {"startOffset": 3252, "endOffset": 3286, "count": 22}, {"startOffset": 3316, "endOffset": 3351, "count": 30}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3822, "endOffset": 4473, "count": 3}, {"startOffset": 3938, "endOffset": 4160, "count": 15000}, {"startOffset": 4028, "endOffset": 4062, "count": 41}, {"startOffset": 4119, "endOffset": 4154, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4528, "endOffset": 4880, "count": 3}, {"startOffset": 4615, "endOffset": 4727, "count": 15000}, {"startOffset": 4691, "endOffset": 4721, "count": 23}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4925, "endOffset": 5309, "count": 3}, {"startOffset": 5012, "endOffset": 5125, "count": 15000}, {"startOffset": 5089, "endOffset": 5119, "count": 18}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5363, "endOffset": 5870, "count": 3}, {"startOffset": 5567, "endOffset": 5602, "count": 288}, {"startOffset": 5645, "endOffset": 5713, "count": 15000}], "isBlockCoverage": true}, {"functionName": "testGeneratorXoroshiro128plus", "ranges": [{"startOffset": 5954, "endOffset": 6005, "count": 1}], "isBlockCoverage": true}, {"functionName": "testGeneratorXorshift32", "ranges": [{"startOffset": 6080, "endOffset": 6153, "count": 1}], "isBlockCoverage": true}, {"functionName": "testGeneratorMt19937", "ranges": [{"startOffset": 6225, "endOffset": 6295, "count": 1}], "isBlockCoverage": true}, {"functionName": "printDistribution", "ranges": [{"startOffset": 6405, "endOffset": 6817, "count": 0}], "isBlockCoverage": false}, {"functionName": "testNumberDistributions", "ranges": [{"startOffset": 6913, "endOffset": 7183, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6972, "endOffset": 7030, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7056, "endOffset": 7108, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7131, "endOffset": 7180, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "110", "url": "file:///home/<USER>/ylabs/lib0/statistics.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 653, "count": 1}], "isBlockCoverage": true}, {"functionName": "testMedian", "ranges": [{"startOffset": 175, "endOffset": 652, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "111", "url": "file:///home/<USER>/ylabs/lib0/binary.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 614, "count": 1}], "isBlockCoverage": true}, {"functionName": "testBitx", "ranges": [{"startOffset": 131, "endOffset": 274, "count": 1}, {"startOffset": 171, "endOffset": 272, "count": 32}], "isBlockCoverage": true}, {"functionName": "testBitsx", "ranges": [{"startOffset": 335, "endOffset": 613, "count": 1}, {"startOffset": 405, "endOffset": 570, "count": 31}], "isBlockCoverage": true}]}, {"scriptId": "112", "url": "file:///home/<USER>/ylabs/lib0/random.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2783, "count": 1}], "isBlockCoverage": true}, {"functionName": "testRandom", "ranges": [{"startOffset": 243, "endOffset": 310, "count": 1}], "isBlockCoverage": true}, {"functionName": "testUint32", "ranges": [{"startOffset": 372, "endOffset": 1317, "count": 1}, {"startOffset": 551, "endOffset": 811, "count": 10000}, {"startOffset": 718, "endOffset": 748, "count": 10}, {"startOffset": 776, "endOffset": 807, "count": 9}, {"startOffset": 811, "endOffset": 1316, "count": 10000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 669, "endOffset": 683, "count": 309958}], "isBlockCoverage": true}, {"functionName": "testUint53", "ranges": [{"startOffset": 1379, "endOffset": 2135, "count": 1}, {"startOffset": 1561, "endOffset": 1821, "count": 10000}, {"startOffset": 1728, "endOffset": 1758, "count": 9}, {"startOffset": 1786, "endOffset": 1817, "count": 10}, {"startOffset": 1821, "endOffset": 2134, "count": 10000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1679, "endOffset": 1693, "count": 519879}], "isBlockCoverage": true}, {"functionName": "testUuidv4", "ranges": [{"startOffset": 2197, "endOffset": 2257, "count": 1}], "isBlockCoverage": true}, {"functionName": "testUuidv4Overlaps", "ranges": [{"startOffset": 2327, "endOffset": 2782, "count": 1}, {"startOffset": 2392, "endOffset": 2401, "count": 0}, {"startOffset": 2475, "endOffset": 2742, "count": 10000}, {"startOffset": 2535, "endOffset": 2578, "count": 0}, {"startOffset": 2660, "endOffset": 2738, "count": 20}], "isBlockCoverage": true}]}, {"scriptId": "113", "url": "file:///home/<USER>/ylabs/lib0/queue.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 913, "count": 1}], "isBlockCoverage": true}, {"functionName": "testEnqueueDequeue", "ranges": [{"startOffset": 140, "endOffset": 912, "count": 1}, {"startOffset": 351, "endOffset": 434, "count": 30}, {"startOffset": 465, "endOffset": 548, "count": 30}, {"startOffset": 646, "endOffset": 729, "count": 30}, {"startOffset": 760, "endOffset": 843, "count": 30}], "isBlockCoverage": true}]}, {"scriptId": "114", "url": "file:///home/<USER>/ylabs/lib0/promise.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2531, "count": 1}], "isBlockCoverage": true}, {"functionName": "measureP", "ranges": [{"startOffset": 243, "endOffset": 511, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 313, "endOffset": 508, "count": 5}], "isBlockCoverage": true}, {"functionName": "failsP", "ranges": [{"startOffset": 600, "endOffset": 708, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 620, "endOffset": 707, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 648, "endOffset": 697, "count": 0}], "isBlockCoverage": false}, {"functionName": "testRepeatPromise", "ranges": [{"startOffset": 778, "endOffset": 1724, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 824, "endOffset": 832, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1110, "endOffset": 1118, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1148, "endOffset": 1156, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1303, "endOffset": 1314, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1371, "endOffset": 1382, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1473, "endOffset": 1517, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1607, "endOffset": 1652, "count": 11}], "isBlockCoverage": true}, {"functionName": "testispromise", "ranges": [{"startOffset": 1790, "endOffset": 2224, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1840, "endOffset": 1848, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1896, "endOffset": 1904, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1986, "endOffset": 1994, "count": 1}], "isBlockCoverage": true}, {"functionName": "then", "ranges": [{"startOffset": 2082, "endOffset": 2090, "count": 0}], "isBlockCoverage": false}, {"functionName": "catch", "ranges": [{"startOffset": 2099, "endOffset": 2107, "count": 0}], "isBlockCoverage": false}, {"functionName": "finally", "ranges": [{"startOffset": 2118, "endOffset": 2126, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2141, "endOffset": 2221, "count": 1}], "isBlockCoverage": true}, {"functionName": "then", "ranges": [{"startOffset": 2188, "endOffset": 2196, "count": 0}], "isBlockCoverage": false}, {"functionName": "catch", "ranges": [{"startOffset": 2205, "endOffset": 2213, "count": 0}], "isBlockCoverage": false}, {"functionName": "testTypings", "ranges": [{"startOffset": 2288, "endOffset": 2530, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "115", "url": "file:///home/<USER>/ylabs/lib0/eventloop.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2814, "count": 1}], "isBlockCoverage": true}, {"functionName": "testEventloopOrder", "ranges": [{"startOffset": 188, "endOffset": 553, "count": 1}, {"startOffset": 244, "endOffset": 552, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 285, "endOffset": 329, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 393, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 466, "endOffset": 503, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 527, "endOffset": 545, "count": 1}], "isBlockCoverage": true}, {"functionName": "testTimeout", "ranges": [{"startOffset": 617, "endOffset": 840, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 689, "endOffset": 715, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 760, "endOffset": 811, "count": 1}], "isBlockCoverage": true}, {"functionName": "testInterval", "ranges": [{"startOffset": 905, "endOffset": 1166, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 978, "endOffset": 1004, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1062, "endOffset": 1081, "count": 319}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1108, "endOffset": 1119, "count": 1}], "isBlockCoverage": true}, {"functionName": "testAnimationFrame", "ranges": [{"startOffset": 1237, "endOffset": 1364, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1295, "endOffset": 1313, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1340, "endOffset": 1347, "count": 1}], "isBlockCoverage": true}, {"functionName": "testIdleCallback", "ranges": [{"startOffset": 1433, "endOffset": 1526, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1471, "endOffset": 1523, "count": 1}], "isBlockCoverage": true}, {"functionName": "testDebouncer", "ranges": [{"startOffset": 1592, "endOffset": 1826, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1683, "endOffset": 1708, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1721, "endOffset": 1750, "count": 1}], "isBlockCoverage": true}, {"functionName": "testDebouncerTriggerAfter", "ranges": [{"startOffset": 1904, "endOffset": 2331, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2001, "endOffset": 2024, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2062, "endOffset": 2085, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2123, "endOffset": 2146, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2184, "endOffset": 2207, "count": 1}], "isBlockCoverage": true}, {"functionName": "testDebouncerClear", "ranges": [{"startOffset": 2402, "endOffset": 2813, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2493, "endOffset": 2516, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2553, "endOffset": 2576, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2613, "endOffset": 2636, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2673, "endOffset": 2696, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "116", "url": "file:///home/<USER>/ylabs/lib0/map.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1337, "count": 1}], "isBlockCoverage": true}, {"functionName": "testMap", "ranges": [{"startOffset": 159, "endOffset": 853, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 288, "endOffset": 319, "count": 2}], "isBlockCoverage": true}, {"functionName": "createT", "ranges": [{"startOffset": 389, "endOffset": 411, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 603, "endOffset": 631, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 656, "endOffset": 684, "count": 2}, {"startOffset": 674, "endOffset": 684, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 710, "endOffset": 721, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 747, "endOffset": 775, "count": 2}, {"startOffset": 765, "endOffset": 775, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 800, "endOffset": 849, "count": 3}, {"startOffset": 815, "endOffset": 825, "count": 2}, {"startOffset": 826, "endOffset": 849, "count": 1}], "isBlockCoverage": true}, {"functionName": "testTypeDefinitions", "ranges": [{"startOffset": 925, "endOffset": 1336, "count": 1}], "isBlockCoverage": true}, {"functionName": "A", "ranges": [{"startOffset": 1046, "endOffset": 1085, "count": 1}], "isBlockCoverage": true}, {"functionName": "B", "ranges": [{"startOffset": 1116, "endOffset": 1169, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1303, "endOffset": 1316, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "117", "url": "file:///home/<USER>/ylabs/lib0/time.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1292, "count": 1}], "isBlockCoverage": true}, {"functionName": "testTime", "ranges": [{"startOffset": 161, "endOffset": 311, "count": 1}], "isBlockCoverage": true}, {"functionName": "testHumanDuration", "ranges": [{"startOffset": 380, "endOffset": 1291, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "118", "url": "file:///home/<USER>/ylabs/lib0/pair.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 636, "count": 1}], "isBlockCoverage": true}, {"functionName": "testPair", "ranges": [{"startOffset": 161, "endOffset": 635, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 355, "endOffset": 423, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 521, "endOffset": 533, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 592, "endOffset": 614, "count": 3}], "isBlockCoverage": true}]}, {"scriptId": "119", "url": "file:///home/<USER>/ylabs/lib0/object.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2223, "count": 1}], "isBlockCoverage": true}, {"functionName": "testObject", "ranges": [{"startOffset": 168, "endOffset": 1800, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 890, "endOffset": 926, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 976, "endOffset": 1012, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1082, "endOffset": 1112, "count": 2}, {"startOffset": 1100, "endOffset": 1112, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1155, "endOffset": 1173, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1215, "endOffset": 1225, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1268, "endOffset": 1285, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1387, "endOffset": 1417, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1513, "endOffset": 1525, "count": 2}], "isBlockCoverage": true}, {"functionName": "testFreeze", "ranges": [{"startOffset": 1863, "endOffset": 2222, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2000, "endOffset": 2030, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2042, "endOffset": 2070, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2082, "endOffset": 2108, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2120, "endOffset": 2146, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2158, "endOffset": 2219, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "120", "url": "file:///home/<USER>/ylabs/lib0/observable.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1230, "count": 1}], "isBlockCoverage": true}, {"functionName": "testTypedObservable", "ranges": [{"startOffset": 152, "endOffset": 1229, "count": 1}], "isBlockCoverage": true}, {"functionName": "listener", "ranges": [{"startOffset": 422, "endOffset": 521, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 560, "endOffset": 604, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 944, "endOffset": 1002, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "121", "url": "file:///home/<USER>/ylabs/lib0/pledge.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2225, "count": 1}], "isBlockCoverage": true}, {"functionName": "testPledgeCoroutine", "ranges": [{"startOffset": 183, "endOffset": 479, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 248, "endOffset": 417, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 298, "endOffset": 306, "count": 1}], "isBlockCoverage": true}, {"functionName": "testPledgeVsPromisePerformanceTimeout", "ranges": [{"startOffset": 569, "endOffset": 1032, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 703, "endOffset": 804, "count": 1}, {"startOffset": 758, "endOffset": 800, "count": 100}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 878, "endOffset": 1026, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 905, "endOffset": 1015, "count": 1}, {"startOffset": 964, "endOffset": 1009, "count": 100}], "isBlockCoverage": true}, {"functionName": "testPledgeVsPromisePerformanceResolved", "ranges": [{"startOffset": 1184, "endOffset": 2224, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1291, "endOffset": 1357, "count": 1}, {"startOffset": 1340, "endOffset": 1353, "count": 100}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1432, "endOffset": 1529, "count": 1}, {"startOffset": 1487, "endOffset": 1525, "count": 100}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1614, "endOffset": 1774, "count": 1}, {"startOffset": 1669, "endOffset": 1770, "count": 100}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1848, "endOffset": 1976, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1875, "endOffset": 1965, "count": 1}, {"startOffset": 1934, "endOffset": 1959, "count": 100}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2055, "endOffset": 2221, "count": 1}, {"startOffset": 2173, "endOffset": 2220, "count": 100}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2203, "endOffset": 2210, "count": 100}], "isBlockCoverage": true}]}, {"scriptId": "122", "url": "file:///home/<USER>/ylabs/lib0/math.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1043, "count": 1}], "isBlockCoverage": true}, {"functionName": "testMath", "ranges": [{"startOffset": 163, "endOffset": 1042, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "123", "url": "file:///home/<USER>/ylabs/lib0/number.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1973, "count": 1}], "isBlockCoverage": true}, {"functionName": "testNumber", "ranges": [{"startOffset": 206, "endOffset": 663, "count": 1}], "isBlockCoverage": true}, {"functionName": "testShiftVsDivision", "ranges": [{"startOffset": 809, "endOffset": 1972, "count": 1}, {"startOffset": 912, "endOffset": 951, "count": 10000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 983, "endOffset": 1211, "count": 1}, {"startOffset": 1036, "endOffset": 1207, "count": 10000}, {"startOffset": 1083, "endOffset": 1201, "count": 49380}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1239, "endOffset": 1363, "count": 1}, {"startOffset": 1306, "endOffset": 1340, "count": 10000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1394, "endOffset": 1493, "count": 1}, {"startOffset": 1447, "endOffset": 1489, "count": 10000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1666, "endOffset": 1787, "count": 1}, {"startOffset": 1721, "endOffset": 1781, "count": 10000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1817, "endOffset": 1926, "count": 1}, {"startOffset": 1872, "endOffset": 1920, "count": 10000}], "isBlockCoverage": true}]}, {"scriptId": "124", "url": "file:///home/<USER>/ylabs/lib0/buffer.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1418, "count": 1}], "isBlockCoverage": true}, {"functionName": "testEncodingHelper", "ranges": [{"startOffset": 264, "endOffset": 741, "count": 19761}, {"startOffset": 668, "endOffset": 710, "count": 464909}], "isBlockCoverage": true}, {"functionName": "testRepeatBase64urlEncoding", "ranges": [{"startOffset": 820, "endOffset": 910, "count": 6841}], "isBlockCoverage": true}, {"functionName": "testRepeatBase64Encoding", "ranges": [{"startOffset": 986, "endOffset": 1056, "count": 6489}], "isBlockCoverage": true}, {"functionName": "testRepeatHexEncoding", "ranges": [{"startOffset": 1129, "endOffset": 1205, "count": 6431}], "isBlockCoverage": true}, {"functionName": "testAnyEncoding", "ranges": [{"startOffset": 1273, "endOffset": 1417, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "125", "url": "file:///home/<USER>/ylabs/lib0/set.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 380, "count": 1}], "isBlockCoverage": true}, {"functionName": "testFirst", "ranges": [{"startOffset": 127, "endOffset": 379, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "126", "url": "file:///home/<USER>/ylabs/lib0/sort.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4899, "count": 1}], "isBlockCoverage": true}, {"functionName": "runSortTest", "ranges": [{"startOffset": 276, "endOffset": 947, "count": 36}, {"startOffset": 525, "endOffset": 725, "count": 31}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 446, "endOffset": 483, "count": 36}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 562, "endOffset": 627, "count": 31}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 755, "endOffset": 808, "count": 36}], "isBlockCoverage": true}, {"functionName": "createSortTest", "ranges": [{"startOffset": 1185, "endOffset": 2222, "count": 4}, {"startOffset": 2025, "endOffset": 2220, "count": 0}], "isBlockCoverage": true}, {"functionName": "testSortUint8", "ranges": [{"startOffset": 2287, "endOffset": 2707, "count": 1}], "isBlockCoverage": true}, {"functionName": "getVal", "ranges": [{"startOffset": 2393, "endOffset": 2399, "count": 446720}], "isBlockCoverage": true}, {"functionName": "compare", "ranges": [{"startOffset": 2498, "endOffset": 2513, "count": 7517384}], "isBlockCoverage": true}, {"functionName": "createArray", "ranges": [{"startOffset": 2602, "endOffset": 2654, "count": 9}], "isBlockCoverage": true}, {"functionName": "testSortUint32", "ranges": [{"startOffset": 2773, "endOffset": 3190, "count": 1}], "isBlockCoverage": true}, {"functionName": "getVal", "ranges": [{"startOffset": 2879, "endOffset": 2885, "count": 223360}], "isBlockCoverage": true}, {"functionName": "compare", "ranges": [{"startOffset": 2984, "endOffset": 2999, "count": 29990345}], "isBlockCoverage": true}, {"functionName": "createArray", "ranges": [{"startOffset": 3088, "endOffset": 3137, "count": 9}], "isBlockCoverage": true}, {"functionName": "testSortUint16", "ranges": [{"startOffset": 3256, "endOffset": 3673, "count": 1}], "isBlockCoverage": true}, {"functionName": "getVal", "ranges": [{"startOffset": 3362, "endOffset": 3368, "count": 223360}], "isBlockCoverage": true}, {"functionName": "compare", "ranges": [{"startOffset": 3467, "endOffset": 3482, "count": 29463601}], "isBlockCoverage": true}, {"functionName": "createArray", "ranges": [{"startOffset": 3571, "endOffset": 3620, "count": 9}], "isBlockCoverage": true}, {"functionName": "testSortObjectUint32", "ranges": [{"startOffset": 3745, "endOffset": 4220, "count": 1}], "isBlockCoverage": true}, {"functionName": "getVal", "ranges": [{"startOffset": 3837, "endOffset": 3853, "count": 223360}], "isBlockCoverage": true}, {"functionName": "compare", "ranges": [{"startOffset": 3968, "endOffset": 3995, "count": 29508231}], "isBlockCoverage": true}, {"functionName": "createArray", "ranges": [{"startOffset": 4092, "endOffset": 4167, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4146, "endOffset": 4166, "count": 111680}], "isBlockCoverage": true}, {"functionName": "testListVsArrayPerformance", "ranges": [{"startOffset": 4298, "endOffset": 4898, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4460, "endOffset": 4616, "count": 1}, {"startOffset": 4577, "endOffset": 4612, "count": 100000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4651, "endOffset": 4895, "count": 1}, {"startOffset": 4798, "endOffset": 4891, "count": 99999}], "isBlockCoverage": true}]}, {"scriptId": "127", "url": "file:///home/<USER>/ylabs/lib0/url.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 954, "count": 1}], "isBlockCoverage": true}, {"functionName": "paramTest", "ranges": [{"startOffset": 131, "endOffset": 261, "count": 3}], "isBlockCoverage": true}, {"functionName": "testUrlPara<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 330, "endOffset": 953, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "128", "url": "file:///home/<USER>/ylabs/lib0/metric.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1627, "count": 1}], "isBlockCoverage": true}, {"functionName": "testMetricPrefix", "ranges": [{"startOffset": 139, "endOffset": 1626, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "129", "url": "file:///home/<USER>/ylabs/lib0/function.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3493, "count": 1}], "isBlockCoverage": true}, {"functionName": "testBasics", "ranges": [{"startOffset": 131, "endOffset": 963, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 166, "endOffset": 179, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 348, "endOffset": 960, "count": 4}, {"startOffset": 381, "endOffset": 476, "count": 1}, {"startOffset": 502, "endOffset": 593, "count": 1}, {"startOffset": 619, "endOffset": 710, "count": 1}, {"startOffset": 738, "endOffset": 829, "count": 1}, {"startOffset": 865, "endOffset": 956, "count": 1}], "isBlockCoverage": true}, {"functionName": "testCallAll", "ranges": [{"startOffset": 1027, "endOffset": 1346, "count": 1}, {"startOffset": 1300, "endOffset": 1345, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1108, "endOffset": 1139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1147, "endOffset": 1180, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1201, "endOffset": 1232, "count": 1}], "isBlockCoverage": true}, {"functionName": "testDeepEquality", "ranges": [{"startOffset": 1415, "endOffset": 3492, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "130", "url": "file:///home/<USER>/ylabs/lib0/storage.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 351, "count": 1}], "isBlockCoverage": true}, {"functionName": "testStorageModule", "ranges": [{"startOffset": 142, "endOffset": 350, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 252, "endOffset": 288, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "131", "url": "file:///home/<USER>/ylabs/lib0/list.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1995, "count": 1}], "isBlockCoverage": true}, {"functionName": "QueueItem", "ranges": [{"startOffset": 146, "endOffset": 194, "count": 64}], "isBlockCoverage": true}, {"functionName": "testEnqueueDequeue", "ranges": [{"startOffset": 267, "endOffset": 1054, "count": 1}, {"startOffset": 459, "endOffset": 533, "count": 30}, {"startOffset": 564, "endOffset": 674, "count": 30}, {"startOffset": 771, "endOffset": 845, "count": 30}, {"startOffset": 876, "endOffset": 986, "count": 30}], "isBlockCoverage": true}, {"functionName": "testSelectivePop", "ranges": [{"startOffset": 1123, "endOffset": 1994, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1448, "endOffset": 1456, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1502, "endOffset": 1510, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1564, "endOffset": 1575, "count": 3}], "isBlockCoverage": true}]}, {"scriptId": "132", "url": "file:///home/<USER>/ylabs/lib0/cache.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2780, "count": 1}], "isBlockCoverage": true}, {"functionName": "testCache", "ranges": [{"startOffset": 170, "endOffset": 2779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 419, "endOffset": 449, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 492, "endOffset": 522, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1807, "endOffset": 1833, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2367, "endOffset": 2398, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "133", "url": "file:///home/<USER>/ylabs/lib0/symbol.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 315, "count": 1}], "isBlockCoverage": true}, {"functionName": "testBasicSymbolFeatures", "ranges": [{"startOffset": 147, "endOffset": 314, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "134", "url": "file:///home/<USER>/ylabs/lib0/traits.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1205, "count": 1}], "isBlockCoverage": true}, {"functionName": "testEqualityTrait1", "ranges": [{"startOffset": 179, "endOffset": 521, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 322, "endOffset": 384, "count": 2}], "isBlockCoverage": true}, {"functionName": "testEqualityTrait2", "ranges": [{"startOffset": 592, "endOffset": 1204, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 818, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1094, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "135", "url": "file:///home/<USER>/ylabs/lib0/environment.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4212, "count": 1}, {"startOffset": 538, "endOffset": 541, "count": 0}, {"startOffset": 643, "endOffset": 677, "count": 0}, {"startOffset": 678, "endOffset": 688, "count": 0}, {"startOffset": 803, "endOffset": 810, "count": 0}, {"startOffset": 4047, "endOffset": 4188, "count": 0}], "isBlockCoverage": true}, {"functionName": "computeParams", "ranges": [{"startOffset": 922, "endOffset": 2175, "count": 7}, {"startOffset": 958, "endOffset": 2157, "count": 1}, {"startOffset": 1115, "endOffset": 1513, "count": 5}, {"startOffset": 1176, "endOffset": 1312, "count": 2}, {"startOffset": 1216, "endOffset": 1271, "count": 0}, {"startOffset": 1312, "endOffset": 1505, "count": 3}, {"startOffset": 1358, "endOffset": 1448, "count": 1}, {"startOffset": 1448, "endOffset": 1495, "count": 2}, {"startOffset": 1702, "endOffset": 2153, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1869, "endOffset": 2105, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2296, "endOffset": 2331, "count": 6}], "isBlockCoverage": true}, {"functionName": "getPara<PERSON>", "ranges": [{"startOffset": 2462, "endOffset": 2525, "count": 1}, {"startOffset": 2512, "endOffset": 2525, "count": 0}], "isBlockCoverage": true}, {"functionName": "getVariable", "ranges": [{"startOffset": 2634, "endOffset": 2806, "count": 3}, {"startOffset": 2744, "endOffset": 2806, "count": 0}], "isBlockCoverage": true}, {"functionName": "getConf", "ranges": [{"startOffset": 2911, "endOffset": 2976, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureConf", "ranges": [{"startOffset": 3079, "endOffset": 3233, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasConf", "ranges": [{"startOffset": 3334, "endOffset": 3397, "count": 3}, {"startOffset": 3368, "endOffset": 3397, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "136", "url": "file:///home/<USER>/ylabs/lib0/diff.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4774, "count": 1}], "isBlockCoverage": true}, {"functionName": "simpleDiffString", "ranges": [{"startOffset": 1221, "endOffset": 2026, "count": 10068}, {"startOffset": 1386, "endOffset": 1404, "count": 9910}, {"startOffset": 1405, "endOffset": 1427, "count": 9442}, {"startOffset": 1429, "endOffset": 1445, "count": 349}, {"startOffset": 1560, "endOffset": 1599, "count": 333}, {"startOffset": 1601, "endOffset": 1607, "count": 3}, {"startOffset": 1641, "endOffset": 1667, "count": 9922}, {"startOffset": 1668, "endOffset": 1722, "count": 9434}, {"startOffset": 1724, "endOffset": 1741, "count": 387}, {"startOffset": 1856, "endOffset": 1902, "count": 368}, {"startOffset": 1904, "endOffset": 1911, "count": 1}], "isBlockCoverage": true}, {"functionName": "simpleDiffArray", "ranges": [{"startOffset": 2688, "endOffset": 3199, "count": 16}, {"startOffset": 2879, "endOffset": 2897, "count": 21}, {"startOffset": 2898, "endOffset": 2926, "count": 21}, {"startOffset": 2928, "endOffset": 2944, "count": 8}, {"startOffset": 2978, "endOffset": 3004, "count": 18}, {"startOffset": 3005, "endOffset": 3065, "count": 17}, {"startOffset": 3067, "endOffset": 3084, "count": 6}], "isBlockCoverage": true}, {"functionName": "simpleDiffStringWithCursor", "ranges": [{"startOffset": 3443, "endOffset": 4773, "count": 19}, {"startOffset": 3745, "endOffset": 3767, "count": 47}, {"startOffset": 3768, "endOffset": 3794, "count": 46}, {"startOffset": 3795, "endOffset": 3815, "count": 35}, {"startOffset": 3820, "endOffset": 3836, "count": 30}, {"startOffset": 3951, "endOffset": 3990, "count": 11}, {"startOffset": 3992, "endOffset": 3998, "count": 3}, {"startOffset": 4102, "endOffset": 4132, "count": 52}, {"startOffset": 4133, "endOffset": 4191, "count": 47}, {"startOffset": 4196, "endOffset": 4213, "count": 36}, {"startOffset": 4328, "endOffset": 4374, "count": 10}, {"startOffset": 4376, "endOffset": 4383, "count": 1}, {"startOffset": 4517, "endOffset": 4547, "count": 23}, {"startOffset": 4548, "endOffset": 4574, "count": 17}, {"startOffset": 4579, "endOffset": 4595, "count": 7}, {"startOffset": 4611, "endOffset": 4650, "count": 10}, {"startOffset": 4652, "endOffset": 4658, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "137", "url": "file:///home/<USER>/ylabs/lib0/math.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1249, "count": 1}], "isBlockCoverage": true}, {"functionName": "add", "ranges": [{"startOffset": 450, "endOffset": 465, "count": 2549070}], "isBlockCoverage": true}, {"functionName": "min", "ranges": [{"startOffset": 600, "endOffset": 623, "count": 751272}, {"startOffset": 616, "endOffset": 619, "count": 1042}, {"startOffset": 620, "endOffset": 623, "count": 750230}], "isBlockCoverage": true}, {"functionName": "max", "ranges": [{"startOffset": 757, "endOffset": 780, "count": 31049126}, {"startOffset": 773, "endOffset": 776, "count": 10675385}, {"startOffset": 777, "endOffset": 780, "count": 20373741}], "isBlockCoverage": true}, {"functionName": "exp10", "ranges": [{"startOffset": 1005, "endOffset": 1029, "count": 445}], "isBlockCoverage": true}, {"functionName": "isNegativeZero", "ranges": [{"startOffset": 1216, "endOffset": 1248, "count": 88367}, {"startOffset": 1229, "endOffset": 1236, "count": 87932}, {"startOffset": 1237, "endOffset": 1248, "count": 435}], "isBlockCoverage": true}]}, {"scriptId": "138", "url": "file:///home/<USER>/ylabs/lib0/string.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4423, "count": 1}, {"startOffset": 1486, "endOffset": 1492, "count": 0}, {"startOffset": 1757, "endOffset": 1778, "count": 0}, {"startOffset": 2460, "endOffset": 2466, "count": 0}, {"startOffset": 2629, "endOffset": 3049, "count": 0}, {"startOffset": 3363, "endOffset": 3384, "count": 0}], "isBlockCoverage": true}, {"functionName": "toLowerCase", "ranges": [{"startOffset": 433, "endOffset": 453, "count": 348}], "isBlockCoverage": true}, {"functionName": "trimLeft", "ranges": [{"startOffset": 559, "endOffset": 592, "count": 155}], "isBlockCoverage": true}, {"functionName": "fromCamelCase", "ranges": [{"startOffset": 740, "endOffset": 843, "count": 155}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 797, "endOffset": 841, "count": 348}], "isBlockCoverage": true}, {"functionName": "utf8ByteLength", "ranges": [{"startOffset": 956, "endOffset": 1003, "count": 73985}], "isBlockCoverage": true}, {"functionName": "_encodeUtf8Polyfill", "ranges": [{"startOffset": 1095, "endOffset": 1349, "count": 3}, {"startOffset": 1263, "endOffset": 1334, "count": 1169541}], "isBlockCoverage": true}, {"functionName": "_encodeUtf8Native", "ranges": [{"startOffset": 1583, "endOffset": 1617, "count": 1061}], "isBlockCoverage": true}, {"functionName": "_decodeUtf8Polyfill", "ranges": [{"startOffset": 1870, "endOffset": 2373, "count": 2}, {"startOffset": 1980, "endOffset": 2320, "count": 87}, {"startOffset": 2023, "endOffset": 2037, "count": 2}, {"startOffset": 2038, "endOffset": 2045, "count": 85}], "isBlockCoverage": true}, {"functionName": "_decodeUtf8Native", "ranges": [{"startOffset": 3160, "endOffset": 3223, "count": 11}], "isBlockCoverage": true}, {"functionName": "splice", "ranges": [{"startOffset": 3606, "endOffset": 3699, "count": 10075}], "isBlockCoverage": true}, {"functionName": "repeat", "ranges": [{"startOffset": 3778, "endOffset": 3831, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3809, "endOffset": 3821, "count": 4}], "isBlockCoverage": true}, {"functionName": "escapeHTML", "ranges": [{"startOffset": 3992, "endOffset": 4151, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4025, "endOffset": 4150, "count": 5}], "isBlockCoverage": true}, {"functionName": "unescapeHTML", "ranges": [{"startOffset": 4242, "endOffset": 4422, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4296, "endOffset": 4421, "count": 5}], "isBlockCoverage": true}]}, {"scriptId": "139", "url": "file:///home/<USER>/ylabs/lib0/random.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 923, "count": 1}], "isBlockCoverage": true}, {"functionName": "uint32", "ranges": [{"startOffset": 354, "endOffset": 398, "count": 455147}], "isBlockCoverage": true}, {"functionName": "uint53", "ranges": [{"startOffset": 422, "endOffset": 554, "count": 10000}], "isBlockCoverage": true}, {"functionName": "oneOf", "ranges": [{"startOffset": 640, "endOffset": 683, "count": 1}], "isBlockCoverage": true}, {"functionName": "uuidv4", "ranges": [{"startOffset": 808, "endOffset": 922, "count": 10001}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 872, "endOffset": 920, "count": 310031}], "isBlockCoverage": true}]}, {"scriptId": "140", "url": "file:///home/<USER>/ylabs/lib0/object.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2783, "count": 1}], "isBlockCoverage": true}, {"functionName": "create", "ranges": [{"startOffset": 154, "endOffset": 179, "count": 1}], "isBlockCoverage": true}, {"functionName": "for<PERSON>ach", "ranges": [{"startOffset": 433, "endOffset": 500, "count": 791}, {"startOffset": 472, "endOffset": 498, "count": 958}, {"startOffset": 498, "endOffset": 499, "count": 788}], "isBlockCoverage": true}, {"functionName": "map", "ranges": [{"startOffset": 677, "endOffset": 796, "count": 43}, {"startOffset": 737, "endOffset": 777, "count": 195}], "isBlockCoverage": true}, {"functionName": "length", "ranges": [{"startOffset": 922, "endOffset": 945, "count": 1606}], "isBlockCoverage": true}, {"functionName": "size", "ranges": [{"startOffset": 1030, "endOffset": 1053, "count": 40}], "isBlockCoverage": true}, {"functionName": "some", "ranges": [{"startOffset": 1182, "endOffset": 1295, "count": 4}, {"startOffset": 1221, "endOffset": 1278, "count": 7}, {"startOffset": 1249, "endOffset": 1274, "count": 2}, {"startOffset": 1278, "endOffset": 1294, "count": 2}], "isBlockCoverage": true}, {"functionName": "isEmpty", "ranges": [{"startOffset": 1366, "endOffset": 1487, "count": 4}, {"startOffset": 1449, "endOffset": 1486, "count": 1}], "isBlockCoverage": true}, {"functionName": "every", "ranges": [{"startOffset": 1617, "endOffset": 1731, "count": 21}, {"startOffset": 1656, "endOffset": 1715, "count": 23}, {"startOffset": 1685, "endOffset": 1711, "count": 5}, {"startOffset": 1715, "endOffset": 1730, "count": 16}], "isBlockCoverage": true}, {"functionName": "hasProperty", "ranges": [{"startOffset": 1886, "endOffset": 1946, "count": 970}], "isBlockCoverage": true}, {"functionName": "equalFlat", "ranges": [{"startOffset": 2068, "endOffset": 2198, "count": 20}, {"startOffset": 2110, "endOffset": 2197, "count": 19}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2122, "endOffset": 2196, "count": 18}, {"startOffset": 2155, "endOffset": 2177, "count": 3}, {"startOffset": 2179, "endOffset": 2196, "count": 16}], "isBlockCoverage": true}, {"functionName": "deepFreeze", "ranges": [{"startOffset": 2612, "endOffset": 2782, "count": 5}, {"startOffset": 2644, "endOffset": 2761, "count": 9}, {"startOffset": 2697, "endOffset": 2723, "count": 6}, {"startOffset": 2725, "endOffset": 2757, "count": 3}], "isBlockCoverage": true}]}, {"scriptId": "141", "url": "file:///home/<USER>/ylabs/lib0/prng.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6079, "count": 1}], "isBlockCoverage": true}, {"functionName": "create", "ranges": [{"startOffset": 1105, "endOffset": 1134, "count": 125115}], "isBlockCoverage": true}, {"functionName": "bool", "ranges": [{"startOffset": 1288, "endOffset": 1314, "count": 25005}], "isBlockCoverage": true}, {"functionName": "int53", "ranges": [{"startOffset": 1663, "endOffset": 1728, "count": 63555}], "isBlockCoverage": true}, {"functionName": "uint53", "ranges": [{"startOffset": 2078, "endOffset": 2127, "count": 28541}], "isBlockCoverage": true}, {"functionName": "int32", "ranges": [{"startOffset": 2476, "endOffset": 2541, "count": 11480074}], "isBlockCoverage": true}, {"functionName": "uint32", "ranges": [{"startOffset": 2891, "endOffset": 2936, "count": 105623}], "isBlockCoverage": true}, {"functionName": "int31", "ranges": [{"startOffset": 3430, "endOffset": 3469, "count": 2142258}], "isBlockCoverage": true}, {"functionName": "real53", "ranges": [{"startOffset": 3665, "endOffset": 3682, "count": 17772}], "isBlockCoverage": true}, {"functionName": "char", "ranges": [{"startOffset": 4086, "endOffset": 4126, "count": 15010}], "isBlockCoverage": true}, {"functionName": "letter", "ranges": [{"startOffset": 4221, "endOffset": 4261, "count": 259438}], "isBlockCoverage": true}, {"functionName": "word", "ranges": [{"startOffset": 4468, "endOffset": 4635, "count": 20125}, {"startOffset": 4592, "endOffset": 4620, "count": 259438}], "isBlockCoverage": true}, {"functionName": "utf16Rune", "ranges": [{"startOffset": 4791, "endOffset": 4874, "count": 1795394}], "isBlockCoverage": true}, {"functionName": "utf16String", "ranges": [{"startOffset": 4965, "endOffset": 5118, "count": 14123}, {"startOffset": 5072, "endOffset": 5103, "count": 1795394}], "isBlockCoverage": true}, {"functionName": "oneOf", "ranges": [{"startOffset": 5375, "endOffset": 5429, "count": 38168}], "isBlockCoverage": true}, {"functionName": "uint8Array", "ranges": [{"startOffset": 5533, "endOffset": 5699, "count": 59087}, {"startOffset": 5638, "endOffset": 5684, "count": 9174438}], "isBlockCoverage": true}, {"functionName": "uint16Array", "ranges": [{"startOffset": 5827, "endOffset": 5889, "count": 9}], "isBlockCoverage": true}, {"functionName": "uint32Array", "ranges": [{"startOffset": 5995, "endOffset": 6057, "count": 18}], "isBlockCoverage": true}]}, {"scriptId": "142", "url": "file:///home/<USER>/ylabs/lib0/statistics.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 540, "count": 1}], "isBlockCoverage": true}, {"functionName": "median", "ranges": [{"startOffset": 246, "endOffset": 413, "count": 25}, {"startOffset": 270, "endOffset": 275, "count": 1}, {"startOffset": 276, "endOffset": 413, "count": 24}, {"startOffset": 300, "endOffset": 327, "count": 17}, {"startOffset": 328, "endOffset": 412, "count": 7}], "isBlockCoverage": true}, {"functionName": "average", "ranges": [{"startOffset": 496, "endOffset": 539, "count": 19}], "isBlockCoverage": true}]}, {"scriptId": "143", "url": "file:///home/<USER>/ylabs/lib0/array.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4651, "count": 1}], "isBlockCoverage": true}, {"functionName": "last", "ranges": [{"startOffset": 254, "endOffset": 280, "count": 20}], "isBlockCoverage": true}, {"functionName": "create", "ranges": [{"startOffset": 349, "endOffset": 383, "count": 1}], "isBlockCoverage": true}, {"functionName": "copy", "ranges": [{"startOffset": 473, "endOffset": 513, "count": 1}], "isBlockCoverage": true}, {"functionName": "appendTo", "ranges": [{"startOffset": 652, "endOffset": 737, "count": 2}, {"startOffset": 708, "endOffset": 735, "count": 4}], "isBlockCoverage": true}, {"functionName": "every", "ranges": [{"startOffset": 1178, "endOffset": 1307, "count": 3}, {"startOffset": 1231, "endOffset": 1291, "count": 8}, {"startOffset": 1261, "endOffset": 1287, "count": 1}, {"startOffset": 1291, "endOffset": 1306, "count": 2}], "isBlockCoverage": true}, {"functionName": "some", "ranges": [{"startOffset": 1546, "endOffset": 1674, "count": 2}, {"startOffset": 1599, "endOffset": 1657, "count": 5}, {"startOffset": 1628, "endOffset": 1653, "count": 1}, {"startOffset": 1657, "endOffset": 1673, "count": 1}], "isBlockCoverage": true}, {"functionName": "equalFlat", "ranges": [{"startOffset": 1811, "endOffset": 1890, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1855, "endOffset": 1889, "count": 2}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 2001, "endOffset": 2081, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2051, "endOffset": 2080, "count": 2}], "isBlockCoverage": true}, {"functionName": "unfold", "ranges": [{"startOffset": 2216, "endOffset": 2342, "count": 8}, {"startOffset": 2293, "endOffset": 2325, "count": 20105}], "isBlockCoverage": true}, {"functionName": "fold", "ranges": [{"startOffset": 2510, "endOffset": 2557, "count": 2274337}], "isBlockCoverage": true}, {"functionName": "unique", "ranges": [{"startOffset": 2689, "endOffset": 2715, "count": 2}], "isBlockCoverage": true}, {"functionName": "uniqueBy", "ranges": [{"startOffset": 2863, "endOffset": 3202, "count": 2}, {"startOffset": 3124, "endOffset": 3180, "count": 1}], "isBlockCoverage": true}, {"functionName": "map", "ranges": [{"startOffset": 3476, "endOffset": 3721, "count": 6448}, {"startOffset": 3602, "endOffset": 3685, "count": 152446}], "isBlockCoverage": true}, {"functionName": "bubblesortItem", "ranges": [{"startOffset": 4270, "endOffset": 4650, "count": 78914}, {"startOffset": 4384, "endOffset": 4415, "count": 91512}, {"startOffset": 4417, "endOffset": 4463, "count": 31666}, {"startOffset": 4478, "endOffset": 4486, "count": 66178}, {"startOffset": 4488, "endOffset": 4637, "count": 61643}, {"startOffset": 4548, "endOffset": 4579, "count": 111788}, {"startOffset": 4581, "endOffset": 4633, "count": 53561}], "isBlockCoverage": true}]}, {"scriptId": "144", "url": "file:///home/<USER>/ylabs/lib0/json.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 296, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "145", "url": "file:///home/<USER>/ylabs/lib0/time.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1229, "count": 1}], "isBlockCoverage": true}, {"functionName": "getDate", "ranges": [{"startOffset": 214, "endOffset": 230, "count": 1}], "isBlockCoverage": true}, {"functionName": "humanizeDuration", "ranges": [{"startOffset": 570, "endOffset": 1228, "count": 432}, {"startOffset": 594, "endOffset": 690, "count": 420}, {"startOffset": 690, "endOffset": 880, "count": 12}, {"startOffset": 880, "endOffset": 993, "count": 4}, {"startOffset": 918, "endOffset": 933, "count": 2}, {"startOffset": 935, "endOffset": 983, "count": 3}, {"startOffset": 957, "endOffset": 968, "count": 2}, {"startOffset": 969, "endOffset": 976, "count": 1}, {"startOffset": 984, "endOffset": 988, "count": 1}, {"startOffset": 993, "endOffset": 1011, "count": 8}, {"startOffset": 1011, "endOffset": 1158, "count": 6}, {"startOffset": 1077, "endOffset": 1092, "count": 4}, {"startOffset": 1094, "endOffset": 1148, "count": 4}, {"startOffset": 1130, "endOffset": 1139, "count": 0}, {"startOffset": 1149, "endOffset": 1153, "count": 2}, {"startOffset": 1158, "endOffset": 1199, "count": 2}, {"startOffset": 1199, "endOffset": 1220, "count": 1}, {"startOffset": 1221, "endOffset": 1225, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "146", "url": "file:///home/<USER>/ylabs/lib0/promise.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3049, "count": 1}], "isBlockCoverage": true}, {"functionName": "create", "ranges": [{"startOffset": 334, "endOffset": 381, "count": 143}], "isBlockCoverage": true}, {"functionName": "createEmpty", "ranges": [{"startOffset": 511, "endOffset": 530, "count": 3}], "isBlockCoverage": true}, {"functionName": "reject", "ranges": [{"startOffset": 878, "endOffset": 910, "count": 5}], "isBlockCoverage": true}, {"functionName": "resolve", "ranges": [{"startOffset": 1010, "endOffset": 1037, "count": 105}], "isBlockCoverage": true}, {"functionName": "resolveWith", "ranges": [{"startOffset": 1131, "endOffset": 1158, "count": 3}], "isBlockCoverage": true}, {"functionName": "until", "ranges": [{"startOffset": 1443, "endOffset": 1970, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1495, "endOffset": 1969, "count": 5}], "isBlockCoverage": true}, {"functionName": "untilInterval", "ranges": [{"startOffset": 1614, "endOffset": 1895, "count": 15}, {"startOffset": 1639, "endOffset": 1698, "count": 4}, {"startOffset": 1698, "endOffset": 1891, "count": 11}, {"startOffset": 1720, "endOffset": 1891, "count": 2}, {"startOffset": 1801, "endOffset": 1885, "count": 1}], "isBlockCoverage": true}, {"functionName": "untilAsync", "ranges": [{"startOffset": 2145, "endOffset": 2499, "count": 2}, {"startOffset": 2352, "endOffset": 2396, "count": 3}, {"startOffset": 2398, "endOffset": 2468, "count": 13}, {"startOffset": 2423, "endOffset": 2429, "count": 1}, {"startOffset": 2429, "endOffset": 2468, "count": 12}, {"startOffset": 2468, "endOffset": 2498, "count": 1}], "isBlockCoverage": true}, {"functionName": "wait", "ranges": [{"startOffset": 2588, "endOffset": 2657, "count": 131}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2606, "endOffset": 2656, "count": 131}], "isBlockCoverage": true}, {"functionName": "isPromise", "ranges": [{"startOffset": 2982, "endOffset": 3048, "count": 149220}, {"startOffset": 3008, "endOffset": 3048, "count": 149181}, {"startOffset": 3014, "endOffset": 3023, "count": 2}, {"startOffset": 3024, "endOffset": 3034, "count": 2}, {"startOffset": 3035, "endOffset": 3047, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "147", "url": "file:///home/<USER>/ylabs/lib0/performance.node.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 614, "count": 1}, {"startOffset": 267, "endOffset": 293, "count": 0}, {"startOffset": 429, "endOffset": 447, "count": 0}, {"startOffset": 587, "endOffset": 613, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "148", "url": "node:perf_hooks", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1065, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "149", "url": "node:internal/perf/performance_entry", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3272, "count": 1}], "isBlockCoverage": false}, {"functionName": "isPerformanceEntry", "ranges": [{"startOffset": 675, "endOffset": 748, "count": 453}], "isBlockCoverage": true}, {"functionName": "PerformanceEntry", "ranges": [{"startOffset": 777, "endOffset": 1132, "count": 453}, {"startOffset": 962, "endOffset": 1012, "count": 0}], "isBlockCoverage": true}, {"functionName": "get name", "ranges": [{"startOffset": 1136, "endOffset": 1236, "count": 0}], "isBlockCoverage": false}, {"functionName": "get entryType", "ranges": [{"startOffset": 1240, "endOffset": 1355, "count": 453}], "isBlockCoverage": true}, {"functionName": "get startTime", "ranges": [{"startOffset": 1359, "endOffset": 1474, "count": 0}], "isBlockCoverage": false}, {"functionName": "get duration", "ranges": [{"startOffset": 1478, "endOffset": 1590, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1594, "endOffset": 1838, "count": 0}], "isBlockCoverage": false}, {"functionName": "toJSON", "ranges": [{"startOffset": 1842, "endOffset": 2064, "count": 0}], "isBlockCoverage": false}, {"functionName": "createPerformanceEntry", "ranges": [{"startOffset": 2286, "endOffset": 2422, "count": 0}], "isBlockCoverage": false}, {"functionName": "get detail", "ranges": [{"startOffset": 2539, "endOffset": 2649, "count": 0}], "isBlockCoverage": false}, {"functionName": "toJSON", "ranges": [{"startOffset": 2653, "endOffset": 2904, "count": 0}], "isBlockCoverage": false}, {"functionName": "createPerformanceNodeEntry", "ranges": [{"startOffset": 2908, "endOffset": 3112, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "150", "url": "node:internal/perf/resource_timing", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8223, "count": 1}], "isBlockCoverage": false}, {"functionName": "PerformanceResourceTiming", "ranges": [{"startOffset": 929, "endOffset": 1143, "count": 0}], "isBlockCoverage": false}, {"functionName": "get name", "ranges": [{"startOffset": 1147, "endOffset": 1272, "count": 0}], "isBlockCoverage": false}, {"functionName": "get startTime", "ranges": [{"startOffset": 1276, "endOffset": 1412, "count": 0}], "isBlockCoverage": false}, {"functionName": "get duration", "ranges": [{"startOffset": 1416, "endOffset": 1579, "count": 0}], "isBlockCoverage": false}, {"functionName": "get initiatorType", "ranges": [{"startOffset": 1583, "endOffset": 1719, "count": 0}], "isBlockCoverage": false}, {"functionName": "get workerStart", "ranges": [{"startOffset": 1723, "endOffset": 1879, "count": 0}], "isBlockCoverage": false}, {"functionName": "get redirectStart", "ranges": [{"startOffset": 1883, "endOffset": 2031, "count": 0}], "isBlockCoverage": false}, {"functionName": "get redirectEnd", "ranges": [{"startOffset": 2035, "endOffset": 2179, "count": 0}], "isBlockCoverage": false}, {"functionName": "get fetchStart", "ranges": [{"startOffset": 2183, "endOffset": 2332, "count": 0}], "isBlockCoverage": false}, {"functionName": "get domainLookupStart", "ranges": [{"startOffset": 2336, "endOffset": 2519, "count": 0}], "isBlockCoverage": false}, {"functionName": "get domainLookupEnd", "ranges": [{"startOffset": 2523, "endOffset": 2702, "count": 0}], "isBlockCoverage": false}, {"functionName": "get connectStart", "ranges": [{"startOffset": 2706, "endOffset": 2882, "count": 0}], "isBlockCoverage": false}, {"functionName": "get connectEnd", "ranges": [{"startOffset": 2886, "endOffset": 3058, "count": 0}], "isBlockCoverage": false}, {"functionName": "get secureConnectionStart", "ranges": [{"startOffset": 3062, "endOffset": 3260, "count": 0}], "isBlockCoverage": false}, {"functionName": "get nextHopProtocol", "ranges": [{"startOffset": 3264, "endOffset": 3453, "count": 0}], "isBlockCoverage": false}, {"functionName": "get requestStart", "ranges": [{"startOffset": 3457, "endOffset": 3615, "count": 0}], "isBlockCoverage": false}, {"functionName": "get responseStart", "ranges": [{"startOffset": 3619, "endOffset": 3779, "count": 0}], "isBlockCoverage": false}, {"functionName": "get responseEnd", "ranges": [{"startOffset": 3783, "endOffset": 3919, "count": 0}], "isBlockCoverage": false}, {"functionName": "get encodedBodySize", "ranges": [{"startOffset": 3923, "endOffset": 4071, "count": 0}], "isBlockCoverage": false}, {"functionName": "get decodedBodySize", "ranges": [{"startOffset": 4075, "endOffset": 4223, "count": 0}], "isBlockCoverage": false}, {"functionName": "get transferSize", "ranges": [{"startOffset": 4227, "endOffset": 4481, "count": 0}], "isBlockCoverage": false}, {"functionName": "get deliveryType", "ranges": [{"startOffset": 4485, "endOffset": 4616, "count": 0}], "isBlockCoverage": false}, {"functionName": "get responseStatus", "ranges": [{"startOffset": 4620, "endOffset": 4755, "count": 0}], "isBlockCoverage": false}, {"functionName": "toJSON", "ranges": [{"startOffset": 4759, "endOffset": 5782, "count": 0}], "isBlockCoverage": false}, {"functionName": "createPerformanceResourceTiming", "ranges": [{"startOffset": 6726, "endOffset": 7480, "count": 0}], "isBlockCoverage": false}, {"functionName": "markResourceTiming", "ranges": [{"startOffset": 7549, "endOffset": 8148, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "151", "url": "node:internal/perf/observe", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16722, "count": 1}], "isBlockCoverage": false}, {"functionName": "queuePending", "ranges": [{"startOffset": 2600, "endOffset": 2861, "count": 0}], "isBlockCoverage": false}, {"functionName": "getObserverType", "ranges": [{"startOffset": 2863, "endOffset": 3203, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeDecrementObserverCounts", "ranges": [{"startOffset": 3205, "endOffset": 3626, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeIncrementObserverCount", "ranges": [{"startOffset": 3628, "endOffset": 3970, "count": 0}], "isBlockCoverage": false}, {"functionName": "performanceObserverSorter", "ranges": [{"startOffset": 4047, "endOffset": 4114, "count": 0}], "isBlockCoverage": false}, {"functionName": "PerformanceObserverEntryList", "ranges": [{"startOffset": 4156, "endOffset": 4384, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEntries", "ranges": [{"startOffset": 4388, "endOffset": 4527, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEntriesByType", "ranges": [{"startOffset": 4531, "endOffset": 4835, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEntriesByName", "ranges": [{"startOffset": 4839, "endOffset": 5332, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5336, "endOffset": 5584, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5962, "endOffset": 9533, "count": 0}], "isBlockCoverage": false}, {"functionName": "enqueue", "ranges": [{"startOffset": 10015, "endOffset": 10217, "count": 453}, {"startOffset": 10079, "endOffset": 10146, "count": 0}, {"startOffset": 10180, "endOffset": 10215, "count": 0}], "isBlockCoverage": true}, {"functionName": "bufferUserTiming", "ranges": [{"startOffset": 10278, "endOffset": 11304, "count": 453}, {"startOffset": 10392, "endOffset": 10427, "count": 302}, {"startOffset": 10427, "endOffset": 10523, "count": 151}, {"startOffset": 10500, "endOffset": 10523, "count": 0}, {"startOffset": 10640, "endOffset": 10680, "count": 0}, {"startOffset": 10682, "endOffset": 11302, "count": 0}], "isBlockCoverage": true}, {"functionName": "bufferResourceTiming", "ranges": [{"startOffset": 11592, "endOffset": 12856, "count": 0}], "isBlockCoverage": false}, {"functionName": "setResourceTimingBufferSize", "ranges": [{"startOffset": 12944, "endOffset": 13216, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDispatchBufferFull", "ranges": [{"startOffset": 13218, "endOffset": 13283, "count": 1}], "isBlockCoverage": true}, {"functionName": "clearEntriesFromBuffer", "ranges": [{"startOffset": 13285, "endOffset": 13885, "count": 0}], "isBlockCoverage": false}, {"functionName": "filterBufferMapByNameAndType", "ranges": [{"startOffset": 13887, "endOffset": 14729, "count": 0}], "isBlockCoverage": false}, {"functionName": "observerCallback", "ranges": [{"startOffset": 14731, "endOffset": 15847, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasObserver", "ranges": [{"startOffset": 15884, "endOffset": 16003, "count": 0}], "isBlockCoverage": false}, {"functionName": "startPerf", "ranges": [{"startOffset": 16006, "endOffset": 16116, "count": 0}], "isBlockCoverage": false}, {"functionName": "stopPerf", "ranges": [{"startOffset": 16118, "endOffset": 16433, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "152", "url": "node:internal/perf/usertiming", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6460, "count": 1}], "isBlockCoverage": false}, {"functionName": "getMark", "ranges": [{"startOffset": 1148, "endOffset": 1600, "count": 302}, {"startOffset": 1199, "endOffset": 1206, "count": 0}, {"startOffset": 1239, "endOffset": 1336, "count": 0}, {"startOffset": 1407, "endOffset": 1431, "count": 0}, {"startOffset": 1496, "endOffset": 1585, "count": 0}], "isBlockCoverage": true}, {"functionName": "PerformanceMark", "ranges": [{"startOffset": 1653, "endOffset": 2384, "count": 302}, {"startOffset": 1726, "endOffset": 1775, "count": 0}, {"startOffset": 1852, "endOffset": 1898, "count": 0}, {"startOffset": 2098, "endOffset": 2153, "count": 0}, {"startOffset": 2255, "endOffset": 2286, "count": 0}], "isBlockCoverage": true}, {"functionName": "get detail", "ranges": [{"startOffset": 2388, "endOffset": 2493, "count": 0}], "isBlockCoverage": false}, {"functionName": "toJSON", "ranges": [{"startOffset": 2497, "endOffset": 2680, "count": 0}], "isBlockCoverage": false}, {"functionName": "PerformanceMeasure", "ranges": [{"startOffset": 2931, "endOffset": 3228, "count": 151}, {"startOffset": 3116, "endOffset": 3166, "count": 0}], "isBlockCoverage": true}, {"functionName": "get detail", "ranges": [{"startOffset": 3232, "endOffset": 3340, "count": 0}], "isBlockCoverage": false}, {"functionName": "toJSON", "ranges": [{"startOffset": 3344, "endOffset": 3527, "count": 0}], "isBlockCoverage": false}, {"functionName": "createPerformanceMeasure", "ranges": [{"startOffset": 3729, "endOffset": 3934, "count": 151}], "isBlockCoverage": true}, {"functionName": "mark", "ranges": [{"startOffset": 3936, "endOffset": 4077, "count": 302}], "isBlockCoverage": true}, {"functionName": "calculateStartDuration", "ranges": [{"startOffset": 4079, "endOffset": 5662, "count": 151}, {"startOffset": 4293, "endOffset": 4415, "count": 0}, {"startOffset": 4436, "endOffset": 4994, "count": 0}, {"startOffset": 5058, "endOffset": 5284, "count": 0}, {"startOffset": 5384, "endOffset": 5603, "count": 0}], "isBlockCoverage": true}, {"functionName": "measure", "ranges": [{"startOffset": 5664, "endOffset": 6099, "count": 151}, {"startOffset": 5922, "endOffset": 5947, "count": 0}], "isBlockCoverage": true}, {"functionName": "clearMarkTimings", "ranges": [{"startOffset": 6101, "endOffset": 6356, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "153", "url": "node:internal/perf/nodetiming", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3858, "count": 1}], "isBlockCoverage": false}, {"functionName": "PerformanceNodeTiming", "ranges": [{"startOffset": 746, "endOffset": 3084, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1411, "endOffset": 1507, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1623, "endOffset": 1717, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1837, "endOffset": 1934, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2052, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2265, "endOffset": 2360, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2486, "endOffset": 2603, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2864, "endOffset": 3062, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3088, "endOffset": 3329, "count": 0}], "isBlockCoverage": false}, {"functionName": "toJSON", "ranges": [{"startOffset": 3333, "endOffset": 3719, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "154", "url": "node:internal/perf/performance", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6391, "count": 1}], "isBlockCoverage": false}, {"functionName": "Performance", "ranges": [{"startOffset": 1291, "endOffset": 1351, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1355, "endOffset": 1650, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearMarks", "ranges": [{"startOffset": 1654, "endOffset": 1885, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearMeasures", "ranges": [{"startOffset": 1889, "endOffset": 2098, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearResourceTimings", "ranges": [{"startOffset": 2102, "endOffset": 2319, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEntries", "ranges": [{"startOffset": 2323, "endOffset": 2451, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEntriesByName", "ranges": [{"startOffset": 2455, "endOffset": 2785, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEntriesByType", "ranges": [{"startOffset": 2789, "endOffset": 3046, "count": 0}], "isBlockCoverage": false}, {"functionName": "mark", "ranges": [{"startOffset": 3050, "endOffset": 3271, "count": 302}, {"startOffset": 3186, "endOffset": 3235, "count": 0}], "isBlockCoverage": true}, {"functionName": "measure", "ranges": [{"startOffset": 3275, "endOffset": 3560, "count": 151}, {"startOffset": 3449, "endOffset": 3498, "count": 0}], "isBlockCoverage": true}, {"functionName": "now", "ranges": [{"startOffset": 3564, "endOffset": 3660, "count": 149747}], "isBlockCoverage": true}, {"functionName": "setResourceTimingBufferSize", "ranges": [{"startOffset": 3664, "endOffset": 3980, "count": 0}], "isBlockCoverage": false}, {"functionName": "get timeO<PERSON>in", "ranges": [{"startOffset": 3984, "endOffset": 4110, "count": 0}], "isBlockCoverage": false}, {"functionName": "toJSON", "ranges": [{"startOffset": 4114, "endOffset": 4342, "count": 0}], "isBlockCoverage": false}, {"functionName": "createPerformance", "ranges": [{"startOffset": 5943, "endOffset": 6112, "count": 1}], "isBlockCoverage": true}, {"functionName": "Performance", "ranges": [{"startOffset": 6000, "endOffset": 6091, "count": 1}], "isBlockCoverage": true}, {"functionName": "dispatchBufferFull", "ranges": [{"startOffset": 6156, "endOffset": 6294, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "155", "url": "node:internal/perf/event_loop_utilization", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1527, "count": 1}], "isBlockCoverage": false}, {"functionName": "eventLoopUtilization", "ranges": [{"startOffset": 153, "endOffset": 446, "count": 0}], "isBlockCoverage": false}, {"functionName": "internalEventLoopUtilization", "ranges": [{"startOffset": 448, "endOffset": 1447, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "156", "url": "node:internal/perf/timerify", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2280, "count": 1}], "isBlockCoverage": false}, {"functionName": "processComplete", "ranges": [{"startOffset": 610, "endOffset": 985, "count": 0}], "isBlockCoverage": false}, {"functionName": "timerify", "ranges": [{"startOffset": 987, "endOffset": 2251, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "157", "url": "node:internal/histogram", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8467, "count": 1}], "isBlockCoverage": false}, {"functionName": "isHistogram", "ranges": [{"startOffset": 894, "endOffset": 968, "count": 0}], "isBlockCoverage": false}, {"functionName": "Histogram", "ranges": [{"startOffset": 1032, "endOffset": 1169, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1173, "endOffset": 1585, "count": 0}], "isBlockCoverage": false}, {"functionName": "get count", "ranges": [{"startOffset": 1636, "endOffset": 1763, "count": 0}], "isBlockCoverage": false}, {"functionName": "get countBigInt", "ranges": [{"startOffset": 1814, "endOffset": 1953, "count": 0}], "isBlockCoverage": false}, {"functionName": "get min", "ranges": [{"startOffset": 2004, "endOffset": 2127, "count": 0}], "isBlockCoverage": false}, {"functionName": "get minBigInt", "ranges": [{"startOffset": 2178, "endOffset": 2313, "count": 0}], "isBlockCoverage": false}, {"functionName": "get max", "ranges": [{"startOffset": 2364, "endOffset": 2487, "count": 0}], "isBlockCoverage": false}, {"functionName": "get maxBigInt", "ranges": [{"startOffset": 2538, "endOffset": 2673, "count": 0}], "isBlockCoverage": false}, {"functionName": "get mean", "ranges": [{"startOffset": 2724, "endOffset": 2849, "count": 0}], "isBlockCoverage": false}, {"functionName": "get exceeds", "ranges": [{"startOffset": 2900, "endOffset": 3031, "count": 0}], "isBlockCoverage": false}, {"functionName": "get exceedsBigInt", "ranges": [{"startOffset": 3082, "endOffset": 3225, "count": 0}], "isBlockCoverage": false}, {"functionName": "get stddev", "ranges": [{"startOffset": 3276, "endOffset": 3405, "count": 0}], "isBlockCoverage": false}, {"functionName": "percentile", "ranges": [{"startOffset": 3476, "endOffset": 3825, "count": 0}], "isBlockCoverage": false}, {"functionName": "percentileBigInt", "ranges": [{"startOffset": 3896, "endOffset": 4257, "count": 0}], "isBlockCoverage": false}, {"functionName": "get percentiles", "ranges": [{"startOffset": 4320, "endOffset": 4509, "count": 0}], "isBlockCoverage": false}, {"functionName": "get percentilesBigInt", "ranges": [{"startOffset": 4572, "endOffset": 4773, "count": 0}], "isBlockCoverage": false}, {"functionName": "reset", "ranges": [{"startOffset": 4810, "endOffset": 4926, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4930, "endOffset": 5085, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5089, "endOffset": 5149, "count": 0}], "isBlockCoverage": false}, {"functionName": "toJSON", "ranges": [{"startOffset": 5153, "endOffset": 5410, "count": 0}], "isBlockCoverage": false}, {"functionName": "RecordableHistogram", "ranges": [{"startOffset": 5462, "endOffset": 5628, "count": 0}], "isBlockCoverage": false}, {"functionName": "record", "ranges": [{"startOffset": 5697, "endOffset": 5971, "count": 0}], "isBlockCoverage": false}, {"functionName": "recordDel<PERSON>", "ranges": [{"startOffset": 6008, "endOffset": 6159, "count": 0}], "isBlockCoverage": false}, {"functionName": "add", "ranges": [{"startOffset": 6215, "endOffset": 6488, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6492, "endOffset": 6657, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6661, "endOffset": 6721, "count": 0}], "isBlockCoverage": false}, {"functionName": "ClonedHistogram", "ranges": [{"startOffset": 6725, "endOffset": 6935, "count": 0}], "isBlockCoverage": false}, {"functionName": "ClonedHistogram.<computed>", "ranges": [{"startOffset": 6979, "endOffset": 6988, "count": 0}], "isBlockCoverage": false}, {"functionName": "ClonedRecordableHistogram", "ranges": [{"startOffset": 6991, "endOffset": 7306, "count": 0}], "isBlockCoverage": false}, {"functionName": "ClonedRecordableHistogram.<computed>", "ranges": [{"startOffset": 7360, "endOffset": 7369, "count": 0}], "isBlockCoverage": false}, {"functionName": "createRecordableHistogram", "ranges": [{"startOffset": 7372, "endOffset": 7466, "count": 0}], "isBlockCoverage": false}, {"functionName": "createHistogram", "ranges": [{"startOffset": 7609, "endOffset": 8294, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "158", "url": "node:internal/perf/event_loop_delay", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1749, "count": 1}], "isBlockCoverage": false}, {"functionName": "ELDHistogram", "ranges": [{"startOffset": 625, "endOffset": 686, "count": 0}], "isBlockCoverage": false}, {"functionName": "enable", "ranges": [{"startOffset": 726, "endOffset": 937, "count": 0}], "isBlockCoverage": false}, {"functionName": "disable", "ranges": [{"startOffset": 977, "endOffset": 1190, "count": 0}], "isBlockCoverage": false}, {"functionName": "monitorEventLoopDelay", "ranges": [{"startOffset": 1283, "endOffset": 1707, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "159", "url": "file:///home/<USER>/ylabs/lib0/traits.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 148, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "160", "url": "file:///home/<USER>/ylabs/lib0/broadcastchannel.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3042, "count": 1}, {"startOffset": 1710, "endOffset": 1732, "count": 0}], "isBlockCoverage": true}, {"functionName": "LocalStoragePolyfill", "ranges": [{"startOffset": 999, "endOffset": 1348, "count": 0}], "isBlockCoverage": false}, {"functionName": "postMessage", "ranges": [{"startOffset": 1394, "endOffset": 1521, "count": 0}], "isBlockCoverage": false}, {"functionName": "close", "ranges": [{"startOffset": 1525, "endOffset": 1577, "count": 0}], "isBlockCoverage": false}, {"functionName": "getChannel", "ranges": [{"startOffset": 1825, "endOffset": 2129, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1870, "endOffset": 2128, "count": 2}], "isBlockCoverage": true}, {"functionName": "bc.onmessage", "ranges": [{"startOffset": 2033, "endOffset": 2090, "count": 0}], "isBlockCoverage": false}, {"functionName": "subscribe", "ranges": [{"startOffset": 2282, "endOffset": 2340, "count": 1}], "isBlockCoverage": true}, {"functionName": "unsubscribe", "ranges": [{"startOffset": 2499, "endOffset": 2720, "count": 1}], "isBlockCoverage": true}, {"functionName": "publish", "ranges": [{"startOffset": 2910, "endOffset": 3041, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3014, "endOffset": 3038, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "161", "url": "file:///home/<USER>/ylabs/lib0/crypto/jwt.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2028, "count": 1}], "isBlockCoverage": true}, {"functionName": "_stringify", "ranges": [{"startOffset": 281, "endOffset": 355, "count": 4}], "isBlockCoverage": true}, {"functionName": "_parse", "ranges": [{"startOffset": 409, "endOffset": 491, "count": 7}], "isBlockCoverage": true}, {"functionName": "encodeJwt", "ranges": [{"startOffset": 586, "endOffset": 1063, "count": 2}, {"startOffset": 781, "endOffset": 813, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 992, "endOffset": 1057, "count": 2}], "isBlockCoverage": true}, {"functionName": "verifyJwt", "ranges": [{"startOffset": 1153, "endOffset": 1692, "count": 2}, {"startOffset": 1445, "endOffset": 1485, "count": 0}, {"startOffset": 1552, "endOffset": 1587, "count": 1}, {"startOffset": 1589, "endOffset": 1691, "count": 1}], "isBlockCoverage": true}, {"functionName": "unsafeDecode", "ranges": [{"startOffset": 1878, "endOffset": 2027, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "162", "url": "file:///home/<USER>/ylabs/lib0/crypto/aes-gcm.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3420, "count": 1}], "isBlockCoverage": true}, {"functionName": "encrypt", "ranges": [{"startOffset": 495, "endOffset": 1064, "count": 1003}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 746, "endOffset": 1061, "count": 1003}], "isBlockCoverage": true}, {"functionName": "decrypt", "ranges": [{"startOffset": 1293, "endOffset": 1612, "count": 1003}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1581, "endOffset": 1609, "count": 1002}], "isBlockCoverage": true}, {"functionName": "importKeyJwk", "ranges": [{"startOffset": 1822, "endOffset": 2071, "count": 1}, {"startOffset": 1945, "endOffset": 1961, "count": 0}], "isBlockCoverage": true}, {"functionName": "importKeyRaw", "ranges": [{"startOffset": 2277, "endOffset": 2436, "count": 1}], "isBlockCoverage": true}, {"functionName": "toBinary", "ranges": [{"startOffset": 2521, "endOffset": 2586, "count": 16}, {"startOffset": 2580, "endOffset": 2586, "count": 0}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2913, "endOffset": 3419, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3100, "endOffset": 3415, "count": 8}], "isBlockCoverage": true}]}, {"scriptId": "163", "url": "file:///home/<USER>/ylabs/lib0/crypto/rsa-oaep.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1836, "count": 1}], "isBlockCoverage": true}, {"functionName": "encrypt", "ranges": [{"startOffset": 478, "endOffset": 611, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 584, "endOffset": 610, "count": 1}], "isBlockCoverage": true}, {"functionName": "decrypt", "ranges": [{"startOffset": 840, "endOffset": 975, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 946, "endOffset": 974, "count": 1}], "isBlockCoverage": true}, {"functionName": "generateKeyPair", "ranges": [{"startOffset": 1148, "endOffset": 1404, "count": 4}], "isBlockCoverage": true}, {"functionName": "importKeyJwk", "ranges": [{"startOffset": 1558, "endOffset": 1835, "count": 4}, {"startOffset": 1629, "endOffset": 1701, "count": 2}, {"startOffset": 1681, "endOffset": 1697, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "164", "url": "file:///home/<USER>/ylabs/lib0/crypto/ecdsa.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2144, "count": 1}], "isBlockCoverage": true}, {"functionName": "sign", "ranges": [{"startOffset": 535, "endOffset": 667, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 628, "endOffset": 666, "count": 3}], "isBlockCoverage": true}, {"functionName": "verify", "ranges": [{"startOffset": 893, "endOffset": 1008, "count": 4}], "isBlockCoverage": true}, {"functionName": "generateKeyPair", "ranges": [{"startOffset": 1238, "endOffset": 1384, "count": 4}], "isBlockCoverage": true}, {"functionName": "importKeyJwk", "ranges": [{"startOffset": 1538, "endOffset": 1799, "count": 6}, {"startOffset": 1609, "endOffset": 1683, "count": 4}, {"startOffset": 1663, "endOffset": 1679, "count": 0}], "isBlockCoverage": true}, {"functionName": "importKeyRaw", "ranges": [{"startOffset": 1998, "endOffset": 2143, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "165", "url": "file:///home/<USER>/ylabs/lib0/hash/rabin-gf2-polynomial.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8884, "count": 1}], "isBlockCoverage": true}, {"functionName": "_degreeToMinByteLength", "ranges": [{"startOffset": 622, "endOffset": 658, "count": 32400}], "isBlockCoverage": true}, {"functionName": "GF2Polynomial", "ranges": [{"startOffset": 928, "endOffset": 1022, "count": 6411329}], "isBlockCoverage": true}, {"functionName": "createFromBytes", "ranges": [{"startOffset": 1123, "endOffset": 1427, "count": 225898}, {"startOffset": 1234, "endOffset": 1414, "count": 538766}, {"startOffset": 1300, "endOffset": 1410, "count": 4310128}, {"startOffset": 1342, "endOffset": 1385, "count": 2068161}], "isBlockCoverage": true}, {"functionName": "toUint8Array", "ranges": [{"startOffset": 1560, "endOffset": 1869, "count": 3014}], "isBlockCoverage": true}, {"functionName": "setBit", "ranges": [{"startOffset": 1736, "endOffset": 1826, "count": 74441}], "isBlockCoverage": true}, {"functionName": "createFromUint", "ranges": [{"startOffset": 2021, "endOffset": 2164, "count": 4}, {"startOffset": 2093, "endOffset": 2132, "count": 16}], "isBlockCoverage": true}, {"functionName": "createRandom", "ranges": [{"startOffset": 2284, "endOffset": 2762, "count": 29386}], "isBlockCoverage": true}, {"functionName": "getHighestDegree", "ranges": [{"startOffset": 2850, "endOffset": 2901, "count": 2274330}], "isBlockCoverage": true}, {"functionName": "addInto", "ranges": [{"startOffset": 3118, "endOffset": 3285, "count": 4473375}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3153, "endOffset": 3282, "count": 75798389}, {"startOffset": 3197, "endOffset": 3236, "count": 39250984}, {"startOffset": 3236, "endOffset": 3278, "count": 36547405}], "isBlockCoverage": true}, {"functionName": "orInto", "ranges": [{"startOffset": 3501, "endOffset": 3581, "count": 195000}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3536, "endOffset": 3578, "count": 780780}], "isBlockCoverage": true}, {"functionName": "add", "ranges": [{"startOffset": 3793, "endOffset": 4085, "count": 68211}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3865, "endOffset": 3954, "count": 68211}, {"startOffset": 3910, "endOffset": 3950, "count": 57251}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3977, "endOffset": 4066, "count": 405634}, {"startOffset": 4022, "endOffset": 4062, "count": 394674}], "isBlockCoverage": true}, {"functionName": "clone", "ranges": [{"startOffset": 4269, "endOffset": 4380, "count": 1106418}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4335, "endOffset": 4361, "count": 13600612}], "isBlockCoverage": true}, {"functionName": "addDegreeInto", "ranges": [{"startOffset": 4598, "endOffset": 4717, "count": 29613557}, {"startOffset": 4644, "endOffset": 4678, "count": 13775086}, {"startOffset": 4678, "endOffset": 4715, "count": 15838471}], "isBlockCoverage": true}, {"functionName": "multiply", "ranges": [{"startOffset": 4867, "endOffset": 5064, "count": 340927}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4939, "endOffset": 5045, "count": 1725954}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4975, "endOffset": 5040, "count": 29613557}], "isBlockCoverage": true}, {"functionName": "shiftLeft", "ranges": [{"startOffset": 5210, "endOffset": 5382, "count": 4668375}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5283, "endOffset": 5363, "count": 80410507}], "isBlockCoverage": true}, {"functionName": "mod", "ranges": [{"startOffset": 5522, "endOffset": 5830, "count": 1106418}, {"startOffset": 5688, "endOffset": 5812, "count": 8145757}, {"startOffset": 5731, "endOffset": 5808, "count": 4473375}], "isBlockCoverage": true}, {"functionName": "modPow", "ranges": [{"startOffset": 6025, "endOffset": 6244, "count": 68211}, {"startOffset": 6074, "endOffset": 6242, "count": 341022}, {"startOffset": 6099, "endOffset": 6149, "count": 68115}, {"startOffset": 6180, "endOffset": 6207, "count": 68211}, {"startOffset": 6207, "endOffset": 6242, "count": 272811}], "isBlockCoverage": true}, {"functionName": "gcd", "ranges": [{"startOffset": 6396, "endOffset": 6518, "count": 68211}, {"startOffset": 6440, "endOffset": 6504, "count": 500780}], "isBlockCoverage": true}, {"functionName": "equals", "ranges": [{"startOffset": 6636, "endOffset": 6798, "count": 68212}, {"startOffset": 6693, "endOffset": 6705, "count": 20935}, {"startOffset": 6705, "endOffset": 6736, "count": 47277}, {"startOffset": 6736, "endOffset": 6782, "count": 47288}, {"startOffset": 6766, "endOffset": 6778, "count": 7441}, {"startOffset": 6782, "endOffset": 6797, "count": 39836}], "isBlockCoverage": true}, {"functionName": "reduceExponent", "ranges": [{"startOffset": 7159, "endOffset": 7317, "count": 68211}], "isBlockCoverage": true}, {"functionName": "isIrreducibleBenOr", "ranges": [{"startOffset": 7673, "endOffset": 7888, "count": 29391}, {"startOffset": 7756, "endOffset": 7872, "count": 68211}, {"startOffset": 7842, "endOffset": 7868, "count": 28376}, {"startOffset": 7872, "endOffset": 7887, "count": 1015}], "isBlockCoverage": true}, {"functionName": "createIrreducible", "ranges": [{"startOffset": 7957, "endOffset": 8066, "count": 10}, {"startOffset": 7984, "endOffset": 8064, "count": 302}, {"startOffset": 8052, "endOffset": 8060, "count": 10}], "isBlockCoverage": true}, {"functionName": "fingerprint", "ranges": [{"startOffset": 8228, "endOffset": 8331, "count": 1500}], "isBlockCoverage": true}, {"functionName": "RabinPolynomialEncoder", "ranges": [{"startOffset": 8442, "endOffset": 8521, "count": 1500}], "isBlockCoverage": true}, {"functionName": "write", "ranges": [{"startOffset": 8560, "endOffset": 8758, "count": 195000}], "isBlockCoverage": true}, {"functionName": "getFingerprint", "ranges": [{"startOffset": 8762, "endOffset": 8881, "count": 1500}], "isBlockCoverage": true}]}, {"scriptId": "166", "url": "file:///home/<USER>/ylabs/lib0/hash/rabin-uncached.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1772, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 395, "endOffset": 646, "count": 1500}], "isBlockCoverage": true}, {"functionName": "add", "ranges": [{"startOffset": 815, "endOffset": 1070, "count": 743454}, {"startOffset": 971, "endOffset": 1066, "count": 5171563}], "isBlockCoverage": true}, {"functionName": "write", "ranges": [{"startOffset": 1112, "endOffset": 1560, "count": 195000}, {"startOffset": 1334, "endOffset": 1452, "count": 1560000}, {"startOffset": 1386, "endOffset": 1446, "count": 743454}], "isBlockCoverage": true}, {"functionName": "getFingerprint", "ranges": [{"startOffset": 1564, "endOffset": 1769, "count": 1500}, {"startOffset": 1681, "endOffset": 1747, "count": 9300}], "isBlockCoverage": true}]}, {"scriptId": "167", "url": "file:///home/<USER>/ylabs/lib0/buffer.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4120, "count": 1}, {"startOffset": 1918, "endOffset": 1935, "count": 0}, {"startOffset": 2013, "endOffset": 2032, "count": 0}], "isBlockCoverage": true}, {"functionName": "createUint8ArrayFromLen", "ranges": [{"startOffset": 389, "endOffset": 415, "count": 81867}], "isBlockCoverage": true}, {"functionName": "createUint8ArrayViewFromArrayBuffer", "ranges": [{"startOffset": 620, "endOffset": 694, "count": 13339}], "isBlockCoverage": true}, {"functionName": "createUint8ArrayFromArrayBuffer", "ranges": [{"startOffset": 839, "endOffset": 871, "count": 1}], "isBlockCoverage": true}, {"functionName": "toBase64<PERSON><PERSON>er", "ranges": [{"startOffset": 976, "endOffset": 1147, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBase64Node", "ranges": [{"startOffset": 1248, "endOffset": 1337, "count": 18141}], "isBlockCoverage": true}, {"functionName": "fromBase64Browser", "ranges": [{"startOffset": 1440, "endOffset": 1646, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromBase64Node", "ranges": [{"startOffset": 1721, "endOffset": 1857, "count": 13339}], "isBlockCoverage": true}, {"functionName": "toBase64UrlEncoded", "ranges": [{"startOffset": 2206, "endOffset": 2288, "count": 6847}], "isBlockCoverage": true}, {"functionName": "fromBase64UrlEncoded", "ranges": [{"startOffset": 2360, "endOffset": 2430, "count": 6850}], "isBlockCoverage": true}, {"functionName": "toHexString", "ranges": [{"startOffset": 2581, "endOffset": 2649, "count": 6448}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2603, "endOffset": 2639, "count": 152446}], "isBlockCoverage": true}, {"functionName": "fromHexString", "ranges": [{"startOffset": 2782, "endOffset": 3012, "count": 6432}, {"startOffset": 2903, "endOffset": 2997, "count": 151902}], "isBlockCoverage": true}, {"functionName": "copyUint8Array", "ranges": [{"startOffset": 3177, "endOffset": 3299, "count": 19761}], "isBlockCoverage": true}, {"functionName": "encodeAny", "ranges": [{"startOffset": 3505, "endOffset": 3575, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3531, "endOffset": 3574, "count": 1}], "isBlockCoverage": true}, {"functionName": "decodeAny", "ranges": [{"startOffset": 3689, "endOffset": 3741, "count": 1}], "isBlockCoverage": true}, {"functionName": "shiftNBitsLeft", "ranges": [{"startOffset": 3933, "endOffset": 4119, "count": 743496}, {"startOffset": 3961, "endOffset": 3970, "count": 92931}, {"startOffset": 3970, "endOffset": 4049, "count": 650565}, {"startOffset": 4049, "endOffset": 4105, "count": 3875331}, {"startOffset": 4105, "endOffset": 4118, "count": 650565}], "isBlockCoverage": true}]}, {"scriptId": "168", "url": "file:///home/<USER>/ylabs/lib0/hash/rabin.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3053, "count": 1}], "isBlockCoverage": true}, {"functionName": "ensureCache", "ranges": [{"startOffset": 872, "endOffset": 1779, "count": 3105}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 946, "endOffset": 1778, "count": 6}, {"startOffset": 1198, "endOffset": 1761, "count": 42}, {"startOffset": 1327, "endOffset": 1757, "count": 1524}, {"startOffset": 1516, "endOffset": 1655, "count": 9652}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1869, "endOffset": 2152, "count": 3105}], "isBlockCoverage": true}, {"functionName": "write", "ranges": [{"startOffset": 2194, "endOffset": 2560, "count": 392811}, {"startOffset": 2431, "endOffset": 2516, "count": 2813622}], "isBlockCoverage": true}, {"functionName": "getFingerprint", "ranges": [{"startOffset": 2564, "endOffset": 2769, "count": 3100}, {"startOffset": 2681, "endOffset": 2747, "count": 18700}], "isBlockCoverage": true}, {"functionName": "fingerprint", "ranges": [{"startOffset": 2871, "endOffset": 3052, "count": 1600}, {"startOffset": 2984, "endOffset": 3016, "count": 197811}], "isBlockCoverage": true}]}, {"scriptId": "169", "url": "file:///home/<USER>/ylabs/lib0/map.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2211, "count": 1}], "isBlockCoverage": true}, {"functionName": "create", "ranges": [{"startOffset": 196, "endOffset": 211, "count": 10}], "isBlockCoverage": true}, {"functionName": "copy", "ranges": [{"startOffset": 365, "endOffset": 444, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 405, "endOffset": 430, "count": 3}], "isBlockCoverage": true}, {"functionName": "setIfUndefined", "ranges": [{"startOffset": 927, "endOffset": 1056, "count": 3218}, {"startOffset": 1002, "endOffset": 1041, "count": 97}], "isBlockCoverage": true}, {"functionName": "map", "ranges": [{"startOffset": 1334, "endOffset": 1443, "count": 2}, {"startOffset": 1395, "endOffset": 1428, "count": 87}], "isBlockCoverage": true}, {"functionName": "any", "ranges": [{"startOffset": 1741, "endOffset": 1856, "count": 3}, {"startOffset": 1785, "endOffset": 1839, "count": 6}, {"startOffset": 1810, "endOffset": 1835, "count": 2}, {"startOffset": 1839, "endOffset": 1855, "count": 1}], "isBlockCoverage": true}, {"functionName": "all", "ranges": [{"startOffset": 2094, "endOffset": 2210, "count": 2}, {"startOffset": 2138, "endOffset": 2194, "count": 5}, {"startOffset": 2164, "endOffset": 2190, "count": 1}, {"startOffset": 2194, "endOffset": 2209, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "170", "url": "file:///home/<USER>/ylabs/lib0/webcrypto.node.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 195, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "171", "url": "node:crypto", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8421, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyWebCrypto", "ranges": [{"startOffset": 3092, "endOffset": 3194, "count": 2}], "isBlockCoverage": true}, {"functionName": "lazyOwnsProcessState", "ranges": [{"startOffset": 3218, "endOffset": 3348, "count": 0}], "isBlockCoverage": false}, {"functionName": "createHash", "ranges": [{"startOffset": 3490, "endOffset": 3572, "count": 20000}], "isBlockCoverage": true}, {"functionName": "createCipheriv", "ranges": [{"startOffset": 3574, "endOffset": 3676, "count": 0}], "isBlockCoverage": false}, {"functionName": "createDecipheriv", "ranges": [{"startOffset": 3678, "endOffset": 3784, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3786, "endOffset": 3942, "count": 0}], "isBlockCoverage": false}, {"functionName": "createDiffieHellmanGroup", "ranges": [{"startOffset": 3944, "endOffset": 4026, "count": 0}], "isBlockCoverage": false}, {"functionName": "createECDH", "ranges": [{"startOffset": 4028, "endOffset": 4084, "count": 0}], "isBlockCoverage": false}, {"functionName": "createHmac", "ranges": [{"startOffset": 4086, "endOffset": 4168, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSign", "ranges": [{"startOffset": 4170, "endOffset": 4252, "count": 0}], "isBlockCoverage": false}, {"functionName": "createVerify", "ranges": [{"startOffset": 4254, "endOffset": 4340, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFips", "ranges": [{"startOffset": 5531, "endOffset": 5616, "count": 0}], "isBlockCoverage": false}, {"functionName": "setFips", "ranges": [{"startOffset": 5618, "endOffset": 5902, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRandomValues", "ranges": [{"startOffset": 5904, "endOffset": 5995, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6073, "endOffset": 6327, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6331, "endOffset": 6520, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRandomBytesAlias", "ranges": [{"startOffset": 6569, "endOffset": 7402, "count": 3}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 6667, "endOffset": 7157, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7163, "endOffset": 7394, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7794, "endOffset": 7834, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 7946, "endOffset": 7993, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8119, "endOffset": 8140, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "172", "url": "node:internal/crypto/random", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16246, "count": 1}], "isBlockCoverage": false}, {"functionName": "assertOffset", "ranges": [{"startOffset": 1260, "endOffset": 1626, "count": 495536}, {"startOffset": 1495, "endOffset": 1577, "count": 0}], "isBlockCoverage": true}, {"functionName": "assertSize", "ranges": [{"startOffset": 1628, "endOffset": 2089, "count": 0}], "isBlockCoverage": false}, {"functionName": "randomBytes", "ranges": [{"startOffset": 2091, "endOffset": 2634, "count": 0}], "isBlockCoverage": false}, {"functionName": "randomFillSync", "ranges": [{"startOffset": 2636, "endOffset": 3304, "count": 495536}, {"startOffset": 2742, "endOffset": 2850, "count": 0}, {"startOffset": 2896, "endOffset": 2900, "count": 0}, {"startOffset": 3033, "endOffset": 3110, "count": 0}, {"startOffset": 3134, "endOffset": 3145, "count": 0}, {"startOffset": 3277, "endOffset": 3287, "count": 0}], "isBlockCoverage": true}, {"functionName": "randomFill", "ranges": [{"startOffset": 3306, "endOffset": 4352, "count": 0}], "isBlockCoverage": false}, {"functionName": "randomInt", "ranges": [{"startOffset": 4902, "endOffset": 7142, "count": 0}], "isBlockCoverage": false}, {"functionName": "asyncRefillRandomIntCache", "ranges": [{"startOffset": 7144, "endOffset": 8020, "count": 0}], "isBlockCoverage": false}, {"functionName": "onJobDone", "ranges": [{"startOffset": 8023, "endOffset": 8185, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRandomValues", "ranges": [{"startOffset": 8417, "endOffset": 9033, "count": 495536}, {"startOffset": 8537, "endOffset": 8844, "count": 0}, {"startOffset": 8876, "endOffset": 8989, "count": 0}], "isBlockCoverage": true}, {"functionName": "getHexBytes", "ranges": [{"startOffset": 9398, "endOffset": 9697, "count": 0}], "isBlockCoverage": false}, {"functionName": "serializeUUID", "ranges": [{"startOffset": 9699, "endOffset": 10425, "count": 0}], "isBlockCoverage": false}, {"functionName": "getBufferedUUID", "ranges": [{"startOffset": 10427, "endOffset": 10730, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUnbufferedUUID", "ranges": [{"startOffset": 10732, "endOffset": 10970, "count": 0}], "isBlockCoverage": false}, {"functionName": "randomUUID", "ranges": [{"startOffset": 10972, "endOffset": 11292, "count": 0}], "isBlockCoverage": false}, {"functionName": "createRandomPrimeJob", "ranges": [{"startOffset": 11294, "endOffset": 12527, "count": 0}], "isBlockCoverage": false}, {"functionName": "generatePrime", "ranges": [{"startOffset": 12529, "endOffset": 12985, "count": 0}], "isBlockCoverage": false}, {"functionName": "generatePrimeSync", "ranges": [{"startOffset": 12987, "endOffset": 13245, "count": 0}], "isBlockCoverage": false}, {"functionName": "numberToHexCharCode", "ranges": [{"startOffset": 13519, "endOffset": 13563, "count": 0}], "isBlockCoverage": false}, {"functionName": "arrayBufferToUnsignedBigInt", "ranges": [{"startOffset": 13638, "endOffset": 14065, "count": 0}], "isBlockCoverage": false}, {"functionName": "unsignedBigIntToBuffer", "ranges": [{"startOffset": 14067, "endOffset": 14364, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkPrime", "ranges": [{"startOffset": 14366, "endOffset": 15259, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkPrimeSync", "ranges": [{"startOffset": 15261, "endOffset": 16059, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "173", "url": "node:internal/crypto/pbkdf2", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2973, "count": 1}], "isBlockCoverage": false}, {"functionName": "pbkdf2", "ranges": [{"startOffset": 487, "endOffset": 1151, "count": 8}, {"startOffset": 595, "endOffset": 647, "count": 0}], "isBlockCoverage": true}, {"functionName": "job.ondone", "ranges": [{"startOffset": 932, "endOffset": 1134, "count": 8}, {"startOffset": 984, "endOffset": 1033, "count": 0}], "isBlockCoverage": true}, {"functionName": "pbkdf2Sync", "ranges": [{"startOffset": 1153, "endOffset": 1558, "count": 0}], "isBlockCoverage": false}, {"functionName": "check", "ranges": [{"startOffset": 1560, "endOffset": 2031, "count": 8}], "isBlockCoverage": true}, {"functionName": "pbkdf2DeriveBits", "ranges": [{"startOffset": 2074, "endOffset": 2905, "count": 8}, {"startOffset": 2212, "endOffset": 2295, "count": 0}, {"startOffset": 2321, "endOffset": 2347, "count": 0}, {"startOffset": 2375, "endOffset": 2441, "count": 0}, {"startOffset": 2460, "endOffset": 2558, "count": 0}, {"startOffset": 2723, "endOffset": 2878, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "174", "url": "node:internal/crypto/util", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16325, "count": 1}], "isBlockCoverage": false}, {"functionName": "to<PERSON>uf", "ranges": [{"startOffset": 1942, "endOffset": 2121, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHashCache", "ranges": [{"startOffset": 2139, "endOffset": 2408, "count": 40000}, {"startOffset": 2197, "endOffset": 2385, "count": 1}, {"startOffset": 2266, "endOffset": 2381, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2340, "endOffset": 2373, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCachedHashId", "ranges": [{"startOffset": 2410, "endOffset": 2538, "count": 20000}, {"startOffset": 2522, "endOffset": 2526, "count": 1}, {"startOffset": 2527, "endOffset": 2535, "count": 19999}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2572, "endOffset": 2615, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2649, "endOffset": 2691, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2725, "endOffset": 2767, "count": 0}], "isBlockCoverage": false}, {"functionName": "setEngine", "ranges": [{"startOffset": 2771, "endOffset": 3178, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3225, "endOffset": 3710, "count": 33}, {"startOffset": 3291, "endOffset": 3305, "count": 8}, {"startOffset": 3305, "endOffset": 3340, "count": 25}, {"startOffset": 3340, "endOffset": 3443, "count": 0}, {"startOffset": 3443, "endOffset": 3478, "count": 25}, {"startOffset": 3478, "endOffset": 3691, "count": 0}, {"startOffset": 3691, "endOffset": 3709, "count": 25}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 6657, "endOffset": 6766, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateMax<PERSON><PERSON>er<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 7574, "endOffset": 7787, "count": 44027}, {"startOffset": 7663, "endOffset": 7785, "count": 0}], "isBlockCoverage": true}, {"functionName": "normalizeAlgorithm", "ranges": [{"startOffset": 8013, "endOffset": 10448, "count": 82158}, {"startOffset": 8099, "endOffset": 8150, "count": 40045}, {"startOffset": 8150, "endOffset": 8534, "count": 42113}, {"startOffset": 8534, "endOffset": 8799, "count": 169022}, {"startOffset": 8603, "endOffset": 8626, "count": 0}, {"startOffset": 8720, "endOffset": 8795, "count": 42113}, {"startOffset": 8799, "endOffset": 8837, "count": 42113}, {"startOffset": 8837, "endOffset": 8912, "count": 0}, {"startOffset": 8912, "endOffset": 9015, "count": 42113}, {"startOffset": 9015, "endOffset": 9040, "count": 40062}, {"startOffset": 9040, "endOffset": 9353, "count": 2051}, {"startOffset": 9353, "endOffset": 9371, "count": 2043}, {"startOffset": 9372, "endOffset": 9376, "count": 8}, {"startOffset": 9422, "endOffset": 10415, "count": 4046}, {"startOffset": 9516, "endOffset": 9525, "count": 0}, {"startOffset": 9655, "endOffset": 9666, "count": 4023}, {"startOffset": 9668, "endOffset": 10080, "count": 2014}, {"startOffset": 9865, "endOffset": 9875, "count": 0}, {"startOffset": 9940, "endOffset": 9943, "count": 0}, {"startOffset": 10008, "endOffset": 10053, "count": 0}, {"startOffset": 10080, "endOffset": 10411, "count": 2032}, {"startOffset": 10129, "endOffset": 10212, "count": 23}, {"startOffset": 10212, "endOffset": 10411, "count": 2009}, {"startOffset": 10257, "endOffset": 10411, "count": 0}, {"startOffset": 10415, "endOffset": 10447, "count": 2051}], "isBlockCoverage": true}, {"functionName": "getDataViewOrTypedArrayBuffer", "ranges": [{"startOffset": 10450, "endOffset": 10589, "count": 46071}, {"startOffset": 10517, "endOffset": 10552, "count": 0}], "isBlockCoverage": true}, {"functionName": "getDataViewOrTypedArrayByteOffset", "ranges": [{"startOffset": 10591, "endOffset": 10742, "count": 2014}, {"startOffset": 10662, "endOffset": 10701, "count": 0}], "isBlockCoverage": true}, {"functionName": "getDataViewOrTypedArrayByteLength", "ranges": [{"startOffset": 10744, "endOffset": 10895, "count": 2014}, {"startOffset": 10815, "endOffset": 10854, "count": 0}], "isBlockCoverage": true}, {"functionName": "hasAnyNotIn", "ranges": [{"startOffset": 10897, "endOffset": 11036, "count": 45}, {"startOffset": 10960, "endOffset": 11018, "count": 63}, {"startOffset": 11006, "endOffset": 11018, "count": 0}], "isBlockCoverage": true}, {"functionName": "validateBitLength", "ranges": [{"startOffset": 11038, "endOffset": 11380, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateByteLength", "ranges": [{"startOffset": 11382, "endOffset": 11578, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11623, "endOffset": 11913, "count": 0}], "isBlockCoverage": false}, {"functionName": "onDone", "ranges": [{"startOffset": 11917, "endOffset": 12149, "count": 42030}, {"startOffset": 11976, "endOffset": 12128, "count": 1}, {"startOffset": 12128, "endOffset": 12148, "count": 42029}], "isBlockCoverage": true}, {"functionName": "jobPromise", "ranges": [{"startOffset": 12151, "endOffset": 12421, "count": 42030}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12202, "endOffset": 12417, "count": 42030}, {"startOffset": 12358, "endOffset": 12413, "count": 0}], "isBlockCoverage": true}, {"functionName": "bigIntArrayToUnsignedInt", "ranges": [{"startOffset": 12957, "endOffset": 13237, "count": 4}, {"startOffset": 13060, "endOffset": 13217, "count": 12}, {"startOffset": 13131, "endOffset": 13142, "count": 0}, {"startOffset": 13150, "endOffset": 13157, "count": 0}], "isBlockCoverage": true}, {"functionName": "bigIntArrayToUnsignedBigInt", "ranges": [{"startOffset": 13239, "endOffset": 13475, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStringOption", "ranges": [{"startOffset": 13477, "endOffset": 13642, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUsagesUnion", "ranges": [{"startOffset": 13644, "endOffset": 13856, "count": 16}, {"startOffset": 13754, "endOffset": 13837, "count": 24}, {"startOffset": 13795, "endOffset": 13833, "count": 16}], "isBlockCoverage": true}, {"functionName": "getBlockSize", "ranges": [{"startOffset": 13858, "endOffset": 14070, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDigestSizeInBytes", "ranges": [{"startOffset": 14072, "endOffset": 14255, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateKeyOps", "ranges": [{"startOffset": 14394, "endOffset": 15360, "count": 11}, {"startOffset": 14467, "endOffset": 14474, "count": 0}, {"startOffset": 14578, "endOffset": 15118, "count": 12}, {"startOffset": 14708, "endOffset": 14717, "count": 0}, {"startOffset": 14801, "endOffset": 14864, "count": 0}, {"startOffset": 15186, "endOffset": 15354, "count": 12}, {"startOffset": 15236, "endOffset": 15348, "count": 0}], "isBlockCoverage": true}, {"functionName": "secureHeapUsed", "ranges": [{"startOffset": 15362, "endOffset": 15745, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "175", "url": "node:internal/crypto/hashnames", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2502, "count": 1}], "isBlockCoverage": false}, {"functionName": "normalizeHashName", "ranges": [{"startOffset": 1890, "endOffset": 2115, "count": 40040}, {"startOffset": 1989, "endOffset": 2001, "count": 0}, {"startOffset": 2105, "endOffset": 2112, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "176", "url": "node:internal/crypto/scrypt", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3331, "count": 1}], "isBlockCoverage": false}, {"functionName": "scrypt", "ranges": [{"startOffset": 626, "endOffset": 1292, "count": 0}], "isBlockCoverage": false}, {"functionName": "scryptSync", "ranges": [{"startOffset": 1294, "endOffset": 1700, "count": 0}], "isBlockCoverage": false}, {"functionName": "check", "ranges": [{"startOffset": 1702, "endOffset": 3283, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "177", "url": "node:internal/crypto/hkdf", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3376, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 831, "endOffset": 1382, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1386, "endOffset": 1814, "count": 0}], "isBlockCoverage": false}, {"functionName": "hkdf", "ranges": [{"startOffset": 1816, "endOffset": 2282, "count": 0}], "isBlockCoverage": false}, {"functionName": "hkdfSync", "ranges": [{"startOffset": 2284, "endOffset": 2622, "count": 0}], "isBlockCoverage": false}, {"functionName": "hkdfDeriveBits", "ranges": [{"startOffset": 2661, "endOffset": 3314, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "178", "url": "node:internal/crypto/keys", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27403, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2385, "endOffset": 9425, "count": 1}], "isBlockCoverage": true}, {"functionName": "KeyObject", "ranges": [{"startOffset": 2495, "endOffset": 3037, "count": 45}, {"startOffset": 2551, "endOffset": 2571, "count": 27}, {"startOffset": 2572, "endOffset": 2593, "count": 13}, {"startOffset": 2603, "endOffset": 2649, "count": 0}, {"startOffset": 2736, "endOffset": 2795, "count": 0}], "isBlockCoverage": true}, {"functionName": "get type", "ranges": [{"startOffset": 3043, "endOffset": 3090, "count": 51}], "isBlockCoverage": true}, {"functionName": "from", "ranges": [{"startOffset": 3103, "endOffset": 3244, "count": 0}], "isBlockCoverage": false}, {"functionName": "equals", "ranges": [{"startOffset": 3250, "endOffset": 3534, "count": 0}], "isBlockCoverage": false}, {"functionName": "SecretKeyObject", "ranges": [{"startOffset": 3767, "endOffset": 3825, "count": 18}], "isBlockCoverage": true}, {"functionName": "get symmetricKeySize", "ranges": [{"startOffset": 3831, "endOffset": 3911, "count": 0}], "isBlockCoverage": false}, {"functionName": "export", "ranges": [{"startOffset": 3917, "endOffset": 4265, "count": 10}, {"startOffset": 3968, "endOffset": 4222, "count": 0}], "isBlockCoverage": true}, {"functionName": "toCryptoKey", "ranges": [{"startOffset": 4271, "endOffset": 5708, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalizeKeyDetails", "ranges": [{"startOffset": 5841, "endOffset": 6110, "count": 0}], "isBlockCoverage": false}, {"functionName": "AsymmetricKeyObject", "ranges": [{"startOffset": 6219, "endOffset": 6279, "count": 27}], "isBlockCoverage": true}, {"functionName": "get asymmetricKeyType", "ranges": [{"startOffset": 6285, "endOffset": 6396, "count": 11}], "isBlockCoverage": true}, {"functionName": "get asymmetricKeyDetails", "ranges": [{"startOffset": 6402, "endOffset": 6729, "count": 0}], "isBlockCoverage": false}, {"functionName": "toCryptoKey", "ranges": [{"startOffset": 6735, "endOffset": 8281, "count": 0}], "isBlockCoverage": false}, {"functionName": "PublicKeyObject", "ranges": [{"startOffset": 8345, "endOffset": 8403, "count": 14}], "isBlockCoverage": true}, {"functionName": "export", "ranges": [{"startOffset": 8409, "endOffset": 8700, "count": 0}], "isBlockCoverage": false}, {"functionName": "PrivateKeyObject", "ranges": [{"startOffset": 8765, "endOffset": 8824, "count": 13}], "isBlockCoverage": true}, {"functionName": "export", "ranges": [{"startOffset": 8830, "endOffset": 9344, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseKeyFormat", "ranges": [{"startOffset": 9429, "endOffset": 9814, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseKeyType", "ranges": [{"startOffset": 9816, "endOffset": 10668, "count": 0}], "isBlockCoverage": false}, {"functionName": "option", "ranges": [{"startOffset": 10670, "endOffset": 10791, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseKeyFormatAndType", "ranges": [{"startOffset": 10793, "endOffset": 11493, "count": 0}], "isBlockCoverage": false}, {"functionName": "isStringOrBuffer", "ranges": [{"startOffset": 11495, "endOffset": 11632, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseKeyEncoding", "ranges": [{"startOffset": 11634, "endOffset": 12992, "count": 0}], "isBlockCoverage": false}, {"functionName": "parsePublicKeyEncoding", "ranges": [{"startOffset": 13191, "endOffset": 13327, "count": 0}], "isBlockCoverage": false}, {"functionName": "parsePrivateKeyEncoding", "ranges": [{"startOffset": 13527, "endOffset": 13643, "count": 0}], "isBlockCoverage": false}, {"functionName": "getKeyObjectHandle", "ranges": [{"startOffset": 13645, "endOffset": 14223, "count": 0}], "isBlockCoverage": false}, {"functionName": "getKeyTypes", "ranges": [{"startOffset": 14225, "endOffset": 14730, "count": 0}], "isBlockCoverage": false}, {"functionName": "getKeyObjectHandleFromJwk", "ranges": [{"startOffset": 14732, "endOffset": 17281, "count": 0}], "isBlockCoverage": false}, {"functionName": "prepareAsymmetricKey", "ranges": [{"startOffset": 17283, "endOffset": 18826, "count": 0}], "isBlockCoverage": false}, {"functionName": "preparePrivateKey", "ranges": [{"startOffset": 18828, "endOffset": 18916, "count": 0}], "isBlockCoverage": false}, {"functionName": "preparePublicOrPrivateKey", "ranges": [{"startOffset": 18918, "endOffset": 19013, "count": 0}], "isBlockCoverage": false}, {"functionName": "prepareSecret<PERSON>ey", "ranges": [{"startOffset": 19015, "endOffset": 19713, "count": 17}, {"startOffset": 19097, "endOffset": 19451, "count": 0}, {"startOffset": 19515, "endOffset": 19546, "count": 8}, {"startOffset": 19548, "endOffset": 19658, "count": 0}], "isBlockCoverage": true}, {"functionName": "createSecretKey", "ranges": [{"startOffset": 19715, "endOffset": 19919, "count": 17}], "isBlockCoverage": true}, {"functionName": "createPublicKey", "ranges": [{"startOffset": 19921, "endOffset": 20258, "count": 0}], "isBlockCoverage": false}, {"functionName": "createPrivateKey", "ranges": [{"startOffset": 20260, "endOffset": 20601, "count": 0}], "isBlockCoverage": false}, {"functionName": "isKeyObject", "ranges": [{"startOffset": 20603, "endOffset": 20685, "count": 0}], "isBlockCoverage": false}, {"functionName": "CryptoKey", "ranges": [{"startOffset": 21108, "endOffset": 21168, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21172, "endOffset": 21521, "count": 0}], "isBlockCoverage": false}, {"functionName": "get type", "ranges": [{"startOffset": 21525, "endOffset": 21660, "count": 51}, {"startOffset": 21582, "endOffset": 21622, "count": 0}], "isBlockCoverage": true}, {"functionName": "get extractable", "ranges": [{"startOffset": 21664, "endOffset": 21803, "count": 34}, {"startOffset": 21728, "endOffset": 21768, "count": 0}], "isBlockCoverage": true}, {"functionName": "get algorithm", "ranges": [{"startOffset": 21807, "endOffset": 21942, "count": 4065}, {"startOffset": 21869, "endOffset": 21909, "count": 0}], "isBlockCoverage": true}, {"functionName": "get usages", "ranges": [{"startOffset": 21946, "endOffset": 22078, "count": 2085}, {"startOffset": 22005, "endOffset": 22045, "count": 0}], "isBlockCoverage": true}, {"functionName": "defineCryptoKeyProperties", "ranges": [{"startOffset": 22531, "endOffset": 23434, "count": 45}], "isBlockCoverage": true}, {"functionName": "InternalCryptoKey", "ranges": [{"startOffset": 23730, "endOffset": 24096, "count": 45}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24100, "endOffset": 24461, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 24465, "endOffset": 24612, "count": 0}], "isBlockCoverage": false}, {"functionName": "isCryptoKey", "ranges": [{"startOffset": 24741, "endOffset": 24825, "count": 0}], "isBlockCoverage": false}, {"functionName": "importGenericSecretKey", "ranges": [{"startOffset": 24827, "endOffset": 26926, "count": 8}, {"startOffset": 24998, "endOffset": 25072, "count": 0}, {"startOffset": 25133, "endOffset": 25237, "count": 0}, {"startOffset": 25263, "endOffset": 26013, "count": 0}, {"startOffset": 26095, "endOffset": 26215, "count": 0}, {"startOffset": 26574, "endOffset": 26599, "count": 0}, {"startOffset": 26601, "endOffset": 26677, "count": 0}, {"startOffset": 26813, "endOffset": 26925, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "179", "url": "node:internal/crypto/keygen", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10712, "count": 1}], "isBlockCoverage": false}, {"functionName": "isJwk", "ranges": [{"startOffset": 1183, "endOffset": 1253, "count": 16}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1255, "endOffset": 1409, "count": 16}, {"startOffset": 1372, "endOffset": 1383, "count": 0}], "isBlockCoverage": true}, {"functionName": "generateKeyPair", "ranges": [{"startOffset": 1411, "endOffset": 2047, "count": 8}, {"startOffset": 1500, "endOffset": 1554, "count": 0}], "isBlockCoverage": true}, {"functionName": "job.ondone", "ranges": [{"startOffset": 1671, "endOffset": 2030, "count": 8}, {"startOffset": 1707, "endOffset": 1758, "count": 0}], "isBlockCoverage": true}, {"functionName": "generateKeyPairSync", "ranges": [{"startOffset": 2193, "endOffset": 2310, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleError", "ranges": [{"startOffset": 2312, "endOffset": 2690, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseKeyEncoding", "ranges": [{"startOffset": 2692, "endOffset": 3942, "count": 8}, {"startOffset": 2928, "endOffset": 3286, "count": 0}, {"startOffset": 3426, "endOffset": 3825, "count": 0}], "isBlockCoverage": true}, {"functionName": "createJob", "ranges": [{"startOffset": 3944, "endOffset": 9232, "count": 8}, {"startOffset": 4187, "endOffset": 4198, "count": 4}, {"startOffset": 4203, "endOffset": 6292, "count": 4}, {"startOffset": 4445, "endOffset": 4488, "count": 0}, {"startOffset": 4802, "endOffset": 6204, "count": 0}, {"startOffset": 6232, "endOffset": 6243, "count": 0}, {"startOffset": 6297, "endOffset": 6765, "count": 0}, {"startOffset": 6770, "endOffset": 7379, "count": 4}, {"startOffset": 6994, "endOffset": 7022, "count": 0}, {"startOffset": 7071, "endOffset": 7259, "count": 0}, {"startOffset": 7384, "endOffset": 7399, "count": 0}, {"startOffset": 7404, "endOffset": 7417, "count": 0}, {"startOffset": 7422, "endOffset": 7436, "count": 0}, {"startOffset": 7441, "endOffset": 7851, "count": 0}, {"startOffset": 7856, "endOffset": 9110, "count": 0}, {"startOffset": 9115, "endOffset": 9123, "count": 0}, {"startOffset": 9149, "endOffset": 9231, "count": 0}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>ob", "ranges": [{"startOffset": 9263, "endOffset": 9800, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleGenerateKeyError", "ranges": [{"startOffset": 9802, "endOffset": 10004, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON>ey", "ranges": [{"startOffset": 10006, "endOffset": 10476, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateKeySync", "ranges": [{"startOffset": 10478, "endOffset": 10612, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "180", "url": "node:internal/crypto/diffie<PERSON>man", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10104, "count": 1}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1246, "endOffset": 3050, "count": 0}], "isBlockCoverage": false}, {"functionName": "DiffieHellmanGroup", "ranges": [{"startOffset": 3053, "endOffset": 3379, "count": 0}], "isBlockCoverage": false}, {"functionName": "dhGenerateKeys", "ranges": [{"startOffset": 3490, "endOffset": 3605, "count": 0}], "isBlockCoverage": false}, {"functionName": "dhComputeSecret", "ranges": [{"startOffset": 3719, "endOffset": 3977, "count": 0}], "isBlockCoverage": false}, {"functionName": "dhGetPrime", "ranges": [{"startOffset": 4076, "endOffset": 4185, "count": 0}], "isBlockCoverage": false}, {"functionName": "dhGetGenerator", "ranges": [{"startOffset": 4296, "endOffset": 4421, "count": 0}], "isBlockCoverage": false}, {"functionName": "dhGetPublicKey", "ranges": [{"startOffset": 4532, "endOffset": 4645, "count": 0}], "isBlockCoverage": false}, {"functionName": "dhGetPrivateKey", "ranges": [{"startOffset": 4759, "endOffset": 4874, "count": 0}], "isBlockCoverage": false}, {"functionName": "setPublicKey", "ranges": [{"startOffset": 4916, "endOffset": 5058, "count": 0}], "isBlockCoverage": false}, {"functionName": "setPrivateKey", "ranges": [{"startOffset": 5102, "endOffset": 5246, "count": 0}], "isBlockCoverage": false}, {"functionName": "ECDH", "ranges": [{"startOffset": 5250, "endOffset": 5404, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateKeys", "ranges": [{"startOffset": 5715, "endOffset": 5837, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPublicKey", "ranges": [{"startOffset": 5870, "endOffset": 6021, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 6042, "endOffset": 6308, "count": 0}], "isBlockCoverage": false}, {"functionName": "encode", "ranges": [{"startOffset": 6311, "endOffset": 6446, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFormat", "ranges": [{"startOffset": 6448, "endOffset": 6771, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 6845, "endOffset": 7913, "count": 0}], "isBlockCoverage": false}, {"functionName": "ecdhDeriveBits", "ranges": [{"startOffset": 8044, "endOffset": 9998, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "181", "url": "node:internal/crypto/cipher", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9762, "count": 1}], "isBlockCoverage": false}, {"functionName": "rsaFunctionFor", "ranges": [{"startOffset": 1233, "endOffset": 1981, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1301, "endOffset": 1978, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDecoder", "ranges": [{"startOffset": 2482, "endOffset": 2839, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUIntOption", "ranges": [{"startOffset": 2841, "endOffset": 3075, "count": 0}], "isBlockCoverage": false}, {"functionName": "createCipherBase", "ranges": [{"startOffset": 3077, "endOffset": 3496, "count": 0}], "isBlockCoverage": false}, {"functionName": "createCipherWithIV", "ranges": [{"startOffset": 3498, "endOffset": 3837, "count": 0}], "isBlockCoverage": false}, {"functionName": "Cipher", "ranges": [{"startOffset": 4080, "endOffset": 4209, "count": 0}], "isBlockCoverage": false}, {"functionName": "_transform", "ranges": [{"startOffset": 4352, "endOffset": 4468, "count": 0}], "isBlockCoverage": false}, {"functionName": "_flush", "ranges": [{"startOffset": 4497, "endOffset": 4635, "count": 0}], "isBlockCoverage": false}, {"functionName": "update", "ranges": [{"startOffset": 4664, "endOffset": 5180, "count": 0}], "isBlockCoverage": false}, {"functionName": "final", "ranges": [{"startOffset": 5209, "endOffset": 5453, "count": 0}], "isBlockCoverage": false}, {"functionName": "setAutoPadding", "ranges": [{"startOffset": 5491, "endOffset": 5638, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAuthTag", "ranges": [{"startOffset": 5671, "endOffset": 5831, "count": 0}], "isBlockCoverage": false}, {"functionName": "setAuthTag", "ranges": [{"startOffset": 5835, "endOffset": 6047, "count": 0}], "isBlockCoverage": false}, {"functionName": "setAAD", "ranges": [{"startOffset": 6075, "endOffset": 6417, "count": 0}], "isBlockCoverage": false}, {"functionName": "Cipheriv", "ranges": [{"startOffset": 6665, "endOffset": 6875, "count": 0}], "isBlockCoverage": false}, {"functionName": "addCipherPrototypeFunctions", "ranges": [{"startOffset": 6877, "endOffset": 7468, "count": 3}, {"startOffset": 7273, "endOffset": 7346, "count": 1}, {"startOffset": 7346, "endOffset": 7408, "count": 2}], "isBlockCoverage": true}, {"functionName": "Decipher", "ranges": [{"startOffset": 7869, "endOffset": 8004, "count": 0}], "isBlockCoverage": false}, {"functionName": "Decipheriv", "ranges": [{"startOffset": 8409, "endOffset": 8626, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCipherInfo", "ranges": [{"startOffset": 8788, "endOffset": 9625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "182", "url": "node:internal/streams/lazy_transform", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1386, "count": 1}], "isBlockCoverage": false}, {"functionName": "LazyTransform", "ranges": [{"startOffset": 401, "endOffset": 463, "count": 20000}], "isBlockCoverage": true}, {"functionName": "makeGetter", "ranges": [{"startOffset": 595, "endOffset": 769, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 632, "endOffset": 766, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeSetter", "ranges": [{"startOffset": 771, "endOffset": 990, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 808, "endOffset": 987, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "183", "url": "node:stream", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5095, "count": 1}], "isBlockCoverage": false}, {"functionName": "fn", "ranges": [{"startOffset": 2552, "endOffset": 2712, "count": 0}], "isBlockCoverage": false}, {"functionName": "fn", "ranges": [{"startOffset": 3214, "endOffset": 3352, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4400, "endOffset": 4432, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4530, "endOffset": 4571, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4664, "endOffset": 4705, "count": 0}], "isBlockCoverage": false}, {"functionName": "_uint8ArrayToBuffer", "ranges": [{"startOffset": 4886, "endOffset": 5093, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "184", "url": "node:internal/streams/operators", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10140, "count": 1}], "isBlockCoverage": false}, {"functionName": "compose", "ranges": [{"startOffset": 971, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "map", "ranges": [{"startOffset": 1553, "endOffset": 4656, "count": 0}], "isBlockCoverage": false}, {"functionName": "some", "ranges": [{"startOffset": 4658, "endOffset": 4806, "count": 0}], "isBlockCoverage": false}, {"functionName": "every", "ranges": [{"startOffset": 4808, "endOffset": 5136, "count": 0}], "isBlockCoverage": false}, {"functionName": "find", "ranges": [{"startOffset": 5138, "endOffset": 5280, "count": 0}], "isBlockCoverage": false}, {"functionName": "for<PERSON>ach", "ranges": [{"startOffset": 5282, "endOffset": 5652, "count": 0}], "isBlockCoverage": false}, {"functionName": "filter", "ranges": [{"startOffset": 5654, "endOffset": 5981, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReduceAwareErrMissingArgs", "ranges": [{"startOffset": 6197, "endOffset": 6311, "count": 0}], "isBlockCoverage": false}, {"functionName": "reduce", "ranges": [{"startOffset": 6315, "endOffset": 7757, "count": 0}], "isBlockCoverage": false}, {"functionName": "toArray", "ranges": [{"startOffset": 7759, "endOffset": 8188, "count": 0}], "isBlockCoverage": false}, {"functionName": "flatMap", "ranges": [{"startOffset": 8190, "endOffset": 8384, "count": 0}], "isBlockCoverage": false}, {"functionName": "toIntegerOrInfinity", "ranges": [{"startOffset": 8386, "endOffset": 8708, "count": 0}], "isBlockCoverage": false}, {"functionName": "drop", "ranges": [{"startOffset": 8710, "endOffset": 9258, "count": 0}], "isBlockCoverage": false}, {"functionName": "take", "ranges": [{"startOffset": 9260, "endOffset": 9930, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "185", "url": "node:internal/abort_controller", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15245, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyMessageChannel", "ranges": [{"startOffset": 1746, "endOffset": 1879, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2023, "endOffset": 2272, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2386, "endOffset": 2799, "count": 0}], "isBlockCoverage": false}, {"functionName": "customInspect", "ranges": [{"startOffset": 3154, "endOffset": 3413, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateThisAbortSignal", "ranges": [{"startOffset": 3415, "endOffset": 3540, "count": 0}], "isBlockCoverage": false}, {"functionName": "setWeakAbortSignalTimeout", "ranges": [{"startOffset": 4122, "endOffset": 4522, "count": 0}], "isBlockCoverage": false}, {"functionName": "AbortSignal", "ranges": [{"startOffset": 4782, "endOffset": 5293, "count": 0}], "isBlockCoverage": false}, {"functionName": "get aborted", "ranges": [{"startOffset": 5330, "endOffset": 5413, "count": 0}], "isBlockCoverage": false}, {"functionName": "get reason", "ranges": [{"startOffset": 5446, "endOffset": 5525, "count": 0}], "isBlockCoverage": false}, {"functionName": "throwIfAborted", "ranges": [{"startOffset": 5529, "endOffset": 5645, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5649, "endOffset": 5778, "count": 0}], "isBlockCoverage": false}, {"functionName": "abort", "ranges": [{"startOffset": 5856, "endOffset": 6016, "count": 0}], "isBlockCoverage": false}, {"functionName": "timeout", "ranges": [{"startOffset": 6094, "endOffset": 6374, "count": 0}], "isBlockCoverage": false}, {"functionName": "any", "ranges": [{"startOffset": 6461, "endOffset": 8487, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8491, "endOffset": 9259, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9263, "endOffset": 9617, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9621, "endOffset": 10228, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10232, "endOffset": 10484, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10488, "endOffset": 10936, "count": 0}], "isBlockCoverage": false}, {"functionName": "ClonedAbortSignal", "ranges": [{"startOffset": 10940, "endOffset": 11040, "count": 0}], "isBlockCoverage": false}, {"functionName": "ClonedAbortSignal.<computed>", "ranges": [{"startOffset": 11085, "endOffset": 11093, "count": 0}], "isBlockCoverage": false}, {"functionName": "abortSignal", "ranges": [{"startOffset": 11463, "endOffset": 12741, "count": 0}], "isBlockCoverage": false}, {"functionName": "runAbort", "ranges": [{"startOffset": 12795, "endOffset": 12923, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 12947, "endOffset": 13626, "count": 0}], "isBlockCoverage": false}, {"functionName": "transferableAbortSignal", "ranges": [{"startOffset": 13774, "endOffset": 13985, "count": 0}], "isBlockCoverage": false}, {"functionName": "transferableAbortController", "ranges": [{"startOffset": 14057, "endOffset": 14146, "count": 0}], "isBlockCoverage": false}, {"functionName": "aborted", "ranges": [{"startOffset": 14240, "endOffset": 14796, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "186", "url": "node:internal/streams/end-of-stream", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8484, "count": 1}], "isBlockCoverage": false}, {"functionName": "isRequest", "ranges": [{"startOffset": 883, "endOffset": 978, "count": 0}], "isBlockCoverage": false}, {"functionName": "nop", "ranges": [{"startOffset": 992, "endOffset": 1000, "count": 0}], "isBlockCoverage": false}, {"functionName": "eos", "ranges": [{"startOffset": 1003, "endOffset": 7053, "count": 0}], "isBlockCoverage": false}, {"functionName": "eosWeb", "ranges": [{"startOffset": 7055, "endOffset": 7964, "count": 0}], "isBlockCoverage": false}, {"functionName": "finished", "ranges": [{"startOffset": 7966, "endOffset": 8424, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "187", "url": "node:internal/streams/compose", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5511, "count": 1}], "isBlockCoverage": false}, {"functionName": "compose", "ranges": [{"startOffset": 592, "endOffset": 5509, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "188", "url": "node:internal/streams/pipeline", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12467, "count": 1}], "isBlockCoverage": false}, {"functionName": "destroyer", "ranges": [{"startOffset": 1056, "endOffset": 1485, "count": 0}], "isBlockCoverage": false}, {"functionName": "popCallback", "ranges": [{"startOffset": 1487, "endOffset": 1815, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeAsyncIterable", "ranges": [{"startOffset": 1817, "endOffset": 2103, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromReadable", "ranges": [{"startOffset": 2105, "endOffset": 2255, "count": 0}], "isBlockCoverage": false}, {"functionName": "pumpToNode", "ranges": [{"startOffset": 2257, "endOffset": 3301, "count": 0}], "isBlockCoverage": false}, {"functionName": "pumpToWeb", "ranges": [{"startOffset": 3303, "endOffset": 3901, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipeline", "ranges": [{"startOffset": 3903, "endOffset": 3996, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipelineImpl", "ranges": [{"startOffset": 3998, "endOffset": 10588, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipe", "ranges": [{"startOffset": 10590, "endOffset": 12420, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "189", "url": "node:internal/streams/destroy", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7355, "count": 1}], "isBlockCoverage": false}, {"functionName": "checkError", "ranges": [{"startOffset": 488, "endOffset": 799, "count": 0}], "isBlockCoverage": false}, {"functionName": "destroy", "ranges": [{"startOffset": 909, "endOffset": 1786, "count": 0}], "isBlockCoverage": false}, {"functionName": "_destroy", "ranges": [{"startOffset": 1788, "endOffset": 2412, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitErrorCloseNT", "ranges": [{"startOffset": 2414, "endOffset": 2501, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitCloseNT", "ranges": [{"startOffset": 2503, "endOffset": 2823, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitErrorNT", "ranges": [{"startOffset": 2825, "endOffset": 3172, "count": 0}], "isBlockCoverage": false}, {"functionName": "undestroy", "ranges": [{"startOffset": 3174, "endOffset": 3864, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3866, "endOffset": 5005, "count": 0}], "isBlockCoverage": false}, {"functionName": "construct", "ranges": [{"startOffset": 5007, "endOffset": 5423, "count": 0}], "isBlockCoverage": false}, {"functionName": "constructNT", "ranges": [{"startOffset": 5425, "endOffset": 6164, "count": 0}], "isBlockCoverage": false}, {"functionName": "isRequest", "ranges": [{"startOffset": 6166, "endOffset": 6262, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitCloseLegacy", "ranges": [{"startOffset": 6264, "endOffset": 6324, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitErrorCloseLegacy", "ranges": [{"startOffset": 6326, "endOffset": 6446, "count": 0}], "isBlockCoverage": false}, {"functionName": "destroyer", "ranges": [{"startOffset": 6481, "endOffset": 7263, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "190", "url": "node:internal/streams/duplex", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6529, "count": 1}], "isBlockCoverage": false}, {"functionName": "Duplex", "ranges": [{"startOffset": 2315, "endOffset": 4278, "count": 2}, {"startOffset": 2379, "endOffset": 2406, "count": 0}, {"startOffset": 3267, "endOffset": 3442, "count": 0}, {"startOffset": 3494, "endOffset": 3520, "count": 0}, {"startOffset": 3573, "endOffset": 3601, "count": 0}, {"startOffset": 3655, "endOffset": 3685, "count": 0}, {"startOffset": 3740, "endOffset": 3772, "count": 0}, {"startOffset": 3825, "endOffset": 3853, "count": 0}, {"startOffset": 3910, "endOffset": 3946, "count": 0}, {"startOffset": 3978, "endOffset": 4015, "count": 0}, {"startOffset": 4019, "endOffset": 4061, "count": 0}, {"startOffset": 4125, "endOffset": 4276, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4159, "endOffset": 4270, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5426, "endOffset": 5638, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 5644, "endOffset": 5905, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyWebStreams", "ranges": [{"startOffset": 5979, "endOffset": 6143, "count": 0}], "isBlockCoverage": false}, {"functionName": "Duplex.fromWeb", "ranges": [{"startOffset": 6162, "endOffset": 6280, "count": 0}], "isBlockCoverage": false}, {"functionName": "Duplex.toWeb", "ranges": [{"startOffset": 6298, "endOffset": 6387, "count": 0}], "isBlockCoverage": false}, {"functionName": "Duplex.from", "ranges": [{"startOffset": 6420, "endOffset": 6527, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "191", "url": "node:internal/streams/legacy", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3251, "count": 1}], "isBlockCoverage": false}, {"functionName": "Stream", "ranges": [{"startOffset": 130, "endOffset": 178, "count": 2}], "isBlockCoverage": true}, {"functionName": "Stream.pipe", "ranges": [{"startOffset": 292, "endOffset": 2094, "count": 0}], "isBlockCoverage": false}, {"functionName": "eventNames", "ranges": [{"startOffset": 2127, "endOffset": 2393, "count": 0}], "isBlockCoverage": false}, {"functionName": "prependListener", "ranges": [{"startOffset": 2396, "endOffset": 3203, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "192", "url": "node:internal/streams/readable", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 51284, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1880, "endOffset": 1905, "count": 0}], "isBlockCoverage": false}, {"functionName": "nop", "ranges": [{"startOffset": 2867, "endOffset": 2875, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeBitMapDescriptor", "ranges": [{"startOffset": 3771, "endOffset": 3993, "count": 19}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3846, "endOffset": 3890, "count": 2}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 3896, "endOffset": 3985, "count": 20}, {"startOffset": 3926, "endOffset": 3979, "count": 10}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 6167, "endOffset": 6255, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6261, "endOffset": 6427, "count": 2}, {"startOffset": 6291, "endOffset": 6371, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 6504, "endOffset": 6604, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6610, "endOffset": 6840, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6909, "endOffset": 6997, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7003, "endOffset": 7169, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7239, "endOffset": 7329, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7335, "endOffset": 7504, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7573, "endOffset": 7676, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7682, "endOffset": 7949, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReadableState", "ranges": [{"startOffset": 7962, "endOffset": 9928, "count": 2}, {"startOffset": 8352, "endOffset": 8380, "count": 0}, {"startOffset": 8433, "endOffset": 8461, "count": 0}, {"startOffset": 8709, "endOffset": 8745, "count": 0}, {"startOffset": 9087, "endOffset": 9117, "count": 0}, {"startOffset": 9396, "endOffset": 9425, "count": 0}, {"startOffset": 9426, "endOffset": 9456, "count": 0}, {"startOffset": 9505, "endOffset": 9664, "count": 0}, {"startOffset": 9827, "endOffset": 9926, "count": 0}], "isBlockCoverage": true}, {"functionName": "onConstructed", "ranges": [{"startOffset": 9972, "endOffset": 10089, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable", "ranges": [{"startOffset": 10092, "endOffset": 11126, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable._destroy", "ranges": [{"startOffset": 11263, "endOffset": 11295, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.<computed>", "ranges": [{"startOffset": 11346, "endOffset": 11384, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.<computed>", "ranges": [{"startOffset": 11428, "endOffset": 11684, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.push", "ranges": [{"startOffset": 11913, "endOffset": 12182, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.unshift", "ranges": [{"startOffset": 12278, "endOffset": 12545, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkUnshiftByteMode", "ranges": [{"startOffset": 12549, "endOffset": 13605, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkUnshiftObjectMode", "ranges": [{"startOffset": 13607, "endOffset": 13847, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkUnshiftValue", "ranges": [{"startOffset": 13849, "endOffset": 14182, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkPushByteMode", "ranges": [{"startOffset": 14184, "endOffset": 15566, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkPushObjectMode", "ranges": [{"startOffset": 15568, "endOffset": 16165, "count": 0}], "isBlockCoverage": false}, {"functionName": "canPushMore", "ranges": [{"startOffset": 16167, "endOffset": 16497, "count": 0}], "isBlockCoverage": false}, {"functionName": "addChunk", "ranges": [{"startOffset": 16499, "endOffset": 17443, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.isPaused", "ranges": [{"startOffset": 17475, "endOffset": 17630, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.setEncoding", "ranges": [{"startOffset": 17694, "endOffset": 18264, "count": 0}], "isBlockCoverage": false}, {"functionName": "computeNewHighWaterMark", "ranges": [{"startOffset": 18325, "endOffset": 18676, "count": 0}], "isBlockCoverage": false}, {"functionName": "howMuchToRead", "ranges": [{"startOffset": 18789, "endOffset": 19270, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.read", "ranges": [{"startOffset": 19367, "endOffset": 24042, "count": 0}], "isBlockCoverage": false}, {"functionName": "onEofChunk", "ranges": [{"startOffset": 24045, "endOffset": 24999, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitReadable", "ranges": [{"startOffset": 25200, "endOffset": 25531, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitReadable_", "ranges": [{"startOffset": 25533, "endOffset": 26205, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeReadMore", "ranges": [{"startOffset": 26555, "endOffset": 26762, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeReadMore_", "ranges": [{"startOffset": 26764, "endOffset": 28593, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable._read", "ranges": [{"startOffset": 28864, "endOffset": 28930, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.pipe", "ranges": [{"startOffset": 28959, "endOffset": 33469, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipeOnDrain", "ranges": [{"startOffset": 33472, "endOffset": 34149, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.unpipe", "ranges": [{"startOffset": 34180, "endOffset": 34881, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.on", "ranges": [{"startOffset": 35007, "endOffset": 36016, "count": 778}, {"startOffset": 35139, "endOffset": 35568, "count": 0}, {"startOffset": 35597, "endOffset": 35999, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable.removeListener", "ranges": [{"startOffset": 36111, "endOffset": 36818, "count": 776}, {"startOffset": 36311, "endOffset": 36697, "count": 0}, {"startOffset": 36721, "endOffset": 36756, "count": 0}, {"startOffset": 36758, "endOffset": 36801, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable.removeAllListeners", "ranges": [{"startOffset": 36921, "endOffset": 37515, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateReadableListening", "ranges": [{"startOffset": 37518, "endOffset": 38220, "count": 0}], "isBlockCoverage": false}, {"functionName": "nReadingNextTick", "ranges": [{"startOffset": 38222, "endOffset": 38310, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.resume", "ranges": [{"startOffset": 38464, "endOffset": 38969, "count": 0}], "isBlockCoverage": false}, {"functionName": "resume", "ranges": [{"startOffset": 38972, "endOffset": 39145, "count": 0}], "isBlockCoverage": false}, {"functionName": "resume_", "ranges": [{"startOffset": 39147, "endOffset": 39461, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.pause", "ranges": [{"startOffset": 39490, "endOffset": 39802, "count": 0}], "isBlockCoverage": false}, {"functionName": "flow", "ranges": [{"startOffset": 39805, "endOffset": 39956, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.wrap", "ranges": [{"startOffset": 40140, "endOffset": 41161, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.<computed>", "ranges": [{"startOffset": 41206, "endOffset": 41258, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.iterator", "ranges": [{"startOffset": 41291, "endOffset": 41434, "count": 0}], "isBlockCoverage": false}, {"functionName": "streamToAsyncIterator", "ranges": [{"startOffset": 41437, "endOffset": 41686, "count": 0}], "isBlockCoverage": false}, {"functionName": "createAsyncIterator", "ranges": [{"startOffset": 41688, "endOffset": 42764, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 42983, "endOffset": 43372, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 43378, "endOffset": 43508, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 43590, "endOffset": 43654, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 43736, "endOffset": 43944, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44032, "endOffset": 44098, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44179, "endOffset": 44239, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44321, "endOffset": 44381, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 44392, "endOffset": 44501, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44577, "endOffset": 44631, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44711, "endOffset": 44799, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44877, "endOffset": 44962, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45031, "endOffset": 45115, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45160, "endOffset": 45244, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45315, "endOffset": 45402, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 45408, "endOffset": 45697, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45772, "endOffset": 45860, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 46001, "endOffset": 46046, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 46126, "endOffset": 46184, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 46190, "endOffset": 46351, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromList", "ranges": [{"startOffset": 46657, "endOffset": 49109, "count": 0}], "isBlockCoverage": false}, {"functionName": "endReadable", "ranges": [{"startOffset": 49111, "endOffset": 49337, "count": 0}], "isBlockCoverage": false}, {"functionName": "endReadableNT", "ranges": [{"startOffset": 49339, "endOffset": 50232, "count": 0}], "isBlockCoverage": false}, {"functionName": "endWritableNT", "ranges": [{"startOffset": 50234, "endOffset": 50394, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.from", "ranges": [{"startOffset": 50412, "endOffset": 50481, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyWebStreams", "ranges": [{"startOffset": 50546, "endOffset": 50710, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.fromWeb", "ranges": [{"startOffset": 50731, "endOffset": 50865, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.toWeb", "ranges": [{"startOffset": 50885, "endOffset": 51019, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.wrap", "ranges": [{"startOffset": 51038, "endOffset": 51282, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "193", "url": "node:internal/streams/add-abort-signal", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1734, "count": 1}], "isBlockCoverage": false}, {"functionName": "validateAbortSignal", "ranges": [{"startOffset": 563, "endOffset": 722, "count": 0}], "isBlockCoverage": false}, {"functionName": "addAbortSignal", "ranges": [{"startOffset": 757, "endOffset": 1068, "count": 0}], "isBlockCoverage": false}, {"functionName": "module.exports.addAbortSignalNoValidate", "ranges": [{"startOffset": 1113, "endOffset": 1732, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "194", "url": "node:internal/streams/state", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1440, "count": 1}], "isBlockCoverage": false}, {"functionName": "highWaterMarkFrom", "ranges": [{"startOffset": 395, "endOffset": 562, "count": 4}, {"startOffset": 493, "endOffset": 516, "count": 0}, {"startOffset": 553, "endOffset": 559, "count": 0}], "isBlockCoverage": true}, {"functionName": "getDefaultHighWaterMark", "ranges": [{"startOffset": 564, "endOffset": 694, "count": 4}, {"startOffset": 631, "endOffset": 663, "count": 0}], "isBlockCoverage": true}, {"functionName": "setDefaultHighWaterMark", "ranges": [{"startOffset": 696, "endOffset": 907, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHighWaterMark", "ranges": [{"startOffset": 909, "endOffset": 1342, "count": 4}, {"startOffset": 1056, "endOffset": 1268, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "195", "url": "node:internal/streams/from", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4321, "count": 1}], "isBlockCoverage": false}, {"functionName": "from", "ranges": [{"startOffset": 241, "endOffset": 4296, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "196", "url": "node:internal/streams/writable", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33592, "count": 1}], "isBlockCoverage": false}, {"functionName": "nop", "ranges": [{"startOffset": 2779, "endOffset": 2796, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeBitMapDescriptor", "ranges": [{"startOffset": 3852, "endOffset": 4074, "count": 20}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3927, "endOffset": 3971, "count": 2}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 3977, "endOffset": 4066, "count": 20}, {"startOffset": 4007, "endOffset": 4027, "count": 2}, {"startOffset": 4027, "endOffset": 4060, "count": 18}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 7038, "endOffset": 7116, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7122, "endOffset": 7288, "count": 2}, {"startOffset": 7152, "endOffset": 7232, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 7358, "endOffset": 7458, "count": 6}, {"startOffset": 7409, "endOffset": 7443, "count": 0}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 7464, "endOffset": 7737, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7814, "endOffset": 7914, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7920, "endOffset": 8150, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8291, "endOffset": 8368, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 8374, "endOffset": 8538, "count": 1552}, {"startOffset": 8439, "endOffset": 8532, "count": 776}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8725, "endOffset": 8825, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 8831, "endOffset": 9028, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9098, "endOffset": 9176, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 9182, "endOffset": 9349, "count": 0}], "isBlockCoverage": false}, {"functionName": "WritableState", "ranges": [{"startOffset": 9362, "endOffset": 11644, "count": 2}, {"startOffset": 9630, "endOffset": 9658, "count": 0}, {"startOffset": 9711, "endOffset": 9739, "count": 0}, {"startOffset": 10030, "endOffset": 10066, "count": 0}, {"startOffset": 10120, "endOffset": 10151, "count": 0}, {"startOffset": 10409, "endOffset": 10439, "count": 0}, {"startOffset": 10696, "endOffset": 10702, "count": 0}, {"startOffset": 10734, "endOffset": 10763, "count": 0}, {"startOffset": 10764, "endOffset": 10794, "count": 0}, {"startOffset": 10843, "endOffset": 11052, "count": 0}], "isBlockCoverage": true}, {"functionName": "resetBuffer", "ranges": [{"startOffset": 11646, "endOffset": 11810, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 11848, "endOffset": 11977, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 12073, "endOffset": 12184, "count": 0}], "isBlockCoverage": false}, {"functionName": "onConstructed", "ranges": [{"startOffset": 12233, "endOffset": 12419, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable", "ranges": [{"startOffset": 12422, "endOffset": 13528, "count": 0}], "isBlockCoverage": false}, {"functionName": "value", "ranges": [{"startOffset": 13610, "endOffset": 13814, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.pipe", "ranges": [{"startOffset": 13915, "endOffset": 13983, "count": 0}], "isBlockCoverage": false}, {"functionName": "_write", "ranges": [{"startOffset": 13986, "endOffset": 15384, "count": 776}, {"startOffset": 14119, "endOffset": 14138, "count": 0}, {"startOffset": 14162, "endOffset": 14207, "count": 0}, {"startOffset": 14346, "endOffset": 14369, "count": 0}, {"startOffset": 14376, "endOffset": 14496, "count": 0}, {"startOffset": 14585, "endOffset": 14669, "count": 0}, {"startOffset": 14675, "endOffset": 14995, "count": 0}, {"startOffset": 15051, "endOffset": 15100, "count": 0}, {"startOffset": 15146, "endOffset": 15196, "count": 0}, {"startOffset": 15209, "endOffset": 15300, "count": 0}], "isBlockCoverage": true}, {"functionName": "Writable.write", "ranges": [{"startOffset": 15413, "endOffset": 15604, "count": 776}], "isBlockCoverage": true}, {"functionName": "Writable.cork", "ranges": [{"startOffset": 15633, "endOffset": 15731, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.uncork", "ranges": [{"startOffset": 15762, "endOffset": 16000, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDefaultEncoding", "ranges": [{"startOffset": 16043, "endOffset": 16368, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeOr<PERSON>uffer", "ranges": [{"startOffset": 16558, "endOffset": 17762, "count": 776}, {"startOffset": 16675, "endOffset": 16678, "count": 0}, {"startOffset": 16809, "endOffset": 17216, "count": 0}, {"startOffset": 17506, "endOffset": 17527, "count": 0}, {"startOffset": 17542, "endOffset": 17580, "count": 0}], "isBlockCoverage": true}, {"functionName": "doWrite", "ranges": [{"startOffset": 17764, "endOffset": 18201, "count": 0}], "isBlockCoverage": false}, {"functionName": "onwriteError", "ranges": [{"startOffset": 18203, "endOffset": 18588, "count": 0}], "isBlockCoverage": false}, {"functionName": "onwrite", "ranges": [{"startOffset": 18590, "endOffset": 21196, "count": 776}, {"startOffset": 18707, "endOffset": 18781, "count": 0}, {"startOffset": 18898, "endOffset": 18903, "count": 0}, {"startOffset": 19046, "endOffset": 19660, "count": 0}, {"startOffset": 19711, "endOffset": 19752, "count": 0}, {"startOffset": 19829, "endOffset": 19850, "count": 0}, {"startOffset": 20214, "endOffset": 20565, "count": 0}, {"startOffset": 20619, "endOffset": 20680, "count": 731}, {"startOffset": 20682, "endOffset": 20740, "count": 731}, {"startOffset": 20740, "endOffset": 21131, "count": 45}, {"startOffset": 20988, "endOffset": 21131, "count": 0}, {"startOffset": 21137, "endOffset": 21190, "count": 0}], "isBlockCoverage": true}, {"functionName": "afterWriteTick", "ranges": [{"startOffset": 21198, "endOffset": 21385, "count": 44}], "isBlockCoverage": true}, {"functionName": "afterWrite", "ranges": [{"startOffset": 21387, "endOffset": 21895, "count": 44}, {"startOffset": 21565, "endOffset": 21586, "count": 0}, {"startOffset": 21605, "endOffset": 21670, "count": 0}, {"startOffset": 21694, "endOffset": 21736, "count": 765}, {"startOffset": 21780, "endOffset": 21809, "count": 0}, {"startOffset": 21850, "endOffset": 21893, "count": 0}], "isBlockCoverage": true}, {"functionName": "errorBuffer", "ranges": [{"startOffset": 21967, "endOffset": 22518, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 22584, "endOffset": 24254, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable._write", "ranges": [{"startOffset": 24284, "endOffset": 24453, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.end", "ranges": [{"startOffset": 24517, "endOffset": 26157, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>sh", "ranges": [{"startOffset": 26160, "endOffset": 26589, "count": 0}], "isBlockCoverage": false}, {"functionName": "onFinish", "ranges": [{"startOffset": 26591, "endOffset": 27223, "count": 0}], "isBlockCoverage": false}, {"functionName": "prefinish", "ranges": [{"startOffset": 27225, "endOffset": 27749, "count": 0}], "isBlockCoverage": false}, {"functionName": "finishMaybe", "ranges": [{"startOffset": 27751, "endOffset": 28250, "count": 0}], "isBlockCoverage": false}, {"functionName": "finish", "ranges": [{"startOffset": 28252, "endOffset": 28895, "count": 0}], "isBlockCoverage": false}, {"functionName": "callFinishedCallbacks", "ranges": [{"startOffset": 28897, "endOffset": 29215, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 29299, "endOffset": 29402, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 29450, "endOffset": 29556, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 29562, "endOffset": 29812, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 29859, "endOffset": 30262, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 30268, "endOffset": 30402, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30457, "endOffset": 30575, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30632, "endOffset": 30752, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30805, "endOffset": 30900, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30952, "endOffset": 31068, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31124, "endOffset": 31277, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31337, "endOffset": 31426, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31479, "endOffset": 31572, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31625, "endOffset": 31707, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31776, "endOffset": 31873, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31932, "endOffset": 32189, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.destroy", "ranges": [{"startOffset": 32267, "endOffset": 32547, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable._destroy", "ranges": [{"startOffset": 32635, "endOffset": 32667, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.<computed>", "ranges": [{"startOffset": 32718, "endOffset": 32756, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyWebStreams", "ranges": [{"startOffset": 32821, "endOffset": 32985, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.fromWeb", "ranges": [{"startOffset": 33006, "endOffset": 33140, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.toWeb", "ranges": [{"startOffset": 33160, "endOffset": 33267, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.<computed>", "ranges": [{"startOffset": 33311, "endOffset": 33590, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "197", "url": "node:stream/promises", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 917, "count": 1}], "isBlockCoverage": false}, {"functionName": "pipeline", "ranges": [{"startOffset": 318, "endOffset": 869, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "198", "url": "node:internal/streams/transform", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7123, "count": 1}], "isBlockCoverage": false}, {"functionName": "Transform", "ranges": [{"startOffset": 3920, "endOffset": 5500, "count": 0}], "isBlockCoverage": true}, {"functionName": "final", "ranges": [{"startOffset": 5502, "endOffset": 5946, "count": 0}], "isBlockCoverage": false}, {"functionName": "prefinish", "ranges": [{"startOffset": 5948, "endOffset": 6029, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform._transform", "ranges": [{"startOffset": 6101, "endOffset": 6196, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform._write", "ranges": [{"startOffset": 6228, "endOffset": 6965, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform._read", "ranges": [{"startOffset": 6996, "endOffset": 7121, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "199", "url": "node:internal/streams/passthrough", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1762, "count": 1}], "isBlockCoverage": false}, {"functionName": "PassThrough", "ranges": [{"startOffset": 1529, "endOffset": 1671, "count": 0}], "isBlockCoverage": false}, {"functionName": "PassThrough._transform", "ranges": [{"startOffset": 1708, "endOffset": 1760, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "200", "url": "node:internal/streams/duplexpair", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1373, "count": 1}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 251, "endOffset": 1136, "count": 0}], "isBlockCoverage": false}, {"functionName": "duplexPair", "ranges": [{"startOffset": 1138, "endOffset": 1343, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "201", "url": "node:internal/crypto/sig", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6942, "count": 1}], "isBlockCoverage": false}, {"functionName": "Sign", "ranges": [{"startOffset": 870, "endOffset": 1129, "count": 0}], "isBlockCoverage": false}, {"functionName": "_write", "ranges": [{"startOffset": 1252, "endOffset": 1344, "count": 0}], "isBlockCoverage": false}, {"functionName": "update", "ranges": [{"startOffset": 1371, "endOffset": 1684, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPadding", "ranges": [{"startOffset": 1687, "endOffset": 1762, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSaltLength", "ranges": [{"startOffset": 1764, "endOffset": 1845, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDSASignatureEncoding", "ranges": [{"startOffset": 1847, "endOffset": 2199, "count": 0}], "isBlockCoverage": false}, {"functionName": "getIntOption", "ranges": [{"startOffset": 2201, "endOffset": 2446, "count": 0}], "isBlockCoverage": false}, {"functionName": "sign", "ranges": [{"startOffset": 2470, "endOffset": 3086, "count": 0}], "isBlockCoverage": false}, {"functionName": "signOneShot", "ranges": [{"startOffset": 3089, "endOffset": 4307, "count": 0}], "isBlockCoverage": false}, {"functionName": "Verify", "ranges": [{"startOffset": 4309, "endOffset": 4576, "count": 0}], "isBlockCoverage": false}, {"functionName": "verify", "ranges": [{"startOffset": 4804, "endOffset": 5387, "count": 0}], "isBlockCoverage": false}, {"functionName": "verifyOneShot", "ranges": [{"startOffset": 5390, "endOffset": 6868, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "202", "url": "node:internal/crypto/hash", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5947, "count": 1}], "isBlockCoverage": false}, {"functionName": "Hash", "ranges": [{"startOffset": 1100, "endOffset": 1826, "count": 20000}, {"startOffset": 1159, "endOffset": 1195, "count": 0}, {"startOffset": 1345, "endOffset": 1364, "count": 0}, {"startOffset": 1365, "endOffset": 1391, "count": 0}, {"startOffset": 1437, "endOffset": 1484, "count": 0}, {"startOffset": 1616, "endOffset": 1620, "count": 0}], "isBlockCoverage": true}, {"functionName": "copy", "ranges": [{"startOffset": 1957, "endOffset": 2125, "count": 0}], "isBlockCoverage": false}, {"functionName": "_transform", "ranges": [{"startOffset": 2156, "endOffset": 2261, "count": 0}], "isBlockCoverage": false}, {"functionName": "_flush", "ranges": [{"startOffset": 2288, "endOffset": 2368, "count": 0}], "isBlockCoverage": false}, {"functionName": "update", "ranges": [{"startOffset": 2395, "endOffset": 2859, "count": 20000}, {"startOffset": 2488, "endOffset": 2526, "count": 0}, {"startOffset": 2560, "endOffset": 2603, "count": 0}, {"startOffset": 2639, "endOffset": 2749, "count": 0}, {"startOffset": 2800, "endOffset": 2842, "count": 0}], "isBlockCoverage": true}, {"functionName": "digest", "ranges": [{"startOffset": 2887, "endOffset": 3208, "count": 20000}, {"startOffset": 2980, "endOffset": 3018, "count": 0}, {"startOffset": 3140, "endOffset": 3162, "count": 0}], "isBlockCoverage": true}, {"functionName": "Hmac", "ranges": [{"startOffset": 3211, "endOffset": 3611, "count": 0}], "isBlockCoverage": false}, {"functionName": "digest", "ranges": [{"startOffset": 3792, "endOffset": 4224, "count": 0}], "isBlockCoverage": false}, {"functionName": "asyncDigest", "ranges": [{"startOffset": 4379, "endOffset": 4850, "count": 40013}, {"startOffset": 4499, "endOffset": 4512, "count": 0}, {"startOffset": 4769, "endOffset": 4849, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4663, "endOffset": 4763, "count": 40013}], "isBlockCoverage": true}, {"functionName": "hash", "ranges": [{"startOffset": 4852, "endOffset": 5884, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "203", "url": "node:internal/crypto/x509", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10479, "count": 1}], "isBlockCoverage": false}, {"functionName": "isX509Certificate", "ranges": [{"startOffset": 1157, "endOffset": 1240, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFlags", "ranges": [{"startOffset": 1242, "endOffset": 2487, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2519, "endOffset": 2656, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2680, "endOffset": 10233, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "204", "url": "node:internal/crypto/certificate", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1546, "count": 1}], "isBlockCoverage": false}, {"functionName": "verifySpkac", "ranges": [{"startOffset": 385, "endOffset": 505, "count": 0}], "isBlockCoverage": false}, {"functionName": "exportPublicKey", "ranges": [{"startOffset": 507, "endOffset": 635, "count": 0}], "isBlockCoverage": false}, {"functionName": "exportChallenge", "ranges": [{"startOffset": 637, "endOffset": 765, "count": 0}], "isBlockCoverage": false}, {"functionName": "Certificate", "ranges": [{"startOffset": 1122, "endOffset": 1216, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "205", "url": "node:internal/crypto/webcrypto", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 29519, "count": 1}], "isBlockCoverage": false}, {"functionName": "digest", "ranges": [{"startOffset": 1150, "endOffset": 1759, "count": 40013}, {"startOffset": 1214, "endOffset": 1257, "count": 0}], "isBlockCoverage": true}, {"functionName": "randomUUID", "ranges": [{"startOffset": 1761, "endOffset": 1871, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON>ey", "ranges": [{"startOffset": 1873, "endOffset": 4368, "count": 8}, {"startOffset": 1967, "endOffset": 2010, "count": 0}, {"startOffset": 2646, "endOffset": 2671, "count": 0}, {"startOffset": 2698, "endOffset": 2713, "count": 0}, {"startOffset": 2740, "endOffset": 2917, "count": 4}, {"startOffset": 2922, "endOffset": 2937, "count": 0}, {"startOffset": 2964, "endOffset": 2977, "count": 0}, {"startOffset": 3004, "endOffset": 3018, "count": 0}, {"startOffset": 3045, "endOffset": 3220, "count": 0}, {"startOffset": 3225, "endOffset": 3238, "count": 4}, {"startOffset": 3265, "endOffset": 3436, "count": 4}, {"startOffset": 3441, "endOffset": 3611, "count": 0}, {"startOffset": 3616, "endOffset": 3631, "count": 0}, {"startOffset": 3658, "endOffset": 3673, "count": 0}, {"startOffset": 3700, "endOffset": 3715, "count": 0}, {"startOffset": 3742, "endOffset": 3913, "count": 0}, {"startOffset": 3918, "endOffset": 4008, "count": 0}, {"startOffset": 4053, "endOffset": 4117, "count": 0}, {"startOffset": 4118, "endOffset": 4153, "count": 0}, {"startOffset": 4240, "endOffset": 4348, "count": 0}], "isBlockCoverage": true}, {"functionName": "deriveBits", "ranges": [{"startOffset": 4370, "endOffset": 5937, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 5939, "endOffset": 6559, "count": 8}, {"startOffset": 6009, "endOffset": 6024, "count": 0}, {"startOffset": 6029, "endOffset": 6044, "count": 0}, {"startOffset": 6154, "endOffset": 6217, "count": 0}, {"startOffset": 6244, "endOffset": 6498, "count": 0}, {"startOffset": 6503, "endOffset": 6515, "count": 0}, {"startOffset": 6520, "endOffset": 6553, "count": 0}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 6561, "endOffset": 8741, "count": 8}, {"startOffset": 6687, "endOffset": 6730, "count": 0}, {"startOffset": 7667, "endOffset": 7776, "count": 0}, {"startOffset": 7830, "endOffset": 7901, "count": 0}, {"startOffset": 8030, "endOffset": 8044, "count": 0}, {"startOffset": 8071, "endOffset": 8083, "count": 0}, {"startOffset": 8110, "endOffset": 8248, "count": 0}, {"startOffset": 8253, "endOffset": 8382, "count": 0}, {"startOffset": 8527, "endOffset": 8617, "count": 0}], "isBlockCoverage": true}, {"functionName": "exportKeySpki", "ranges": [{"startOffset": 8743, "endOffset": 9721, "count": 0}], "isBlockCoverage": false}, {"functionName": "exportKeyPkcs8", "ranges": [{"startOffset": 9723, "endOffset": 10710, "count": 0}], "isBlockCoverage": false}, {"functionName": "exportKeyRaw", "ranges": [{"startOffset": 10712, "endOffset": 11645, "count": 4}, {"startOffset": 10783, "endOffset": 10796, "count": 2}, {"startOffset": 10823, "endOffset": 10989, "count": 2}, {"startOffset": 10976, "endOffset": 10989, "count": 0}, {"startOffset": 10994, "endOffset": 11009, "count": 0}, {"startOffset": 11036, "endOffset": 11049, "count": 0}, {"startOffset": 11076, "endOffset": 11090, "count": 0}, {"startOffset": 11117, "endOffset": 11287, "count": 0}, {"startOffset": 11292, "endOffset": 11307, "count": 0}, {"startOffset": 11334, "endOffset": 11349, "count": 0}, {"startOffset": 11376, "endOffset": 11391, "count": 2}, {"startOffset": 11418, "endOffset": 11432, "count": 2}, {"startOffset": 11459, "endOffset": 11517, "count": 2}, {"startOffset": 11521, "endOffset": 11644, "count": 0}], "isBlockCoverage": true}, {"functionName": "exportKeyJWK", "ranges": [{"startOffset": 11647, "endOffset": 13177, "count": 15}, {"startOffset": 11832, "endOffset": 11986, "count": 0}, {"startOffset": 11991, "endOffset": 12138, "count": 0}, {"startOffset": 12143, "endOffset": 12292, "count": 6}, {"startOffset": 12297, "endOffset": 12310, "count": 6}, {"startOffset": 12337, "endOffset": 12411, "count": 6}, {"startOffset": 12416, "endOffset": 12430, "count": 0}, {"startOffset": 12457, "endOffset": 12525, "count": 0}, {"startOffset": 12530, "endOffset": 12545, "count": 0}, {"startOffset": 12572, "endOffset": 12641, "count": 0}, {"startOffset": 12646, "endOffset": 12661, "count": 0}, {"startOffset": 12688, "endOffset": 12703, "count": 0}, {"startOffset": 12730, "endOffset": 12745, "count": 3}, {"startOffset": 12772, "endOffset": 12920, "count": 3}, {"startOffset": 12925, "endOffset": 13067, "count": 0}, {"startOffset": 13072, "endOffset": 13080, "count": 0}, {"startOffset": 13106, "endOffset": 13176, "count": 0}], "isBlockCoverage": true}, {"functionName": "exportKey", "ranges": [{"startOffset": 13179, "endOffset": 14039, "count": 19}, {"startOffset": 13242, "endOffset": 13285, "count": 0}, {"startOffset": 13679, "endOffset": 13754, "count": 0}, {"startOffset": 13780, "endOffset": 13819, "count": 0}, {"startOffset": 13824, "endOffset": 13865, "count": 0}, {"startOffset": 13870, "endOffset": 13907, "count": 15}, {"startOffset": 13912, "endOffset": 13949, "count": 4}, {"startOffset": 13953, "endOffset": 14038, "count": 0}], "isBlockCoverage": true}, {"functionName": "importKey", "ranges": [{"startOffset": 14041, "endOffset": 16747, "count": 29}, {"startOffset": 14154, "endOffset": 14197, "count": 0}, {"startOffset": 14504, "endOffset": 14518, "count": 11}, {"startOffset": 14519, "endOffset": 14535, "count": 18}, {"startOffset": 15066, "endOffset": 15091, "count": 0}, {"startOffset": 15118, "endOffset": 15133, "count": 0}, {"startOffset": 15160, "endOffset": 15310, "count": 4}, {"startOffset": 15315, "endOffset": 15328, "count": 7}, {"startOffset": 15355, "endOffset": 15499, "count": 7}, {"startOffset": 15504, "endOffset": 15519, "count": 0}, {"startOffset": 15546, "endOffset": 15559, "count": 0}, {"startOffset": 15586, "endOffset": 15600, "count": 0}, {"startOffset": 15627, "endOffset": 15775, "count": 0}, {"startOffset": 15780, "endOffset": 15927, "count": 0}, {"startOffset": 15932, "endOffset": 15947, "count": 0}, {"startOffset": 15974, "endOffset": 15989, "count": 0}, {"startOffset": 16016, "endOffset": 16031, "count": 10}, {"startOffset": 16058, "endOffset": 16206, "count": 10}, {"startOffset": 16211, "endOffset": 16223, "count": 0}, {"startOffset": 16250, "endOffset": 16409, "count": 8}, {"startOffset": 16414, "endOffset": 16504, "count": 0}, {"startOffset": 16542, "endOffset": 16570, "count": 11}, {"startOffset": 16572, "endOffset": 16601, "count": 23}, {"startOffset": 16603, "endOffset": 16727, "count": 0}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 16840, "endOffset": 18375, "count": 0}], "isBlockCoverage": false}, {"functionName": "unwrap<PERSON><PERSON>", "ranges": [{"startOffset": 18470, "endOffset": 20433, "count": 0}], "isBlockCoverage": false}, {"functionName": "signVerify", "ranges": [{"startOffset": 20435, "endOffset": 21538, "count": 7}, {"startOffset": 20543, "endOffset": 20570, "count": 4}, {"startOffset": 20721, "endOffset": 20827, "count": 0}, {"startOffset": 20861, "endOffset": 20876, "count": 0}, {"startOffset": 20903, "endOffset": 21029, "count": 0}, {"startOffset": 21154, "endOffset": 21169, "count": 0}, {"startOffset": 21196, "endOffset": 21335, "count": 0}, {"startOffset": 21340, "endOffset": 21454, "count": 0}, {"startOffset": 21458, "endOffset": 21537, "count": 0}], "isBlockCoverage": true}, {"functionName": "sign", "ranges": [{"startOffset": 21540, "endOffset": 22166, "count": 3}, {"startOffset": 21607, "endOffset": 21650, "count": 0}], "isBlockCoverage": true}, {"functionName": "verify", "ranges": [{"startOffset": 22168, "endOffset": 22925, "count": 4}, {"startOffset": 22248, "endOffset": 22291, "count": 0}], "isBlockCoverage": true}, {"functionName": "cipherOrWrap", "ranges": [{"startOffset": 22927, "endOffset": 24338, "count": 2009}, {"startOffset": 23405, "endOffset": 23534, "count": 1}, {"startOffset": 23534, "endOffset": 23775, "count": 2008}, {"startOffset": 23775, "endOffset": 23883, "count": 2}, {"startOffset": 23888, "endOffset": 23903, "count": 0}, {"startOffset": 23930, "endOffset": 23945, "count": 0}, {"startOffset": 23972, "endOffset": 24079, "count": 2006}, {"startOffset": 24084, "endOffset": 24254, "count": 0}, {"startOffset": 24258, "endOffset": 24337, "count": 0}], "isBlockCoverage": true}, {"functionName": "encrypt", "ranges": [{"startOffset": 24340, "endOffset": 25066, "count": 1005}, {"startOffset": 24410, "endOffset": 24453, "count": 0}], "isBlockCoverage": true}, {"functionName": "decrypt", "ranges": [{"startOffset": 25068, "endOffset": 25794, "count": 1004}, {"startOffset": 25138, "endOffset": 25181, "count": 0}], "isBlockCoverage": true}, {"functionName": "SubtleCrypto", "ranges": [{"startOffset": 25950, "endOffset": 26010, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26045, "endOffset": 26058, "count": 1}], "isBlockCoverage": true}, {"functionName": "Crypto", "ranges": [{"startOffset": 26097, "endOffset": 26157, "count": 0}], "isBlockCoverage": true}, {"functionName": "get subtle", "ranges": [{"startOffset": 26161, "endOffset": 26261, "count": 2}, {"startOffset": 26201, "endOffset": 26238, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26296, "endOffset": 26309, "count": 1}], "isBlockCoverage": true}, {"functionName": "getRandomValues", "ranges": [{"startOffset": 26325, "endOffset": 26658, "count": 495536}, {"startOffset": 26382, "endOffset": 26419, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "206", "url": "file:///home/<USER>/ylabs/lib0/hash/sha256.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5306, "count": 1}], "isBlockCoverage": true}, {"functionName": "rotr", "ranges": [{"startOffset": 349, "endOffset": 398, "count": 37498752}], "isBlockCoverage": true}, {"functionName": "sum0to256", "ranges": [{"startOffset": 491, "endOffset": 534, "count": 4166528}], "isBlockCoverage": true}, {"functionName": "sum1to256", "ranges": [{"startOffset": 627, "endOffset": 670, "count": 4166528}], "isBlockCoverage": true}, {"functionName": "sigma0to256", "ranges": [{"startOffset": 765, "endOffset": 804, "count": 3124896}], "isBlockCoverage": true}, {"functionName": "sigma1to256", "ranges": [{"startOffset": 899, "endOffset": 940, "count": 3124896}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON>", "ranges": [{"startOffset": 2470, "endOffset": 2746, "count": 20014}], "isBlockCoverage": true}, {"functionName": "_updateHash", "ranges": [{"startOffset": 2750, "endOffset": 3501, "count": 65102}, {"startOffset": 2845, "endOffset": 2935, "count": 3124896}, {"startOffset": 3116, "endOffset": 3385, "count": 4166528}], "isBlockCoverage": true}, {"functionName": "digest", "ranges": [{"startOffset": 3581, "endOffset": 5180, "count": 20014}, {"startOffset": 3646, "endOffset": 4177, "count": 45088}, {"startOffset": 3721, "endOffset": 3743, "count": 721406}, {"startOffset": 3750, "endOffset": 3843, "count": 721402}, {"startOffset": 3868, "endOffset": 4146, "count": 4}, {"startOffset": 4001, "endOffset": 4081, "count": 6}, {"startOffset": 4354, "endOffset": 4472, "count": 160050}, {"startOffset": 4387, "endOffset": 4405, "count": 620193}, {"startOffset": 4413, "endOffset": 4466, "count": 600186}, {"startOffset": 4545, "endOffset": 4631, "count": 20010}, {"startOffset": 4578, "endOffset": 4581, "count": 3}, {"startOffset": 4582, "endOffset": 4585, "count": 20007}, {"startOffset": 5055, "endOffset": 5162, "count": 160112}, {"startOffset": 5094, "endOffset": 5156, "count": 640448}], "isBlockCoverage": true}, {"functionName": "digest", "ranges": [{"startOffset": 5272, "endOffset": 5305, "count": 20014}], "isBlockCoverage": true}]}, {"scriptId": "207", "url": "file:///home/<USER>/ylabs/lib0/function.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3795, "count": 1}], "isBlockCoverage": true}, {"functionName": "callAll", "ranges": [{"startOffset": 372, "endOffset": 544, "count": 2}, {"startOffset": 434, "endOffset": 462, "count": 4}, {"startOffset": 500, "endOffset": 538, "count": 1}], "isBlockCoverage": true}, {"functionName": "nop", "ranges": [{"startOffset": 565, "endOffset": 573, "count": 5}], "isBlockCoverage": true}, {"functionName": "apply", "ranges": [{"startOffset": 661, "endOffset": 669, "count": 1}], "isBlockCoverage": true}, {"functionName": "id", "ranges": [{"startOffset": 746, "endOffset": 752, "count": 3}], "isBlockCoverage": true}, {"functionName": "equalityStrict", "ranges": [{"startOffset": 863, "endOffset": 880, "count": 34}], "isBlockCoverage": true}, {"functionName": "equalityFlat", "ranges": [{"startOffset": 1017, "endOffset": 1225, "count": 4}, {"startOffset": 1097, "endOffset": 1224, "count": 2}, {"startOffset": 1119, "endOffset": 1169, "count": 1}, {"startOffset": 1171, "endOffset": 1223, "count": 1}], "isBlockCoverage": true}, {"functionName": "equalityDeep", "ranges": [{"startOffset": 1343, "endOffset": 2910, "count": 53}, {"startOffset": 1370, "endOffset": 1391, "count": 10}, {"startOffset": 1391, "endOffset": 1420, "count": 43}, {"startOffset": 1421, "endOffset": 1455, "count": 41}, {"startOffset": 1457, "endOffset": 1479, "count": 5}, {"startOffset": 1479, "endOffset": 1525, "count": 38}, {"startOffset": 1525, "endOffset": 1574, "count": 1}, {"startOffset": 1574, "endOffset": 1606, "count": 37}, {"startOffset": 1606, "endOffset": 1679, "count": 4}, {"startOffset": 1731, "endOffset": 1952, "count": 8}, {"startOffset": 1791, "endOffset": 1863, "count": 4}, {"startOffset": 1892, "endOffset": 1926, "count": 2}, {"startOffset": 1934, "endOffset": 1952, "count": 2}, {"startOffset": 1957, "endOffset": 2147, "count": 6}, {"startOffset": 1998, "endOffset": 2028, "count": 2}, {"startOffset": 2028, "endOffset": 2129, "count": 4}, {"startOffset": 2087, "endOffset": 2121, "count": 2}, {"startOffset": 2129, "endOffset": 2147, "count": 2}, {"startOffset": 2152, "endOffset": 2386, "count": 3}, {"startOffset": 2193, "endOffset": 2223, "count": 1}, {"startOffset": 2223, "endOffset": 2368, "count": 2}, {"startOffset": 2284, "endOffset": 2324, "count": 1}, {"startOffset": 2326, "endOffset": 2360, "count": 1}, {"startOffset": 2368, "endOffset": 2386, "count": 1}, {"startOffset": 2391, "endOffset": 2641, "count": 11}, {"startOffset": 2453, "endOffset": 2483, "count": 2}, {"startOffset": 2483, "endOffset": 2511, "count": 9}, {"startOffset": 2587, "endOffset": 2621, "count": 1}, {"startOffset": 2629, "endOffset": 2641, "count": 8}, {"startOffset": 2646, "endOffset": 2858, "count": 7}, {"startOffset": 2691, "endOffset": 2721, "count": 2}, {"startOffset": 2721, "endOffset": 2763, "count": 5}, {"startOffset": 2763, "endOffset": 2846, "count": 4}, {"startOffset": 2804, "endOffset": 2838, "count": 1}, {"startOffset": 2846, "endOffset": 2858, "count": 4}, {"startOffset": 2863, "endOffset": 2890, "count": 2}, {"startOffset": 2894, "endOffset": 2909, "count": 17}], "isBlockCoverage": true}, {"functionName": "isOneOf", "ranges": [{"startOffset": 3049, "endOffset": 3092, "count": 3}], "isBlockCoverage": true}, {"functionName": "isString", "ranges": [{"startOffset": 3228, "endOffset": 3264, "count": 4}], "isBlockCoverage": true}, {"functionName": "isNumber", "ranges": [{"startOffset": 3341, "endOffset": 3383, "count": 4}], "isBlockCoverage": true}, {"functionName": "is", "ranges": [{"startOffset": 3540, "endOffset": 3574, "count": 4}], "isBlockCoverage": true}, {"functionName": "isTemplate", "ranges": [{"startOffset": 3684, "endOffset": 3794, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3765, "endOffset": 3794, "count": 4}], "isBlockCoverage": true}]}, {"scriptId": "208", "url": "file:///home/<USER>/ylabs/lib0/decoding.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17649, "count": 1}, {"startOffset": 10767, "endOffset": 10791, "count": 0}], "isBlockCoverage": true}, {"functionName": "Decoder", "ranges": [{"startOffset": 1440, "endOffset": 1665, "count": 84427}], "isBlockCoverage": true}, {"functionName": "createDecoder", "ranges": [{"startOffset": 1774, "endOffset": 1811, "count": 84416}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1909, "endOffset": 1954, "count": 72190}], "isBlockCoverage": true}, {"functionName": "clone", "ranges": [{"startOffset": 2222, "endOffset": 2348, "count": 1}], "isBlockCoverage": true}, {"functionName": "readUint8Array", "ranges": [{"startOffset": 2857, "endOffset": 3006, "count": 2093070}], "isBlockCoverage": true}, {"functionName": "readVarUint8Array", "ranges": [{"startOffset": 3394, "endOffset": 3450, "count": 2091070}], "isBlockCoverage": true}, {"functionName": "readTailAsUint8Array", "ranges": [{"startOffset": 3611, "endOffset": 3679, "count": 3}], "isBlockCoverage": true}, {"functionName": "skip8", "ranges": [{"startOffset": 3855, "endOffset": 3879, "count": 1}], "isBlockCoverage": true}, {"functionName": "readUint8", "ranges": [{"startOffset": 4057, "endOffset": 4094, "count": 30229}], "isBlockCoverage": true}, {"functionName": "readUint16", "ranges": [{"startOffset": 4252, "endOffset": 4385, "count": 1004}], "isBlockCoverage": true}, {"functionName": "readUint32", "ranges": [{"startOffset": 4543, "endOffset": 4770, "count": 1002}], "isBlockCoverage": true}, {"functionName": "readUint32BigEndian", "ranges": [{"startOffset": 4990, "endOffset": 5217, "count": 1012}], "isBlockCoverage": true}, {"functionName": "peekUint8", "ranges": [{"startOffset": 5438, "endOffset": 5473, "count": 1}], "isBlockCoverage": true}, {"functionName": "peekUint16", "ranges": [{"startOffset": 5695, "endOffset": 5772, "count": 1}], "isBlockCoverage": true}, {"functionName": "peekUint32", "ranges": [{"startOffset": 5994, "endOffset": 6163, "count": 1}], "isBlockCoverage": true}, {"functionName": "readVarUint", "ranges": [{"startOffset": 6494, "endOffset": 7062, "count": 4148386}, {"startOffset": 6597, "endOffset": 7026, "count": 4328492}, {"startOffset": 6865, "endOffset": 6889, "count": 4148385}, {"startOffset": 6889, "endOffset": 6955, "count": 180107}, {"startOffset": 6955, "endOffset": 6997, "count": 0}, {"startOffset": 7026, "endOffset": 7061, "count": 1}], "isBlockCoverage": true}, {"functionName": "readVarInt", "ranges": [{"startOffset": 7505, "endOffset": 8167, "count": 82343}, {"startOffset": 7637, "endOffset": 7641, "count": 35759}, {"startOffset": 7642, "endOffset": 7645, "count": 46584}, {"startOffset": 7677, "endOffset": 7734, "count": 13573}, {"startOffset": 7734, "endOffset": 7796, "count": 68770}, {"startOffset": 7796, "endOffset": 8131, "count": 422699}, {"startOffset": 7963, "endOffset": 7994, "count": 68769}, {"startOffset": 7994, "endOffset": 8060, "count": 353930}, {"startOffset": 8060, "endOffset": 8102, "count": 0}, {"startOffset": 8131, "endOffset": 8166, "count": 1}], "isBlockCoverage": true}, {"functionName": "peekVarUint", "ranges": [{"startOffset": 8329, "endOffset": 8433, "count": 13579}], "isBlockCoverage": true}, {"functionName": "peekVarInt", "ranges": [{"startOffset": 8594, "endOffset": 8697, "count": 10675}], "isBlockCoverage": true}, {"functionName": "_readVarStringPolyfill", "ranges": [{"startOffset": 9277, "endOffset": 10256, "count": 1000}, {"startOffset": 9358, "endOffset": 9377, "count": 0}, {"startOffset": 9517, "endOffset": 9677, "count": 0}, {"startOffset": 9716, "endOffset": 10191, "count": 4000}, {"startOffset": 9763, "endOffset": 9777, "count": 1000}, {"startOffset": 9778, "endOffset": 9785, "count": 3000}], "isBlockCoverage": true}, {"functionName": "_readVarStringNative", "ranges": [{"startOffset": 10400, "endOffset": 10489, "count": 2086283}], "isBlockCoverage": true}, {"functionName": "readTerminatedUint8Array", "ranges": [{"startOffset": 10893, "endOffset": 11160, "count": 4}, {"startOffset": 10972, "endOffset": 11158, "count": 39}, {"startOffset": 11018, "endOffset": 11069, "count": 4}, {"startOffset": 11069, "endOffset": 11087, "count": 35}, {"startOffset": 11087, "endOffset": 11123, "count": 9}, {"startOffset": 11123, "endOffset": 11158, "count": 35}], "isBlockCoverage": true}, {"functionName": "readTerminatedString", "ranges": [{"startOffset": 11254, "endOffset": 11317, "count": 2}], "isBlockCoverage": true}, {"functionName": "peekVarString", "ranges": [{"startOffset": 11483, "endOffset": 11589, "count": 9407}], "isBlockCoverage": true}, {"functionName": "readFromDataView", "ranges": [{"startOffset": 11704, "endOffset": 11847, "count": 6109}], "isBlockCoverage": true}, {"functionName": "readFloat32", "ranges": [{"startOffset": 11912, "endOffset": 11972, "count": 50}], "isBlockCoverage": true}, {"functionName": "readFloat64", "ranges": [{"startOffset": 12037, "endOffset": 12097, "count": 5380}], "isBlockCoverage": true}, {"functionName": "readBigInt64", "ranges": [{"startOffset": 12163, "endOffset": 12245, "count": 678}], "isBlockCoverage": true}, {"functionName": "readBigUint64", "ranges": [{"startOffset": 12312, "endOffset": 12395, "count": 1}], "isBlockCoverage": true}, {"functionName": "obj", "ranges": [{"startOffset": 12476, "endOffset": 12496, "count": 2719}], "isBlockCoverage": true}, {"functionName": "obj", "ranges": [{"startOffset": 12523, "endOffset": 12538, "count": 2761}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12703, "endOffset": 12719, "count": 2782}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12752, "endOffset": 12767, "count": 2752}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12836, "endOffset": 13122, "count": 2742}, {"startOffset": 13021, "endOffset": 13103, "count": 2744}], "isBlockCoverage": true}, {"functionName": "readAnyLookupTable", "ranges": [{"startOffset": 13126, "endOffset": 13312, "count": 2766}, {"startOffset": 13253, "endOffset": 13293, "count": 24416}], "isBlockCoverage": true}, {"functionName": "readAny", "ranges": [{"startOffset": 13420, "endOffset": 13484, "count": 28180}], "isBlockCoverage": true}, {"functionName": "RleDecoder", "ranges": [{"startOffset": 13668, "endOffset": 13883, "count": 1}], "isBlockCoverage": true}, {"functionName": "read", "ranges": [{"startOffset": 13887, "endOffset": 14244, "count": 5050}, {"startOffset": 13923, "endOffset": 14186, "count": 100}, {"startOffset": 13986, "endOffset": 14107, "count": 99}, {"startOffset": 14107, "endOffset": 14180, "count": 1}], "isBlockCoverage": true}, {"functionName": "IntDiffDecoder", "ranges": [{"startOffset": 14371, "endOffset": 14508, "count": 3}], "isBlockCoverage": true}, {"functionName": "read", "ranges": [{"startOffset": 14546, "endOffset": 14608, "count": 20200}], "isBlockCoverage": true}, {"functionName": "RleIntDiffDecoder", "ranges": [{"startOffset": 14738, "endOffset": 14894, "count": 2}], "isBlockCoverage": true}, {"functionName": "read", "ranges": [{"startOffset": 14932, "endOffset": 15294, "count": 15150}, {"startOffset": 14968, "endOffset": 15231, "count": 10084}, {"startOffset": 15031, "endOffset": 15152, "count": 10082}, {"startOffset": 15152, "endOffset": 15225, "count": 2}], "isBlockCoverage": true}, {"functionName": "UintOptRleDecoder", "ranges": [{"startOffset": 15397, "endOffset": 15521, "count": 2}], "isBlockCoverage": true}, {"functionName": "read", "ranges": [{"startOffset": 15525, "endOffset": 15915, "count": 6080}, {"startOffset": 15561, "endOffset": 15852, "count": 1074}, {"startOffset": 15769, "endOffset": 15846, "count": 150}], "isBlockCoverage": true}, {"functionName": "IncUintOptRleDecoder", "ranges": [{"startOffset": 16021, "endOffset": 16145, "count": 1}], "isBlockCoverage": true}, {"functionName": "read", "ranges": [{"startOffset": 16149, "endOffset": 16541, "count": 5050}, {"startOffset": 16185, "endOffset": 16476, "count": 4951}, {"startOffset": 16393, "endOffset": 16470, "count": 99}], "isBlockCoverage": true}, {"functionName": "IntDiffOptRleDecoder", "ranges": [{"startOffset": 16647, "endOffset": 16789, "count": 2}], "isBlockCoverage": true}, {"functionName": "read", "ranges": [{"startOffset": 16827, "endOffset": 17200, "count": 15150}, {"startOffset": 16863, "endOffset": 17137, "count": 10167}, {"startOffset": 17079, "endOffset": 17131, "count": 130}], "isBlockCoverage": true}, {"functionName": "StringDecoder", "ranges": [{"startOffset": 17283, "endOffset": 17465, "count": 1}], "isBlockCoverage": true}, {"functionName": "read", "ranges": [{"startOffset": 17503, "endOffset": 17646, "count": 1030}], "isBlockCoverage": true}]}, {"scriptId": "209", "url": "file:///home/<USER>/ylabs/lib0/binary.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2038, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "210", "url": "file:///home/<USER>/ylabs/lib0/encoding.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26422, "count": 1}, {"startOffset": 9013, "endOffset": 9038, "count": 0}], "isBlockCoverage": true}, {"functionName": "Encoder", "ranges": [{"startOffset": 1191, "endOffset": 1333, "count": 84455}], "isBlockCoverage": true}, {"functionName": "createEncoder", "ranges": [{"startOffset": 1408, "endOffset": 1427, "count": 84443}], "isBlockCoverage": true}, {"functionName": "encode", "ranges": [{"startOffset": 1496, "endOffset": 1584, "count": 9408}], "isBlockCoverage": true}, {"functionName": "length", "ranges": [{"startOffset": 1723, "endOffset": 1863, "count": 158438}, {"startOffset": 1809, "endOffset": 1848, "count": 1163}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1999, "endOffset": 2053, "count": 74006}, {"startOffset": 2027, "endOffset": 2053, "count": 1}], "isBlockCoverage": true}, {"functionName": "toUint8Array", "ranges": [{"startOffset": 2212, "endOffset": 2524, "count": 84451}, {"startOffset": 2341, "endOffset": 2427, "count": 1148}], "isBlockCoverage": true}, {"functionName": "verifyLen", "ranges": [{"startOffset": 2750, "endOffset": 3016, "count": 6110}, {"startOffset": 2848, "endOffset": 3014, "count": 5}], "isBlockCoverage": true}, {"functionName": "write", "ranges": [{"startOffset": 3180, "endOffset": 3424, "count": 12122264}, {"startOffset": 3274, "endOffset": 3385, "count": 150}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 3704, "endOffset": 4099, "count": 9}, {"startOffset": 3836, "endOffset": 3854, "count": 2}, {"startOffset": 3861, "endOffset": 3996, "count": 2}, {"startOffset": 3917, "endOffset": 3992, "count": 1}, {"startOffset": 4020, "endOffset": 4077, "count": 8}], "isBlockCoverage": true}, {"functionName": "writeUint16", "ranges": [{"startOffset": 4735, "endOffset": 4838, "count": 1004}], "isBlockCoverage": true}, {"functionName": "setUint16", "ranges": [{"startOffset": 5107, "endOffset": 5225, "count": 1}], "isBlockCoverage": true}, {"functionName": "writeUint32", "ranges": [{"startOffset": 5405, "endOffset": 5516, "count": 1002}, {"startOffset": 5455, "endOffset": 5514, "count": 4008}], "isBlockCoverage": true}, {"functionName": "writeUint32BigEndian", "ranges": [{"startOffset": 5759, "endOffset": 5870, "count": 1012}, {"startOffset": 5810, "endOffset": 5868, "count": 4048}], "isBlockCoverage": true}, {"functionName": "setUint32", "ranges": [{"startOffset": 6140, "endOffset": 6263, "count": 1}, {"startOffset": 6195, "endOffset": 6261, "count": 4}], "isBlockCoverage": true}, {"functionName": "writeVarUint", "ranges": [{"startOffset": 6478, "endOffset": 6673, "count": 4808080}, {"startOffset": 6527, "endOffset": 6634, "count": 178229}], "isBlockCoverage": true}, {"functionName": "writeVarInt", "ranges": [{"startOffset": 6926, "endOffset": 7544, "count": 82342}, {"startOffset": 7010, "endOffset": 7030, "count": 35758}, {"startOffset": 7161, "endOffset": 7174, "count": 68769}, {"startOffset": 7175, "endOffset": 7178, "count": 13573}, {"startOffset": 7194, "endOffset": 7207, "count": 35758}, {"startOffset": 7208, "endOffset": 7211, "count": 46584}, {"startOffset": 7408, "endOffset": 7542, "count": 422699}, {"startOffset": 7449, "endOffset": 7462, "count": 353930}, {"startOffset": 7463, "endOffset": 7466, "count": 68769}], "isBlockCoverage": true}, {"functionName": "_writeVarStringNative", "ranges": [{"startOffset": 7860, "endOffset": 8270, "count": 2446917}, {"startOffset": 7913, "endOffset": 8201, "count": 2445894}, {"startOffset": 8076, "endOffset": 8080, "count": 626}, {"startOffset": 8154, "endOffset": 8197, "count": 5213764}, {"startOffset": 8201, "endOffset": 8268, "count": 1023}], "isBlockCoverage": true}, {"functionName": "_writeVarStringPolyfill", "ranges": [{"startOffset": 8455, "endOffset": 8709, "count": 300040}, {"startOffset": 8629, "endOffset": 8707, "count": 1377820}], "isBlockCoverage": true}, {"functionName": "writeTerminatedString", "ranges": [{"startOffset": 9528, "endOffset": 9606, "count": 2}], "isBlockCoverage": true}, {"functionName": "writeTerminatedUint8Array", "ranges": [{"startOffset": 10391, "endOffset": 10585, "count": 4}, {"startOffset": 10450, "endOffset": 10563, "count": 35}, {"startOffset": 10489, "endOffset": 10499, "count": 28}, {"startOffset": 10501, "endOffset": 10532, "count": 9}], "isBlockCoverage": true}, {"functionName": "writeBinaryEncoder", "ranges": [{"startOffset": 11024, "endOffset": 11091, "count": 1}], "isBlockCoverage": true}, {"functionName": "writeUint8Array", "ranges": [{"startOffset": 11260, "endOffset": 11943, "count": 7810}, {"startOffset": 11594, "endOffset": 11941, "count": 1036}], "isBlockCoverage": true}, {"functionName": "writeVarUint8Array", "ranges": [{"startOffset": 12101, "endOffset": 12215, "count": 5810}], "isBlockCoverage": true}, {"functionName": "writeOnDataView", "ranges": [{"startOffset": 12744, "endOffset": 12897, "count": 6109}], "isBlockCoverage": true}, {"functionName": "writeFloat32", "ranges": [{"startOffset": 12986, "endOffset": 13057, "count": 50}], "isBlockCoverage": true}, {"functionName": "writeFloat64", "ranges": [{"startOffset": 13146, "endOffset": 13217, "count": 5380}], "isBlockCoverage": true}, {"functionName": "writeBigInt64", "ranges": [{"startOffset": 13307, "endOffset": 13400, "count": 678}], "isBlockCoverage": true}, {"functionName": "writeBigUint64", "ranges": [{"startOffset": 13491, "endOffset": 13585, "count": 1}], "isBlockCoverage": true}, {"functionName": "isFloat32", "ranges": [{"startOffset": 13769, "endOffset": 13857, "count": 5428}], "isBlockCoverage": true}, {"functionName": "writeAny", "ranges": [{"startOffset": 16078, "endOffset": 17810, "count": 28180}, {"startOffset": 16128, "endOffset": 16242, "count": 2756}, {"startOffset": 16247, "endOffset": 16681, "count": 5431}, {"startOffset": 16295, "endOffset": 16329, "count": 2660}, {"startOffset": 16331, "endOffset": 16432, "count": 3}, {"startOffset": 16432, "endOffset": 16669, "count": 5428}, {"startOffset": 16459, "endOffset": 16561, "count": 49}, {"startOffset": 16561, "endOffset": 16669, "count": 5379}, {"startOffset": 16686, "endOffset": 16799, "count": 677}, {"startOffset": 16804, "endOffset": 17621, "count": 11063}, {"startOffset": 16844, "endOffset": 16907, "count": 2761}, {"startOffset": 16907, "endOffset": 17609, "count": 8302}, {"startOffset": 16938, "endOffset": 17140, "count": 2766}, {"startOffset": 17084, "endOffset": 17132, "count": 24416}, {"startOffset": 17140, "endOffset": 17609, "count": 5536}, {"startOffset": 17178, "endOffset": 17290, "count": 2794}, {"startOffset": 17290, "endOffset": 17609, "count": 2742}, {"startOffset": 17482, "endOffset": 17601, "count": 2744}, {"startOffset": 17626, "endOffset": 17736, "count": 5534}, {"startOffset": 17712, "endOffset": 17717, "count": 2752}, {"startOffset": 17718, "endOffset": 17723, "count": 2782}, {"startOffset": 17741, "endOffset": 17804, "count": 2719}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 18390, "endOffset": 18578, "count": 1}], "isBlockCoverage": true}, {"functionName": "write", "ranges": [{"startOffset": 18612, "endOffset": 19005, "count": 5050}, {"startOffset": 18646, "endOffset": 18672, "count": 4950}, {"startOffset": 18672, "endOffset": 19001, "count": 100}, {"startOffset": 18706, "endOffset": 18908, "count": 99}], "isBlockCoverage": true}, {"functionName": "IntDiffEncoder", "ranges": [{"startOffset": 19255, "endOffset": 19370, "count": 3}], "isBlockCoverage": true}, {"functionName": "write", "ranges": [{"startOffset": 19409, "endOffset": 19473, "count": 20200}], "isBlockCoverage": true}, {"functionName": "RleIntDiffEncoder", "ranges": [{"startOffset": 19836, "endOffset": 19970, "count": 2}], "isBlockCoverage": true}, {"functionName": "write", "ranges": [{"startOffset": 20009, "endOffset": 20434, "count": 15150}, {"startOffset": 20042, "endOffset": 20059, "count": 5066}, {"startOffset": 20061, "endOffset": 20087, "count": 5066}, {"startOffset": 20087, "endOffset": 20430, "count": 10084}, {"startOffset": 20121, "endOffset": 20323, "count": 10082}], "isBlockCoverage": true}, {"functionName": "flushUintOptRleEncoder", "ranges": [{"startOffset": 20515, "endOffset": 21032, "count": 6026}, {"startOffset": 20553, "endOffset": 21030, "count": 6025}, {"startOffset": 20830, "endOffset": 20841, "count": 5776}, {"startOffset": 20842, "endOffset": 20854, "count": 249}, {"startOffset": 20883, "endOffset": 21026, "count": 249}], "isBlockCoverage": true}, {"functionName": "UintOptRleEncoder", "ranges": [{"startOffset": 21462, "endOffset": 21587, "count": 2}], "isBlockCoverage": true}, {"functionName": "write", "ranges": [{"startOffset": 21626, "endOffset": 21776, "count": 6080}, {"startOffset": 21660, "endOffset": 21686, "count": 5007}, {"startOffset": 21686, "endOffset": 21772, "count": 1073}], "isBlockCoverage": true}, {"functionName": "toUint8Array", "ranges": [{"startOffset": 21910, "endOffset": 22002, "count": 2}], "isBlockCoverage": true}, {"functionName": "IncUintOptRleEncoder", "ranges": [{"startOffset": 22320, "endOffset": 22445, "count": 1}], "isBlockCoverage": true}, {"functionName": "write", "ranges": [{"startOffset": 22484, "endOffset": 22647, "count": 5050}, {"startOffset": 22531, "endOffset": 22557, "count": 100}, {"startOffset": 22557, "endOffset": 22643, "count": 4950}], "isBlockCoverage": true}, {"functionName": "toUint8Array", "ranges": [{"startOffset": 22781, "endOffset": 22873, "count": 1}], "isBlockCoverage": true}, {"functionName": "flushIntDiffOptRleEncoder", "ranges": [{"startOffset": 22960, "endOffset": 23676, "count": 10169}, {"startOffset": 22998, "endOffset": 23674, "count": 10167}, {"startOffset": 23213, "endOffset": 23216, "count": 10037}, {"startOffset": 23217, "endOffset": 23220, "count": 130}, {"startOffset": 23527, "endOffset": 23670, "count": 130}], "isBlockCoverage": true}, {"functionName": "IntDiffOptRleEncoder", "ranges": [{"startOffset": 24583, "endOffset": 24726, "count": 2}], "isBlockCoverage": true}, {"functionName": "write", "ranges": [{"startOffset": 24765, "endOffset": 24976, "count": 15150}, {"startOffset": 24811, "endOffset": 24854, "count": 4983}, {"startOffset": 24854, "endOffset": 24972, "count": 10167}], "isBlockCoverage": true}, {"functionName": "toUint8Array", "ranges": [{"startOffset": 25110, "endOffset": 25205, "count": 2}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 25835, "endOffset": 25976, "count": 1}], "isBlockCoverage": true}, {"functionName": "write", "ranges": [{"startOffset": 26020, "endOffset": 26180, "count": 1030}, {"startOffset": 26086, "endOffset": 26140, "count": 384}], "isBlockCoverage": true}, {"functionName": "toUint8Array", "ranges": [{"startOffset": 26184, "endOffset": 26419, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "211", "url": "file:///home/<USER>/ylabs/lib0/number.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 884, "count": 1}, {"startOffset": 448, "endOffset": 527, "count": 0}], "isBlockCoverage": true}, {"functionName": "isInteger", "ranges": [{"startOffset": 452, "endOffset": 526, "count": 0}], "isBlockCoverage": false}, {"functionName": "countBits", "ranges": [{"startOffset": 776, "endOffset": 883, "count": 3}, {"startOffset": 832, "endOffset": 866, "count": 6}], "isBlockCoverage": true}]}, {"scriptId": "212", "url": "file:///home/<USER>/ylabs/lib0/error.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 454, "count": 1}], "isBlockCoverage": true}, {"functionName": "create", "ranges": [{"startOffset": 138, "endOffset": 155, "count": 4}], "isBlockCoverage": true}, {"functionName": "methodUnimplemented", "ranges": [{"startOffset": 261, "endOffset": 309, "count": 0}], "isBlockCoverage": false}, {"functionName": "unexpectedCase", "ranges": [{"startOffset": 410, "endOffset": 453, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "213", "url": "file:///home/<USER>/ylabs/lib0/indexeddb.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7011, "count": 1}], "isBlockCoverage": true}, {"functionName": "rtop", "ranges": [{"startOffset": 320, "endOffset": 530, "count": 0}], "isBlockCoverage": false}, {"functionName": "openDB", "ranges": [{"startOffset": 707, "endOffset": 1224, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteDB", "ranges": [{"startOffset": 1282, "endOffset": 1326, "count": 0}], "isBlockCoverage": false}, {"functionName": "createStores", "ranges": [{"startOffset": 1485, "endOffset": 1584, "count": 0}], "isBlockCoverage": false}, {"functionName": "transact", "ranges": [{"startOffset": 1757, "endOffset": 1910, "count": 0}], "isBlockCoverage": false}, {"functionName": "count", "ranges": [{"startOffset": 2035, "endOffset": 2079, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2280, "endOffset": 2318, "count": 0}], "isBlockCoverage": false}, {"functionName": "del", "ranges": [{"startOffset": 2461, "endOffset": 2502, "count": 0}], "isBlockCoverage": false}, {"functionName": "put", "ranges": [{"startOffset": 2696, "endOffset": 2746, "count": 0}], "isBlockCoverage": false}, {"functionName": "add", "ranges": [{"startOffset": 2966, "endOffset": 3016, "count": 0}], "isBlockCoverage": false}, {"functionName": "addAutoKey", "ranges": [{"startOffset": 3195, "endOffset": 3235, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAll", "ranges": [{"startOffset": 3392, "endOffset": 3451, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllKeys", "ranges": [{"startOffset": 3612, "endOffset": 3675, "count": 0}], "isBlockCoverage": false}, {"functionName": "query<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3867, "endOffset": 4058, "count": 0}], "isBlockCoverage": false}, {"functionName": "getLastKey", "ranges": [{"startOffset": 4186, "endOffset": 4243, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFirst<PERSON>ey", "ranges": [{"startOffset": 4372, "endOffset": 4429, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllKeysValues", "ranges": [{"startOffset": 4709, "endOffset": 4883, "count": 0}], "isBlockCoverage": false}, {"functionName": "iterateOnRequest", "ranges": [{"startOffset": 5047, "endOffset": 5357, "count": 0}], "isBlockCoverage": false}, {"functionName": "iterate", "ranges": [{"startOffset": 5656, "endOffset": 5796, "count": 0}], "isBlockCoverage": false}, {"functionName": "iterateKeys", "ranges": [{"startOffset": 6098, "endOffset": 6227, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStore", "ranges": [{"startOffset": 6375, "endOffset": 6409, "count": 0}], "isBlockCoverage": false}, {"functionName": "createIDBKeyRangeBound", "ranges": [{"startOffset": 6561, "endOffset": 6654, "count": 0}], "isBlockCoverage": false}, {"functionName": "createIDBKeyRangeUpperBound", "ranges": [{"startOffset": 6759, "endOffset": 6821, "count": 0}], "isBlockCoverage": false}, {"functionName": "createIDBKeyRangeLowerBound", "ranges": [{"startOffset": 6926, "endOffset": 6988, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "214", "url": "file:///home/<USER>/ylabs/lib0/indexeddbV2.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8633, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 399, "endOffset": 570, "count": 0}], "isBlockCoverage": false}, {"functionName": "openDB", "ranges": [{"startOffset": 761, "endOffset": 1330, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteDB", "ranges": [{"startOffset": 1444, "endOffset": 1547, "count": 0}], "isBlockCoverage": false}, {"functionName": "createStores", "ranges": [{"startOffset": 1706, "endOffset": 1805, "count": 0}], "isBlockCoverage": false}, {"functionName": "transact", "ranges": [{"startOffset": 2023, "endOffset": 2242, "count": 0}], "isBlockCoverage": false}, {"functionName": "count", "ranges": [{"startOffset": 2406, "endOffset": 2523, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2767, "endOffset": 2874, "count": 0}], "isBlockCoverage": false}, {"functionName": "del", "ranges": [{"startOffset": 3032, "endOffset": 3142, "count": 0}], "isBlockCoverage": false}, {"functionName": "put", "ranges": [{"startOffset": 3351, "endOffset": 3482, "count": 0}], "isBlockCoverage": false}, {"functionName": "add", "ranges": [{"startOffset": 3731, "endOffset": 3862, "count": 0}], "isBlockCoverage": false}, {"functionName": "addAutoKey", "ranges": [{"startOffset": 4070, "endOffset": 4181, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAll", "ranges": [{"startOffset": 4367, "endOffset": 4513, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllKeys", "ranges": [{"startOffset": 4703, "endOffset": 4853, "count": 0}], "isBlockCoverage": false}, {"functionName": "query<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 5059, "endOffset": 5249, "count": 0}], "isBlockCoverage": false}, {"functionName": "getLastKey", "ranges": [{"startOffset": 5391, "endOffset": 5448, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFirst<PERSON>ey", "ranges": [{"startOffset": 5591, "endOffset": 5648, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAllKeysValues", "ranges": [{"startOffset": 6007, "endOffset": 6279, "count": 0}], "isBlockCoverage": false}, {"functionName": "iterateOnRequest", "ranges": [{"startOffset": 6458, "endOffset": 6761, "count": 0}], "isBlockCoverage": false}, {"functionName": "iterate", "ranges": [{"startOffset": 7090, "endOffset": 7309, "count": 0}], "isBlockCoverage": false}, {"functionName": "iterateKeys", "ranges": [{"startOffset": 7641, "endOffset": 7849, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStore", "ranges": [{"startOffset": 7997, "endOffset": 8031, "count": 0}], "isBlockCoverage": false}, {"functionName": "createIDBKeyRangeBound", "ranges": [{"startOffset": 8183, "endOffset": 8276, "count": 0}], "isBlockCoverage": false}, {"functionName": "createIDBKeyRangeUpperBound", "ranges": [{"startOffset": 8381, "endOffset": 8443, "count": 0}], "isBlockCoverage": false}, {"functionName": "createIDBKeyRangeLowerBound", "ranges": [{"startOffset": 8548, "endOffset": 8610, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "215", "url": "file:///home/<USER>/ylabs/lib0/pledge.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6118, "count": 1}], "isBlockCoverage": true}, {"functionName": "runInGlobalContext", "ranges": [{"startOffset": 416, "endOffset": 665, "count": 201}, {"startOffset": 525, "endOffset": 663, "count": 102}, {"startOffset": 561, "endOffset": 659, "count": 201}], "isBlockCoverage": true}, {"functionName": "PledgeInstance", "ranges": [{"startOffset": 837, "endOffset": 1154, "count": 105}], "isBlockCoverage": true}, {"functionName": "get isDone", "ranges": [{"startOffset": 1158, "endOffset": 1216, "count": 0}], "isBlockCoverage": false}, {"functionName": "get isCanceled", "ranges": [{"startOffset": 1220, "endOffset": 1302, "count": 104}, {"startOffset": 1268, "endOffset": 1298, "count": 103}], "isBlockCoverage": true}, {"functionName": "resolve", "ranges": [{"startOffset": 1338, "endOffset": 1623, "count": 105}, {"startOffset": 1427, "endOffset": 1433, "count": 0}, {"startOffset": 1587, "endOffset": 1619, "count": 104}], "isBlockCoverage": true}, {"functionName": "cancel", "ranges": [{"startOffset": 1673, "endOffset": 1945, "count": 0}], "isBlockCoverage": false}, {"functionName": "map", "ranges": [{"startOffset": 2051, "endOffset": 2500, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2167, "endOffset": 2482, "count": 1}, {"startOffset": 2244, "endOffset": 2435, "count": 0}], "isBlockCoverage": true}, {"functionName": "whenResolved", "ranges": [{"startOffset": 2551, "endOffset": 2691, "count": 105}, {"startOffset": 2595, "endOffset": 2640, "count": 1}, {"startOffset": 2640, "endOffset": 2687, "count": 104}], "isBlockCoverage": true}, {"functionName": "whenCanceled", "ranges": [{"startOffset": 2754, "endOffset": 2903, "count": 104}, {"startOffset": 2798, "endOffset": 2852, "count": 0}, {"startOffset": 2884, "endOffset": 2890, "count": 103}], "isBlockCoverage": true}, {"functionName": "promise", "ranges": [{"startOffset": 2947, "endOffset": 3081, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2983, "endOffset": 3076, "count": 3}], "isBlockCoverage": true}, {"functionName": "create", "ranges": [{"startOffset": 3161, "endOffset": 3187, "count": 104}], "isBlockCoverage": true}, {"functionName": "createWithDependencies", "ranges": [{"startOffset": 3837, "endOffset": 4027, "count": 0}], "isBlockCoverage": false}, {"functionName": "whenResolved", "ranges": [{"startOffset": 4135, "endOffset": 4232, "count": 201}, {"startOffset": 4182, "endOffset": 4216, "count": 101}, {"startOffset": 4216, "endOffset": 4231, "count": 100}], "isBlockCoverage": true}, {"functionName": "whenCanceled", "ranges": [{"startOffset": 4438, "endOffset": 4514, "count": 201}, {"startOffset": 4485, "endOffset": 4512, "count": 101}], "isBlockCoverage": true}, {"functionName": "map", "ranges": [{"startOffset": 4646, "endOffset": 4734, "count": 100}, {"startOffset": 4693, "endOffset": 4718, "count": 0}], "isBlockCoverage": true}, {"functionName": "all", "ranges": [{"startOffset": 4851, "endOffset": 5287, "count": 0}], "isBlockCoverage": false}, {"functionName": "coroutine", "ranges": [{"startOffset": 5510, "endOffset": 5913, "count": 3}], "isBlockCoverage": true}, {"functionName": "handleGen", "ranges": [{"startOffset": 5611, "endOffset": 5886, "count": 204}, {"startOffset": 5668, "endOffset": 5715, "count": 3}, {"startOffset": 5715, "endOffset": 5885, "count": 201}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5762, "endOffset": 5805, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5830, "endOffset": 5876, "count": 201}], "isBlockCoverage": true}, {"functionName": "wait", "ranges": [{"startOffset": 6009, "endOffset": 6096, "count": 101}], "isBlockCoverage": true}]}, {"scriptId": "216", "url": "file:///home/<USER>/ylabs/lib0/logging.common.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3064, "count": 1}], "isBlockCoverage": true}, {"functionName": "computeNoColorLoggingArgs", "ranges": [{"startOffset": 718, "endOffset": 1552, "count": 765}, {"startOffset": 752, "endOffset": 788, "count": 4}, {"startOffset": 790, "endOffset": 896, "count": 1}, {"startOffset": 1046, "endOffset": 1281, "count": 4084}, {"startOffset": 1099, "endOffset": 1118, "count": 1}, {"startOffset": 1118, "endOffset": 1277, "count": 4083}, {"startOffset": 1155, "endOffset": 1184, "count": 2060}, {"startOffset": 1186, "endOffset": 1220, "count": 2025}, {"startOffset": 1220, "endOffset": 1277, "count": 2058}, {"startOffset": 1258, "endOffset": 1277, "count": 5}, {"startOffset": 1295, "endOffset": 1385, "count": 762}, {"startOffset": 1438, "endOffset": 1533, "count": 11}], "isBlockCoverage": true}, {"functionName": "createModuleLogger", "ranges": [{"startOffset": 1857, "endOffset": 3042, "count": 1}, {"startOffset": 2014, "endOffset": 2130, "count": 0}, {"startOffset": 2244, "endOffset": 3040, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2246, "endOffset": 3040, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "217", "url": "file:///home/<USER>/ylabs/lib0/prng/Mt19937.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2007, "count": 1}], "isBlockCoverage": true}, {"functionName": "twist", "ranges": [{"startOffset": 192, "endOffset": 278, "count": 50544}, {"startOffset": 260, "endOffset": 272, "count": 25252}, {"startOffset": 273, "endOffset": 276, "count": 25292}], "isBlockCoverage": true}, {"functionName": "nextState", "ranges": [{"startOffset": 336, "endOffset": 618, "count": 81}, {"startOffset": 399, "endOffset": 464, "count": 18387}, {"startOffset": 489, "endOffset": 558, "count": 32076}], "isBlockCoverage": true}, {"functionName": "Mt19937", "ranges": [{"startOffset": 1278, "endOffset": 1588, "count": 1}, {"startOffset": 1409, "endOffset": 1517, "count": 623}], "isBlockCoverage": true}, {"functionName": "next", "ranges": [{"startOffset": 1695, "endOffset": 2004, "count": 50000}, {"startOffset": 1728, "endOffset": 1819, "count": 80}], "isBlockCoverage": true}]}, {"scriptId": "218", "url": "file:///home/<USER>/ylabs/lib0/prng/Xorshift32.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 603, "count": 1}], "isBlockCoverage": true}, {"functionName": "Xorshift32", "ranges": [{"startOffset": 237, "endOffset": 343, "count": 125117}], "isBlockCoverage": true}, {"functionName": "next", "ranges": [{"startOffset": 450, "endOffset": 600, "count": 550464}], "isBlockCoverage": true}]}, {"scriptId": "219", "url": "file:///home/<USER>/ylabs/lib0/prng/Xoroshiro128plus.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3408, "count": 1}], "isBlockCoverage": true}, {"functionName": "Xoroshiro128plus", "ranges": [{"startOffset": 1054, "endOffset": 1369, "count": 125116}, {"startOffset": 1279, "endOffset": 1342, "count": 500464}], "isBlockCoverage": true}, {"functionName": "next", "ranges": [{"startOffset": 1429, "endOffset": 2337, "count": 11501406}, {"startOffset": 1489, "endOffset": 1587, "count": 5797581}, {"startOffset": 1587, "endOffset": 2333, "count": 5703825}], "isBlockCoverage": true}]}, {"scriptId": "220", "url": "file:///home/<USER>/ylabs/lib0/queue.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1338, "count": 1}], "isBlockCoverage": true}, {"functionName": "QueueNode", "ranges": [{"startOffset": 27, "endOffset": 114, "count": 261}], "isBlockCoverage": true}, {"functionName": "QueueValue", "ranges": [{"startOffset": 217, "endOffset": 265, "count": 261}], "isBlockCoverage": true}, {"functionName": "Queue", "ranges": [{"startOffset": 327, "endOffset": 469, "count": 2}], "isBlockCoverage": true}, {"functionName": "create", "ranges": [{"startOffset": 658, "endOffset": 675, "count": 2}], "isBlockCoverage": true}, {"functionName": "isEmpty", "ranges": [{"startOffset": 737, "endOffset": 766, "count": 567}], "isBlockCoverage": true}, {"functionName": "enqueue", "ranges": [{"startOffset": 898, "endOffset": 1037, "count": 261}, {"startOffset": 940, "endOffset": 986, "count": 157}, {"startOffset": 986, "endOffset": 1035, "count": 104}], "isBlockCoverage": true}, {"functionName": "dequeue", "ranges": [{"startOffset": 1146, "endOffset": 1337, "count": 264}, {"startOffset": 1199, "endOffset": 1321, "count": 261}, {"startOffset": 1274, "endOffset": 1304, "count": 104}, {"startOffset": 1321, "endOffset": 1336, "count": 3}], "isBlockCoverage": true}]}, {"scriptId": "221", "url": "file:///home/<USER>/ylabs/lib0/dom.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6297, "count": 1}, {"startOffset": 286, "endOffset": 296, "count": 0}, {"startOffset": 733, "endOffset": 750, "count": 0}], "isBlockCoverage": true}, {"functionName": "createElement", "ranges": [{"startOffset": 390, "endOffset": 421, "count": 0}], "isBlockCoverage": false}, {"functionName": "createDocumentFragment", "ranges": [{"startOffset": 499, "endOffset": 533, "count": 0}], "isBlockCoverage": false}, {"functionName": "createTextNode", "ranges": [{"startOffset": 615, "endOffset": 647, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitCustomEvent", "ranges": [{"startOffset": 874, "endOffset": 939, "count": 0}], "isBlockCoverage": false}, {"functionName": "setAttributes", "ranges": [{"startOffset": 1105, "endOffset": 1374, "count": 0}], "isBlockCoverage": false}, {"functionName": "setAttributesMap", "ranges": [{"startOffset": 1523, "endOffset": 1618, "count": 0}], "isBlockCoverage": false}, {"functionName": "fragment", "ranges": [{"startOffset": 1730, "endOffset": 1896, "count": 0}], "isBlockCoverage": false}, {"functionName": "append", "ranges": [{"startOffset": 2006, "endOffset": 2083, "count": 0}], "isBlockCoverage": false}, {"functionName": "remove", "ranges": [{"startOffset": 2142, "endOffset": 2159, "count": 0}], "isBlockCoverage": false}, {"functionName": "addEventListener", "ranges": [{"startOffset": 2280, "endOffset": 2325, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeEventListener", "ranges": [{"startOffset": 2449, "endOffset": 2497, "count": 0}], "isBlockCoverage": false}, {"functionName": "addEventListeners", "ranges": [{"startOffset": 2641, "endOffset": 2751, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeEventListeners", "ranges": [{"startOffset": 2898, "endOffset": 3011, "count": 0}], "isBlockCoverage": false}, {"functionName": "element", "ranges": [{"startOffset": 3223, "endOffset": 3321, "count": 0}], "isBlockCoverage": false}, {"functionName": "canvas", "ranges": [{"startOffset": 3404, "endOffset": 3546, "count": 0}], "isBlockCoverage": false}, {"functionName": "pairToStyleString", "ranges": [{"startOffset": 3714, "endOffset": 3751, "count": 0}], "isBlockCoverage": false}, {"functionName": "pairsToStyleString", "ranges": [{"startOffset": 3865, "endOffset": 3911, "count": 0}], "isBlockCoverage": false}, {"functionName": "mapToStyleString", "ranges": [{"startOffset": 4006, "endOffset": 4066, "count": 0}], "isBlockCoverage": false}, {"functionName": "querySelector", "ranges": [{"startOffset": 4249, "endOffset": 4287, "count": 0}], "isBlockCoverage": false}, {"functionName": "querySelectorAll", "ranges": [{"startOffset": 4429, "endOffset": 4470, "count": 0}], "isBlockCoverage": false}, {"functionName": "getElementById", "ranges": [{"startOffset": 4557, "endOffset": 4614, "count": 0}], "isBlockCoverage": false}, {"functionName": "_parse", "ranges": [{"startOffset": 4688, "endOffset": 4776, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseFragment", "ranges": [{"startOffset": 4869, "endOffset": 4931, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseElement", "ranges": [{"startOffset": 5018, "endOffset": 5083, "count": 0}], "isBlockCoverage": false}, {"functionName": "replaceWith", "ranges": [{"startOffset": 5197, "endOffset": 5239, "count": 0}], "isBlockCoverage": false}, {"functionName": "insertBefore", "ranges": [{"startOffset": 5386, "endOffset": 5435, "count": 0}], "isBlockCoverage": false}, {"functionName": "append<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 5537, "endOffset": 5581, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkNodeType", "ranges": [{"startOffset": 6021, "endOffset": 6059, "count": 0}], "isBlockCoverage": false}, {"functionName": "isParentOf", "ranges": [{"startOffset": 6149, "endOffset": 6275, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "222", "url": "file:///home/<USER>/ylabs/lib0/eventloop.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3155, "count": 1}], "isBlockCoverage": true}, {"functionName": "_runQueue", "ranges": [{"startOffset": 292, "endOffset": 376, "count": 1}, {"startOffset": 341, "endOffset": 361, "count": 12}], "isBlockCoverage": true}, {"functionName": "enqueue", "ranges": [{"startOffset": 439, "endOffset": 524, "count": 12}, {"startOffset": 488, "endOffset": 522, "count": 1}], "isBlockCoverage": true}, {"functionName": "createTimeoutClass", "ranges": [{"startOffset": 699, "endOffset": 872, "count": 4}], "isBlockCoverage": true}, {"functionName": "TT", "ranges": [{"startOffset": 772, "endOffset": 824, "count": 6}], "isBlockCoverage": true}, {"functionName": "destroy", "ranges": [{"startOffset": 828, "endOffset": 870, "count": 2}], "isBlockCoverage": true}, {"functionName": "timeout", "ranges": [{"startOffset": 1039, "endOffset": 1104, "count": 4}], "isBlockCoverage": true}, {"functionName": "interval", "ranges": [{"startOffset": 1274, "endOffset": 1341, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1408, "endOffset": 1488, "count": 0}], "isBlockCoverage": false}, {"functionName": "animationFrame", "ranges": [{"startOffset": 1614, "endOffset": 1724, "count": 1}, {"startOffset": 1682, "endOffset": 1724, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1793, "endOffset": 1868, "count": 0}], "isBlockCoverage": false}, {"functionName": "idleCallback", "ranges": [{"startOffset": 2069, "endOffset": 2173, "count": 1}, {"startOffset": 2118, "endOffset": 2153, "count": 0}], "isBlockCoverage": true}, {"functionName": "createDebouncer", "ranges": [{"startOffset": 2424, "endOffset": 3154, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2695, "endOffset": 3152, "count": 11}, {"startOffset": 2739, "endOffset": 3113, "count": 10}, {"startOffset": 2770, "endOffset": 3019, "count": 4}, {"startOffset": 2842, "endOffset": 2856, "count": 1}, {"startOffset": 2900, "endOffset": 3011, "count": 1}, {"startOffset": 3019, "endOffset": 3113, "count": 9}, {"startOffset": 3113, "endOffset": 3148, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3065, "endOffset": 3096, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "223", "url": "file:///home/<USER>/ylabs/lib0/pair.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 885, "count": 1}], "isBlockCoverage": true}, {"functionName": "Pair", "ranges": [{"startOffset": 159, "endOffset": 234, "count": 3}], "isBlockCoverage": true}, {"functionName": "create", "ranges": [{"startOffset": 347, "endOffset": 385, "count": 2}], "isBlockCoverage": true}, {"functionName": "createReversed", "ranges": [{"startOffset": 504, "endOffset": 542, "count": 1}], "isBlockCoverage": true}, {"functionName": "for<PERSON>ach", "ranges": [{"startOffset": 658, "endOffset": 706, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 682, "endOffset": 705, "count": 3}], "isBlockCoverage": true}, {"functionName": "map", "ranges": [{"startOffset": 840, "endOffset": 884, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 860, "endOffset": 883, "count": 6}], "isBlockCoverage": true}]}, {"scriptId": "224", "url": "file:///home/<USER>/ylabs/lib0/observable.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3680, "count": 1}], "isBlockCoverage": true}, {"functionName": "ObservableV2", "ranges": [{"startOffset": 438, "endOffset": 564, "count": 1}], "isBlockCoverage": true}, {"functionName": "on", "ranges": [{"startOffset": 677, "endOffset": 797, "count": 3}], "isBlockCoverage": true}, {"functionName": "once", "ranges": [{"startOffset": 910, "endOffset": 1117, "count": 1}], "isBlockCoverage": true}, {"functionName": "_f", "ranges": [{"startOffset": 987, "endOffset": 1070, "count": 1}], "isBlockCoverage": true}, {"functionName": "off", "ranges": [{"startOffset": 1230, "endOffset": 1443, "count": 2}, {"startOffset": 1387, "endOffset": 1433, "count": 1}], "isBlockCoverage": true}, {"functionName": "emit", "ranges": [{"startOffset": 1810, "endOffset": 2084, "count": 6}, {"startOffset": 2029, "endOffset": 2044, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2064, "endOffset": 2079, "count": 6}], "isBlockCoverage": true}, {"functionName": "destroy", "ranges": [{"startOffset": 2088, "endOffset": 2139, "count": 1}], "isBlockCoverage": true}, {"functionName": "Observable", "ranges": [{"startOffset": 2259, "endOffset": 2375, "count": 0}], "isBlockCoverage": false}, {"functionName": "on", "ranges": [{"startOffset": 2437, "endOffset": 2520, "count": 0}], "isBlockCoverage": false}, {"functionName": "once", "ranges": [{"startOffset": 2582, "endOffset": 2747, "count": 0}], "isBlockCoverage": false}, {"functionName": "off", "ranges": [{"startOffset": 2809, "endOffset": 3022, "count": 0}], "isBlockCoverage": false}, {"functionName": "emit", "ranges": [{"startOffset": 3328, "endOffset": 3602, "count": 0}], "isBlockCoverage": false}, {"functionName": "destroy", "ranges": [{"startOffset": 3606, "endOffset": 3657, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "225", "url": "file:///home/<USER>/ylabs/lib0/set.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 472, "count": 1}], "isBlockCoverage": true}, {"functionName": "create", "ranges": [{"startOffset": 86, "endOffset": 101, "count": 7}], "isBlockCoverage": true}, {"functionName": "toArray", "ranges": [{"startOffset": 194, "endOffset": 216, "count": 1}], "isBlockCoverage": true}, {"functionName": "first", "ranges": [{"startOffset": 300, "endOffset": 347, "count": 3}, {"startOffset": 335, "endOffset": 347, "count": 1}], "isBlockCoverage": true}, {"functionName": "from", "ranges": [{"startOffset": 444, "endOffset": 471, "count": 4}], "isBlockCoverage": true}]}, {"scriptId": "226", "url": "file:///home/<USER>/ylabs/lib0/sort.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2173, "count": 1}], "isBlockCoverage": true}, {"functionName": "_insertionSort", "ranges": [{"startOffset": 476, "endOffset": 697, "count": 23238}, {"startOffset": 541, "endOffset": 695, "count": 573562}, {"startOffset": 569, "endOffset": 603, "count": 80295130}, {"startOffset": 610, "endOffset": 691, "count": 79721774}], "isBlockCoverage": true}, {"functionName": "insertionSort", "ranges": [{"startOffset": 835, "endOffset": 906, "count": 31}], "isBlockCoverage": true}, {"functionName": "_quickSort", "ranges": [{"startOffset": 1060, "endOffset": 1651, "count": 46378}, {"startOffset": 1108, "endOffset": 1154, "count": 23207}, {"startOffset": 1154, "endOffset": 1649, "count": 23171}, {"startOffset": 1258, "endOffset": 1569, "count": 1814574}, {"startOffset": 1301, "endOffset": 1322, "count": 2533806}, {"startOffset": 1364, "endOffset": 1385, "count": 2397612}, {"startOffset": 1404, "endOffset": 1427, "count": 23171}, {"startOffset": 1427, "endOffset": 1569, "count": 1791403}], "isBlockCoverage": true}, {"functionName": "quicksort", "ranges": [{"startOffset": 2105, "endOffset": 2172, "count": 36}], "isBlockCoverage": true}]}, {"scriptId": "227", "url": "file:///home/<USER>/ylabs/lib0/list.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3473, "count": 1}], "isBlockCoverage": true}, {"functionName": "ListNode", "ranges": [{"startOffset": 98, "endOffset": 242, "count": 64}], "isBlockCoverage": true}, {"functionName": "List", "ranges": [{"startOffset": 302, "endOffset": 461, "count": 3}], "isBlockCoverage": true}, {"functionName": "create", "ranges": [{"startOffset": 651, "endOffset": 667, "count": 3}], "isBlockCoverage": true}, {"functionName": "isEmpty", "ranges": [{"startOffset": 755, "endOffset": 784, "count": 63}], "isBlockCoverage": true}, {"functionName": "remove", "ranges": [{"startOffset": 1002, "endOffset": 1244, "count": 74}, {"startOffset": 1083, "endOffset": 1109, "count": 3}, {"startOffset": 1109, "endOffset": 1143, "count": 71}, {"startOffset": 1156, "endOffset": 1182, "count": 65}, {"startOffset": 1182, "endOffset": 1214, "count": 9}], "isBlockCoverage": true}, {"functionName": "insertBetween", "ranges": [{"startOffset": 1500, "endOffset": 1861, "count": 75}, {"startOffset": 1575, "endOffset": 1597, "count": 66}, {"startOffset": 1599, "endOffset": 1637, "count": 0}, {"startOffset": 1673, "endOffset": 1699, "count": 66}, {"startOffset": 1699, "endOffset": 1733, "count": 9}, {"startOffset": 1747, "endOffset": 1774, "count": 1}, {"startOffset": 1774, "endOffset": 1806, "count": 74}], "isBlockCoverage": true}, {"functionName": "replace", "ranges": [{"startOffset": 2102, "endOffset": 2202, "count": 1}], "isBlockCoverage": true}, {"functionName": "pushEnd", "ranges": [{"startOffset": 2306, "endOffset": 2362, "count": 72}], "isBlockCoverage": true}, {"functionName": "pushFront", "ranges": [{"startOffset": 2468, "endOffset": 2526, "count": 1}], "isBlockCoverage": true}, {"functionName": "popFront", "ranges": [{"startOffset": 2635, "endOffset": 2693, "count": 65}, {"startOffset": 2656, "endOffset": 2686, "count": 62}, {"startOffset": 2687, "endOffset": 2693, "count": 3}], "isBlockCoverage": true}, {"functionName": "popEnd", "ranges": [{"startOffset": 2800, "endOffset": 2854, "count": 3}, {"startOffset": 2819, "endOffset": 2847, "count": 2}, {"startOffset": 2848, "endOffset": 2854, "count": 1}], "isBlockCoverage": true}, {"functionName": "map", "ranges": [{"startOffset": 3002, "endOffset": 3155, "count": 2}, {"startOffset": 3101, "endOffset": 3140, "count": 6}], "isBlockCoverage": true}, {"functionName": "toArray", "ranges": [{"startOffset": 3242, "endOffset": 3263, "count": 1}], "isBlockCoverage": true}, {"functionName": "for<PERSON>ach", "ranges": [{"startOffset": 3393, "endOffset": 3472, "count": 1}, {"startOffset": 3441, "endOffset": 3470, "count": 3}], "isBlockCoverage": true}]}, {"scriptId": "228", "url": "file:///home/<USER>/ylabs/lib0/cache.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3970, "count": 1}], "isBlockCoverage": true}, {"functionName": "Entry", "ranges": [{"startOffset": 355, "endOffset": 587, "count": 5}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON>", "ranges": [{"startOffset": 681, "endOffset": 895, "count": 1}], "isBlockCoverage": true}, {"functionName": "removeStale", "ranges": [{"startOffset": 1035, "endOffset": 1235, "count": 26}, {"startOffset": 1117, "endOffset": 1157, "count": 20}, {"startOffset": 1159, "endOffset": 1220, "count": 2}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 1353, "endOffset": 1673, "count": 7}, {"startOffset": 1473, "endOffset": 1565, "count": 5}, {"startOffset": 1565, "endOffset": 1671, "count": 2}], "isBlockCoverage": true}, {"functionName": "getNode", "ranges": [{"startOffset": 1805, "endOffset": 1905, "count": 15}, {"startOffset": 1885, "endOffset": 1903, "count": 11}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2030, "endOffset": 2143, "count": 11}, {"startOffset": 2091, "endOffset": 2121, "count": 8}, {"startOffset": 2122, "endOffset": 2129, "count": 7}, {"startOffset": 2130, "endOffset": 2141, "count": 4}], "isBlockCoverage": true}, {"functionName": "refreshTimeout", "ranges": [{"startOffset": 2252, "endOffset": 2441, "count": 2}, {"startOffset": 2365, "endOffset": 2439, "count": 1}], "isBlockCoverage": true}, {"functionName": "getAsync", "ranges": [{"startOffset": 2757, "endOffset": 2839, "count": 4}, {"startOffset": 2818, "endOffset": 2825, "count": 3}, {"startOffset": 2826, "endOffset": 2837, "count": 1}], "isBlockCoverage": true}, {"functionName": "remove", "ranges": [{"startOffset": 2940, "endOffset": 3134, "count": 3}, {"startOffset": 2999, "endOffset": 3132, "count": 2}, {"startOffset": 3078, "endOffset": 3108, "count": 1}, {"startOffset": 3109, "endOffset": 3116, "count": 1}, {"startOffset": 3117, "endOffset": 3128, "count": 1}], "isBlockCoverage": true}, {"functionName": "setIfUndefined", "ranges": [{"startOffset": 3440, "endOffset": 3881, "count": 4}, {"startOffset": 3567, "endOffset": 3589, "count": 1}, {"startOffset": 3589, "endOffset": 3879, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3720, "endOffset": 3861, "count": 3}, {"startOffset": 3753, "endOffset": 3783, "count": 2}, {"startOffset": 3805, "endOffset": 3817, "count": 1}, {"startOffset": 3819, "endOffset": 3855, "count": 1}], "isBlockCoverage": true}, {"functionName": "create", "ranges": [{"startOffset": 3940, "endOffset": 3969, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "229", "url": "file:///home/<USER>/ylabs/lib0/symbol.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 241, "count": 1}], "isBlockCoverage": true}, {"functionName": "isSymbol", "ranges": [{"startOffset": 214, "endOffset": 240, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "230", "url": "file:///home/<USER>/ylabs/lib0/conditions.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 218, "count": 1}], "isBlockCoverage": true}, {"functionName": "undefinedToNull", "ranges": [{"startOffset": 186, "endOffset": 217, "count": 3}, {"startOffset": 214, "endOffset": 217, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "231", "url": "file:///home/<USER>/ylabs/lib0/storage.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1755, "count": 1}, {"startOffset": 753, "endOffset": 768, "count": 0}, {"startOffset": 770, "endOffset": 832, "count": 0}, {"startOffset": 835, "endOffset": 848, "count": 0}], "isBlockCoverage": true}, {"functionName": "VarStoragePolyfill", "ranges": [{"startOffset": 216, "endOffset": 261, "count": 1}], "isBlockCoverage": true}, {"functionName": "setItem", "ranges": [{"startOffset": 329, "endOffset": 390, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 431, "endOffset": 479, "count": 0}], "isBlockCoverage": false}, {"functionName": "onChange", "ranges": [{"startOffset": 1285, "endOffset": 1378, "count": 1}, {"startOffset": 1313, "endOffset": 1378, "count": 0}], "isBlockCoverage": true}, {"functionName": "offChange", "ranges": [{"startOffset": 1658, "endOffset": 1754, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "232", "url": "file:///home/<USER>/ylabs/lib0/url.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 875, "count": 1}], "isBlockCoverage": true}, {"functionName": "decodeQueryParams", "ranges": [{"startOffset": 244, "endOffset": 659, "count": 9}, {"startOffset": 466, "endOffset": 642, "count": 12}, {"startOffset": 519, "endOffset": 638, "count": 9}, {"startOffset": 626, "endOffset": 631, "count": 2}], "isBlockCoverage": true}, {"functionName": "encodeQueryParams", "ranges": [{"startOffset": 763, "endOffset": 874, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 794, "endOffset": 863, "count": 3}], "isBlockCoverage": true}]}, {"scriptId": "233", "url": "file:///home/<USER>/ylabs/lib0/metric.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1563, "count": 1}], "isBlockCoverage": true}, {"functionName": "prefix", "ranges": [{"startOffset": 1077, "endOffset": 1562, "count": 445}, {"startOffset": 1129, "endOffset": 1132, "count": 1}, {"startOffset": 1133, "endOffset": 1148, "count": 444}, {"startOffset": 1189, "endOffset": 1211, "count": 262}, {"startOffset": 1213, "endOffset": 1250, "count": 261}, {"startOffset": 1281, "endOffset": 1302, "count": 54}, {"startOffset": 1304, "endOffset": 1341, "count": 53}, {"startOffset": 1378, "endOffset": 1407, "count": 424}, {"startOffset": 1408, "endOffset": 1434, "count": 21}, {"startOffset": 1474, "endOffset": 1500, "count": 18}, {"startOffset": 1501, "endOffset": 1528, "count": 427}], "isBlockCoverage": true}]}, {"scriptId": "234", "url": "file:///home/<USER>/ylabs/lib0/crypto/common.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 445, "count": 1}], "isBlockCoverage": true}, {"functionName": "exportKeyJwk", "ranges": [{"startOffset": 107, "endOffset": 222, "count": 15}], "isBlockCoverage": true}, {"functionName": "exportKeyRaw", "ranges": [{"startOffset": 364, "endOffset": 444, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 417, "endOffset": 443, "count": 4}], "isBlockCoverage": true}]}, {"scriptId": "235", "url": "node:internal/navigator", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3406, "count": 1}], "isBlockCoverage": false}, {"functionName": "getNavigatorPlatform", "ranges": [{"startOffset": 654, "endOffset": 1941, "count": 1}, {"startOffset": 731, "endOffset": 844, "count": 0}, {"startOffset": 876, "endOffset": 1086, "count": 0}, {"startOffset": 1145, "endOffset": 1179, "count": 0}, {"startOffset": 1241, "endOffset": 1940, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1959, "endOffset": 3075, "count": 1}], "isBlockCoverage": false}, {"functionName": "Navigator", "ranges": [{"startOffset": 2091, "endOffset": 2211, "count": 1}, {"startOffset": 2166, "endOffset": 2210, "count": 0}], "isBlockCoverage": true}, {"functionName": "get hardwareConcurrency", "ranges": [{"startOffset": 2249, "endOffset": 2381, "count": 0}], "isBlockCoverage": false}, {"functionName": "get language", "ranges": [{"startOffset": 2419, "endOffset": 2574, "count": 0}], "isBlockCoverage": false}, {"functionName": "get languages", "ranges": [{"startOffset": 2619, "endOffset": 2723, "count": 0}], "isBlockCoverage": false}, {"functionName": "get userAgent", "ranges": [{"startOffset": 2761, "endOffset": 2927, "count": 0}], "isBlockCoverage": false}, {"functionName": "get platform", "ranges": [{"startOffset": 2965, "endOffset": 3073, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "236", "url": "node:net", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 70860, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1645, "endOffset": 1670, "count": 0}], "isBlockCoverage": false}, {"functionName": "noop", "ranges": [{"startOffset": 4225, "endOffset": 4233, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFlags", "ranges": [{"startOffset": 4706, "endOffset": 4935, "count": 0}], "isBlockCoverage": false}, {"functionName": "createHandle", "ranges": [{"startOffset": 4937, "endOffset": 5320, "count": 2}, {"startOffset": 5104, "endOffset": 5126, "count": 0}, {"startOffset": 5161, "endOffset": 5319, "count": 0}], "isBlockCoverage": true}, {"functionName": "getNewAsyncId", "ranges": [{"startOffset": 5323, "endOffset": 5461, "count": 2}, {"startOffset": 5418, "endOffset": 5436, "count": 0}], "isBlockCoverage": true}, {"functionName": "isPipeName", "ranges": [{"startOffset": 5464, "endOffset": 5547, "count": 0}], "isBlockCoverage": false}, {"functionName": "createServer", "ranges": [{"startOffset": 5748, "endOffset": 5852, "count": 0}], "isBlockCoverage": false}, {"functionName": "connect", "ranges": [{"startOffset": 6075, "endOffset": 6476, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDefaultAutoSelectFamily", "ranges": [{"startOffset": 6478, "endOffset": 6553, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDefaultAutoSelectFamily", "ranges": [{"startOffset": 6555, "endOffset": 6671, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDefaultAutoSelectFamilyAttemptTimeout", "ranges": [{"startOffset": 6673, "endOffset": 6776, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDefaultAutoSelectFamilyAttemptTimeout", "ranges": [{"startOffset": 6778, "endOffset": 6965, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalizeArgs", "ranges": [{"startOffset": 7459, "endOffset": 8185, "count": 0}], "isBlockCoverage": false}, {"functionName": "initSocketHandle", "ranges": [{"startOffset": 8257, "endOffset": 8860, "count": 2}, {"startOffset": 8613, "endOffset": 8854, "count": 0}], "isBlockCoverage": true}, {"functionName": "closeSocketHandle", "ranges": [{"startOffset": 8862, "endOffset": 9202, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket", "ranges": [{"startOffset": 9454, "endOffset": 14321, "count": 2}, {"startOffset": 9514, "endOffset": 9541, "count": 0}, {"startOffset": 9569, "endOffset": 9698, "count": 0}, {"startOffset": 9768, "endOffset": 10016, "count": 0}, {"startOffset": 10078, "endOffset": 10274, "count": 0}, {"startOffset": 10836, "endOffset": 10862, "count": 0}, {"startOffset": 11268, "endOffset": 11376, "count": 0}, {"startOffset": 11904, "endOffset": 11942, "count": 0}, {"startOffset": 12018, "endOffset": 12029, "count": 1}, {"startOffset": 12087, "endOffset": 12620, "count": 0}, {"startOffset": 12711, "endOffset": 12788, "count": 0}, {"startOffset": 12789, "endOffset": 12835, "count": 0}, {"startOffset": 12837, "endOffset": 13052, "count": 0}, {"startOffset": 13587, "endOffset": 13852, "count": 0}, {"startOffset": 13876, "endOffset": 13928, "count": 0}, {"startOffset": 14105, "endOffset": 14319, "count": 0}], "isBlockCoverage": true}, {"functionName": "_unrefTimer", "ranges": [{"startOffset": 14494, "endOffset": 14624, "count": 776}, {"startOffset": 14596, "endOffset": 14618, "count": 0}], "isBlockCoverage": true}, {"functionName": "Socket._final", "ranges": [{"startOffset": 14744, "endOffset": 15367, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterShutdown", "ranges": [{"startOffset": 15370, "endOffset": 15516, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeAfterFIN", "ranges": [{"startOffset": 15723, "endOffset": 16231, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._onTimeout", "ranges": [{"startOffset": 16313, "endOffset": 16817, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setNoDelay", "ranges": [{"startOffset": 16851, "endOffset": 17228, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setKeepAlive", "ranges": [{"startOffset": 17264, "endOffset": 17870, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.address", "ranges": [{"startOffset": 17901, "endOffset": 17945, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 18031, "endOffset": 18075, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 18155, "endOffset": 18211, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 18322, "endOffset": 18637, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 18726, "endOffset": 18806, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 18894, "endOffset": 18939, "count": 0}], "isBlockCoverage": false}, {"functionName": "tryReadStart", "ranges": [{"startOffset": 18947, "endOffset": 19197, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._read", "ranges": [{"startOffset": 19289, "endOffset": 19608, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.end", "ranges": [{"startOffset": 19635, "endOffset": 19791, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.resetAndDestroy", "ranges": [{"startOffset": 19829, "endOffset": 20179, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.pause", "ranges": [{"startOffset": 20207, "endOffset": 20524, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.resume", "ranges": [{"startOffset": 20554, "endOffset": 20738, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.read", "ranges": [{"startOffset": 20766, "endOffset": 20952, "count": 0}], "isBlockCoverage": false}, {"functionName": "onReadableStreamEnd", "ranges": [{"startOffset": 20999, "endOffset": 21098, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.destroySoon", "ranges": [{"startOffset": 21132, "endOffset": 21279, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._destroy", "ranges": [{"startOffset": 21311, "endOffset": 22825, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._reset", "ranges": [{"startOffset": 22854, "endOffset": 22953, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._getpeername", "ranges": [{"startOffset": 22988, "endOffset": 23285, "count": 0}], "isBlockCoverage": false}, {"functionName": "protoGetter", "ranges": [{"startOffset": 23288, "endOffset": 23470, "count": 9}], "isBlockCoverage": true}, {"functionName": "bytesRead", "ranges": [{"startOffset": 23497, "endOffset": 23588, "count": 0}], "isBlockCoverage": false}, {"functionName": "remoteAddress", "ranges": [{"startOffset": 23621, "endOffset": 23687, "count": 0}], "isBlockCoverage": false}, {"functionName": "remoteFamily", "ranges": [{"startOffset": 23719, "endOffset": 23783, "count": 0}], "isBlockCoverage": false}, {"functionName": "remotePort", "ranges": [{"startOffset": 23813, "endOffset": 23873, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._getsockname", "ranges": [{"startOffset": 23910, "endOffset": 24186, "count": 0}], "isBlockCoverage": false}, {"functionName": "localAddress", "ranges": [{"startOffset": 24218, "endOffset": 24283, "count": 0}], "isBlockCoverage": false}, {"functionName": "localPort", "ranges": [{"startOffset": 24313, "endOffset": 24372, "count": 0}], "isBlockCoverage": false}, {"functionName": "localFamily", "ranges": [{"startOffset": 24403, "endOffset": 24466, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.<computed>", "ranges": [{"startOffset": 24507, "endOffset": 24554, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._writeGeneric", "ranges": [{"startOffset": 24590, "endOffset": 25502, "count": 776}, {"startOffset": 24812, "endOffset": 25156, "count": 0}, {"startOffset": 25237, "endOffset": 25293, "count": 0}, {"startOffset": 25347, "endOffset": 25383, "count": 0}, {"startOffset": 25462, "endOffset": 25500, "count": 0}], "isBlockCoverage": true}, {"functionName": "connect", "ranges": [{"startOffset": 24907, "endOffset": 25021, "count": 0}], "isBlockCoverage": false}, {"functionName": "onClose", "ranges": [{"startOffset": 25028, "endOffset": 25107, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._writev", "ranges": [{"startOffset": 25533, "endOffset": 25601, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._write", "ranges": [{"startOffset": 25631, "endOffset": 25712, "count": 776}], "isBlockCoverage": true}, {"functionName": "_bytesDispatched", "ranges": [{"startOffset": 25905, "endOffset": 26009, "count": 0}], "isBlockCoverage": false}, {"functionName": "bytes<PERSON>ritten", "ranges": [{"startOffset": 26041, "endOffset": 26970, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkBindError", "ranges": [{"startOffset": 26975, "endOffset": 27774, "count": 0}], "isBlockCoverage": false}, {"functionName": "internalConnect", "ranges": [{"startOffset": 27777, "endOffset": 29981, "count": 0}], "isBlockCoverage": false}, {"functionName": "internalConnectMultiple", "ranges": [{"startOffset": 29984, "endOffset": 33202, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.connect", "ranges": [{"startOffset": 33231, "endOffset": 34812, "count": 0}], "isBlockCoverage": false}, {"functionName": "reinitializeHandle", "ranges": [{"startOffset": 34855, "endOffset": 35009, "count": 0}], "isBlockCoverage": false}, {"functionName": "socketToDnsFamily", "ranges": [{"startOffset": 35012, "endOffset": 35158, "count": 0}], "isBlockCoverage": false}, {"functionName": "lookupAndConnect", "ranges": [{"startOffset": 35160, "endOffset": 39091, "count": 0}], "isBlockCoverage": false}, {"functionName": "lookupAndConnectMultiple", "ranges": [{"startOffset": 39093, "endOffset": 43149, "count": 0}], "isBlockCoverage": false}, {"functionName": "connectErrorNT", "ranges": [{"startOffset": 43151, "endOffset": 43210, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.ref", "ranges": [{"startOffset": 43236, "endOffset": 43423, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.unref", "ranges": [{"startOffset": 43452, "endOffset": 43645, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterConnect", "ranges": [{"startOffset": 43649, "endOffset": 45337, "count": 0}], "isBlockCoverage": false}, {"functionName": "addClientAbortSignalOption", "ranges": [{"startOffset": 45339, "endOffset": 45749, "count": 0}], "isBlockCoverage": false}, {"functionName": "createConnectionError", "ranges": [{"startOffset": 45751, "endOffset": 46272, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterConnectMultiple", "ranges": [{"startOffset": 46274, "endOffset": 47592, "count": 0}], "isBlockCoverage": false}, {"functionName": "internalConnectMultipleTimeout", "ranges": [{"startOffset": 47594, "endOffset": 48090, "count": 0}], "isBlockCoverage": false}, {"functionName": "addServerAbortSignalOption", "ranges": [{"startOffset": 48092, "endOffset": 48522, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server", "ranges": [{"startOffset": 48524, "endOffset": 50376, "count": 0}], "isBlockCoverage": false}, {"functionName": "toNumber", "ranges": [{"startOffset": 50487, "endOffset": 50552, "count": 0}], "isBlockCoverage": false}, {"functionName": "createServerHandle", "ranges": [{"startOffset": 50620, "endOffset": 52108, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupListenHandle", "ranges": [{"startOffset": 52110, "endOffset": 54804, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitErrorNT", "ranges": [{"startOffset": 54871, "endOffset": 54933, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitListeningNT", "ranges": [{"startOffset": 54936, "endOffset": 55051, "count": 0}], "isBlockCoverage": false}, {"functionName": "listenInCluster", "ranges": [{"startOffset": 55054, "endOffset": 56636, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.listen", "ranges": [{"startOffset": 56665, "endOffset": 61101, "count": 0}], "isBlockCoverage": false}, {"functionName": "isIpv6LinkLocal", "ranges": [{"startOffset": 61104, "endOffset": 61742, "count": 0}], "isBlockCoverage": false}, {"functionName": "filter<PERSON>nly<PERSON>alidAddress", "ranges": [{"startOffset": 61744, "endOffset": 62029, "count": 0}], "isBlockCoverage": false}, {"functionName": "lookupAndListen", "ranges": [{"startOffset": 62031, "endOffset": 62652, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 62734, "endOffset": 62777, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.address", "ranges": [{"startOffset": 62853, "endOffset": 63131, "count": 0}], "isBlockCoverage": false}, {"functionName": "onconnection", "ranges": [{"startOffset": 63134, "endOffset": 65394, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.getConnections", "ranges": [{"startOffset": 65543, "endOffset": 66296, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.close", "ranges": [{"startOffset": 66325, "endOffset": 67215, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.<computed>", "ranges": [{"startOffset": 67257, "endOffset": 67377, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server._emitCloseIfDrained", "ranges": [{"startOffset": 67419, "endOffset": 67805, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitCloseNT", "ranges": [{"startOffset": 67809, "endOffset": 67892, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.<computed>", "ranges": [{"startOffset": 67951, "endOffset": 68113, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 68335, "endOffset": 68371, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 68375, "endOffset": 68416, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 68496, "endOffset": 68527, "count": 792}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 68531, "endOffset": 68567, "count": 2}], "isBlockCoverage": true}, {"functionName": "Server._setupWorker", "ranges": [{"startOffset": 68606, "endOffset": 68847, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.ref", "ranges": [{"startOffset": 68873, "endOffset": 68971, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.unref", "ranges": [{"startOffset": 68999, "endOffset": 69098, "count": 0}], "isBlockCoverage": false}, {"functionName": "_setSimultaneousAccepts", "ranges": [{"startOffset": 69240, "endOffset": 69909, "count": 0}], "isBlockCoverage": false}, {"functionName": "_setSimultaneousAccepts", "ranges": [{"startOffset": 69948, "endOffset": 70189, "count": 0}], "isBlockCoverage": false}, {"functionName": "get BlockList", "ranges": [{"startOffset": 70318, "endOffset": 70420, "count": 0}], "isBlockCoverage": false}, {"functionName": "get <PERSON>cket<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 70424, "endOffset": 70546, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "237", "url": "node:internal/stream_base_commons", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7070, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1057, "endOffset": 1082, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleWriteReq", "ranges": [{"startOffset": 1201, "endOffset": 2056, "count": 776}, {"startOffset": 1301, "endOffset": 1468, "count": 0}, {"startOffset": 1473, "endOffset": 1487, "count": 0}, {"startOffset": 1492, "endOffset": 1556, "count": 0}, {"startOffset": 1644, "endOffset": 1706, "count": 0}, {"startOffset": 1711, "endOffset": 1723, "count": 0}, {"startOffset": 1728, "endOffset": 1741, "count": 0}, {"startOffset": 1746, "endOffset": 1761, "count": 0}, {"startOffset": 1766, "endOffset": 1830, "count": 0}, {"startOffset": 1835, "endOffset": 2050, "count": 0}], "isBlockCoverage": true}, {"functionName": "onWriteComplete", "ranges": [{"startOffset": 2058, "endOffset": 2647, "count": 0}], "isBlockCoverage": false}, {"functionName": "createWriteWrap", "ranges": [{"startOffset": 2649, "endOffset": 2887, "count": 776}], "isBlockCoverage": true}, {"functionName": "writevGeneric", "ranges": [{"startOffset": 2889, "endOffset": 3528, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeGeneric", "ranges": [{"startOffset": 3530, "endOffset": 3735, "count": 776}], "isBlockCoverage": true}, {"functionName": "afterWriteDispatched", "ranges": [{"startOffset": 3737, "endOffset": 4044, "count": 776}, {"startOffset": 3904, "endOffset": 3959, "count": 0}], "isBlockCoverage": true}, {"functionName": "onStreamRead", "ranges": [{"startOffset": 4046, "endOffset": 5985, "count": 0}], "isBlockCoverage": false}, {"functionName": "setStreamTimeout", "ranges": [{"startOffset": 5987, "endOffset": 6863, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "238", "url": "node:internal/worker/io", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14082, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyMessageEvent", "ranges": [{"startOffset": 2346, "endOffset": 2524, "count": 0}], "isBlockCoverage": false}, {"functionName": "MessagePort.hasRef", "ranges": [{"startOffset": 3832, "endOffset": 3915, "count": 0}], "isBlockCoverage": false}, {"functionName": "value", "ranges": [{"startOffset": 4082, "endOffset": 4400, "count": 0}], "isBlockCoverage": false}, {"functionName": "oninit", "ranges": [{"startOffset": 4539, "endOffset": 4682, "count": 2}], "isBlockCoverage": true}, {"functionName": "MessagePortCloseEvent", "ranges": [{"startOffset": 4984, "endOffset": 5023, "count": 1}], "isBlockCoverage": true}, {"functionName": "onclose", "ranges": [{"startOffset": 5096, "endOffset": 5169, "count": 1}], "isBlockCoverage": true}, {"functionName": "MessagePort.close", "ranges": [{"startOffset": 5350, "endOffset": 5485, "count": 1}, {"startOffset": 5401, "endOffset": 5424, "count": 0}], "isBlockCoverage": true}, {"functionName": "inspect", "ranges": [{"startOffset": 5618, "endOffset": 6246, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupPortReferencing", "ranges": [{"startOffset": 6253, "endOffset": 7616, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6614, "endOffset": 6709, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6748, "endOffset": 6846, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6948, "endOffset": 7093, "count": 4}, {"startOffset": 7008, "endOffset": 7030, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7204, "endOffset": 7351, "count": 2}, {"startOffset": 7264, "endOffset": 7285, "count": 1}], "isBlockCoverage": true}, {"functionName": "newListener", "ranges": [{"startOffset": 7357, "endOffset": 7498, "count": 2}], "isBlockCoverage": true}, {"functionName": "removeListener", "ranges": [{"startOffset": 7502, "endOffset": 7614, "count": 1}], "isBlockCoverage": true}, {"functionName": "ReadableWorkerStdio", "ranges": [{"startOffset": 7666, "endOffset": 8015, "count": 0}], "isBlockCoverage": false}, {"functionName": "_read", "ranges": [{"startOffset": 8019, "endOffset": 8325, "count": 0}], "isBlockCoverage": false}, {"functionName": "WritableWorkerStdio", "ranges": [{"startOffset": 8376, "endOffset": 8526, "count": 0}], "isBlockCoverage": false}, {"functionName": "_writev", "ranges": [{"startOffset": 8530, "endOffset": 9184, "count": 0}], "isBlockCoverage": false}, {"functionName": "_final", "ranges": [{"startOffset": 9188, "endOffset": 9368, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9372, "endOffset": 9596, "count": 0}], "isBlockCoverage": false}, {"functionName": "createWorkerStdio", "ranges": [{"startOffset": 9600, "endOffset": 9870, "count": 0}], "isBlockCoverage": false}, {"functionName": "receiveMessageOnPort", "ranges": [{"startOffset": 9872, "endOffset": 10052, "count": 0}], "isBlockCoverage": false}, {"functionName": "onMessageEvent", "ranges": [{"startOffset": 10054, "endOffset": 10149, "count": 0}], "isBlockCoverage": false}, {"functionName": "isBroadcastChannel", "ranges": [{"startOffset": 10151, "endOffset": 10237, "count": 5}], "isBlockCoverage": true}, {"functionName": "BroadcastChannel", "ranges": [{"startOffset": 10324, "endOffset": 10838, "count": 2}, {"startOffset": 10382, "endOffset": 10417, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10842, "endOffset": 11255, "count": 0}], "isBlockCoverage": false}, {"functionName": "get name", "ranges": [{"startOffset": 11291, "endOffset": 11420, "count": 0}], "isBlockCoverage": false}, {"functionName": "close", "ranges": [{"startOffset": 11457, "endOffset": 11855, "count": 1}, {"startOffset": 11508, "endOffset": 11555, "count": 0}, {"startOffset": 11599, "endOffset": 11606, "count": 0}], "isBlockCoverage": true}, {"functionName": "postMessage", "ranges": [{"startOffset": 11923, "endOffset": 12354, "count": 4}, {"startOffset": 11987, "endOffset": 12034, "count": 0}, {"startOffset": 12073, "endOffset": 12111, "count": 0}, {"startOffset": 12155, "endOffset": 12230, "count": 0}, {"startOffset": 12295, "endOffset": 12350, "count": 0}], "isBlockCoverage": true}, {"functionName": "ref", "ranges": [{"startOffset": 12637, "endOffset": 12804, "count": 0}], "isBlockCoverage": false}, {"functionName": "unref", "ranges": [{"startOffset": 13089, "endOffset": 13260, "count": 0}], "isBlockCoverage": false}, {"functionName": "markAsUncloneable", "ranges": [{"startOffset": 13541, "endOffset": 13724, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "239", "url": "node:internal/util/colors", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1136, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyInternalTTY", "ranges": [{"startOffset": 32, "endOffset": 127, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldColorize", "ranges": [{"startOffset": 257, "endOffset": 517, "count": 777}, {"startOffset": 329, "endOffset": 388, "count": 0}, {"startOffset": 414, "endOffset": 512, "count": 0}], "isBlockCoverage": true}, {"functionName": "refresh", "ranges": [{"startOffset": 521, "endOffset": 1104, "count": 1}, {"startOffset": 638, "endOffset": 652, "count": 0}, {"startOffset": 696, "endOffset": 710, "count": 0}, {"startOffset": 754, "endOffset": 768, "count": 0}, {"startOffset": 813, "endOffset": 827, "count": 0}, {"startOffset": 869, "endOffset": 883, "count": 0}, {"startOffset": 926, "endOffset": 940, "count": 0}, {"startOffset": 984, "endOffset": 995, "count": 0}, {"startOffset": 1039, "endOffset": 1052, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "240", "url": "node:internal/crypto/webidl", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15752, "count": 1}], "isBlockCoverage": false}, {"functionName": "toNumber", "ranges": [{"startOffset": 1248, "endOffset": 1649, "count": 20}, {"startOffset": 1365, "endOffset": 1481, "count": 0}, {"startOffset": 1486, "endOffset": 1602, "count": 0}, {"startOffset": 1607, "endOffset": 1643, "count": 0}], "isBlockCoverage": true}, {"functionName": "type", "ranges": [{"startOffset": 1651, "endOffset": 2396, "count": 88340}, {"startOffset": 1692, "endOffset": 1706, "count": 0}, {"startOffset": 1734, "endOffset": 1777, "count": 0}, {"startOffset": 1782, "endOffset": 1821, "count": 0}, {"startOffset": 1826, "endOffset": 1863, "count": 0}, {"startOffset": 1868, "endOffset": 1905, "count": 40045}, {"startOffset": 1910, "endOffset": 1947, "count": 0}, {"startOffset": 1952, "endOffset": 1989, "count": 0}, {"startOffset": 1994, "endOffset": 2008, "count": 48295}, {"startOffset": 2029, "endOffset": 2045, "count": 48295}, {"startOffset": 2066, "endOffset": 2390, "count": 48295}], "isBlockCoverage": true}, {"functionName": "createIntegerConversion", "ranges": [{"startOffset": 2507, "endOffset": 3401, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2687, "endOffset": 3398, "count": 20}, {"startOffset": 2808, "endOffset": 2900, "count": 0}, {"startOffset": 2973, "endOffset": 3166, "count": 0}, {"startOffset": 3189, "endOffset": 3228, "count": 0}, {"startOffset": 3230, "endOffset": 3322, "count": 0}, {"startOffset": 3324, "endOffset": 3397, "count": 0}], "isBlockCoverage": true}, {"functionName": "converters.boolean", "ranges": [{"startOffset": 3448, "endOffset": 3462, "count": 56}], "isBlockCoverage": true}, {"functionName": "converters.DOMString", "ranges": [{"startOffset": 3654, "endOffset": 3894, "count": 84290}, {"startOffset": 3737, "endOffset": 3893, "count": 0}], "isBlockCoverage": true}, {"functionName": "converters.object", "ranges": [{"startOffset": 3917, "endOffset": 4045, "count": 2060}, {"startOffset": 3960, "endOffset": 4030, "count": 0}], "isBlockCoverage": true}, {"functionName": "isNonSharedArrayBuffer", "ranges": [{"startOffset": 4048, "endOffset": 4150, "count": 8}], "isBlockCoverage": true}, {"functionName": "isSharedArrayBuffer", "ranges": [{"startOffset": 4152, "endOffset": 4392, "count": 44061}, {"startOffset": 4374, "endOffset": 4391, "count": 0}], "isBlockCoverage": true}, {"functionName": "converters.Uint8Array", "ranges": [{"startOffset": 4418, "endOffset": 4809, "count": 4}, {"startOffset": 4544, "endOffset": 4625, "count": 0}, {"startOffset": 4686, "endOffset": 4794, "count": 0}], "isBlockCoverage": true}, {"functionName": "converters.BufferSource", "ranges": [{"startOffset": 4838, "endOffset": 5264, "count": 44065}, {"startOffset": 4896, "endOffset": 5096, "count": 44057}, {"startOffset": 4961, "endOffset": 5077, "count": 0}, {"startOffset": 5096, "endOffset": 5132, "count": 8}, {"startOffset": 5132, "endOffset": 5249, "count": 0}, {"startOffset": 5249, "endOffset": 5263, "count": 8}], "isBlockCoverage": true}, {"functionName": "requiredArguments", "ranges": [{"startOffset": 5354, "endOffset": 5667, "count": 537629}, {"startOffset": 5447, "endOffset": 5665, "count": 0}], "isBlockCoverage": true}, {"functionName": "createDictionaryConverter", "ranges": [{"startOffset": 5669, "endOffset": 7474, "count": 22}, {"startOffset": 5829, "endOffset": 5976, "count": 72}, {"startOffset": 5892, "endOffset": 5928, "count": 45}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6010, "endOffset": 6109, "count": 104}, {"startOffset": 6047, "endOffset": 6070, "count": 0}, {"startOffset": 6096, "endOffset": 6100, "count": 54}, {"startOffset": 6101, "endOffset": 6104, "count": 50}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6122, "endOffset": 7471, "count": 44175}, {"startOffset": 6211, "endOffset": 6228, "count": 0}, {"startOffset": 6235, "endOffset": 6247, "count": 0}, {"startOffset": 6290, "endOffset": 6394, "count": 0}, {"startOffset": 6503, "endOffset": 6521, "count": 0}, {"startOffset": 6523, "endOffset": 6552, "count": 0}, {"startOffset": 6614, "endOffset": 7446, "count": 50449}, {"startOffset": 6725, "endOffset": 6769, "count": 0}, {"startOffset": 6862, "endOffset": 7216, "count": 46316}, {"startOffset": 6959, "endOffset": 6963, "count": 0}, {"startOffset": 7216, "endOffset": 7440, "count": 4133}, {"startOffset": 7243, "endOffset": 7440, "count": 0}], "isBlockCoverage": true}, {"functionName": "createInterfaceConverter", "ranges": [{"startOffset": 7476, "endOffset": 7711, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7538, "endOffset": 7708, "count": 2043}, {"startOffset": 7606, "endOffset": 7690, "count": 0}], "isBlockCoverage": true}, {"functionName": "converters.AlgorithmIdentifier", "ranges": [{"startOffset": 7746, "endOffset": 7912, "count": 42105}, {"startOffset": 7826, "endOffset": 7870, "count": 2060}, {"startOffset": 7870, "endOffset": 7911, "count": 40045}], "isBlockCoverage": true}, {"functionName": "converter", "ranges": [{"startOffset": 8696, "endOffset": 8778, "count": 4}], "isBlockCoverage": true}, {"functionName": "converter", "ranges": [{"startOffset": 10188, "endOffset": 10273, "count": 0}], "isBlockCoverage": false}, {"functionName": "converter", "ranges": [{"startOffset": 10588, "endOffset": 10672, "count": 0}], "isBlockCoverage": false}, {"functionName": "converter", "ranges": [{"startOffset": 10854, "endOffset": 10938, "count": 0}], "isBlockCoverage": false}, {"functionName": "converter", "ranges": [{"startOffset": 11685, "endOffset": 11769, "count": 0}], "isBlockCoverage": false}, {"functionName": "simpleDomStringKey", "ranges": [{"startOffset": 11812, "endOffset": 11863, "count": 18}], "isBlockCoverage": true}, {"functionName": "converter", "ranges": [{"startOffset": 13611, "endOffset": 13695, "count": 8}], "isBlockCoverage": true}, {"functionName": "converter", "ranges": [{"startOffset": 14005, "endOffset": 14090, "count": 8}], "isBlockCoverage": true}, {"functionName": "converter", "ranges": [{"startOffset": 14606, "endOffset": 14679, "count": 0}], "isBlockCoverage": false}, {"functionName": "converter", "ranges": [{"startOffset": 15040, "endOffset": 15113, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "241", "url": "node:internal/crypto/aes", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8882, "count": 1}], "isBlockCoverage": false}, {"functionName": "getAlgorithmName", "ranges": [{"startOffset": 1248, "endOffset": 1487, "count": 4}, {"startOffset": 1312, "endOffset": 1351, "count": 0}, {"startOffset": 1356, "endOffset": 1395, "count": 0}, {"startOffset": 1444, "endOffset": 1481, "count": 0}], "isBlockCoverage": true}, {"functionName": "validate<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1489, "endOffset": 1648, "count": 19}, {"startOffset": 1588, "endOffset": 1646, "count": 0}], "isBlockCoverage": true}, {"functionName": "getVariant", "ranges": [{"startOffset": 1650, "endOffset": 2553, "count": 2006}, {"startOffset": 1708, "endOffset": 1915, "count": 0}, {"startOffset": 1920, "endOffset": 2127, "count": 0}, {"startOffset": 2180, "endOffset": 2220, "count": 0}, {"startOffset": 2229, "endOffset": 2269, "count": 0}, {"startOffset": 2326, "endOffset": 2339, "count": 0}, {"startOffset": 2344, "endOffset": 2547, "count": 0}], "isBlockCoverage": true}, {"functionName": "asyncAesCtrCipher", "ranges": [{"startOffset": 2555, "endOffset": 3160, "count": 0}], "isBlockCoverage": false}, {"functionName": "asyncAesCbcCipher", "ranges": [{"startOffset": 3162, "endOffset": 3437, "count": 0}], "isBlockCoverage": false}, {"functionName": "asyncAesKwCipher", "ranges": [{"startOffset": 3439, "endOffset": 3650, "count": 0}], "isBlockCoverage": false}, {"functionName": "asyncAesGcmCipher", "ranges": [{"startOffset": 3652, "endOffset": 5077, "count": 2006}, {"startOffset": 3803, "endOffset": 3933, "count": 0}, {"startOffset": 4019, "endOffset": 4097, "count": 0}, {"startOffset": 4182, "endOffset": 4793, "count": 1003}, {"startOffset": 4293, "endOffset": 4320, "count": 0}, {"startOffset": 4597, "endOffset": 4728, "count": 0}, {"startOffset": 4798, "endOffset": 4867, "count": 1003}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4893, "endOffset": 5073, "count": 2006}], "isBlockCoverage": true}, {"functionName": "aesCipher", "ranges": [{"startOffset": 5079, "endOffset": 5444, "count": 2006}, {"startOffset": 5160, "endOffset": 5229, "count": 0}, {"startOffset": 5234, "endOffset": 5303, "count": 0}, {"startOffset": 5382, "endOffset": 5438, "count": 0}], "isBlockCoverage": true}, {"functionName": "aesGenerateKey", "ranges": [{"startOffset": 5446, "endOffset": 6391, "count": 0}], "isBlockCoverage": false}, {"functionName": "aesImportKey", "ranges": [{"startOffset": 6393, "endOffset": 8791, "count": 10}, {"startOffset": 6730, "endOffset": 6831, "count": 0}, {"startOffset": 6888, "endOffset": 7008, "count": 0}, {"startOffset": 7013, "endOffset": 7138, "count": 9}, {"startOffset": 7143, "endOffset": 8431, "count": 1}, {"startOffset": 7189, "endOffset": 7244, "count": 0}, {"startOffset": 7287, "endOffset": 7354, "count": 0}, {"startOffset": 7424, "endOffset": 7458, "count": 0}, {"startOffset": 7460, "endOffset": 7545, "count": 0}, {"startOffset": 7669, "endOffset": 7702, "count": 0}, {"startOffset": 7704, "endOffset": 7827, "count": 0}, {"startOffset": 7926, "endOffset": 8044, "count": 0}, {"startOffset": 8243, "endOffset": 8356, "count": 0}, {"startOffset": 8436, "endOffset": 8562, "count": 0}, {"startOffset": 8596, "endOffset": 8686, "count": 9}], "isBlockCoverage": true}]}, {"scriptId": "242", "url": "node:internal/crypto/ec", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8219, "count": 1}], "isBlockCoverage": false}, {"functionName": "verifyAcceptableEcKeyUse", "ranges": [{"startOffset": 811, "endOffset": 1333, "count": 7}, {"startOffset": 909, "endOffset": 996, "count": 0}, {"startOffset": 1041, "endOffset": 1053, "count": 4}, {"startOffset": 1054, "endOffset": 1064, "count": 3}, {"startOffset": 1083, "endOffset": 1185, "count": 0}, {"startOffset": 1227, "endOffset": 1331, "count": 0}], "isBlockCoverage": true}, {"functionName": "createECPublicKeyRaw", "ranges": [{"startOffset": 1335, "endOffset": 1601, "count": 1}, {"startOffset": 1495, "endOffset": 1560, "count": 0}], "isBlockCoverage": true}, {"functionName": "ecGenerateKey", "ranges": [{"startOffset": 1603, "endOffset": 3297, "count": 4}, {"startOffset": 1787, "endOffset": 1881, "count": 0}, {"startOffset": 2015, "endOffset": 2134, "count": 0}, {"startOffset": 2152, "endOffset": 2345, "count": 0}, {"startOffset": 2804, "endOffset": 2929, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2441, "endOffset": 2593, "count": 0}], "isBlockCoverage": false}, {"functionName": "ecExportKey", "ranges": [{"startOffset": 3299, "endOffset": 3447, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3355, "endOffset": 3443, "count": 2}], "isBlockCoverage": true}, {"functionName": "ecImportKey", "ranges": [{"startOffset": 3449, "endOffset": 7452, "count": 7}, {"startOffset": 3654, "endOffset": 3748, "count": 0}, {"startOffset": 3835, "endOffset": 3976, "count": 0}, {"startOffset": 3981, "endOffset": 4331, "count": 0}, {"startOffset": 4336, "endOffset": 4690, "count": 0}, {"startOffset": 4695, "endOffset": 6652, "count": 6}, {"startOffset": 4741, "endOffset": 4796, "count": 0}, {"startOffset": 4837, "endOffset": 4904, "count": 0}, {"startOffset": 4951, "endOffset": 5060, "count": 0}, {"startOffset": 5221, "endOffset": 5404, "count": 0}, {"startOffset": 5528, "endOffset": 5561, "count": 0}, {"startOffset": 5563, "endOffset": 5686, "count": 0}, {"startOffset": 5755, "endOffset": 6166, "count": 0}, {"startOffset": 6300, "endOffset": 6418, "count": 0}, {"startOffset": 6457, "endOffset": 6512, "count": 0}, {"startOffset": 6556, "endOffset": 6594, "count": 3}, {"startOffset": 6595, "endOffset": 6632, "count": 3}, {"startOffset": 6657, "endOffset": 6805, "count": 1}, {"startOffset": 6952, "endOffset": 7008, "count": 0}, {"startOffset": 7071, "endOffset": 7136, "count": 0}, {"startOffset": 7283, "endOffset": 7343, "count": 0}], "isBlockCoverage": true}, {"functionName": "ecdsaSignVerify", "ranges": [{"startOffset": 7454, "endOffset": 8129, "count": 7}, {"startOffset": 7558, "endOffset": 7576, "count": 3}, {"startOffset": 7577, "endOffset": 7597, "count": 4}, {"startOffset": 7640, "endOffset": 7651, "count": 3}, {"startOffset": 7652, "endOffset": 7662, "count": 4}, {"startOffset": 7694, "endOffset": 7768, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7840, "endOffset": 8125, "count": 7}], "isBlockCoverage": true}]}, {"scriptId": "243", "url": "node:internal/crypto/rsa", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8953, "count": 1}], "isBlockCoverage": false}, {"functionName": "verifyAcceptableRsaKeyUse", "ranges": [{"startOffset": 1195, "endOffset": 1794, "count": 4}, {"startOffset": 1337, "endOffset": 1361, "count": 2}, {"startOffset": 1362, "endOffset": 1388, "count": 2}, {"startOffset": 1407, "endOffset": 1422, "count": 0}, {"startOffset": 1449, "endOffset": 1538, "count": 0}, {"startOffset": 1543, "endOffset": 1645, "count": 0}, {"startOffset": 1687, "endOffset": 1792, "count": 0}], "isBlockCoverage": true}, {"functionName": "rsaOaepCipher", "ranges": [{"startOffset": 1796, "endOffset": 2367, "count": 2}, {"startOffset": 1897, "endOffset": 1907, "count": 1}, {"startOffset": 1908, "endOffset": 1919, "count": 1}, {"startOffset": 1946, "endOffset": 2075, "count": 0}, {"startOffset": 2103, "endOffset": 2163, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2185, "endOffset": 2363, "count": 2}], "isBlockCoverage": true}, {"functionName": "rsaKeyGenerate", "ranges": [{"startOffset": 2369, "endOffset": 4392, "count": 4}, {"startOffset": 2696, "endOffset": 2829, "count": 0}, {"startOffset": 2973, "endOffset": 3089, "count": 0}, {"startOffset": 3107, "endOffset": 3285, "count": 0}, {"startOffset": 3928, "endOffset": 4070, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3415, "endOffset": 3567, "count": 0}], "isBlockCoverage": false}, {"functionName": "rsaExportKey", "ranges": [{"startOffset": 4394, "endOffset": 4582, "count": 0}], "isBlockCoverage": false}, {"functionName": "rsaImportKey", "ranges": [{"startOffset": 4584, "endOffset": 7839, "count": 4}, {"startOffset": 4756, "endOffset": 4908, "count": 0}, {"startOffset": 4913, "endOffset": 5274, "count": 0}, {"startOffset": 5279, "endOffset": 5644, "count": 0}, {"startOffset": 5695, "endOffset": 5750, "count": 0}, {"startOffset": 5793, "endOffset": 5860, "count": 0}, {"startOffset": 6032, "endOffset": 6229, "count": 0}, {"startOffset": 6353, "endOffset": 6386, "count": 0}, {"startOffset": 6388, "endOffset": 6511, "count": 0}, {"startOffset": 6704, "endOffset": 6817, "count": 0}, {"startOffset": 6947, "endOffset": 7065, "count": 0}, {"startOffset": 7104, "endOffset": 7159, "count": 0}, {"startOffset": 7204, "endOffset": 7242, "count": 2}, {"startOffset": 7243, "endOffset": 7280, "count": 2}, {"startOffset": 7306, "endOffset": 7432, "count": 0}, {"startOffset": 7483, "endOffset": 7549, "count": 0}], "isBlockCoverage": true}, {"functionName": "rsaSignVerify", "ranges": [{"startOffset": 7841, "endOffset": 8833, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "244", "url": "file:///home/<USER>/ylabs/lib0/hash/sha256.node.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 197, "count": 1}], "isBlockCoverage": true}, {"functionName": "digest", "ranges": [{"startOffset": 100, "endOffset": 196, "count": 20000}], "isBlockCoverage": true}]}], "timestamp": 11970.682377}