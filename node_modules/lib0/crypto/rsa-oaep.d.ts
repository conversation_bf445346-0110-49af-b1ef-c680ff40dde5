export { exportKeyJwk } from "./common.js";
export function encrypt(key: <PERSON><PERSON><PERSON><PERSON><PERSON>, data: Uint8Array): PromiseLike<Uint8Array>;
export function decrypt(key: <PERSON>pt<PERSON><PERSON><PERSON>, data: Uint8Array): PromiseLike<Uint8Array>;
export function generateKeyPair({ extractable, usages }?: {
    extractable?: boolean | undefined;
    usages?: Usages | undefined;
}): Promise<CryptoKeyPair>;
export function importKeyJwk(jwk: any, { extractable, usages }?: {
    extractable?: boolean | undefined;
    usages?: Usages | undefined;
}): Promise<CryptoKey>;
export type Usages = Array<'encrypt' | 'decrypt'>;
//# sourceMappingURL=rsa-oaep.d.ts.map