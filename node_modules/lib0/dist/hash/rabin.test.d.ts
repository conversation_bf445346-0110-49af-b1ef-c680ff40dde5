export function testPolynomialBasics(_tc: t.TestCase): void;
export function testIrreducibleInput(_tc: t.TestCase): void;
export function testIrreducibleSpread(_tc: t.TestCase): void;
export function testGenerateIrreducibles(_tc: t.TestCase): void;
export function testFingerprintCompatiblity(tc: t.TestCase): void;
export function testConflicts(tc: t.TestCase): void;
import * as t from '../testing.js';
//# sourceMappingURL=rabin.test.d.ts.map