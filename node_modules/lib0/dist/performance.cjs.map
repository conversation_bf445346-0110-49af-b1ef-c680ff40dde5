{"version": 3, "file": "performance.cjs", "sources": ["../performance.js"], "sourcesContent": ["/* eslint-env browser */\n\nexport const measure = performance.measure.bind(performance)\nexport const now = performance.now.bind(performance)\nexport const mark = performance.mark.bind(performance)\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACY,MAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAC;AAChD,MAAC,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAC;AACxC,MAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;;;;;;"}