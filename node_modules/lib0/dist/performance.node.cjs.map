{"version": 3, "file": "performance.node.cjs", "sources": ["../performance.node.js"], "sourcesContent": ["import { performance } from 'node:perf_hooks'\nimport { nop } from './function.js'\nimport * as time from './time.js'\n\n/**\n * @type {typeof performance.measure}\n */\n/* c8 ignore next */\nexport const measure = performance.measure ? performance.measure.bind(performance) : /** @type {any} */ (nop)\n\n/**\n * @type {typeof performance.now}\n */\n/* c8 ignore next */\nexport const now = performance.now ? performance.now.bind(performance) : time.getUnixTime\n\n/**\n * @type {typeof performance.mark}\n */\n/* c8 ignore next */\nexport const mark = performance.mark ? performance.mark.bind(performance) : /** @type {any} */ (nop)\n"], "names": ["performance", "nop", "time.getUnixTime"], "mappings": ";;;;;;;;;;;;;;AAIA;AACA;AACA;AACA;AACY,MAAC,OAAO,GAAGA,2BAAW,CAAC,OAAO,GAAGA,2BAAW,CAAC,OAAO,CAAC,IAAI,CAACA,2BAAW,CAAC,uBAAuBC,aAAG,EAAC;AAC7G;AACA;AACA;AACA;AACA;AACY,MAAC,GAAG,GAAGD,2BAAW,CAAC,GAAG,GAAGA,2BAAW,CAAC,GAAG,CAAC,IAAI,CAACA,2BAAW,CAAC,GAAGE,iBAAgB;AACzF;AACA;AACA;AACA;AACA;AACY,MAAC,IAAI,GAAGF,2BAAW,CAAC,IAAI,GAAGA,2BAAW,CAAC,IAAI,CAAC,IAAI,CAACA,2BAAW,CAAC,uBAAuBC,aAAG;;;;;;"}