/// <reference types="node" resolution-mode="require"/>
/**
 * @type {typeof performance.measure}
 */
export const measure: typeof performance.measure;
/**
 * @type {typeof performance.now}
 */
export const now: typeof performance.now;
/**
 * @type {typeof performance.mark}
 */
export const mark: typeof performance.mark;
import { performance } from 'node:perf_hooks';
//# sourceMappingURL=performance.node.d.ts.map