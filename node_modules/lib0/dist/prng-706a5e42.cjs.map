{"version": 3, "file": "prng-706a5e42.cjs", "sources": ["../prng/Xorshift32.js", "../prng/Xoroshiro128plus.js", "../prng.js"], "sourcesContent": ["/**\n * @module prng\n */\n\nimport * as binary from '../binary.js'\n\n/**\n * Xorshift32 is a very simple but elegang PRNG with a period of `2^32-1`.\n */\nexport class Xorshift32 {\n  /**\n   * @param {number} seed Unsigned 32 bit number\n   */\n  constructor (seed) {\n    this.seed = seed\n    /**\n     * @type {number}\n     */\n    this._state = seed\n  }\n\n  /**\n   * Generate a random signed integer.\n   *\n   * @return {Number} A 32 bit signed integer.\n   */\n  next () {\n    let x = this._state\n    x ^= x << 13\n    x ^= x >> 17\n    x ^= x << 5\n    this._state = x\n    return (x >>> 0) / (binary.BITS32 + 1)\n  }\n}\n", "/**\n * @module prng\n */\n\nimport { Xorshift32 } from './Xorshift32.js'\nimport * as binary from '../binary.js'\n\n/**\n * This is a variant of xoroshiro128plus - the fastest full-period generator passing BigCrush without systematic failures.\n *\n * This implementation follows the idea of the original xoroshiro128plus implementation,\n * but is optimized for the JavaScript runtime. I.e.\n * * The operations are performed on 32bit integers (the original implementation works with 64bit values).\n * * The initial 128bit state is computed based on a 32bit seed and Xorshift32.\n * * This implementation returns two 32bit values based on the 64bit value that is computed by xoroshiro128plus.\n *   Caution: The last addition step works slightly different than in the original implementation - the add carry of the\n *   first 32bit addition is not carried over to the last 32bit.\n *\n * [Reference implementation](http://vigna.di.unimi.it/xorshift/xoroshiro128plus.c)\n */\nexport class Xoroshiro128plus {\n  /**\n   * @param {number} seed Unsigned 32 bit number\n   */\n  constructor (seed) {\n    this.seed = seed\n    // This is a variant of Xoroshiro128plus to fill the initial state\n    const xorshift32 = new Xorshift32(seed)\n    this.state = new Uint32Array(4)\n    for (let i = 0; i < 4; i++) {\n      this.state[i] = xorshift32.next() * binary.BITS32\n    }\n    this._fresh = true\n  }\n\n  /**\n   * @return {number} Float/Double in [0,1)\n   */\n  next () {\n    const state = this.state\n    if (this._fresh) {\n      this._fresh = false\n      return ((state[0] + state[2]) >>> 0) / (binary.BITS32 + 1)\n    } else {\n      this._fresh = true\n      const s0 = state[0]\n      const s1 = state[1]\n      const s2 = state[2] ^ s0\n      const s3 = state[3] ^ s1\n      // function js_rotl (x, k) {\n      //   k = k - 32\n      //   const x1 = x[0]\n      //   const x2 = x[1]\n      //   x[0] = x2 << k | x1 >>> (32 - k)\n      //   x[1] = x1 << k | x2 >>> (32 - k)\n      // }\n      // rotl(s0, 55) // k = 23 = 55 - 32; j = 9 =  32 - 23\n      state[0] = (s1 << 23 | s0 >>> 9) ^ s2 ^ (s2 << 14 | s3 >>> 18)\n      state[1] = (s0 << 23 | s1 >>> 9) ^ s3 ^ (s3 << 14)\n      // rol(s1, 36) // k = 4 = 36 - 32; j = 23 = 32 - 9\n      state[2] = s3 << 4 | s2 >>> 28\n      state[3] = s2 << 4 | s3 >>> 28\n      return (((state[1] + state[3]) >>> 0) / (binary.BITS32 + 1))\n    }\n  }\n}\n\n/*\n// Reference implementation\n// Source: http://vigna.di.unimi.it/xorshift/xoroshiro128plus.c\n// By David Blackman and Sebastiano Vigna\n// Who published the reference implementation under Public Domain (CC0)\n\n#include <stdint.h>\n#include <stdio.h>\n\nuint64_t s[2];\n\nstatic inline uint64_t rotl(const uint64_t x, int k) {\n    return (x << k) | (x >> (64 - k));\n}\n\nuint64_t next(void) {\n    const uint64_t s0 = s[0];\n    uint64_t s1 = s[1];\n    s1 ^= s0;\n    s[0] = rotl(s0, 55) ^ s1 ^ (s1 << 14); // a, b\n    s[1] = rotl(s1, 36); // c\n    return (s[0] + s[1]) & 0xFFFFFFFF;\n}\n\nint main(void)\n{\n    int i;\n    s[0] = 1111 | (1337ul << 32);\n    s[1] = 1234 | (9999ul << 32);\n\n    printf(\"1000 outputs of genrand_int31()\\n\");\n    for (i=0; i<100; i++) {\n        printf(\"%10lu \", i);\n        printf(\"%10lu \", next());\n        printf(\"- %10lu \", s[0] >> 32);\n        printf(\"%10lu \", (s[0] << 32) >> 32);\n        printf(\"%10lu \", s[1] >> 32);\n        printf(\"%10lu \", (s[1] << 32) >> 32);\n        printf(\"\\n\");\n        // if (i%5==4) printf(\"\\n\");\n    }\n    return 0;\n}\n*/\n", "/**\n * Fast Pseudo Random Number Generators.\n *\n * Given a seed a PRNG generates a sequence of numbers that cannot be reasonably predicted.\n * Two PRNGs must generate the same random sequence of numbers if  given the same seed.\n *\n * @module prng\n */\n\nimport * as binary from './binary.js'\nimport { fromCharCode, fromCodePoint } from './string.js'\nimport * as math from './math.js'\nimport { Xoroshiro128plus } from './prng/Xoroshiro128plus.js'\nimport * as buffer from './buffer.js'\n\n/**\n * Description of the function\n *  @callback generatorNext\n *  @return {number} A random float in the cange of [0,1)\n */\n\n/**\n * A random type generator.\n *\n * @typedef {Object} PRNG\n * @property {generatorNext} next Generate new number\n */\nexport const DefaultPRNG = Xoroshiro128plus\n\n/**\n * Create a Xoroshiro128plus Pseudo-Random-Number-Generator.\n * This is the fastest full-period generator passing BigCrush without systematic failures.\n * But there are more PRNGs available in ./PRNG/.\n *\n * @param {number} seed A positive 32bit integer. Do not use negative numbers.\n * @return {PRNG}\n */\nexport const create = seed => new DefaultPRNG(seed)\n\n/**\n * Generates a single random bool.\n *\n * @param {PRNG} gen A random number generator.\n * @return {Boolean} A random boolean\n */\nexport const bool = gen => (gen.next() >= 0.5)\n\n/**\n * Generates a random integer with 53 bit resolution.\n *\n * @param {PRNG} gen A random number generator.\n * @param {Number} min The lower bound of the allowed return values (inclusive).\n * @param {Number} max The upper bound of the allowed return values (inclusive).\n * @return {Number} A random integer on [min, max]\n */\nexport const int53 = (gen, min, max) => math.floor(gen.next() * (max + 1 - min) + min)\n\n/**\n * Generates a random integer with 53 bit resolution.\n *\n * @param {PRNG} gen A random number generator.\n * @param {Number} min The lower bound of the allowed return values (inclusive).\n * @param {Number} max The upper bound of the allowed return values (inclusive).\n * @return {Number} A random integer on [min, max]\n */\nexport const uint53 = (gen, min, max) => math.abs(int53(gen, min, max))\n\n/**\n * Generates a random integer with 32 bit resolution.\n *\n * @param {PRNG} gen A random number generator.\n * @param {Number} min The lower bound of the allowed return values (inclusive).\n * @param {Number} max The upper bound of the allowed return values (inclusive).\n * @return {Number} A random integer on [min, max]\n */\nexport const int32 = (gen, min, max) => math.floor(gen.next() * (max + 1 - min) + min)\n\n/**\n * Generates a random integer with 53 bit resolution.\n *\n * @param {PRNG} gen A random number generator.\n * @param {Number} min The lower bound of the allowed return values (inclusive).\n * @param {Number} max The upper bound of the allowed return values (inclusive).\n * @return {Number} A random integer on [min, max]\n */\nexport const uint32 = (gen, min, max) => int32(gen, min, max) >>> 0\n\n/**\n * @deprecated\n * Optimized version of prng.int32. It has the same precision as prng.int32, but should be preferred when\n * openaring on smaller ranges.\n *\n * @param {PRNG} gen A random number generator.\n * @param {Number} min The lower bound of the allowed return values (inclusive).\n * @param {Number} max The upper bound of the allowed return values (inclusive). The max inclusive number is `binary.BITS31-1`\n * @return {Number} A random integer on [min, max]\n */\nexport const int31 = (gen, min, max) => int32(gen, min, max)\n\n/**\n * Generates a random real on [0, 1) with 53 bit resolution.\n *\n * @param {PRNG} gen A random number generator.\n * @return {Number} A random real number on [0, 1).\n */\nexport const real53 = gen => gen.next() // (((gen.next() >>> 5) * binary.BIT26) + (gen.next() >>> 6)) / MAX_SAFE_INTEGER\n\n/**\n * Generates a random character from char code 32 - 126. I.e. Characters, Numbers, special characters, and Space:\n *\n * @param {PRNG} gen A random number generator.\n * @return {string}\n *\n * (Space)!\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[/]^_`abcdefghijklmnopqrstuvwxyz{|}~\n */\nexport const char = gen => fromCharCode(int31(gen, 32, 126))\n\n/**\n * @param {PRNG} gen\n * @return {string} A single letter (a-z)\n */\nexport const letter = gen => fromCharCode(int31(gen, 97, 122))\n\n/**\n * @param {PRNG} gen\n * @param {number} [minLen=0]\n * @param {number} [maxLen=20]\n * @return {string} A random word (0-20 characters) without spaces consisting of letters (a-z)\n */\nexport const word = (gen, minLen = 0, maxLen = 20) => {\n  const len = int31(gen, minLen, maxLen)\n  let str = ''\n  for (let i = 0; i < len; i++) {\n    str += letter(gen)\n  }\n  return str\n}\n\n/**\n * TODO: this function produces invalid runes. Does not cover all of utf16!!\n *\n * @param {PRNG} gen\n * @return {string}\n */\nexport const utf16Rune = gen => {\n  const codepoint = int31(gen, 0, 256)\n  return fromCodePoint(codepoint)\n}\n\n/**\n * @param {PRNG} gen\n * @param {number} [maxlen = 20]\n */\nexport const utf16String = (gen, maxlen = 20) => {\n  const len = int31(gen, 0, maxlen)\n  let str = ''\n  for (let i = 0; i < len; i++) {\n    str += utf16Rune(gen)\n  }\n  return str\n}\n\n/**\n * Returns one element of a given array.\n *\n * @param {PRNG} gen A random number generator.\n * @param {Array<T>} array Non empty Array of possible values.\n * @return {T} One of the values of the supplied Array.\n * @template T\n */\nexport const oneOf = (gen, array) => array[int31(gen, 0, array.length - 1)]\n\n/**\n * @param {PRNG} gen\n * @param {number} len\n * @return {Uint8Array}\n */\nexport const uint8Array = (gen, len) => {\n  const buf = buffer.createUint8ArrayFromLen(len)\n  for (let i = 0; i < buf.length; i++) {\n    buf[i] = int32(gen, 0, binary.BITS8)\n  }\n  return buf\n}\n\n/* c8 ignore start */\n/**\n * @param {PRNG} gen\n * @param {number} len\n * @return {Uint16Array}\n */\nexport const uint16Array = (gen, len) => new Uint16Array(uint8Array(gen, len * 2).buffer)\n\n/**\n * @param {PRNG} gen\n * @param {number} len\n * @return {Uint32Array}\n */\nexport const uint32Array = (gen, len) => new Uint32Array(uint8Array(gen, len * 4).buffer)\n/* c8 ignore stop */\n"], "names": ["binary.BITS32", "math.floor", "math.abs", "fromCharCode", "fromCodePoint", "buffer.createUint8ArrayFromLen", "binary.BITS8"], "mappings": ";;;;;;;AAAA;AACA;AACA;AAGA;AACA;AACA;AACA;AACO,MAAM,UAAU,CAAC;AACxB;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,IAAI,EAAE;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,KAAI;AACpB;AACA;AACA;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,KAAI;AACtB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,GAAG;AACV,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,OAAM;AACvB,IAAI,CAAC,IAAI,CAAC,IAAI,GAAE;AAChB,IAAI,CAAC,IAAI,CAAC,IAAI,GAAE;AAChB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAC;AACf,IAAI,IAAI,CAAC,MAAM,GAAG,EAAC;AACnB,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,KAAKA,aAAa,GAAG,CAAC,CAAC;AAC1C,GAAG;AACH;;AClCA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,gBAAgB,CAAC;AAC9B;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,IAAI,EAAE;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,KAAI;AACpB;AACA,IAAI,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,EAAC;AAC3C,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,WAAW,CAAC,CAAC,EAAC;AACnC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAChC,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,IAAI,EAAE,GAAGA,cAAa;AACvD,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,GAAG,KAAI;AACtB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,GAAG;AACV,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,MAAK;AAC5B,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,MAAM,IAAI,CAAC,MAAM,GAAG,MAAK;AACzB,MAAM,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAKA,aAAa,GAAG,CAAC,CAAC;AAChE,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,MAAM,GAAG,KAAI;AACxB,MAAM,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,EAAC;AACzB,MAAM,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,EAAC;AACzB,MAAM,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAE;AAC9B,MAAM,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,EAAC;AACpE,MAAM,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC;AACxD;AACA,MAAM,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,GAAE;AACpC,MAAM,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,GAAE;AACpC,MAAM,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC;AAClE,KAAK;AACL,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AC9GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,WAAW,GAAG,iBAAgB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,MAAM,GAAG,IAAI,IAAI,IAAI,WAAW,CAAC,IAAI,EAAC;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,IAAI,GAAG,GAAG,KAAK,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,EAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,KAAKC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,EAAC;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,KAAKC,QAAQ,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,KAAKD,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,EAAC;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,KAAK,EAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAC;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,GAAE;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,IAAI,GAAG,GAAG,IAAIE,mBAAY,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC,EAAC;AAC5D;AACA;AACA;AACA;AACA;AACY,MAAC,MAAM,GAAG,GAAG,IAAIA,mBAAY,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC,EAAC;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,IAAI,GAAG,CAAC,GAAG,EAAE,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,EAAE,KAAK;AACtD,EAAE,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAC;AACxC,EAAE,IAAI,GAAG,GAAG,GAAE;AACd,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAChC,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,EAAC;AACtB,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,SAAS,GAAG,GAAG,IAAI;AAChC,EAAE,MAAM,SAAS,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAC;AACtC,EAAE,OAAOC,oBAAa,CAAC,SAAS,CAAC;AACjC,EAAC;AACD;AACA;AACA;AACA;AACA;AACY,MAAC,WAAW,GAAG,CAAC,GAAG,EAAE,MAAM,GAAG,EAAE,KAAK;AACjD,EAAE,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAC;AACnC,EAAE,IAAI,GAAG,GAAG,GAAE;AACd,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAChC,IAAI,GAAG,IAAI,SAAS,CAAC,GAAG,EAAC;AACzB,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,KAAK,GAAG,CAAC,GAAG,EAAE,KAAK,KAAK,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAAC;AAC3E;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,KAAK;AACxC,EAAE,MAAM,GAAG,GAAGC,8BAA8B,CAAC,GAAG,EAAC;AACjD,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACvC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,EAAEC,YAAY,EAAC;AACxC,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,KAAK,IAAI,WAAW,CAAC,UAAU,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,MAAM,EAAC;AACzF;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,KAAK,IAAI,WAAW,CAAC,UAAU,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,MAAM,EAAC;AACzF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}