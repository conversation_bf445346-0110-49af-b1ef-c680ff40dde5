/**
 * Xorshift32 is a very simple but elegang PRNG with a period of `2^32-1`.
 */
export class Xorshift32 {
    /**
     * @param {number} seed Unsigned 32 bit number
     */
    constructor(seed: number);
    seed: number;
    /**
     * @type {number}
     */
    _state: number;
    /**
     * Generate a random signed integer.
     *
     * @return {Number} A 32 bit signed integer.
     */
    next(): number;
}
//# sourceMappingURL=Xorshift32.d.ts.map