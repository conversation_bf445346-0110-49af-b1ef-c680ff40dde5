{"version": 3, "file": "awareness.cjs", "sources": ["../awareness.js"], "sourcesContent": ["/**\n * @module awareness-protocol\n */\n\nimport * as encoding from 'lib0/encoding'\nimport * as decoding from 'lib0/decoding'\nimport * as time from 'lib0/time'\nimport * as math from 'lib0/math'\nimport { Observable } from 'lib0/observable'\nimport * as f from 'lib0/function'\nimport * as Y from 'yjs' // eslint-disable-line\n\nexport const outdatedTimeout = 30000\n\n/**\n * @typedef {Object} MetaClientState\n * @property {number} MetaClientState.clock\n * @property {number} MetaClientState.lastUpdated unix timestamp\n */\n\n/**\n * The Awareness class implements a simple shared state protocol that can be used for non-persistent data like awareness information\n * (cursor, username, status, ..). Each client can update its own local state and listen to state changes of\n * remote clients. Every client may set a state of a remote peer to `null` to mark the client as offline.\n *\n * Each client is identified by a unique client id (something we borrow from `doc.clientID`). A client can override\n * its own state by propagating a message with an increasing timestamp (`clock`). If such a message is received, it is\n * applied if the known state of that client is older than the new state (`clock < newClock`). If a client thinks that\n * a remote client is offline, it may propagate a message with\n * `{ clock: currentClientClock, state: null, client: remoteClient }`. If such a\n * message is received, and the known clock of that client equals the received clock, it will override the state with `null`.\n *\n * Before a client disconnects, it should propagate a `null` state with an updated clock.\n *\n * Awareness states must be updated every 30 seconds. Otherwise the Awareness instance will delete the client state.\n *\n * @extends {Observable<string>}\n */\nexport class Awareness extends Observable {\n  /**\n   * @param {Y.Doc} doc\n   */\n  constructor (doc) {\n    super()\n    this.doc = doc\n    /**\n     * @type {number}\n     */\n    this.clientID = doc.clientID\n    /**\n     * Maps from client id to client state\n     * @type {Map<number, Object<string, any>>}\n     */\n    this.states = new Map()\n    /**\n     * @type {Map<number, MetaClientState>}\n     */\n    this.meta = new Map()\n    this._checkInterval = /** @type {any} */ (setInterval(() => {\n      const now = time.getUnixTime()\n      if (this.getLocalState() !== null && (outdatedTimeout / 2 <= now - /** @type {{lastUpdated:number}} */ (this.meta.get(this.clientID)).lastUpdated)) {\n        // renew local clock\n        this.setLocalState(this.getLocalState())\n      }\n      /**\n       * @type {Array<number>}\n       */\n      const remove = []\n      this.meta.forEach((meta, clientid) => {\n        if (clientid !== this.clientID && outdatedTimeout <= now - meta.lastUpdated && this.states.has(clientid)) {\n          remove.push(clientid)\n        }\n      })\n      if (remove.length > 0) {\n        removeAwarenessStates(this, remove, 'timeout')\n      }\n    }, math.floor(outdatedTimeout / 10)))\n    doc.on('destroy', () => {\n      this.destroy()\n    })\n    this.setLocalState({})\n  }\n\n  destroy () {\n    this.emit('destroy', [this])\n    this.setLocalState(null)\n    super.destroy()\n    clearInterval(this._checkInterval)\n  }\n\n  /**\n   * @return {Object<string,any>|null}\n   */\n  getLocalState () {\n    return this.states.get(this.clientID) || null\n  }\n\n  /**\n   * @param {Object<string,any>|null} state\n   */\n  setLocalState (state) {\n    const clientID = this.clientID\n    const currLocalMeta = this.meta.get(clientID)\n    const clock = currLocalMeta === undefined ? 0 : currLocalMeta.clock + 1\n    const prevState = this.states.get(clientID)\n    if (state === null) {\n      this.states.delete(clientID)\n    } else {\n      this.states.set(clientID, state)\n    }\n    this.meta.set(clientID, {\n      clock,\n      lastUpdated: time.getUnixTime()\n    })\n    const added = []\n    const updated = []\n    const filteredUpdated = []\n    const removed = []\n    if (state === null) {\n      removed.push(clientID)\n    } else if (prevState == null) {\n      if (state != null) {\n        added.push(clientID)\n      }\n    } else {\n      updated.push(clientID)\n      if (!f.equalityDeep(prevState, state)) {\n        filteredUpdated.push(clientID)\n      }\n    }\n    if (added.length > 0 || filteredUpdated.length > 0 || removed.length > 0) {\n      this.emit('change', [{ added, updated: filteredUpdated, removed }, 'local'])\n    }\n    this.emit('update', [{ added, updated, removed }, 'local'])\n  }\n\n  /**\n   * @param {string} field\n   * @param {any} value\n   */\n  setLocalStateField (field, value) {\n    const state = this.getLocalState()\n    if (state !== null) {\n      this.setLocalState({\n        ...state,\n        [field]: value\n      })\n    }\n  }\n\n  /**\n   * @return {Map<number,Object<string,any>>}\n   */\n  getStates () {\n    return this.states\n  }\n}\n\n/**\n * Mark (remote) clients as inactive and remove them from the list of active peers.\n * This change will be propagated to remote clients.\n *\n * @param {Awareness} awareness\n * @param {Array<number>} clients\n * @param {any} origin\n */\nexport const removeAwarenessStates = (awareness, clients, origin) => {\n  const removed = []\n  for (let i = 0; i < clients.length; i++) {\n    const clientID = clients[i]\n    if (awareness.states.has(clientID)) {\n      awareness.states.delete(clientID)\n      if (clientID === awareness.clientID) {\n        const curMeta = /** @type {MetaClientState} */ (awareness.meta.get(clientID))\n        awareness.meta.set(clientID, {\n          clock: curMeta.clock + 1,\n          lastUpdated: time.getUnixTime()\n        })\n      }\n      removed.push(clientID)\n    }\n  }\n  if (removed.length > 0) {\n    awareness.emit('change', [{ added: [], updated: [], removed }, origin])\n    awareness.emit('update', [{ added: [], updated: [], removed }, origin])\n  }\n}\n\n/**\n * @param {Awareness} awareness\n * @param {Array<number>} clients\n * @return {Uint8Array}\n */\nexport const encodeAwarenessUpdate = (awareness, clients, states = awareness.states) => {\n  const len = clients.length\n  const encoder = encoding.createEncoder()\n  encoding.writeVarUint(encoder, len)\n  for (let i = 0; i < len; i++) {\n    const clientID = clients[i]\n    const state = states.get(clientID) || null\n    const clock = /** @type {MetaClientState} */ (awareness.meta.get(clientID)).clock\n    encoding.writeVarUint(encoder, clientID)\n    encoding.writeVarUint(encoder, clock)\n    encoding.writeVarString(encoder, JSON.stringify(state))\n  }\n  return encoding.toUint8Array(encoder)\n}\n\n/**\n * Modify the content of an awareness update before re-encoding it to an awareness update.\n *\n * This might be useful when you have a central server that wants to ensure that clients\n * cant hijack somebody elses identity.\n *\n * @param {Uint8Array} update\n * @param {function(any):any} modify\n * @return {Uint8Array}\n */\nexport const modifyAwarenessUpdate = (update, modify) => {\n  const decoder = decoding.createDecoder(update)\n  const encoder = encoding.createEncoder()\n  const len = decoding.readVarUint(decoder)\n  encoding.writeVarUint(encoder, len)\n  for (let i = 0; i < len; i++) {\n    const clientID = decoding.readVarUint(decoder)\n    const clock = decoding.readVarUint(decoder)\n    const state = JSON.parse(decoding.readVarString(decoder))\n    const modifiedState = modify(state)\n    encoding.writeVarUint(encoder, clientID)\n    encoding.writeVarUint(encoder, clock)\n    encoding.writeVarString(encoder, JSON.stringify(modifiedState))\n  }\n  return encoding.toUint8Array(encoder)\n}\n\n/**\n * @param {Awareness} awareness\n * @param {Uint8Array} update\n * @param {any} origin This will be added to the emitted change event\n */\nexport const applyAwarenessUpdate = (awareness, update, origin) => {\n  const decoder = decoding.createDecoder(update)\n  const timestamp = time.getUnixTime()\n  const added = []\n  const updated = []\n  const filteredUpdated = []\n  const removed = []\n  const len = decoding.readVarUint(decoder)\n  for (let i = 0; i < len; i++) {\n    const clientID = decoding.readVarUint(decoder)\n    let clock = decoding.readVarUint(decoder)\n    const state = JSON.parse(decoding.readVarString(decoder))\n    const clientMeta = awareness.meta.get(clientID)\n    const prevState = awareness.states.get(clientID)\n    const currClock = clientMeta === undefined ? 0 : clientMeta.clock\n    if (currClock < clock || (currClock === clock && state === null && awareness.states.has(clientID))) {\n      if (state === null) {\n        // never let a remote client remove this local state\n        if (clientID === awareness.clientID && awareness.getLocalState() != null) {\n          // remote client removed the local state. Do not remote state. Broadcast a message indicating\n          // that this client still exists by increasing the clock\n          clock++\n        } else {\n          awareness.states.delete(clientID)\n        }\n      } else {\n        awareness.states.set(clientID, state)\n      }\n      awareness.meta.set(clientID, {\n        clock,\n        lastUpdated: timestamp\n      })\n      if (clientMeta === undefined && state !== null) {\n        added.push(clientID)\n      } else if (clientMeta !== undefined && state === null) {\n        removed.push(clientID)\n      } else if (state !== null) {\n        if (!f.equalityDeep(state, prevState)) {\n          filteredUpdated.push(clientID)\n        }\n        updated.push(clientID)\n      }\n    }\n  }\n  if (added.length > 0 || filteredUpdated.length > 0 || removed.length > 0) {\n    awareness.emit('change', [{\n      added, updated: filteredUpdated, removed\n    }, origin])\n  }\n  if (added.length > 0 || updated.length > 0 || removed.length > 0) {\n    awareness.emit('update', [{\n      added, updated, removed\n    }, origin])\n  }\n}\n"], "names": ["Observable", "time", "math", "f", "encoding", "decoding"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AAQA;AACY,MAAC,eAAe,GAAG,MAAK;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,SAAS,SAASA,qBAAU,CAAC;AAC1C;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,GAAG,EAAE;AACpB,IAAI,KAAK,GAAE;AACX,IAAI,IAAI,CAAC,GAAG,GAAG,IAAG;AAClB;AACA;AACA;AACA,IAAI,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,SAAQ;AAChC;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,GAAE;AAC3B;AACA;AACA;AACA,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,GAAE;AACzB,IAAI,IAAI,CAAC,cAAc,uBAAuB,WAAW,CAAC,MAAM;AAChE,MAAM,MAAM,GAAG,GAAGC,eAAI,CAAC,WAAW,GAAE;AACpC,MAAM,IAAI,IAAI,CAAC,aAAa,EAAE,KAAK,IAAI,KAAK,eAAe,GAAG,CAAC,IAAI,GAAG,uCAAuC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,WAAW,CAAC,EAAE;AAC1J;AACA,QAAQ,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE,EAAC;AAChD,OAAO;AACP;AACA;AACA;AACA,MAAM,MAAM,MAAM,GAAG,GAAE;AACvB,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,QAAQ,KAAK;AAC5C,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,IAAI,eAAe,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;AAClH,UAAU,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAC;AAC/B,SAAS;AACT,OAAO,EAAC;AACR,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;AAC7B,QAAQ,qBAAqB,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAC;AACtD,OAAO;AACP,KAAK,EAAEC,eAAI,CAAC,KAAK,CAAC,eAAe,GAAG,EAAE,CAAC,CAAC,EAAC;AACzC,IAAI,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,MAAM;AAC5B,MAAM,IAAI,CAAC,OAAO,GAAE;AACpB,KAAK,EAAC;AACN,IAAI,IAAI,CAAC,aAAa,CAAC,EAAE,EAAC;AAC1B,GAAG;AACH;AACA,EAAE,OAAO,CAAC,GAAG;AACb,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,EAAC;AAChC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,EAAC;AAC5B,IAAI,KAAK,CAAC,OAAO,GAAE;AACnB,IAAI,aAAa,CAAC,IAAI,CAAC,cAAc,EAAC;AACtC,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,GAAG;AACnB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI;AACjD,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,CAAC,KAAK,EAAE;AACxB,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAQ;AAClC,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAC;AACjD,IAAI,MAAM,KAAK,GAAG,aAAa,KAAK,SAAS,GAAG,CAAC,GAAG,aAAa,CAAC,KAAK,GAAG,EAAC;AAC3E,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAC;AAC/C,IAAI,IAAI,KAAK,KAAK,IAAI,EAAE;AACxB,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAC;AAClC,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAC;AACtC,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;AAC5B,MAAM,KAAK;AACX,MAAM,WAAW,EAAED,eAAI,CAAC,WAAW,EAAE;AACrC,KAAK,EAAC;AACN,IAAI,MAAM,KAAK,GAAG,GAAE;AACpB,IAAI,MAAM,OAAO,GAAG,GAAE;AACtB,IAAI,MAAM,eAAe,GAAG,GAAE;AAC9B,IAAI,MAAM,OAAO,GAAG,GAAE;AACtB,IAAI,IAAI,KAAK,KAAK,IAAI,EAAE;AACxB,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAC;AAC5B,KAAK,MAAM,IAAI,SAAS,IAAI,IAAI,EAAE;AAClC,MAAM,IAAI,KAAK,IAAI,IAAI,EAAE;AACzB,QAAQ,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAC;AAC5B,OAAO;AACP,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAC;AAC5B,MAAM,IAAI,CAACE,YAAC,CAAC,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE;AAC7C,QAAQ,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAC;AACtC,OAAO;AACP,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AAC9E,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,eAAe,EAAE,OAAO,EAAE,EAAE,OAAO,CAAC,EAAC;AAClF,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,OAAO,CAAC,EAAC;AAC/D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,kBAAkB,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE;AACpC,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,GAAE;AACtC,IAAI,IAAI,KAAK,KAAK,IAAI,EAAE;AACxB,MAAM,IAAI,CAAC,aAAa,CAAC;AACzB,QAAQ,GAAG,KAAK;AAChB,QAAQ,CAAC,KAAK,GAAG,KAAK;AACtB,OAAO,EAAC;AACR,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,MAAM;AACtB,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,qBAAqB,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,KAAK;AACrE,EAAE,MAAM,OAAO,GAAG,GAAE;AACpB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC3C,IAAI,MAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,EAAC;AAC/B,IAAI,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;AACxC,MAAM,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAC;AACvC,MAAM,IAAI,QAAQ,KAAK,SAAS,CAAC,QAAQ,EAAE;AAC3C,QAAQ,MAAM,OAAO,mCAAmC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAC;AACrF,QAAQ,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;AACrC,UAAU,KAAK,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC;AAClC,UAAU,WAAW,EAAEF,eAAI,CAAC,WAAW,EAAE;AACzC,SAAS,EAAC;AACV,OAAO;AACP,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAC;AAC5B,KAAK;AACL,GAAG;AACH,EAAE,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1B,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,MAAM,CAAC,EAAC;AAC3E,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,MAAM,CAAC,EAAC;AAC3E,GAAG;AACH,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,qBAAqB,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,GAAG,SAAS,CAAC,MAAM,KAAK;AACxF,EAAE,MAAM,GAAG,GAAG,OAAO,CAAC,OAAM;AAC5B,EAAE,MAAM,OAAO,GAAGG,mBAAQ,CAAC,aAAa,GAAE;AAC1C,EAAEA,mBAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,EAAC;AACrC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAChC,IAAI,MAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,EAAC;AAC/B,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,KAAI;AAC9C,IAAI,MAAM,KAAK,kCAAkC,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,MAAK;AACrF,IAAIA,mBAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,QAAQ,EAAC;AAC5C,IAAIA,mBAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,EAAC;AACzC,IAAIA,mBAAQ,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAC;AAC3D,GAAG;AACH,EAAE,OAAOA,mBAAQ,CAAC,YAAY,CAAC,OAAO,CAAC;AACvC,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,qBAAqB,GAAG,CAAC,MAAM,EAAE,MAAM,KAAK;AACzD,EAAE,MAAM,OAAO,GAAGC,mBAAQ,CAAC,aAAa,CAAC,MAAM,EAAC;AAChD,EAAE,MAAM,OAAO,GAAGD,mBAAQ,CAAC,aAAa,GAAE;AAC1C,EAAE,MAAM,GAAG,GAAGC,mBAAQ,CAAC,WAAW,CAAC,OAAO,EAAC;AAC3C,EAAED,mBAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,EAAC;AACrC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAChC,IAAI,MAAM,QAAQ,GAAGC,mBAAQ,CAAC,WAAW,CAAC,OAAO,EAAC;AAClD,IAAI,MAAM,KAAK,GAAGA,mBAAQ,CAAC,WAAW,CAAC,OAAO,EAAC;AAC/C,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAACA,mBAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,EAAC;AAC7D,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,EAAC;AACvC,IAAID,mBAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,QAAQ,EAAC;AAC5C,IAAIA,mBAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,EAAC;AACzC,IAAIA,mBAAQ,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EAAC;AACnE,GAAG;AACH,EAAE,OAAOA,mBAAQ,CAAC,YAAY,CAAC,OAAO,CAAC;AACvC,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,oBAAoB,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,KAAK;AACnE,EAAE,MAAM,OAAO,GAAGC,mBAAQ,CAAC,aAAa,CAAC,MAAM,EAAC;AAChD,EAAE,MAAM,SAAS,GAAGJ,eAAI,CAAC,WAAW,GAAE;AACtC,EAAE,MAAM,KAAK,GAAG,GAAE;AAClB,EAAE,MAAM,OAAO,GAAG,GAAE;AACpB,EAAE,MAAM,eAAe,GAAG,GAAE;AAC5B,EAAE,MAAM,OAAO,GAAG,GAAE;AACpB,EAAE,MAAM,GAAG,GAAGI,mBAAQ,CAAC,WAAW,CAAC,OAAO,EAAC;AAC3C,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAChC,IAAI,MAAM,QAAQ,GAAGA,mBAAQ,CAAC,WAAW,CAAC,OAAO,EAAC;AAClD,IAAI,IAAI,KAAK,GAAGA,mBAAQ,CAAC,WAAW,CAAC,OAAO,EAAC;AAC7C,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAACA,mBAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,EAAC;AAC7D,IAAI,MAAM,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAC;AACnD,IAAI,MAAM,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAC;AACpD,IAAI,MAAM,SAAS,GAAG,UAAU,KAAK,SAAS,GAAG,CAAC,GAAG,UAAU,CAAC,MAAK;AACrE,IAAI,IAAI,SAAS,GAAG,KAAK,KAAK,SAAS,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE;AACxG,MAAM,IAAI,KAAK,KAAK,IAAI,EAAE;AAC1B;AACA,QAAQ,IAAI,QAAQ,KAAK,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,aAAa,EAAE,IAAI,IAAI,EAAE;AAClF;AACA;AACA,UAAU,KAAK,GAAE;AACjB,SAAS,MAAM;AACf,UAAU,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAC;AAC3C,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAC;AAC7C,OAAO;AACP,MAAM,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;AACnC,QAAQ,KAAK;AACb,QAAQ,WAAW,EAAE,SAAS;AAC9B,OAAO,EAAC;AACR,MAAM,IAAI,UAAU,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE;AACtD,QAAQ,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAC;AAC5B,OAAO,MAAM,IAAI,UAAU,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE;AAC7D,QAAQ,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAC;AAC9B,OAAO,MAAM,IAAI,KAAK,KAAK,IAAI,EAAE;AACjC,QAAQ,IAAI,CAACF,YAAC,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE;AAC/C,UAAU,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAC;AACxC,SAAS;AACT,QAAQ,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAC;AAC9B,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5E,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC9B,MAAM,KAAK,EAAE,OAAO,EAAE,eAAe,EAAE,OAAO;AAC9C,KAAK,EAAE,MAAM,CAAC,EAAC;AACf,GAAG;AACH,EAAE,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AACpE,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC9B,MAAM,KAAK,EAAE,OAAO,EAAE,OAAO;AAC7B,KAAK,EAAE,MAAM,CAAC,EAAC;AACf,GAAG;AACH;;;;;;;;;"}