{"version": 3, "file": "sync.cjs", "sources": ["../sync.js"], "sourcesContent": ["/**\n * @module sync-protocol\n */\n\nimport * as encoding from 'lib0/encoding'\nimport * as decoding from 'lib0/decoding'\nimport * as Y from 'yjs'\n\n/**\n * @typedef {Map<number, number>} StateMap\n */\n\n/**\n * Core Yjs defines two message types:\n * • YjsSyncStep1: Includes the State Set of the sending client. When received, the client should reply with YjsSyncStep2.\n * • YjsSyncStep2: Includes all missing structs and the complete delete set. When received, the client is assured that it\n *   received all information from the remote client.\n *\n * In a peer-to-peer network, you may want to introduce a SyncDone message type. Both parties should initiate the connection\n * with SyncStep1. When a client received SyncStep2, it should reply with SyncDone. When the local client received both\n * SyncStep2 and SyncDone, it is assured that it is synced to the remote client.\n *\n * In a client-server model, you want to handle this differently: The client should initiate the connection with SyncStep1.\n * When the server receives SyncStep1, it should reply with SyncStep2 immediately followed by SyncStep1. The client replies\n * with SyncStep2 when it receives SyncStep1. Optionally the server may send a SyncDone after it received SyncStep2, so the\n * client knows that the sync is finished.  There are two reasons for this more elaborated sync model: 1. This protocol can\n * easily be implemented on top of http and websockets. 2. The server should only reply to requests, and not initiate them.\n * Therefore it is necessary that the client initiates the sync.\n *\n * Construction of a message:\n * [messageType : varUint, message definition..]\n *\n * Note: A message does not include information about the room name. This must to be handled by the upper layer protocol!\n *\n * stringify[messageType] stringifies a message definition (messageType is already read from the bufffer)\n */\n\nexport const messageYjsSyncStep1 = 0\nexport const messageYjsSyncStep2 = 1\nexport const messageYjsUpdate = 2\n\n/**\n * Create a sync step 1 message based on the state of the current shared document.\n *\n * @param {encoding.Encoder} encoder\n * @param {Y.Doc} doc\n */\nexport const writeSyncStep1 = (encoder, doc) => {\n  encoding.writeVarUint(encoder, messageYjsSyncStep1)\n  const sv = Y.encodeStateVector(doc)\n  encoding.writeVarUint8Array(encoder, sv)\n}\n\n/**\n * @param {encoding.Encoder} encoder\n * @param {Y.Doc} doc\n * @param {Uint8Array} [encodedStateVector]\n */\nexport const writeSyncStep2 = (encoder, doc, encodedStateVector) => {\n  encoding.writeVarUint(encoder, messageYjsSyncStep2)\n  encoding.writeVarUint8Array(encoder, Y.encodeStateAsUpdate(doc, encodedStateVector))\n}\n\n/**\n * Read SyncStep1 message and reply with SyncStep2.\n *\n * @param {decoding.Decoder} decoder The reply to the received message\n * @param {encoding.Encoder} encoder The received message\n * @param {Y.Doc} doc\n */\nexport const readSyncStep1 = (decoder, encoder, doc) =>\n  writeSyncStep2(encoder, doc, decoding.readVarUint8Array(decoder))\n\n/**\n * Read and apply Structs and then DeleteStore to a y instance.\n *\n * @param {decoding.Decoder} decoder\n * @param {Y.Doc} doc\n * @param {any} transactionOrigin\n */\nexport const readSyncStep2 = (decoder, doc, transactionOrigin) => {\n  try {\n    Y.applyUpdate(doc, decoding.readVarUint8Array(decoder), transactionOrigin)\n  } catch (error) {\n    // This catches errors that are thrown by event handlers\n    console.error('Caught error while handling a Yjs update', error)\n  }\n}\n\n/**\n * @param {encoding.Encoder} encoder\n * @param {Uint8Array} update\n */\nexport const writeUpdate = (encoder, update) => {\n  encoding.writeVarUint(encoder, messageYjsUpdate)\n  encoding.writeVarUint8Array(encoder, update)\n}\n\n/**\n * Read and apply Structs and then DeleteStore to a y instance.\n *\n * @param {decoding.Decoder} decoder\n * @param {Y.Doc} doc\n * @param {any} transactionOrigin\n */\nexport const readUpdate = readSyncStep2\n\n/**\n * @param {decoding.Decoder} decoder A message received from another client\n * @param {encoding.Encoder} encoder The reply message. Does not need to be sent if empty.\n * @param {Y.Doc} doc\n * @param {any} transactionOrigin\n */\nexport const readSyncMessage = (decoder, encoder, doc, transactionOrigin) => {\n  const messageType = decoding.readVarUint(decoder)\n  switch (messageType) {\n    case messageYjsSyncStep1:\n      readSyncStep1(decoder, encoder, doc)\n      break\n    case messageYjsSyncStep2:\n      readSyncStep2(decoder, doc, transactionOrigin)\n      break\n    case messageYjsUpdate:\n      readUpdate(decoder, doc, transactionOrigin)\n      break\n    default:\n      throw new Error('Unknown message type')\n  }\n  return messageType\n}\n"], "names": ["encoding", "Y", "decoding"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,mBAAmB,GAAG,EAAC;AACxB,MAAC,mBAAmB,GAAG,EAAC;AACxB,MAAC,gBAAgB,GAAG,EAAC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,cAAc,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK;AAChD,EAAEA,mBAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,mBAAmB,EAAC;AACrD,EAAE,MAAM,EAAE,GAAGC,YAAC,CAAC,iBAAiB,CAAC,GAAG,EAAC;AACrC,EAAED,mBAAQ,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE,EAAC;AAC1C,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,cAAc,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,kBAAkB,KAAK;AACpE,EAAEA,mBAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,mBAAmB,EAAC;AACrD,EAAEA,mBAAQ,CAAC,kBAAkB,CAAC,OAAO,EAAEC,YAAC,CAAC,mBAAmB,CAAC,GAAG,EAAE,kBAAkB,CAAC,EAAC;AACtF,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG;AACnD,EAAE,cAAc,CAAC,OAAO,EAAE,GAAG,EAAEC,mBAAQ,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,aAAa,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,iBAAiB,KAAK;AAClE,EAAE,IAAI;AACN,IAAID,YAAC,CAAC,WAAW,CAAC,GAAG,EAAEC,mBAAQ,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,iBAAiB,EAAC;AAC9E,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB;AACA,IAAI,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,EAAC;AACpE,GAAG;AACH,EAAC;AACD;AACA;AACA;AACA;AACA;AACY,MAAC,WAAW,GAAG,CAAC,OAAO,EAAE,MAAM,KAAK;AAChD,EAAEF,mBAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,gBAAgB,EAAC;AAClD,EAAEA,mBAAQ,CAAC,kBAAkB,CAAC,OAAO,EAAE,MAAM,EAAC;AAC9C,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,UAAU,GAAG,cAAa;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,eAAe,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,iBAAiB,KAAK;AAC7E,EAAE,MAAM,WAAW,GAAGE,mBAAQ,CAAC,WAAW,CAAC,OAAO,EAAC;AACnD,EAAE,QAAQ,WAAW;AACrB,IAAI,KAAK,mBAAmB;AAC5B,MAAM,aAAa,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,EAAC;AAC1C,MAAM,KAAK;AACX,IAAI,KAAK,mBAAmB;AAC5B,MAAM,aAAa,CAAC,OAAO,EAAE,GAAG,EAAE,iBAAiB,EAAC;AACpD,MAAM,KAAK;AACX,IAAI,KAAK,gBAAgB;AACzB,MAAM,UAAU,CAAC,OAAO,EAAE,GAAG,EAAE,iBAAiB,EAAC;AACjD,MAAM,KAAK;AACX,IAAI;AACJ,MAAM,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC;AAC7C,GAAG;AACH,EAAE,OAAO,WAAW;AACpB;;;;;;;;;;;;;"}