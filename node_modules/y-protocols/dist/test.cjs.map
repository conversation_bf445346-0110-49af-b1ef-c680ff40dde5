{"version": 3, "file": "test.cjs", "sources": ["../awareness.test.js", "../test.js"], "sourcesContent": ["\nimport * as Y from 'yjs'\nimport * as t from 'lib0/testing'\nimport * as awareness from './awareness'\n\n/**\n * @param {t.TestCase} tc\n */\nexport const testAwareness = tc => {\n  const doc1 = new Y.Doc()\n  doc1.clientID = 0\n  const doc2 = new Y.Doc()\n  doc2.clientID = 1\n  const aw1 = new awareness.Awareness(doc1)\n  const aw2 = new awareness.Awareness(doc2)\n  aw1.on('update', /** @param {any} p */ ({ added, updated, removed }) => {\n    const enc = awareness.encodeAwarenessUpdate(aw1, added.concat(updated).concat(removed))\n    awareness.applyAwarenessUpdate(aw2, enc, 'custom')\n  })\n  let lastChangeLocal = /** @type {any} */ (null)\n  aw1.on('change', /** @param {any} change */ change => {\n    lastChangeLocal = change\n  })\n  let lastChange = /** @type {any} */ (null)\n  aw2.on('change', /** @param {any} change */ change => {\n    lastChange = change\n  })\n  aw1.setLocalState({ x: 3 })\n  t.compare(aw2.getStates().get(0), { x: 3 })\n  t.assert(/** @type {any} */ (aw2.meta.get(0)).clock === 1)\n  t.compare(lastChange.added, [0])\n  // When creating an Awareness instance, the the local client is already marked as available, so it is not updated.\n  t.compare(lastChangeLocal, { added: [], updated: [0], removed: [] })\n\n  // update state\n  lastChange = null\n  lastChangeLocal = null\n  aw1.setLocalState({ x: 4 })\n  t.compare(aw2.getStates().get(0), { x: 4 })\n  t.compare(lastChangeLocal, { added: [], updated: [0], removed: [] })\n  t.compare(lastChangeLocal, lastChange)\n\n  lastChange = null\n  lastChangeLocal = null\n  aw1.setLocalState({ x: 4 })\n  t.assert(lastChange === null)\n  t.assert(/** @type {any} */ (aw2.meta.get(0)).clock === 3)\n  t.compare(lastChangeLocal, lastChange)\n  aw1.setLocalState(null)\n  t.assert(lastChange.removed.length === 1)\n  t.compare(aw1.getStates().get(0), undefined)\n  t.compare(lastChangeLocal, lastChange)\n}\n", "import { runTests } from 'lib0/testing'\nimport * as log from 'lib0/logging'\nimport * as awareness from './awareness.test.js'\n\nimport { isBrowser, isNode } from 'lib0/environment'\n\n/* istanbul ignore if */\nif (isBrowser) {\n  log.createVConsole(document.body)\n}\n\nrunTests({\n  awareness\n}).then(success => {\n  /* istanbul ignore next */\n  if (isNode) {\n    process.exit(success ? 0 : 1)\n  }\n})\n"], "names": ["Y", "awareness.Awareness", "awareness.encodeAwarenessUpdate", "awareness.applyAwarenessUpdate", "t", "<PERSON><PERSON><PERSON><PERSON>", "log", "runTests", "isNode"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA;AACA;AACA;AACO,MAAM,aAAa,GAAG,EAAE,IAAI;AACnC,EAAE,MAAM,IAAI,GAAG,IAAIA,YAAC,CAAC,GAAG,GAAE;AAC1B,EAAE,IAAI,CAAC,QAAQ,GAAG,EAAC;AACnB,EAAE,MAAM,IAAI,GAAG,IAAIA,YAAC,CAAC,GAAG,GAAE;AAC1B,EAAE,IAAI,CAAC,QAAQ,GAAG,EAAC;AACnB,EAAE,MAAM,GAAG,GAAG,IAAIC,qBAAmB,CAAC,IAAI,EAAC;AAC3C,EAAE,MAAM,GAAG,GAAG,IAAIA,qBAAmB,CAAC,IAAI,EAAC;AAC3C,EAAE,GAAG,CAAC,EAAE,CAAC,QAAQ,wBAAwB,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC1E,IAAI,MAAM,GAAG,GAAGC,iCAA+B,CAAC,GAAG,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,EAAC;AAC3F,IAAIC,gCAA8B,CAAC,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAC;AACtD,GAAG,EAAC;AACJ,EAAE,IAAI,eAAe,uBAAuB,IAAI,EAAC;AACjD,EAAE,GAAG,CAAC,EAAE,CAAC,QAAQ,6BAA6B,MAAM,IAAI;AACxD,IAAI,eAAe,GAAG,OAAM;AAC5B,GAAG,EAAC;AACJ,EAAE,IAAI,UAAU,uBAAuB,IAAI,EAAC;AAC5C,EAAE,GAAG,CAAC,EAAE,CAAC,QAAQ,6BAA6B,MAAM,IAAI;AACxD,IAAI,UAAU,GAAG,OAAM;AACvB,GAAG,EAAC;AACJ,EAAE,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAC;AAC7B,EAAEC,YAAC,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAC;AAC7C,EAAEA,YAAC,CAAC,MAAM,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,EAAC;AAC5D,EAAEA,YAAC,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAC;AAClC;AACA,EAAEA,YAAC,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,EAAC;AACtE;AACA;AACA,EAAE,UAAU,GAAG,KAAI;AACnB,EAAE,eAAe,GAAG,KAAI;AACxB,EAAE,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAC;AAC7B,EAAEA,YAAC,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAC;AAC7C,EAAEA,YAAC,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,EAAC;AACtE,EAAEA,YAAC,CAAC,OAAO,CAAC,eAAe,EAAE,UAAU,EAAC;AACxC;AACA,EAAE,UAAU,GAAG,KAAI;AACnB,EAAE,eAAe,GAAG,KAAI;AACxB,EAAE,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAC;AAC7B,EAAEA,YAAC,CAAC,MAAM,CAAC,UAAU,KAAK,IAAI,EAAC;AAC/B,EAAEA,YAAC,CAAC,MAAM,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,EAAC;AAC5D,EAAEA,YAAC,CAAC,OAAO,CAAC,eAAe,EAAE,UAAU,EAAC;AACxC,EAAE,GAAG,CAAC,aAAa,CAAC,IAAI,EAAC;AACzB,EAAEA,YAAC,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAC;AAC3C,EAAEA,YAAC,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,SAAS,EAAC;AAC9C,EAAEA,YAAC,CAAC,OAAO,CAAC,eAAe,EAAE,UAAU,EAAC;AACxC;;;;;;;AC9CA;AACA,IAAIC,qBAAS,EAAE;AACf,EAAEC,cAAG,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAC;AACnC,CAAC;AACD;AACAC,UAAQ,CAAC;AACT,EAAE,SAAS;AACX,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI;AACnB;AACA,EAAE,IAAIC,kBAAM,EAAE;AACd,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,EAAC;AACjC,GAAG;AACH,CAAC;;"}