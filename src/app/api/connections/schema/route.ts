import { NextRequest, NextResponse } from 'next/server';
import { ConnectionService } from '@/lib/database/services/connection-service';
import { z } from 'zod';

// Validation schema
const getSchemaSchema = z.object({
  connectionId: z.string().cuid(),
  refresh: z.boolean().optional().default(false),
});

// GET /api/connections/schema - Get database schema
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const connectionId = searchParams.get('connectionId');
    const refresh = searchParams.get('refresh') === 'true';

    if (!connectionId) {
      return NextResponse.json(
        { error: 'connectionId parameter required' },
        { status: 400 }
      );
    }

    const validatedData = getSchemaSchema.parse({ connectionId, refresh });

    // Check if connection exists
    const connection = await ConnectionService.getConnectionById(validatedData.connectionId);
    if (!connection) {
      return NextResponse.json(
        { error: 'Connection not found' },
        { status: 404 }
      );
    }

    // If not refreshing and we have cached schema metadata, return it
    if (!validatedData.refresh && connection.schemaMetadata) {
      try {
        const cachedSchema = typeof connection.schemaMetadata === 'string' 
          ? JSON.parse(connection.schemaMetadata)
          : connection.schemaMetadata;
        
        return NextResponse.json({
          schema: cachedSchema,
          cached: true,
          lastUpdated: connection.updatedAt,
        });
      } catch (error) {
        console.warn('Failed to parse cached schema metadata:', error);
        // Continue to fetch fresh schema
      }
    }

    // Fetch fresh schema
    try {
      const schema = await ConnectionService.getConnectionSchema(validatedData.connectionId);
      
      return NextResponse.json({
        schema,
        cached: false,
        lastUpdated: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Failed to fetch schema:', error);
      return NextResponse.json(
        { 
          error: 'Failed to fetch database schema',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('GET /api/connections/schema error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/connections/schema - Refresh database schema
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = getSchemaSchema.parse({ ...body, refresh: true });

    // Check if connection exists
    const connection = await ConnectionService.getConnectionById(validatedData.connectionId);
    if (!connection) {
      return NextResponse.json(
        { error: 'Connection not found' },
        { status: 404 }
      );
    }

    // Fetch fresh schema
    try {
      const schema = await ConnectionService.getConnectionSchema(validatedData.connectionId);
      
      return NextResponse.json({
        schema,
        refreshed: true,
        lastUpdated: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Failed to refresh schema:', error);
      return NextResponse.json(
        { 
          error: 'Failed to refresh database schema',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('POST /api/connections/schema error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
