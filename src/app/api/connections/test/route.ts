import { NextRequest, NextResponse } from 'next/server';
import { ConnectionService } from '@/lib/database/services/connection-service';
import { z } from 'zod';

// Validation schema for testing connection
const testConnectionSchema = z.object({
  connectionId: z.string().cuid().optional(),
  // For testing without saving
  databaseType: z.enum(['MYSQL', 'POSTGRESQL']).optional(),
  connectionString: z.string().optional(),
  host: z.string().optional(),
  port: z.number().int().min(1).max(65535).optional(),
  database: z.string().optional(),
  username: z.string().optional(),
  password: z.string().optional(),
  ssl: z.boolean().optional(),
});

// POST /api/connections/test - Test database connection
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = testConnectionSchema.parse(body);

    let isConnected = false;
    let error: string | null = null;

    if (validatedData.connectionId) {
      // Test existing connection
      try {
        isConnected = await ConnectionService.testConnection(validatedData.connectionId);
      } catch (err) {
        error = err instanceof Error ? err.message : 'Connection test failed';
      }
    } else if (validatedData.databaseType && validatedData.connectionString) {
      // Test new connection without saving
      try {
        const { ConnectorFactory } = await import('@/lib/database/connectors/connector-factory');
        
        // Parse connection string or build config from individual fields
        let config;
        if (validatedData.connectionString) {
          // Parse connection string
          config = parseConnectionString(validatedData.connectionString);
        } else {
          // Build config from individual fields
          config = {
            host: validatedData.host || 'localhost',
            port: validatedData.port || (validatedData.databaseType === 'MYSQL' ? 3306 : 5432),
            database: validatedData.database || '',
            username: validatedData.username || '',
            password: validatedData.password || '',
            ssl: validatedData.ssl || false,
          };
        }

        const connector = ConnectorFactory.createConnector(
          validatedData.databaseType,
          config
        );

        isConnected = await connector.testConnection();
        await connector.disconnect();
      } catch (err) {
        error = err instanceof Error ? err.message : 'Connection test failed';
      }
    } else {
      return NextResponse.json(
        { error: 'Either connectionId or connection details required' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: isConnected,
      connected: isConnected,
      error,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('POST /api/connections/test error:', error);
    return NextResponse.json(
      { 
        success: false,
        connected: false,
        error: error instanceof Error ? error.message : 'Internal server error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Helper function to parse connection strings
function parseConnectionString(connectionString: string) {
  try {
    if (connectionString.startsWith('postgresql://') || connectionString.startsWith('postgres://')) {
      const url = new URL(connectionString);
      return {
        host: url.hostname,
        port: parseInt(url.port) || 5432,
        database: url.pathname.substring(1),
        username: url.username,
        password: url.password,
        ssl: url.searchParams.get('sslmode') === 'require',
      };
    } else if (connectionString.startsWith('mysql://')) {
      const url = new URL(connectionString);
      return {
        host: url.hostname,
        port: parseInt(url.port) || 3306,
        database: url.pathname.substring(1),
        username: url.username,
        password: url.password,
        ssl: url.searchParams.get('ssl') === 'true',
      };
    } else {
      // Parse format like: host=localhost;port=5432;database=mydb;username=user;password=pass
      const params = new Map();
      connectionString.split(';').forEach(pair => {
        const [key, value] = pair.split('=');
        if (key && value) {
          params.set(key.trim().toLowerCase(), value.trim());
        }
      });

      return {
        host: params.get('host') || 'localhost',
        port: parseInt(params.get('port') || '5432'),
        database: params.get('database') || '',
        username: params.get('username') || params.get('user') || '',
        password: params.get('password') || '',
        ssl: params.get('ssl') === 'true',
      };
    }
  } catch (error) {
    throw new Error(`Invalid connection string format: ${error}`);
  }
}
