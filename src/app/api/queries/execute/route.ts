import { NextRequest, NextResponse } from 'next/server';
import { QueryExecutor } from '@/lib/database/query-executor';
import { z } from 'zod';

// Validation schema for query execution
const executeQuerySchema = z.object({
  connectionId: z.string().cuid(),
  sql: z.string().min(1).max(50000),
  params: z.array(z.any()).optional(),
  maxRows: z.number().int().min(1).max(10000).optional(),
  timeout: z.number().int().min(1000).max(300000).optional(), // 1s to 5min
  userId: z.string().cuid().optional(),
  saveToHistory: z.boolean().optional().default(true),
});

// POST /api/queries/execute - Execute a SQL query
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = executeQuerySchema.parse(body);

    // Execute the query
    const result = await QueryExecutor.executeQuery({
      connectionId: validatedData.connectionId,
      sql: validatedData.sql,
      params: validatedData.params,
      maxRows: validatedData.maxRows,
      timeout: validatedData.timeout,
      userId: validatedData.userId,
    });

    // If execution was successful and saveToHistory is true, save to database
    if (result.success && validatedData.saveToHistory && validatedData.userId) {
      try {
        await saveQueryToHistory({
          userId: validatedData.userId,
          connectionId: validatedData.connectionId,
          sql: validatedData.sql,
          result,
        });
      } catch (error) {
        console.error('Failed to save query to history:', error);
        // Don't fail the request if history saving fails
      }
    }

    return NextResponse.json(result);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Validation failed', 
          details: error.errors 
        },
        { status: 400 }
      );
    }

    console.error('POST /api/queries/execute error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error',
        metadata: {
          connectionId: '',
          executedAt: new Date(),
          executionTime: 0,
          rowsReturned: 0,
          queryHash: '',
        }
      },
      { status: 500 }
    );
  }
}

// Helper function to save query execution to history
async function saveQueryToHistory(data: {
  userId: string;
  connectionId: string;
  sql: string;
  result: any;
}) {
  const { QueryService } = await import('@/lib/database/services/query-service');
  const { ConnectionService } = await import('@/lib/database/services/connection-service');

  try {
    // Get connection details
    const connection = await ConnectionService.getConnectionById(data.connectionId);
    if (!connection) {
      throw new Error('Connection not found');
    }

    // Create or get a session for this execution
    let session;
    try {
      // Try to find an active session for this user and connection
      const sessions = await QueryService.getUserSessions(data.userId, 1);
      const activeSession = sessions.find(s => 
        s.databaseConnectionId === data.connectionId && s.isActive
      );

      if (activeSession) {
        session = activeSession;
      } else {
        // Create a new session
        session = await QueryService.createSession({
          userId: data.userId,
          databaseConnectionId: data.connectionId,
          name: `Query Session - ${new Date().toLocaleString()}`,
        });
      }
    } catch (error) {
      // If session creation fails, create a simple one
      session = await QueryService.createSession({
        userId: data.userId,
        databaseConnectionId: data.connectionId,
        name: `Query Session - ${new Date().toLocaleString()}`,
      });
    }

    // Save the query execution
    await QueryService.createGeneratedQuery({
      sessionId: session.id,
      userId: data.userId,
      userInput: `Executed: ${data.sql.substring(0, 100)}${data.sql.length > 100 ? '...' : ''}`,
      generatedSQL: data.sql,
      explanation: data.result.success 
        ? `Query executed successfully. Returned ${data.result.data?.rowCount || 0} rows in ${data.result.metadata.executionTime}ms.`
        : `Query failed: ${data.result.error}`,
      databaseType: connection.databaseType as 'MYSQL' | 'POSTGRESQL',
      executionTime: data.result.metadata.executionTime,
      rowsAffected: data.result.data?.affectedRows,
      performanceData: {
        executionTime: data.result.metadata.executionTime,
        rowsReturned: data.result.metadata.rowsReturned,
        queryHash: data.result.metadata.queryHash,
        success: data.result.success,
      },
    });
  } catch (error) {
    console.error('Failed to save query to history:', error);
    throw error;
  }
}

// GET /api/queries/execute/validate - Validate a SQL query without executing
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const connectionId = searchParams.get('connectionId');
    const sql = searchParams.get('sql');

    if (!connectionId || !sql) {
      return NextResponse.json(
        { error: 'connectionId and sql parameters are required' },
        { status: 400 }
      );
    }

    // Validate the query
    const validation = await QueryExecutor.validateQuery({
      connectionId,
      sql: decodeURIComponent(sql),
    });

    return NextResponse.json({
      validation,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('GET /api/queries/execute/validate error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
