import { NextRequest, NextResponse } from 'next/server';
import { QueryService } from '@/lib/database/services/query-service';
import { z } from 'zod';

// Validation schemas
const createQuerySchema = z.object({
  sessionId: z.string().cuid(),
  userId: z.string().cuid(),
  userInput: z.string().min(1),
  generatedSQL: z.string().min(1),
  explanation: z.string().optional(),
  databaseType: z.enum(['MYSQL', 'POSTGRESQL']),
  executionTime: z.number().int().min(0).optional(),
  rowsAffected: z.number().int().min(0).optional(),
  performanceData: z.any().optional(),
  optimizationTips: z.any().optional(),
});

const updateQuerySchema = z.object({
  status: z.enum(['GENERATED', 'EXECUTED', 'FAILED', 'OPTIMIZED']).optional(),
  executionTime: z.number().int().min(0).optional(),
  rowsAffected: z.number().int().min(0).optional(),
  errorMessage: z.string().optional(),
  performanceData: z.any().optional(),
  userFeedback: z.number().int().min(1).max(5).optional(),
});

const createSessionSchema = z.object({
  userId: z.string().cuid(),
  databaseConnectionId: z.string().cuid(),
  name: z.string().optional(),
  sessionData: z.any().optional(),
});

// GET /api/queries - Get queries or sessions
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const sessionId = searchParams.get('sessionId');
    const type = searchParams.get('type'); // 'queries' or 'sessions'
    const limit = parseInt(searchParams.get('limit') || '20');
    const databaseType = searchParams.get('databaseType') as 'MYSQL' | 'POSTGRESQL' | null;

    if (type === 'sessions' && userId) {
      const sessions = await QueryService.getUserSessions(userId, limit);
      return NextResponse.json(sessions);
    }

    if (type === 'history' && userId) {
      const queries = await QueryService.getUserQueryHistory(
        userId, 
        limit, 
        databaseType || undefined
      );
      return NextResponse.json(queries);
    }

    if (sessionId) {
      const session = await QueryService.getSessionById(sessionId);
      if (!session) {
        return NextResponse.json(
          { error: 'Session not found' },
          { status: 404 }
        );
      }
      return NextResponse.json(session);
    }

    return NextResponse.json(
      { error: 'Invalid parameters. Use type=sessions&userId=X or type=history&userId=X or sessionId=X' },
      { status: 400 }
    );
  } catch (error) {
    console.error('GET /api/queries error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/queries - Create a new query or session
export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // 'query' or 'session'
    const body = await request.json();

    if (type === 'session') {
      const validatedData = createSessionSchema.parse(body);
      const session = await QueryService.createSession(validatedData);
      return NextResponse.json(session, { status: 201 });
    }

    if (type === 'query') {
      const validatedData = createQuerySchema.parse(body);
      const query = await QueryService.createGeneratedQuery(validatedData);
      return NextResponse.json(query, { status: 201 });
    }

    return NextResponse.json(
      { error: 'Type parameter required (query or session)' },
      { status: 400 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('POST /api/queries error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/queries - Update query or session
export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    const type = searchParams.get('type'); // 'query' or 'session'

    if (!id) {
      return NextResponse.json(
        { error: 'ID required' },
        { status: 400 }
      );
    }

    const body = await request.json();

    if (type === 'query') {
      const validatedData = updateQuerySchema.parse(body);

      if (validatedData.userFeedback) {
        const query = await QueryService.addQueryFeedback(id, validatedData.userFeedback);
        return NextResponse.json(query);
      } else if (validatedData.status) {
        // Only call updateQueryExecution if status is provided
        const executionData = {
          status: validatedData.status,
          executionTime: validatedData.executionTime,
          rowsAffected: validatedData.rowsAffected,
          errorMessage: validatedData.errorMessage,
          performanceData: validatedData.performanceData,
        };
        const query = await QueryService.updateQueryExecution(id, executionData);
        return NextResponse.json(query);
      } else {
        return NextResponse.json(
          { error: 'Either userFeedback or status is required for query updates' },
          { status: 400 }
        );
      }
    }

    if (type === 'session') {
      const sessionData = z.object({
        name: z.string().optional(),
        sessionData: z.any().optional(),
        isActive: z.boolean().optional(),
      }).parse(body);

      const session = await QueryService.updateSession(id, sessionData);
      return NextResponse.json(session);
    }

    return NextResponse.json(
      { error: 'Type parameter required (query or session)' },
      { status: 400 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('PUT /api/queries error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/queries - Delete session
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Session ID required' },
        { status: 400 }
      );
    }

    await QueryService.deleteSession(id);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('DELETE /api/queries error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
