import { NextRequest } from 'next/server';
import { Server as NetServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import { initializeCollaborationServer } from '@/lib/websocket/server';

export const dynamic = 'force-dynamic';

// This will be set when the server starts
let io: SocketIOServer | undefined;

export async function GET(req: NextRequest) {
  if (!io) {
    console.log('Initializing Socket.IO server...');
    
    // Get the HTTP server instance
    const httpServer = (global as any).httpServer as NetServer;
    
    if (!httpServer) {
      return new Response('HTTP server not available', { status: 500 });
    }

    // Initialize collaboration server
    const collaborationServer = initializeCollaborationServer(httpServer);
    io = collaborationServer['io']; // Access the io instance
    
    console.log('Socket.IO server initialized');
  }

  return new Response('Socket.IO server is running', { status: 200 });
}

// Handle WebSocket upgrade
export async function POST(req: NextRequest) {
  return new Response('WebSocket connections should use the Socket.IO client', { 
    status: 400 
  });
}
