'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { QueryExecutor } from '@/components/editor/query-executor';
import { QueryResultsViewer } from '@/components/editor/query-results-viewer';
import { ConnectionTester } from '@/components/database/connection-tester';
import { SchemaViewer } from '@/components/database/schema-viewer';
import { Badge } from '@/components/ui/badge';
import { 
  Database, 
  Play, 
  FileText, 
  Settings,
  TestTube,
  Eye
} from 'lucide-react';

export default function QueryEditorPage() {
  const [sql, setSql] = useState(`-- Welcome to QueryCraft Studio Query Editor
-- Try executing this sample query:

SELECT 
  customer_id,
  name,
  email,
  COUNT(order_id) as total_orders,
  SUM(order_total) as total_spent
FROM customers c
LEFT JOIN orders o ON c.customer_id = o.customer_id
GROUP BY customer_id, name, email
ORDER BY total_spent DESC
LIMIT 10;`);

  const [selectedConnection, setSelectedConnection] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'editor' | 'results' | 'schema' | 'test'>('editor');
  const [queryResult, setQueryResult] = useState<any>(null);

  // Mock connections for demo
  const mockConnections = [
    { id: 'conn1', name: 'Production DB (PostgreSQL)', type: 'POSTGRESQL' },
    { id: 'conn2', name: 'Analytics DB (MySQL)', type: 'MYSQL' },
    { id: 'conn3', name: 'Development DB (PostgreSQL)', type: 'POSTGRESQL' },
  ];

  const handleExecutionComplete = (result: any) => {
    setQueryResult(result);
    if (result.success) {
      setActiveTab('results');
    }
  };

  const tabs = [
    { id: 'editor', label: 'Query Editor', icon: FileText },
    { id: 'results', label: 'Results', icon: Play, disabled: !queryResult },
    { id: 'schema', label: 'Schema', icon: Eye, disabled: !selectedConnection },
    { id: 'test', label: 'Test Connection', icon: TestTube },
  ];

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Query Editor</h1>
          <p className="text-muted-foreground">
            Execute SQL queries against your connected databases
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Select value={selectedConnection || ''} onValueChange={setSelectedConnection}>
            <SelectTrigger className="w-64">
              <SelectValue placeholder="Select a database connection" />
            </SelectTrigger>
            <SelectContent>
              {mockConnections.map((conn) => (
                <SelectItem key={conn.id} value={conn.id}>
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4" />
                    <span>{conn.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {conn.type}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Connection Status */}
      {selectedConnection && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Database className="h-4 w-4 text-green-500" />
              <span className="text-sm">
                Connected to: {mockConnections.find(c => c.id === selectedConnection)?.name}
              </span>
              <Badge variant="default" className="text-xs">
                {mockConnections.find(c => c.id === selectedConnection)?.type}
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Tabs */}
      <div className="flex space-x-1 bg-muted p-1 rounded-lg">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <Button
              key={tab.id}
              variant={activeTab === tab.id ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setActiveTab(tab.id as any)}
              disabled={tab.disabled}
              className="flex items-center gap-2"
            >
              <Icon className="h-4 w-4" />
              {tab.label}
            </Button>
          );
        })}
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'editor' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* SQL Editor */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  SQL Editor
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={sql}
                  onChange={(e) => setSql(e.target.value)}
                  placeholder="Enter your SQL query here..."
                  className="min-h-[400px] font-mono text-sm"
                />
              </CardContent>
            </Card>

            {/* Query Executor */}
            <div>
              <QueryExecutor
                sql={sql}
                connectionId={selectedConnection}
                onExecutionComplete={handleExecutionComplete}
              />
            </div>
          </div>
        )}

        {activeTab === 'results' && queryResult && (
          <div>
            {queryResult.success && queryResult.data ? (
              <QueryResultsViewer
                data={queryResult.data}
                metadata={queryResult.metadata}
              />
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="text-muted-foreground">
                    {queryResult.error ? (
                      <div>
                        <p className="font-medium text-red-600 mb-2">Query Failed</p>
                        <p className="text-sm">{queryResult.error}</p>
                      </div>
                    ) : (
                      <p>No results to display</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {activeTab === 'schema' && selectedConnection && (
          <SchemaViewer connectionId={selectedConnection} />
        )}

        {activeTab === 'test' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Test Existing Connection */}
            {selectedConnection && (
              <ConnectionTester
                connectionId={selectedConnection}
                onConnectionTested={(result) => {
                  console.log('Connection test result:', result);
                }}
              />
            )}

            {/* Test New Connection */}
            <ConnectionTester
              onConnectionTested={(result) => {
                console.log('New connection test result:', result);
              }}
            />
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button
              variant="outline"
              onClick={() => setSql('SELECT * FROM users LIMIT 10;')}
              className="h-auto p-4 flex flex-col items-center gap-2"
            >
              <Database className="h-6 w-6" />
              <span className="text-sm">Sample Query</span>
            </Button>
            
            <Button
              variant="outline"
              onClick={() => setSql('')}
              className="h-auto p-4 flex flex-col items-center gap-2"
            >
              <FileText className="h-6 w-6" />
              <span className="text-sm">Clear Editor</span>
            </Button>
            
            <Button
              variant="outline"
              onClick={() => setActiveTab('schema')}
              disabled={!selectedConnection}
              className="h-auto p-4 flex flex-col items-center gap-2"
            >
              <Eye className="h-6 w-6" />
              <span className="text-sm">View Schema</span>
            </Button>
            
            <Button
              variant="outline"
              onClick={() => setActiveTab('test')}
              className="h-auto p-4 flex flex-col items-center gap-2"
            >
              <TestTube className="h-6 w-6" />
              <span className="text-sm">Test Connection</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
