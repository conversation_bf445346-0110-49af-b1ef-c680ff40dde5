'use client';

import { useEffect, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { useCollaboration } from '@/hooks/use-collaboration';

interface LiveCursorsProps {
  editorRef?: React.RefObject<HTMLTextAreaElement>;
  onCursorUpdate?: (line: number, column: number) => void;
}

interface CursorDisplay {
  id: string;
  user: {
    id: string;
    name: string;
    color: string;
  };
  position: {
    line: number;
    column: number;
    selection?: {
      startLine: number;
      startColumn: number;
      endLine: number;
      endColumn: number;
    };
  };
  element?: HTMLElement;
}

export function LiveCursors({ editorRef, onCursorUpdate }: LiveCursorsProps) {
  const collaboration = useCollaboration();
  const [cursors, setCursors] = useState<CursorDisplay[]>([]);

  // Update cursors when collaboration cursors change
  useEffect(() => {
    const newCursors: CursorDisplay[] = [];
    
    collaboration.cursors.forEach((cursor, userId) => {
      if (userId !== collaboration.currentUser?.id) {
        newCursors.push({
          id: userId,
          user: cursor.user,
          position: {
            line: cursor.line,
            column: cursor.column,
            selection: cursor.selection
          }
        });
      }
    });

    setCursors(newCursors);
  }, [collaboration.cursors, collaboration.currentUser?.id]);

  // Handle cursor position updates from the editor
  useEffect(() => {
    if (!editorRef?.current) return;

    const editor = editorRef.current;

    const handleSelectionChange = () => {
      const selectionStart = editor.selectionStart;
      const selectionEnd = editor.selectionEnd;
      const text = editor.value;

      // Calculate line and column from character position
      const lines = text.substring(0, selectionStart).split('\n');
      const line = lines.length - 1;
      const column = lines[lines.length - 1]?.length || 0;

      // Update collaboration cursor
      if (collaboration.canCollaborate) {
        const position = {
          line,
          column,
          selection: selectionStart !== selectionEnd ? {
            startLine: line,
            startColumn: column,
            endLine: line, // Simplified - assumes single line selection
            endColumn: column + (selectionEnd - selectionStart)
          } : undefined
        };

        collaboration.updateCursorPosition(position);
      }

      // Notify parent component
      onCursorUpdate?.(line, column);
    };

    // Listen for selection changes
    editor.addEventListener('selectionchange', handleSelectionChange);
    editor.addEventListener('keyup', handleSelectionChange);
    editor.addEventListener('mouseup', handleSelectionChange);

    return () => {
      editor.removeEventListener('selectionchange', handleSelectionChange);
      editor.removeEventListener('keyup', handleSelectionChange);
      editor.removeEventListener('mouseup', handleSelectionChange);
    };
  }, [editorRef, collaboration, onCursorUpdate]);

  // Render cursor indicators
  const renderCursorIndicators = () => {
    if (!editorRef?.current || cursors.length === 0) return null;

    return cursors.map((cursor) => (
      <CursorIndicator
        key={cursor.id}
        cursor={cursor}
        editorRef={editorRef}
      />
    ));
  };

  return (
    <div className="relative">
      {/* Cursor indicators will be positioned absolutely */}
      {renderCursorIndicators()}
      
      {/* Active collaborators list */}
      {collaboration.canCollaborate && collaboration.otherParticipants.length > 0 && (
        <div className="flex items-center gap-2 mb-2">
          <span className="text-xs text-muted-foreground">Active collaborators:</span>
          {collaboration.otherParticipants.map((participant) => (
            <Badge
              key={participant.id}
              variant="outline"
              className="text-xs flex items-center gap-1"
              style={{ borderColor: participant.color }}
            >
              <div 
                className="w-2 h-2 rounded-full" 
                style={{ backgroundColor: participant.color }}
              />
              {participant.name}
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
}

// Individual cursor indicator component
function CursorIndicator({ 
  cursor, 
  editorRef 
}: { 
  cursor: CursorDisplay; 
  editorRef: React.RefObject<HTMLTextAreaElement>;
}) {
  const [position, setPosition] = useState<{ top: number; left: number } | null>(null);

  useEffect(() => {
    if (!editorRef?.current) return;

    const editor = editorRef.current;
    const text = editor.value;
    const lines = text.split('\n');

    // Calculate character position from line/column
    let charPosition = 0;
    for (let i = 0; i < cursor.position.line && i < lines.length; i++) {
      charPosition += (lines[i]?.length || 0) + 1; // +1 for newline
    }
    charPosition += Math.min(cursor.position.column, lines[cursor.position.line]?.length || 0);

    // Create a temporary text node to measure position
    const tempTextArea = document.createElement('textarea');
    tempTextArea.style.position = 'absolute';
    tempTextArea.style.visibility = 'hidden';
    tempTextArea.style.whiteSpace = 'pre';
    tempTextArea.style.font = window.getComputedStyle(editor).font;
    tempTextArea.style.fontSize = window.getComputedStyle(editor).fontSize;
    tempTextArea.style.fontFamily = window.getComputedStyle(editor).fontFamily;
    tempTextArea.style.lineHeight = window.getComputedStyle(editor).lineHeight;
    tempTextArea.style.padding = window.getComputedStyle(editor).padding;
    tempTextArea.style.border = window.getComputedStyle(editor).border;
    tempTextArea.style.width = editor.offsetWidth + 'px';
    tempTextArea.value = text.substring(0, charPosition);

    document.body.appendChild(tempTextArea);

    // Get the scroll position
    const scrollTop = editor.scrollTop;
    const scrollLeft = editor.scrollLeft;

    // Calculate approximate position (this is a simplified approach)
    const lineHeight = parseInt(window.getComputedStyle(editor).lineHeight) || 20;
    const charWidth = 8; // Approximate character width for monospace fonts

    const editorRect = editor.getBoundingClientRect();
    const top = editorRect.top + (cursor.position.line * lineHeight) - scrollTop;
    const left = editorRect.left + (cursor.position.column * charWidth) - scrollLeft;

    setPosition({ top, left });

    document.body.removeChild(tempTextArea);
  }, [cursor.position, editorRef]);

  if (!position) return null;

  return (
    <>
      {/* Cursor line */}
      <div
        className="absolute w-0.5 h-5 z-10 pointer-events-none"
        style={{
          top: position.top,
          left: position.left,
          backgroundColor: cursor.user.color,
          animation: 'blink 1s infinite'
        }}
      />
      
      {/* User label */}
      <div
        className="absolute z-20 pointer-events-none"
        style={{
          top: position.top - 25,
          left: position.left,
          transform: 'translateX(-50%)'
        }}
      >
        <Badge
          className="text-xs px-2 py-1 text-white"
          style={{ backgroundColor: cursor.user.color }}
        >
          {cursor.user.name}
        </Badge>
      </div>

      {/* Selection highlight (if any) */}
      {cursor.position.selection && (
        <div
          className="absolute pointer-events-none z-5"
          style={{
            top: position.top,
            left: position.left,
            width: (cursor.position.selection.endColumn - cursor.position.selection.startColumn) * 8,
            height: 20,
            backgroundColor: cursor.user.color,
            opacity: 0.2
          }}
        />
      )}

      <style jsx>{`
        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0; }
        }
      `}</style>
    </>
  );
}
