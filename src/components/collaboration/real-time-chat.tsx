'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  MessageCircle, 
  Send, 
  User, 
  Bot,
  Play,
  AlertCircle
} from 'lucide-react';
import { useCollaboration } from '@/hooks/use-collaboration';
import { ChatMessage } from '@/lib/websocket/server';

interface RealTimeChatProps {
  className?: string;
  maxHeight?: string;
}

export function RealTimeChat({ className, maxHeight = '400px' }: RealTimeChatProps) {
  const [message, setMessage] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const collaboration = useCollaboration();

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [collaboration.chatMessages]);

  const sendMessage = () => {
    if (!message.trim() || !collaboration.canCollaborate) return;

    collaboration.sendChatMessage(message.trim());
    setMessage('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getMessageIcon = (type: ChatMessage['type']) => {
    switch (type) {
      case 'system':
        return <Bot className="h-3 w-3" />;
      case 'query_execution':
        return <Play className="h-3 w-3" />;
      default:
        return <User className="h-3 w-3" />;
    }
  };

  const getMessageStyle = (message: ChatMessage) => {
    const isOwnMessage = message.userId === collaboration.currentUser?.id;
    const isSystemMessage = message.type === 'system' || message.type === 'query_execution';

    if (isSystemMessage) {
      return {
        container: 'bg-muted/50 border-l-2 border-muted-foreground/20',
        text: 'text-muted-foreground text-sm italic'
      };
    }

    if (isOwnMessage) {
      return {
        container: 'bg-primary/10 border-l-2 border-primary/30 ml-4',
        text: 'text-foreground'
      };
    }

    return {
      container: 'bg-background border-l-2 border-muted/30',
      text: 'text-foreground'
    };
  };

  if (!collaboration.canCollaborate) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <MessageCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
          <p className="text-muted-foreground text-sm">
            Join a collaboration session to start chatting
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader 
        className="cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Chat
            {collaboration.chatMessages.length > 0 && (
              <Badge variant="secondary" className="text-xs">
                {collaboration.chatMessages.length}
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            {collaboration.otherParticipants.length > 0 && (
              <Badge variant="outline" className="text-xs">
                {collaboration.participantCount} online
              </Badge>
            )}
          </div>
        </CardTitle>
      </CardHeader>

      {isExpanded && (
        <CardContent className="space-y-4">
          {/* Messages */}
          <ScrollArea style={{ height: maxHeight }}>
            <div className="space-y-3 pr-4">
              {collaboration.chatMessages.length === 0 ? (
                <div className="text-center text-muted-foreground text-sm py-8">
                  <MessageCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No messages yet. Start the conversation!</p>
                </div>
              ) : (
                collaboration.chatMessages.map((msg) => {
                  const styles = getMessageStyle(msg);
                  const isOwnMessage = msg.userId === collaboration.currentUser?.id;
                  
                  return (
                    <div
                      key={msg.id}
                      className={`p-3 rounded-lg ${styles.container}`}
                    >
                      <div className="flex items-start gap-2">
                        <div className="flex-shrink-0 mt-0.5">
                          {getMessageIcon(msg.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium">
                              {isOwnMessage ? 'You' : msg.userName}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {formatTimestamp(msg.timestamp)}
                            </span>
                            {msg.type !== 'message' && (
                              <Badge variant="outline" className="text-xs">
                                {msg.type === 'system' ? 'System' : 'Query'}
                              </Badge>
                            )}
                          </div>
                          <p className={`text-sm ${styles.text} break-words`}>
                            {msg.message}
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })
              )}
              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          {/* Message Input */}
          <div className="flex gap-2">
            <Input
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type a message..."
              className="flex-1"
              maxLength={500}
            />
            <Button
              onClick={sendMessage}
              disabled={!message.trim()}
              size="sm"
              className="flex items-center gap-2"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>

          {/* Typing indicator (placeholder for future enhancement) */}
          {/* You could add a typing indicator here */}
        </CardContent>
      )}
    </Card>
  );
}
