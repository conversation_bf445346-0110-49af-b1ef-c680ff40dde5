'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Users, 
  Share, 
  Copy, 
  UserPlus, 
  UserMinus,
  Crown,
  Wifi,
  WifiOff,
  MessageCircle
} from 'lucide-react';
import { useCollaboration } from '@/hooks/use-collaboration';

interface SessionSharingProps {
  connectionId: string;
  onSessionJoined?: (sessionId: string) => void;
  onSessionLeft?: () => void;
}

export function SessionSharing({ connectionId, onSessionJoined, onSessionLeft }: SessionSharingProps) {
  const [sessionIdInput, setSessionIdInput] = useState('');
  const [sessionName, setSessionName] = useState('');
  const [isCreatingSession, setIsCreatingSession] = useState(false);
  const [shareUrl, setShareUrl] = useState('');

  const collaboration = useCollaboration({
    autoConnect: true
  });

  useEffect(() => {
    if (collaboration.isInSession && collaboration.currentUser) {
      const url = `${window.location.origin}/dashboard/query-editor?session=${collaboration.sessionId}&connection=${connectionId}`;
      setShareUrl(url);
      onSessionJoined?.(collaboration.sessionId || '');
    } else {
      setShareUrl('');
      onSessionLeft?.();
    }
  }, [collaboration.isInSession, collaboration.sessionId, connectionId, onSessionJoined, onSessionLeft]);

  const createNewSession = async () => {
    if (!collaboration.isAuthenticated) {
      await collaboration.authenticate();
    }

    setIsCreatingSession(true);
    
    try {
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const name = sessionName.trim() || `Collaboration Session`;
      
      collaboration.joinSession(sessionId, connectionId, name);
      setSessionName('');
    } catch (error) {
      console.error('Failed to create session:', error);
    } finally {
      setIsCreatingSession(false);
    }
  };

  const joinExistingSession = async () => {
    if (!sessionIdInput.trim()) return;
    
    if (!collaboration.isAuthenticated) {
      await collaboration.authenticate();
    }

    try {
      collaboration.joinSession(sessionIdInput.trim(), connectionId);
      setSessionIdInput('');
    } catch (error) {
      console.error('Failed to join session:', error);
    }
  };

  const leaveSession = () => {
    collaboration.leaveSession();
  };

  const copyShareUrl = async () => {
    if (shareUrl) {
      try {
        await navigator.clipboard.writeText(shareUrl);
        // You could add a toast notification here
      } catch (error) {
        console.error('Failed to copy URL:', error);
      }
    }
  };

  const getConnectionStatusIcon = () => {
    if (collaboration.isConnected) {
      return <Wifi className="h-4 w-4 text-green-500" />;
    }
    return <WifiOff className="h-4 w-4 text-red-500" />;
  };

  const getConnectionStatusText = () => {
    if (!collaboration.isConnected) return 'Disconnected';
    if (!collaboration.isAuthenticated) return 'Connecting...';
    if (collaboration.isInSession) return 'In Session';
    return 'Connected';
  };

  return (
    <div className="space-y-4">
      {/* Connection Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getConnectionStatusIcon()}
            Collaboration Status
            <Badge variant={collaboration.isConnected ? 'default' : 'destructive'}>
              {getConnectionStatusText()}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!collaboration.isConnected && (
            <Alert>
              <WifiOff className="h-4 w-4" />
              <AlertDescription>
                Not connected to collaboration server. Real-time features are unavailable.
              </AlertDescription>
            </Alert>
          )}
          
          {collaboration.isConnected && !collaboration.isAuthenticated && (
            <Alert>
              <AlertDescription>
                Authenticating with collaboration server...
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Current Session */}
      {collaboration.isInSession && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Active Session
              <Badge variant="default">{collaboration.participantCount} participants</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Participants */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Participants</h4>
              <div className="space-y-1">
                {collaboration.participants.map((participant) => (
                  <div key={participant.id} className="flex items-center gap-2 p-2 bg-muted rounded">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: participant.color }}
                    />
                    <span className="text-sm">{participant.name}</span>
                    {participant.id === collaboration.currentUser?.id && (
                      <Badge variant="secondary" className="text-xs">You</Badge>
                    )}
                    {/* Add crown icon for session owner if needed */}
                  </div>
                ))}
              </div>
            </div>

            {/* Share URL */}
            {shareUrl && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Share Session</h4>
                <div className="flex gap-2">
                  <Input
                    value={shareUrl}
                    readOnly
                    className="text-xs"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyShareUrl}
                    className="flex items-center gap-2"
                  >
                    <Copy className="h-4 w-4" />
                    Copy
                  </Button>
                </div>
              </div>
            )}

            {/* Leave Session */}
            <Button
              variant="outline"
              onClick={leaveSession}
              className="w-full flex items-center gap-2"
            >
              <UserMinus className="h-4 w-4" />
              Leave Session
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Join/Create Session */}
      {collaboration.isAuthenticated && !collaboration.isInSession && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Share className="h-5 w-5" />
              Start Collaboration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Create New Session */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Create New Session</h4>
              <div className="flex gap-2">
                <Input
                  placeholder="Session name (optional)"
                  value={sessionName}
                  onChange={(e) => setSessionName(e.target.value)}
                />
                <Button
                  onClick={createNewSession}
                  disabled={isCreatingSession}
                  className="flex items-center gap-2"
                >
                  <UserPlus className="h-4 w-4" />
                  Create
                </Button>
              </div>
            </div>

            {/* Join Existing Session */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Join Existing Session</h4>
              <div className="flex gap-2">
                <Input
                  placeholder="Enter session ID"
                  value={sessionIdInput}
                  onChange={(e) => setSessionIdInput(e.target.value)}
                />
                <Button
                  onClick={joinExistingSession}
                  disabled={!sessionIdInput.trim()}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Users className="h-4 w-4" />
                  Join
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Authentication Required */}
      {collaboration.isConnected && !collaboration.isAuthenticated && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <Button
                onClick={collaboration.authenticate}
                className="flex items-center gap-2"
              >
                <Users className="h-4 w-4" />
                Connect to Collaboration
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
