'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Loader2, CheckCircle, XCircle, Database, RefreshCw } from 'lucide-react';
import { useDatabase } from '@/hooks/use-database';

interface ConnectionTesterProps {
  connectionId?: string;
  onConnectionTested?: (result: { success: boolean; error?: string }) => void;
}

export function ConnectionTester({ connectionId, onConnectionTested }: ConnectionTesterProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [testResult, setTestResult] = useState<{
    success: boolean;
    connected: boolean;
    error?: string;
    timestamp: string;
  } | null>(null);

  // Form state for new connections
  const [formData, setFormData] = useState({
    databaseType: 'POSTGRESQL' as 'MYSQL' | 'POSTGRESQL',
    connectionString: '',
    host: '',
    port: 5432,
    database: '',
    username: '',
    password: '',
    ssl: false,
  });

  const database = useDatabase();

  const handleTestConnection = async () => {
    setIsLoading(true);
    setTestResult(null);

    try {
      let result;
      
      if (connectionId) {
        // Test existing connection
        result = await database.testConnection(connectionId);
      } else {
        // Test new connection
        result = await database.testConnection(undefined, formData);
      }

      setTestResult(result);
      onConnectionTested?.(result);
    } catch (error) {
      const errorResult = {
        success: false,
        connected: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      };
      setTestResult(errorResult);
      onConnectionTested?.(errorResult);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    // Clear previous test result when form changes
    setTestResult(null);
  };

  const getStatusIcon = () => {
    if (isLoading) {
      return <Loader2 className="h-4 w-4 animate-spin" />;
    }
    if (testResult?.connected) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
    if (testResult && !testResult.connected) {
      return <XCircle className="h-4 w-4 text-red-500" />;
    }
    return <Database className="h-4 w-4" />;
  };

  const getStatusBadge = () => {
    if (isLoading) {
      return <Badge variant="secondary">Testing...</Badge>;
    }
    if (testResult?.connected) {
      return <Badge variant="default" className="bg-green-100 text-green-800">Connected</Badge>;
    }
    if (testResult && !testResult.connected) {
      return <Badge variant="destructive">Failed</Badge>;
    }
    return null;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {getStatusIcon()}
          {connectionId ? 'Test Connection' : 'Test Database Connection'}
          {getStatusBadge()}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {!connectionId && (
          <>
            {/* Database Type */}
            <div className="space-y-2">
              <Label htmlFor="databaseType">Database Type</Label>
              <Select
                value={formData.databaseType}
                onValueChange={(value) => handleInputChange('databaseType', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="POSTGRESQL">PostgreSQL</SelectItem>
                  <SelectItem value="MYSQL">MySQL</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Connection String */}
            <div className="space-y-2">
              <Label htmlFor="connectionString">Connection String (Optional)</Label>
              <Input
                id="connectionString"
                type="text"
                placeholder="postgresql://user:password@host:port/database"
                value={formData.connectionString}
                onChange={(e) => handleInputChange('connectionString', e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                Leave empty to use individual connection fields below
              </p>
            </div>

            {/* Individual Connection Fields */}
            {!formData.connectionString && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="host">Host</Label>
                    <Input
                      id="host"
                      type="text"
                      placeholder="localhost"
                      value={formData.host}
                      onChange={(e) => handleInputChange('host', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="port">Port</Label>
                    <Input
                      id="port"
                      type="number"
                      placeholder={formData.databaseType === 'MYSQL' ? '3306' : '5432'}
                      value={formData.port}
                      onChange={(e) => handleInputChange('port', parseInt(e.target.value) || 5432)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="database">Database Name</Label>
                  <Input
                    id="database"
                    type="text"
                    placeholder="my_database"
                    value={formData.database}
                    onChange={(e) => handleInputChange('database', e.target.value)}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="username">Username</Label>
                    <Input
                      id="username"
                      type="text"
                      placeholder="username"
                      value={formData.username}
                      onChange={(e) => handleInputChange('username', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password">Password</Label>
                    <Input
                      id="password"
                      type="password"
                      placeholder="password"
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="ssl"
                    checked={formData.ssl}
                    onCheckedChange={(checked) => handleInputChange('ssl', checked)}
                  />
                  <Label htmlFor="ssl">Use SSL</Label>
                </div>
              </>
            )}
          </>
        )}

        {/* Test Button */}
        <Button
          onClick={handleTestConnection}
          disabled={isLoading || (!connectionId && !formData.connectionString && (!formData.host || !formData.database || !formData.username))}
          className="w-full"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Testing Connection...
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Test Connection
            </>
          )}
        </Button>

        {/* Test Result */}
        {testResult && (
          <Alert className={testResult.connected ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
            <AlertDescription>
              {testResult.connected ? (
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-green-700">Connection successful!</span>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <XCircle className="h-4 w-4 text-red-500" />
                    <span className="text-red-700">Connection failed</span>
                  </div>
                  {testResult.error && (
                    <p className="text-sm text-red-600">{testResult.error}</p>
                  )}
                </div>
              )}
              <p className="text-xs text-muted-foreground mt-2">
                Tested at {new Date(testResult.timestamp).toLocaleString()}
              </p>
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}
