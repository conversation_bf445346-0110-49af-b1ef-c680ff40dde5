'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Database,
  Table,
  Columns,
  Key,
  RefreshCw,
  ChevronDown,
  ChevronRight,
  Eye,
  List,
  Loader2
} from 'lucide-react';
import { useDatabase } from '@/hooks/use-database';

interface SchemaViewerProps {
  connectionId: string;
}

interface SchemaData {
  tables: Array<{
    name: string;
    schema: string;
    columns: Array<{
      name: string;
      type: string;
      nullable: boolean;
      defaultValue?: any;
      isPrimaryKey: boolean;
      isForeignKey: boolean;
      referencedTable?: string;
      referencedColumn?: string;
    }>;
    indexes: Array<{
      name: string;
      columns: string[];
      isUnique: boolean;
      isPrimary: boolean;
    }>;
  }>;
  views: Array<{
    name: string;
    schema: string;
    definition: string;
  }>;
}

export function SchemaViewer({ connectionId }: SchemaViewerProps) {
  const [schema, setSchema] = useState<SchemaData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<string | null>(null);
  const [isCached, setIsCached] = useState(false);
  const [expandedTables, setExpandedTables] = useState<Set<string>>(new Set());
  const [expandedViews, setExpandedViews] = useState<Set<string>>(new Set());

  const database = useDatabase();

  const loadSchema = async (refresh = false) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await database.getConnectionSchema(connectionId, refresh);
      setSchema(result.schema);
      setLastUpdated(result.lastUpdated);
      setIsCached(result.cached);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load schema');
    } finally {
      setIsLoading(false);
    }
  };

  const refreshSchema = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await database.refreshConnectionSchema(connectionId);
      setSchema(result.schema);
      setLastUpdated(result.lastUpdated);
      setIsCached(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh schema');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadSchema();
  }, [connectionId]);

  const toggleTableExpansion = (tableName: string) => {
    const newExpanded = new Set(expandedTables);
    if (newExpanded.has(tableName)) {
      newExpanded.delete(tableName);
    } else {
      newExpanded.add(tableName);
    }
    setExpandedTables(newExpanded);
  };

  const toggleViewExpansion = (viewName: string) => {
    const newExpanded = new Set(expandedViews);
    if (newExpanded.has(viewName)) {
      newExpanded.delete(viewName);
    } else {
      newExpanded.add(viewName);
    }
    setExpandedViews(newExpanded);
  };

  const getColumnIcon = (column: any) => {
    if (column.isPrimaryKey) {
      return <Key className="h-3 w-3 text-yellow-500" title="Primary Key" />;
    }
    if (column.isForeignKey) {
      return <Key className="h-3 w-3 text-blue-500" title="Foreign Key" />;
    }
    return <Columns className="h-3 w-3 text-gray-400" />;
  };

  const getColumnBadges = (column: any) => {
    const badges = [];
    
    if (column.isPrimaryKey) {
      badges.push(<Badge key="pk" variant="secondary" className="text-xs">PK</Badge>);
    }
    if (column.isForeignKey) {
      badges.push(<Badge key="fk" variant="outline" className="text-xs">FK</Badge>);
    }
    if (!column.nullable) {
      badges.push(<Badge key="nn" variant="outline" className="text-xs">NOT NULL</Badge>);
    }

    return badges;
  };

  if (isLoading && !schema) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Loading database schema...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <Alert className="border-red-200 bg-red-50">
            <AlertDescription className="text-red-700">
              {error}
            </AlertDescription>
          </Alert>
          <Button onClick={() => loadSchema()} className="mt-4">
            <RefreshCw className="mr-2 h-4 w-4" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!schema) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <Database className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">No schema data available</p>
            <Button onClick={() => loadSchema(true)} className="mt-4">
              <RefreshCw className="mr-2 h-4 w-4" />
              Load Schema
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Database Schema
              {isCached && <Badge variant="secondary">Cached</Badge>}
            </CardTitle>
            <Button
              onClick={refreshSchema}
              disabled={isLoading}
              variant="outline"
              size="sm"
            >
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="mr-2 h-4 w-4" />
              )}
              Refresh
            </Button>
          </div>
          {lastUpdated && (
            <p className="text-sm text-muted-foreground">
              Last updated: {new Date(lastUpdated).toLocaleString()}
            </p>
          )}
        </CardHeader>
      </Card>

      {/* Tables */}
      {schema.tables && schema.tables.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Table className="h-4 w-4" />
              Tables ({schema.tables.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {schema.tables.map((table) => (
              <Collapsible
                key={`${table.schema}.${table.name}`}
                open={expandedTables.has(table.name)}
                onOpenChange={() => toggleTableExpansion(table.name)}
              >
                <CollapsibleTrigger className="flex items-center gap-2 w-full p-2 hover:bg-muted rounded">
                  {expandedTables.has(table.name) ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                  <Table className="h-4 w-4" />
                  <span className="font-medium">{table.name}</span>
                  <Badge variant="outline" className="text-xs">
                    {table.columns.length} columns
                  </Badge>
                  {table.indexes.length > 0 && (
                    <Badge variant="outline" className="text-xs">
                      {table.indexes.length} indexes
                    </Badge>
                  )}
                </CollapsibleTrigger>
                <CollapsibleContent className="ml-6 mt-2 space-y-2">
                  {/* Columns */}
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium">Columns</h4>
                    {table.columns.map((column) => (
                      <div key={column.name} className="flex items-center gap-2 p-2 bg-muted/50 rounded text-sm">
                        {getColumnIcon(column)}
                        <span className="font-mono">{column.name}</span>
                        <Badge variant="outline" className="text-xs">{column.type}</Badge>
                        <div className="flex gap-1">
                          {getColumnBadges(column)}
                        </div>
                        {column.isForeignKey && column.referencedTable && (
                          <span className="text-xs text-muted-foreground">
                            → {column.referencedTable}.{column.referencedColumn}
                          </span>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Indexes */}
                  {table.indexes.length > 0 && (
                    <div className="space-y-1">
                      <h4 className="text-sm font-medium">Indexes</h4>
                      {table.indexes.map((index) => (
                        <div key={index.name} className="flex items-center gap-2 p-2 bg-muted/50 rounded text-sm">
                          <List className="h-3 w-3 text-gray-400" />
                          <span className="font-mono">{index.name}</span>
                          <span className="text-xs text-muted-foreground">
                            ({index.columns.join(', ')})
                          </span>
                          {index.isUnique && <Badge variant="outline" className="text-xs">UNIQUE</Badge>}
                          {index.isPrimary && <Badge variant="secondary" className="text-xs">PRIMARY</Badge>}
                        </div>
                      ))}
                    </div>
                  )}
                </CollapsibleContent>
              </Collapsible>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Views */}
      {schema.views && schema.views.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Views ({schema.views.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {schema.views.map((view) => (
              <Collapsible
                key={`${view.schema}.${view.name}`}
                open={expandedViews.has(view.name)}
                onOpenChange={() => toggleViewExpansion(view.name)}
              >
                <CollapsibleTrigger className="flex items-center gap-2 w-full p-2 hover:bg-muted rounded">
                  {expandedViews.has(view.name) ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                  <Eye className="h-4 w-4" />
                  <span className="font-medium">{view.name}</span>
                </CollapsibleTrigger>
                <CollapsibleContent className="ml-6 mt-2">
                  <div className="p-3 bg-muted/50 rounded">
                    <pre className="text-xs overflow-x-auto whitespace-pre-wrap">
                      {view.definition}
                    </pre>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {(!schema.tables || schema.tables.length === 0) && (!schema.views || schema.views.length === 0) && (
        <Card>
          <CardContent className="flex items-center justify-center p-8">
            <div className="text-center">
              <Database className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">No tables or views found in this database</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
