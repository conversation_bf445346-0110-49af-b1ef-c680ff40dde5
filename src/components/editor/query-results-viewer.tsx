'use client';

import { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Download, 
  Search, 
  ChevronLeft, 
  ChevronRight,
  Database,
  FileText,
  Grid
} from 'lucide-react';

interface QueryResultsViewerProps {
  data: {
    rows: any[];
    fields: Array<{
      name: string;
      type: string;
      nullable: boolean;
    }>;
    rowCount: number;
    executionTime: number;
    affectedRows?: number;
  };
  metadata: {
    connectionId: string;
    executedAt: string;
    executionTime: number;
    rowsReturned: number;
    queryHash: string;
  };
}

export function QueryResultsViewer({ data, metadata }: QueryResultsViewerProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(50);

  // Filter and paginate data
  const filteredData = useMemo(() => {
    if (!searchTerm) return data.rows;
    
    return data.rows.filter(row =>
      Object.values(row).some(value =>
        String(value).toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  }, [data.rows, searchTerm]);

  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return filteredData.slice(startIndex, startIndex + pageSize);
  }, [filteredData, currentPage, pageSize]);

  const totalPages = Math.ceil(filteredData.length / pageSize);

  const exportToCSV = () => {
    if (!data.rows.length) return;

    // Create CSV content
    const headers = data.fields.map(field => field.name).join(',');
    const rows = data.rows.map(row =>
      data.fields.map(field => {
        const value = row[field.name];
        // Escape commas and quotes in CSV
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value ?? '';
      }).join(',')
    );

    const csvContent = [headers, ...rows].join('\n');

    // Download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `query_results_${metadata.queryHash}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const exportToJSON = () => {
    if (!data.rows.length) return;

    const jsonContent = JSON.stringify(data.rows, null, 2);
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `query_results_${metadata.queryHash}.json`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatCellValue = (value: any, type: string) => {
    if (value === null || value === undefined) {
      return <span className="text-muted-foreground italic">NULL</span>;
    }

    if (type.includes('json') || type.includes('jsonb')) {
      try {
        return <pre className="text-xs">{JSON.stringify(JSON.parse(value), null, 2)}</pre>;
      } catch {
        return String(value);
      }
    }

    if (type.includes('bool')) {
      return (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? 'TRUE' : 'FALSE'}
        </Badge>
      );
    }

    if (type.includes('date') || type.includes('time')) {
      try {
        return new Date(value).toLocaleString();
      } catch {
        return String(value);
      }
    }

    // Truncate long text values
    const stringValue = String(value);
    if (stringValue.length > 100) {
      return (
        <span title={stringValue}>
          {stringValue.substring(0, 100)}...
        </span>
      );
    }

    return stringValue;
  };

  const getTypeIcon = (type: string) => {
    if (type.includes('int') || type.includes('number') || type.includes('decimal')) {
      return '🔢';
    }
    if (type.includes('text') || type.includes('varchar') || type.includes('char')) {
      return '📝';
    }
    if (type.includes('date') || type.includes('time')) {
      return '📅';
    }
    if (type.includes('bool')) {
      return '✅';
    }
    if (type.includes('json')) {
      return '📋';
    }
    return '📄';
  };

  if (!data.rows.length) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <Database className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">No data returned</p>
            <p className="text-sm text-muted-foreground mt-1">
              Query executed successfully but returned no rows
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Results Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Grid className="h-5 w-5" />
              Query Results
              <Badge variant="outline">{data.rowCount} rows</Badge>
            </CardTitle>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={exportToCSV}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                CSV
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={exportToJSON}
                className="flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                JSON
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <span>Execution time: {metadata.executionTime}ms</span>
            <span>•</span>
            <span>Rows returned: {metadata.rowsReturned}</span>
            <span>•</span>
            <span>Executed at: {new Date(metadata.executedAt).toLocaleString()}</span>
          </div>
        </CardContent>
      </Card>

      {/* Search and Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search in results..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  setCurrentPage(1); // Reset to first page when searching
                }}
                className="pl-10"
              />
            </div>
            <div className="text-sm text-muted-foreground">
              {filteredData.length} of {data.rows.length} rows
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  {data.fields.map((field) => (
                    <TableHead key={field.name} className="whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        <span>{getTypeIcon(field.type)}</span>
                        <div>
                          <div className="font-medium">{field.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {field.type}
                            {!field.nullable && <span className="ml-1">NOT NULL</span>}
                          </div>
                        </div>
                      </div>
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedData.map((row, rowIndex) => (
                  <TableRow key={rowIndex}>
                    {data.fields.map((field) => (
                      <TableCell key={field.name} className="max-w-xs">
                        {formatCellValue(row[field.name], field.type)}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="flex items-center gap-2"
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  className="flex items-center gap-2"
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
