'use client';

import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useQueryStore } from '@/stores/query-store';

interface DatabaseProviderProps {
  children: React.ReactNode;
}

export function DatabaseProvider({ children }: DatabaseProviderProps) {
  const { data: session, status } = useSession();
  const {
    setCurrentUser,
    loadUserConnections,
    loadUserSessions,
    currentUser,
    connections
  } = useQueryStore();

  useEffect(() => {
    const initializeUser = async () => {
      // If user is authenticated, use their session
      if (status === 'authenticated' && session?.user && !currentUser) {
        try {
          setCurrentUser({
            id: session.user.id!,
            name: session.user.name || '',
            email: session.user.email || ''
          });

          // Load user data
          await loadUserConnections(session.user.id!);
          await loadUserSessions(session.user.id!);

          // Set the first connection as current if available
          const state = useQueryStore.getState();
          if (state.connections.length > 0 && state.connections[0]) {
            useQueryStore.getState().setCurrentDatabase(state.connections[0]);
          }
        } catch (error) {
          console.error('Failed to initialize authenticated user:', error);
        }
      }
      // If not authenticated, use demo user
      else if (status === 'unauthenticated' && !currentUser) {
        try {
          // Fetch the demo user from the database
          const response = await fetch('/api/users?email=<EMAIL>');
          if (response.ok) {
            const demoUser = await response.json();

            setCurrentUser({
              id: demoUser.id,
              name: demoUser.name,
              email: demoUser.email
            });

            // Load user data
            await loadUserConnections(demoUser.id);
            await loadUserSessions(demoUser.id);

            // Set the first connection as current if available
            const state = useQueryStore.getState();
            if (state.connections.length > 0 && state.connections[0]) {
              useQueryStore.getState().setCurrentDatabase(state.connections[0]);
            }
          } else {
            console.error('Demo user not found in database');
          }
        } catch (error) {
          console.error('Failed to initialize demo user:', error);
        }
      }
    };

    if (status !== 'loading') {
      initializeUser();
    }
  }, [session, status, currentUser, setCurrentUser, loadUserConnections, loadUserSessions]);

  return <>{children}</>;
}
