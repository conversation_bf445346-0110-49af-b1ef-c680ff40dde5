import { useState, useEffect, useCallback, useRef } from 'react';
import { getCollaborationClient, CollaborationClient } from '@/lib/websocket/client';
import { CollaborationUser, CursorPosition, ChatMessage } from '@/lib/websocket/server';

export interface UseCollaborationOptions {
  sessionId?: string;
  connectionId?: string;
  sessionName?: string;
  autoConnect?: boolean;
}

export interface CollaborationState {
  isConnected: boolean;
  isAuthenticated: boolean;
  currentUser: CollaborationUser | null;
  participants: CollaborationUser[];
  cursors: Map<string, CursorPosition & { user: { id: string; name: string; color: string } }>;
  chatMessages: ChatMessage[];
  sqlContent: string;
  isInSession: boolean;
}

export function useCollaboration(options: UseCollaborationOptions = {}) {
  const clientRef = useRef<CollaborationClient | null>(null);
  const [state, setState] = useState<CollaborationState>({
    isConnected: false,
    isAuthenticated: false,
    currentUser: null,
    participants: [],
    cursors: new Map(),
    chatMessages: [],
    sqlContent: '',
    isInSession: false,
  });

  // Initialize client
  useEffect(() => {
    if (!clientRef.current) {
      clientRef.current = getCollaborationClient();
    }

    const client = clientRef.current;

    // Set up event handlers
    client.on('authenticated', (data) => {
      setState(prev => ({
        ...prev,
        isAuthenticated: data.success,
        currentUser: data.user || null
      }));
    });

    client.on('session_joined', (data) => {
      setState(prev => ({
        ...prev,
        isInSession: true,
        participants: data.participants,
        currentUser: data.user
      }));
    });

    client.on('user_joined', (data) => {
      setState(prev => ({
        ...prev,
        participants: data.participants
      }));
    });

    client.on('user_left', (data) => {
      setState(prev => {
        const newCursors = new Map(prev.cursors);
        newCursors.delete(data.userId);
        
        return {
          ...prev,
          participants: data.participants,
          cursors: newCursors
        };
      });
    });

    client.on('sql_sync', (data) => {
      setState(prev => ({
        ...prev,
        sqlContent: data.content
      }));
    });

    client.on('sql_change', (data) => {
      setState(prev => ({
        ...prev,
        sqlContent: data.content
      }));
    });

    client.on('cursor_update', (data) => {
      setState(prev => {
        const newCursors = new Map(prev.cursors);
        newCursors.set(data.user.id, {
          ...data.position,
          user: data.user
        });
        
        return {
          ...prev,
          cursors: newCursors
        };
      });
    });

    client.on('chat_message', (message) => {
      setState(prev => ({
        ...prev,
        chatMessages: [...prev.chatMessages, message]
      }));
    });

    client.on('query_execution_start', (data) => {
      // Handle query execution start notification
      console.log(`${data.userName} started executing a query`);
    });

    client.on('query_execution_complete', (data) => {
      // Handle query execution completion notification
      console.log(`${data.userName} completed query execution`);
    });

    client.on('error', (data) => {
      console.error('Collaboration error:', data.message);
    });

    // Monitor connection status
    const checkConnection = () => {
      setState(prev => ({
        ...prev,
        isConnected: client.connected
      }));
    };

    const interval = setInterval(checkConnection, 1000);

    return () => {
      clearInterval(interval);
      // Don't disconnect here as the client might be used elsewhere
    };
  }, []);

  // Auto-connect if specified
  useEffect(() => {
    if (options.autoConnect && clientRef.current && !state.isAuthenticated) {
      authenticate();
    }
  }, [options.autoConnect]);

  // Auto-join session if specified
  useEffect(() => {
    if (
      options.sessionId && 
      options.connectionId && 
      state.isAuthenticated && 
      !state.isInSession
    ) {
      joinSession(options.sessionId, options.connectionId, options.sessionName);
    }
  }, [options.sessionId, options.connectionId, state.isAuthenticated, state.isInSession]);

  // Methods
  const authenticate = useCallback(async (): Promise<boolean> => {
    if (!clientRef.current) return false;

    try {
      // In a real implementation, get the token from your auth system
      const token = 'demo-token'; // Replace with actual token
      const success = await clientRef.current.authenticate(token);
      return success;
    } catch (error) {
      console.error('Authentication failed:', error);
      return false;
    }
  }, []);

  const joinSession = useCallback((sessionId: string, connectionId: string, sessionName?: string) => {
    if (!clientRef.current) return;
    clientRef.current.joinSession(sessionId, connectionId, sessionName);
  }, []);

  const leaveSession = useCallback(() => {
    if (!clientRef.current) return;
    clientRef.current.leaveSession();
    setState(prev => ({
      ...prev,
      isInSession: false,
      participants: [],
      cursors: new Map(),
      chatMessages: [],
      sqlContent: ''
    }));
  }, []);

  const updateSqlContent = useCallback((content: string, operation?: any) => {
    if (!clientRef.current) return;
    clientRef.current.updateSqlContent(content, operation);
  }, []);

  const updateCursorPosition = useCallback((position: Omit<CursorPosition, 'userId'>) => {
    if (!clientRef.current) return;
    clientRef.current.updateCursorPosition(position);
  }, []);

  const sendChatMessage = useCallback((message: string) => {
    if (!clientRef.current) return;
    clientRef.current.sendChatMessage(message);
  }, []);

  const notifyQueryExecutionStart = useCallback((sql: string) => {
    if (!clientRef.current) return;
    clientRef.current.notifyQueryExecutionStart(sql);
  }, []);

  const notifyQueryExecutionComplete = useCallback((result: any) => {
    if (!clientRef.current) return;
    clientRef.current.notifyQueryExecutionComplete(result);
  }, []);

  const clearChatMessages = useCallback(() => {
    setState(prev => ({
      ...prev,
      chatMessages: []
    }));
  }, []);

  const disconnect = useCallback(() => {
    if (!clientRef.current) return;
    clientRef.current.disconnect();
    setState({
      isConnected: false,
      isAuthenticated: false,
      currentUser: null,
      participants: [],
      cursors: new Map(),
      chatMessages: [],
      sqlContent: '',
      isInSession: false,
    });
  }, []);

  return {
    // State
    ...state,

    // Methods
    authenticate,
    joinSession,
    leaveSession,
    updateSqlContent,
    updateCursorPosition,
    sendChatMessage,
    notifyQueryExecutionStart,
    notifyQueryExecutionComplete,
    clearChatMessages,
    disconnect,

    // Computed values
    participantCount: state.participants.length,
    otherParticipants: state.participants.filter(p => p.id !== state.currentUser?.id),
    canCollaborate: state.isConnected && state.isAuthenticated && state.isInSession,
    sessionId: clientRef.current?.sessionId || null,
  };
}
