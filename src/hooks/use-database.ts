import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { DatabaseConnection, GeneratedQuery, QuerySession, User } from '@/types';

// API client functions
const api = {
  // Users
  async getUser(id: string): Promise<User> {
    const response = await fetch(`/api/users?id=${id}`);
    if (!response.ok) throw new Error('Failed to fetch user');
    return response.json();
  },

  async createUser(data: { email: string; name: string; avatar?: string }): Promise<User> {
    const response = await fetch('/api/users', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) throw new Error('Failed to create user');
    return response.json();
  },

  async updateUser(id: string, data: { name?: string; avatar?: string }): Promise<User> {
    const response = await fetch(`/api/users?id=${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) throw new Error('Failed to update user');
    return response.json();
  },

  // Connections
  async getUserConnections(userId: string): Promise<DatabaseConnection[]> {
    const response = await fetch(`/api/connections?userId=${userId}`);
    if (!response.ok) throw new Error('Failed to fetch connections');
    return response.json();
  },

  async createConnection(data: {
    userId: string;
    name: string;
    databaseType: 'MYSQL' | 'POSTGRESQL';
    connectionString: string;
    host?: string;
    port?: number;
    database?: string;
    username?: string;
  }): Promise<DatabaseConnection> {
    const response = await fetch('/api/connections', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) throw new Error('Failed to create connection');
    return response.json();
  },

  async updateConnection(id: string, data: any): Promise<DatabaseConnection> {
    const response = await fetch(`/api/connections?id=${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) throw new Error('Failed to update connection');
    return response.json();
  },

  async deleteConnection(id: string): Promise<void> {
    const response = await fetch(`/api/connections?id=${id}`, {
      method: 'DELETE',
    });
    if (!response.ok) throw new Error('Failed to delete connection');
  },

  // Queries and Sessions
  async getUserSessions(userId: string): Promise<QuerySession[]> {
    const response = await fetch(`/api/queries?type=sessions&userId=${userId}`);
    if (!response.ok) throw new Error('Failed to fetch sessions');
    return response.json();
  },

  async getSession(sessionId: string): Promise<QuerySession> {
    const response = await fetch(`/api/queries?sessionId=${sessionId}`);
    if (!response.ok) throw new Error('Failed to fetch session');
    return response.json();
  },

  async createSession(data: {
    userId: string;
    databaseConnectionId: string;
    name?: string;
  }): Promise<QuerySession> {
    const response = await fetch('/api/queries?type=session', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) throw new Error('Failed to create session');
    return response.json();
  },

  async createQuery(data: {
    sessionId: string;
    userId: string;
    userInput: string;
    generatedSQL: string;
    explanation?: string;
    databaseType: 'MYSQL' | 'POSTGRESQL';
  }): Promise<GeneratedQuery> {
    const response = await fetch('/api/queries?type=query', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) throw new Error('Failed to create query');
    return response.json();
  },

  async getUserQueryHistory(userId: string, limit = 20): Promise<GeneratedQuery[]> {
    const response = await fetch(`/api/queries?type=history&userId=${userId}&limit=${limit}`);
    if (!response.ok) throw new Error('Failed to fetch query history');
    return response.json();
  },

  // Test database connection
  async testConnection(connectionId?: string, connectionData?: {
    databaseType: 'MYSQL' | 'POSTGRESQL';
    connectionString?: string;
    host?: string;
    port?: number;
    database?: string;
    username?: string;
    password?: string;
    ssl?: boolean;
  }): Promise<{ success: boolean; connected: boolean; error?: string; timestamp: string }> {
    const body = connectionId
      ? { connectionId }
      : connectionData;

    const response = await fetch('/api/connections/test', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to test connection');
    }

    return response.json();
  },

  // Get database schema
  async getConnectionSchema(connectionId: string, refresh = false): Promise<{
    schema: any;
    cached: boolean;
    lastUpdated: string;
  }> {
    const response = await fetch(`/api/connections/schema?connectionId=${connectionId}&refresh=${refresh}`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch schema');
    }
    return response.json();
  },

  // Refresh database schema
  async refreshConnectionSchema(connectionId: string): Promise<{
    schema: any;
    refreshed: boolean;
    lastUpdated: string;
  }> {
    const response = await fetch('/api/connections/schema', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ connectionId }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to refresh schema');
    }

    return response.json();
  },

  // Execute SQL query
  async executeQuery(options: {
    connectionId: string;
    sql: string;
    params?: any[];
    maxRows?: number;
    timeout?: number;
    userId?: string;
    saveToHistory?: boolean;
  }): Promise<{
    success: boolean;
    data?: {
      rows: any[];
      fields: Array<{
        name: string;
        type: string;
        nullable: boolean;
      }>;
      rowCount: number;
      executionTime: number;
      affectedRows?: number;
    };
    error?: string;
    warnings?: string[];
    metadata: {
      connectionId: string;
      executedAt: string;
      executionTime: number;
      rowsReturned: number;
      queryHash: string;
    };
  }> {
    const response = await fetch('/api/queries/execute', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(options),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to execute query');
    }

    return response.json();
  },

  // Validate SQL query
  async validateQuery(connectionId: string, sql: string): Promise<{
    validation: {
      isValid: boolean;
      errors: string[];
      warnings: string[];
      queryType: string;
      isReadOnly: boolean;
      estimatedComplexity: 'LOW' | 'MEDIUM' | 'HIGH';
    };
    timestamp: string;
  }> {
    const response = await fetch(
      `/api/queries/execute/validate?connectionId=${connectionId}&sql=${encodeURIComponent(sql)}`
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to validate query');
    }

    return response.json();
  },

  async updateQueryFeedback(queryId: string, feedback: number): Promise<GeneratedQuery> {
    const response = await fetch(`/api/queries?type=query&id=${queryId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ userFeedback: feedback }),
    });
    if (!response.ok) throw new Error('Failed to update query feedback');
    return response.json();
  },
};

// Custom hooks
export function useUser(id: string) {
  return useQuery({
    queryKey: ['user', id],
    queryFn: () => api.getUser(id),
    enabled: !!id,
  });
}

export function useUserConnections(userId: string) {
  return useQuery({
    queryKey: ['connections', userId],
    queryFn: () => api.getUserConnections(userId),
    enabled: !!userId,
  });
}

export function useUserSessions(userId: string) {
  return useQuery({
    queryKey: ['sessions', userId],
    queryFn: () => api.getUserSessions(userId),
    enabled: !!userId,
  });
}

export function useSession(sessionId: string) {
  return useQuery({
    queryKey: ['session', sessionId],
    queryFn: () => api.getSession(sessionId),
    enabled: !!sessionId,
  });
}

export function useUserQueryHistory(userId: string, limit = 20) {
  return useQuery({
    queryKey: ['queryHistory', userId, limit],
    queryFn: () => api.getUserQueryHistory(userId, limit),
    enabled: !!userId,
  });
}

// Mutations
export function useCreateUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: api.createUser,
    onSuccess: (user) => {
      queryClient.setQueryData(['user', user.id], user);
    },
  });
}

export function useUpdateUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => api.updateUser(id, data),
    onSuccess: (user) => {
      queryClient.setQueryData(['user', user.id], user);
    },
  });
}

export function useCreateConnection() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: api.createConnection,
    onSuccess: (connection) => {
      queryClient.invalidateQueries({ queryKey: ['connections', connection.userId] });
    },
  });
}

export function useUpdateConnection() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => api.updateConnection(id, data),
    onSuccess: (connection) => {
      queryClient.invalidateQueries({ queryKey: ['connections'] });
    },
  });
}

export function useDeleteConnection() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: api.deleteConnection,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['connections'] });
    },
  });
}

export function useCreateSession() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: api.createSession,
    onSuccess: (session) => {
      queryClient.invalidateQueries({ queryKey: ['sessions', session.userId] });
    },
  });
}

export function useCreateQuery() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: api.createQuery,
    onSuccess: (query) => {
      queryClient.invalidateQueries({ queryKey: ['queryHistory'] });
      queryClient.invalidateQueries({ queryKey: ['session', query.sessionId] });
    },
  });
}

export function useUpdateQueryFeedback() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ queryId, feedback }: { queryId: string; feedback: number }) =>
      api.updateQueryFeedback(queryId, feedback),
    onSuccess: (query) => {
      queryClient.invalidateQueries({ queryKey: ['queryHistory'] });
    },
  });
}

// Export the api object as useDatabase for direct API calls
export const useDatabase = () => api;
