export interface DatabaseConnection {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean;
  connectionTimeout?: number;
  queryTimeout?: number;
}

export interface QueryResult {
  rows: any[];
  fields: Array<{
    name: string;
    type: string;
    nullable: boolean;
  }>;
  rowCount: number;
  executionTime: number;
}

export interface SchemaInfo {
  tables: Array<{
    name: string;
    schema: string;
    columns: Array<{
      name: string;
      type: string;
      nullable: boolean;
      defaultValue?: any;
      isPrimaryKey: boolean;
      isForeignKey: boolean;
      referencedTable?: string;
      referencedColumn?: string;
    }>;
    indexes: Array<{
      name: string;
      columns: string[];
      isUnique: boolean;
      isPrimary: boolean;
    }>;
  }>;
  views: Array<{
    name: string;
    schema: string;
    definition: string;
  }>;
}

export abstract class BaseConnector {
  protected config: DatabaseConnection;
  protected pool: any;

  constructor(config: DatabaseConnection) {
    this.config = {
      ...config,
      connectionTimeout: config.connectionTimeout || 30000,
      queryTimeout: config.queryTimeout || 60000,
    };
  }

  abstract connect(): Promise<void>;
  abstract disconnect(): Promise<void>;
  abstract testConnection(): Promise<boolean>;
  abstract executeQuery(sql: string, params?: any[]): Promise<QueryResult>;
  abstract getSchema(): Promise<SchemaInfo>;
  abstract validateQuery(sql: string): Promise<{ isValid: boolean; error?: string }>;

  protected sanitizeQuery(sql: string): string {
    // Remove dangerous SQL patterns
    const dangerousPatterns = [
      /;\s*(drop|delete|truncate|alter)\s+/gi,
      /;\s*exec\s*\(/gi,
      /;\s*execute\s*\(/gi,
      /xp_cmdshell/gi,
      /sp_executesql/gi,
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(sql)) {
        throw new Error('Query contains potentially dangerous SQL patterns');
      }
    }

    return sql.trim();
  }

  protected async withTimeout<T>(
    promise: Promise<T>,
    timeoutMs: number,
    errorMessage: string
  ): Promise<T> {
    const timeout = new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error(errorMessage)), timeoutMs)
    );

    return Promise.race([promise, timeout]);
  }

  protected formatError(error: any): Error {
    if (error instanceof Error) {
      return error;
    }
    return new Error(String(error));
  }
}
