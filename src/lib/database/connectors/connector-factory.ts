import { BaseConnector, DatabaseConnection } from './base-connector';
import { MySQLConnector } from './mysql-connector';
import { PostgreSQLConnector } from './postgresql-connector';

export type DatabaseType = 'MYSQL' | 'POSTGRESQL';

export class ConnectorFactory {
  private static connectors = new Map<string, BaseConnector>();

  static createConnector(
    type: DatabaseType,
    config: DatabaseConnection,
    connectionId?: string
  ): BaseConnector {
    // If connectionId is provided and connector exists, return cached connector
    if (connectionId && this.connectors.has(connectionId)) {
      return this.connectors.get(connectionId)!;
    }

    let connector: BaseConnector;

    switch (type) {
      case 'MYSQL':
        connector = new MySQLConnector(config);
        break;
      case 'POSTGRESQL':
        connector = new PostgreSQLConnector(config);
        break;
      default:
        throw new Error(`Unsupported database type: ${type}`);
    }

    // Cache the connector if connectionId is provided
    if (connectionId) {
      this.connectors.set(connectionId, connector);
    }

    return connector;
  }

  static async removeConnector(connectionId: string): Promise<void> {
    const connector = this.connectors.get(connectionId);
    if (connector) {
      try {
        await connector.disconnect();
      } catch (error) {
        console.error(`Error disconnecting connector ${connectionId}:`, error);
      }
      this.connectors.delete(connectionId);
    }
  }

  static async removeAllConnectors(): Promise<void> {
    const disconnectPromises = Array.from(this.connectors.entries()).map(
      async ([id, connector]) => {
        try {
          await connector.disconnect();
        } catch (error) {
          console.error(`Error disconnecting connector ${id}:`, error);
        }
      }
    );

    await Promise.allSettled(disconnectPromises);
    this.connectors.clear();
  }

  static getActiveConnections(): string[] {
    return Array.from(this.connectors.keys());
  }

  static getConnectionCount(): number {
    return this.connectors.size;
  }

  static hasConnection(connectionId: string): boolean {
    return this.connectors.has(connectionId);
  }
}

// Graceful shutdown handler
process.on('SIGTERM', async () => {
  console.log('Received SIGTERM, closing database connections...');
  await ConnectorFactory.removeAllConnectors();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('Received SIGINT, closing database connections...');
  await ConnectorFactory.removeAllConnectors();
  process.exit(0);
});
