import mysql from 'mysql2/promise';
import { BaseConnector, DatabaseConnection, QueryResult, SchemaInfo } from './base-connector';

export class MySQLConnector extends BaseConnector {
  protected override pool: mysql.Pool;

  constructor(config: DatabaseConnection) {
    super(config);
    
    this.pool = mysql.createPool({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.username,
      password: config.password,
      ssl: config.ssl ? { rejectUnauthorized: false } : undefined,
      connectionLimit: 10,
      charset: 'utf8mb4',
    });
  }

  async connect(): Promise<void> {
    try {
      await this.withTimeout(
        this.pool.getConnection(),
        this.config.connectionTimeout!,
        'MySQL connection timeout'
      );
    } catch (error) {
      throw this.formatError(error);
    }
  }

  async disconnect(): Promise<void> {
    try {
      await this.pool.end();
    } catch (error) {
      throw this.formatError(error);
    }
  }

  async testConnection(): Promise<boolean> {
    let connection: mysql.PoolConnection | null = null;
    try {
      connection = await this.withTimeout(
        this.pool.getConnection(),
        this.config.connectionTimeout!,
        'MySQL connection timeout'
      );
      
      await connection.query('SELECT 1');
      return true;
    } catch (error) {
      console.error('MySQL connection test failed:', error);
      return false;
    } finally {
      if (connection) {
        connection.release();
      }
    }
  }

  async executeQuery(sql: string, params: any[] = []): Promise<QueryResult> {
    const sanitizedSql = this.sanitizeQuery(sql);
    let connection: mysql.PoolConnection | null = null;
    
    try {
      const startTime = Date.now();
      connection = await this.pool.getConnection();
      
      const [rows, fields] = await this.withTimeout(
        connection.execute(sanitizedSql, params),
        this.config.queryTimeout!,
        'Query execution timeout'
      ) as [any[], mysql.FieldPacket[]];
      
      const executionTime = Date.now() - startTime;

      const formattedFields = fields.map(field => ({
        name: field.name,
        type: this.mapMySQLType(field.type || 0),
        nullable: true, // Default to nullable for now
      }));

      return {
        rows: Array.isArray(rows) ? rows : [],
        fields: formattedFields,
        rowCount: Array.isArray(rows) ? rows.length : 0,
        executionTime,
      };
    } catch (error) {
      throw this.formatError(error);
    } finally {
      if (connection) {
        connection.release();
      }
    }
  }

  async getSchema(): Promise<SchemaInfo> {
    const tablesQuery = `
      SELECT 
        t.TABLE_NAME as table_name,
        t.TABLE_SCHEMA as table_schema,
        c.COLUMN_NAME as column_name,
        c.DATA_TYPE as data_type,
        c.IS_NULLABLE as is_nullable,
        c.COLUMN_DEFAULT as column_default,
        CASE WHEN c.COLUMN_KEY = 'PRI' THEN 1 ELSE 0 END as is_primary_key,
        CASE WHEN c.COLUMN_KEY = 'MUL' THEN 1 ELSE 0 END as is_foreign_key,
        kcu.REFERENCED_TABLE_NAME as referenced_table_name,
        kcu.REFERENCED_COLUMN_NAME as referenced_column_name
      FROM information_schema.TABLES t
      LEFT JOIN information_schema.COLUMNS c ON t.TABLE_NAME = c.TABLE_NAME AND t.TABLE_SCHEMA = c.TABLE_SCHEMA
      LEFT JOIN information_schema.KEY_COLUMN_USAGE kcu ON c.TABLE_NAME = kcu.TABLE_NAME 
        AND c.COLUMN_NAME = kcu.COLUMN_NAME 
        AND c.TABLE_SCHEMA = kcu.TABLE_SCHEMA
        AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
      WHERE t.TABLE_SCHEMA = ? AND t.TABLE_TYPE = 'BASE TABLE'
      ORDER BY t.TABLE_NAME, c.ORDINAL_POSITION;
    `;

    const indexesQuery = `
      SELECT 
        s.INDEX_NAME as index_name,
        s.TABLE_NAME as table_name,
        s.TABLE_SCHEMA as schema_name,
        GROUP_CONCAT(s.COLUMN_NAME ORDER BY s.SEQ_IN_INDEX) as columns,
        CASE WHEN s.NON_UNIQUE = 0 THEN 1 ELSE 0 END as is_unique,
        CASE WHEN s.INDEX_NAME = 'PRIMARY' THEN 1 ELSE 0 END as is_primary
      FROM information_schema.STATISTICS s
      WHERE s.TABLE_SCHEMA = ?
      GROUP BY s.INDEX_NAME, s.TABLE_NAME, s.TABLE_SCHEMA, s.NON_UNIQUE
      ORDER BY s.TABLE_NAME, s.INDEX_NAME;
    `;

    const viewsQuery = `
      SELECT 
        TABLE_NAME as view_name,
        TABLE_SCHEMA as schema_name,
        VIEW_DEFINITION as view_definition
      FROM information_schema.VIEWS
      WHERE TABLE_SCHEMA = ?
      ORDER BY TABLE_NAME;
    `;

    try {
      const [tablesResult, indexesResult, viewsResult] = await Promise.all([
        this.executeQuery(tablesQuery, [this.config.database]),
        this.executeQuery(indexesQuery, [this.config.database]),
        this.executeQuery(viewsQuery, [this.config.database]),
      ]);

      // Process tables and columns
      const tablesMap = new Map();
      for (const row of tablesResult.rows) {
        const tableKey = `${row.table_schema}.${row.table_name}`;
        if (!tablesMap.has(tableKey)) {
          tablesMap.set(tableKey, {
            name: row.table_name,
            schema: row.table_schema,
            columns: [],
            indexes: [],
          });
        }

        if (row.column_name) {
          tablesMap.get(tableKey).columns.push({
            name: row.column_name,
            type: row.data_type,
            nullable: row.is_nullable === 'YES',
            defaultValue: row.column_default,
            isPrimaryKey: row.is_primary_key === 1,
            isForeignKey: row.is_foreign_key === 1,
            referencedTable: row.referenced_table_name,
            referencedColumn: row.referenced_column_name,
          });
        }
      }

      // Process indexes
      for (const row of indexesResult.rows) {
        const tableKey = `${row.schema_name}.${row.table_name}`;
        if (tablesMap.has(tableKey)) {
          tablesMap.get(tableKey).indexes.push({
            name: row.index_name,
            columns: row.columns ? row.columns.split(',') : [],
            isUnique: row.is_unique === 1,
            isPrimary: row.is_primary === 1,
          });
        }
      }

      return {
        tables: Array.from(tablesMap.values()),
        views: viewsResult.rows.map(row => ({
          name: row.view_name,
          schema: row.schema_name,
          definition: row.view_definition,
        })),
      };
    } catch (error) {
      throw this.formatError(error);
    }
  }

  async validateQuery(sql: string): Promise<{ isValid: boolean; error?: string }> {
    try {
      const sanitizedSql = this.sanitizeQuery(sql);
      
      // Use EXPLAIN to validate without executing
      const explainSql = `EXPLAIN ${sanitizedSql}`;
      await this.executeQuery(explainSql);
      
      return { isValid: true };
    } catch (error) {
      return { 
        isValid: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private mapMySQLType(type: number): string {
    // Map MySQL field types to readable names
    const typeMap: { [key: number]: string } = {
      0: 'decimal',
      1: 'tinyint',
      2: 'smallint',
      3: 'int',
      4: 'float',
      5: 'double',
      7: 'timestamp',
      8: 'bigint',
      9: 'mediumint',
      10: 'date',
      11: 'time',
      12: 'datetime',
      13: 'year',
      15: 'varchar',
      16: 'bit',
      245: 'json',
      246: 'decimal',
      247: 'enum',
      248: 'set',
      249: 'tinyblob',
      250: 'mediumblob',
      251: 'longblob',
      252: 'blob',
      253: 'varchar',
      254: 'char',
      255: 'geometry',
    };

    return typeMap[type] || 'unknown';
  }
}
