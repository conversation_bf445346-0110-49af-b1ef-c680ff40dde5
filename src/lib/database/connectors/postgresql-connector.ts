import { Pool, PoolClient, PoolConfig } from 'pg';
import { BaseConnector, DatabaseConnection, QueryResult, SchemaInfo } from './base-connector';

export class PostgreSQLConnector extends BaseConnector {
  private pool: Pool;

  constructor(config: DatabaseConnection) {
    super(config);
    
    const poolConfig: PoolConfig = {
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.username,
      password: config.password,
      ssl: config.ssl ? { rejectUnauthorized: false } : false,
      max: 10, // Maximum number of connections in pool
      min: 1,  // Minimum number of connections in pool
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: config.connectionTimeout,
      query_timeout: config.queryTimeout,
    };

    this.pool = new Pool(poolConfig);

    // Handle pool errors
    this.pool.on('error', (err) => {
      console.error('PostgreSQL pool error:', err);
    });
  }

  async connect(): Promise<void> {
    try {
      await this.withTimeout(
        this.pool.connect(),
        this.config.connectionTimeout!,
        'PostgreSQL connection timeout'
      );
    } catch (error) {
      throw this.formatError(error);
    }
  }

  async disconnect(): Promise<void> {
    try {
      await this.pool.end();
    } catch (error) {
      throw this.formatError(error);
    }
  }

  async testConnection(): Promise<boolean> {
    let client: PoolClient | null = null;
    try {
      client = await this.withTimeout(
        this.pool.connect(),
        this.config.connectionTimeout!,
        'PostgreSQL connection timeout'
      );
      
      await client.query('SELECT 1');
      return true;
    } catch (error) {
      console.error('PostgreSQL connection test failed:', error);
      return false;
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  async executeQuery(sql: string, params: any[] = []): Promise<QueryResult> {
    const sanitizedSql = this.sanitizeQuery(sql);
    let client: PoolClient | null = null;
    
    try {
      const startTime = Date.now();
      client = await this.pool.connect();
      
      const result = await this.withTimeout(
        client.query(sanitizedSql, params),
        this.config.queryTimeout!,
        'Query execution timeout'
      );
      
      const executionTime = Date.now() - startTime;

      const fields = result.fields.map(field => ({
        name: field.name,
        type: this.mapPostgreSQLType(field.dataTypeID),
        nullable: true, // PostgreSQL doesn't provide this info directly
      }));

      return {
        rows: result.rows,
        fields,
        rowCount: result.rowCount || 0,
        executionTime,
      };
    } catch (error) {
      throw this.formatError(error);
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  async getSchema(): Promise<SchemaInfo> {
    const tablesQuery = `
      SELECT 
        t.table_name,
        t.table_schema,
        c.column_name,
        c.data_type,
        c.is_nullable,
        c.column_default,
        CASE WHEN pk.column_name IS NOT NULL THEN true ELSE false END as is_primary_key,
        CASE WHEN fk.column_name IS NOT NULL THEN true ELSE false END as is_foreign_key,
        fk.referenced_table_name,
        fk.referenced_column_name
      FROM information_schema.tables t
      LEFT JOIN information_schema.columns c ON t.table_name = c.table_name AND t.table_schema = c.table_schema
      LEFT JOIN (
        SELECT ku.table_name, ku.column_name, ku.table_schema
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage ku ON tc.constraint_name = ku.constraint_name
        WHERE tc.constraint_type = 'PRIMARY KEY'
      ) pk ON c.table_name = pk.table_name AND c.column_name = pk.column_name AND c.table_schema = pk.table_schema
      LEFT JOIN (
        SELECT 
          ku.table_name, 
          ku.column_name, 
          ku.table_schema,
          ccu.table_name as referenced_table_name,
          ccu.column_name as referenced_column_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage ku ON tc.constraint_name = ku.constraint_name
        JOIN information_schema.constraint_column_usage ccu ON tc.constraint_name = ccu.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
      ) fk ON c.table_name = fk.table_name AND c.column_name = fk.column_name AND c.table_schema = fk.table_schema
      WHERE t.table_schema NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
      ORDER BY t.table_name, c.ordinal_position;
    `;

    const indexesQuery = `
      SELECT 
        i.relname as index_name,
        t.relname as table_name,
        n.nspname as schema_name,
        array_agg(a.attname ORDER BY a.attnum) as columns,
        ix.indisunique as is_unique,
        ix.indisprimary as is_primary
      FROM pg_class i
      JOIN pg_index ix ON i.oid = ix.indexrelid
      JOIN pg_class t ON ix.indrelid = t.oid
      JOIN pg_namespace n ON t.relnamespace = n.oid
      JOIN pg_attribute a ON t.oid = a.attrelid AND a.attnum = ANY(ix.indkey)
      WHERE n.nspname NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
      GROUP BY i.relname, t.relname, n.nspname, ix.indisunique, ix.indisprimary
      ORDER BY t.relname, i.relname;
    `;

    const viewsQuery = `
      SELECT 
        table_name as view_name,
        table_schema as schema_name,
        view_definition
      FROM information_schema.views
      WHERE table_schema NOT IN ('information_schema', 'pg_catalog')
      ORDER BY table_name;
    `;

    try {
      const [tablesResult, indexesResult, viewsResult] = await Promise.all([
        this.executeQuery(tablesQuery),
        this.executeQuery(indexesQuery),
        this.executeQuery(viewsQuery),
      ]);

      // Process tables and columns
      const tablesMap = new Map();
      for (const row of tablesResult.rows) {
        const tableKey = `${row.table_schema}.${row.table_name}`;
        if (!tablesMap.has(tableKey)) {
          tablesMap.set(tableKey, {
            name: row.table_name,
            schema: row.table_schema,
            columns: [],
            indexes: [],
          });
        }

        if (row.column_name) {
          tablesMap.get(tableKey).columns.push({
            name: row.column_name,
            type: row.data_type,
            nullable: row.is_nullable === 'YES',
            defaultValue: row.column_default,
            isPrimaryKey: row.is_primary_key,
            isForeignKey: row.is_foreign_key,
            referencedTable: row.referenced_table_name,
            referencedColumn: row.referenced_column_name,
          });
        }
      }

      // Process indexes
      for (const row of indexesResult.rows) {
        const tableKey = `${row.schema_name}.${row.table_name}`;
        if (tablesMap.has(tableKey)) {
          tablesMap.get(tableKey).indexes.push({
            name: row.index_name,
            columns: row.columns,
            isUnique: row.is_unique,
            isPrimary: row.is_primary,
          });
        }
      }

      return {
        tables: Array.from(tablesMap.values()),
        views: viewsResult.rows.map(row => ({
          name: row.view_name,
          schema: row.schema_name,
          definition: row.view_definition,
        })),
      };
    } catch (error) {
      throw this.formatError(error);
    }
  }

  async validateQuery(sql: string): Promise<{ isValid: boolean; error?: string }> {
    try {
      const sanitizedSql = this.sanitizeQuery(sql);
      
      // Use EXPLAIN to validate without executing
      const explainSql = `EXPLAIN ${sanitizedSql}`;
      await this.executeQuery(explainSql);
      
      return { isValid: true };
    } catch (error) {
      return { 
        isValid: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private mapPostgreSQLType(dataTypeID: number): string {
    // Map PostgreSQL OID types to readable names
    const typeMap: { [key: number]: string } = {
      16: 'boolean',
      20: 'bigint',
      21: 'smallint',
      23: 'integer',
      25: 'text',
      700: 'real',
      701: 'double precision',
      1043: 'varchar',
      1082: 'date',
      1114: 'timestamp',
      1184: 'timestamptz',
    };

    return typeMap[dataTypeID] || 'unknown';
  }
}
