import { ConnectorFactory } from './connectors/connector-factory';
import { ConnectionService } from './services/connection-service';
import { QueryResult } from './connectors/base-connector';

export interface QueryExecutionOptions {
  connectionId: string;
  sql: string;
  params?: any[];
  maxRows?: number;
  timeout?: number;
  userId?: string;
}

export interface QueryExecutionResult {
  success: boolean;
  data?: {
    rows: any[];
    fields: Array<{
      name: string;
      type: string;
      nullable: boolean;
    }>;
    rowCount: number;
    executionTime: number;
    affectedRows?: number;
  };
  error?: string;
  warnings?: string[];
  metadata: {
    connectionId: string;
    executedAt: Date;
    executionTime: number;
    rowsReturned: number;
    queryHash: string;
  };
}

export interface QueryValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  queryType: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE' | 'CREATE' | 'ALTER' | 'DROP' | 'UNKNOWN';
  isReadOnly: boolean;
  estimatedComplexity: 'LOW' | 'MEDIUM' | 'HIGH';
}

export class QueryExecutor {
  private static readonly DEFAULT_MAX_ROWS = 1000;
  private static readonly DEFAULT_TIMEOUT = 30000; // 30 seconds
  private static readonly MAX_QUERY_LENGTH = 50000; // 50KB

  // Execute a SQL query
  static async executeQuery(options: QueryExecutionOptions): Promise<QueryExecutionResult> {
    const startTime = Date.now();
    const queryHash = this.generateQueryHash(options.sql);

    try {
      // Validate input
      const validation = await this.validateQuery(options);
      if (!validation.isValid) {
        return {
          success: false,
          error: `Query validation failed: ${validation.errors.join(', ')}`,
          metadata: {
            connectionId: options.connectionId,
            executedAt: new Date(),
            executionTime: Date.now() - startTime,
            rowsReturned: 0,
            queryHash,
          },
        };
      }

      // Get connection details
      const connection = await ConnectionService.getConnectionById(options.connectionId);
      if (!connection) {
        return {
          success: false,
          error: 'Database connection not found',
          metadata: {
            connectionId: options.connectionId,
            executedAt: new Date(),
            executionTime: Date.now() - startTime,
            rowsReturned: 0,
            queryHash,
          },
        };
      }

      // Get connector
      const connector = ConnectorFactory.createConnector(
        connection.databaseType as 'MYSQL' | 'POSTGRESQL',
        await this.getConnectionConfig(options.connectionId),
        options.connectionId
      );

      // Apply query limits for SELECT statements
      let finalSql = options.sql;
      const maxRows = options.maxRows || this.DEFAULT_MAX_ROWS;
      
      if (validation.queryType === 'SELECT' && !this.hasLimitClause(options.sql)) {
        finalSql = this.addLimitClause(options.sql, maxRows, connection.databaseType);
      }

      // Execute query with timeout
      const result = await this.executeWithTimeout(
        () => connector.executeQuery(finalSql, options.params),
        options.timeout || this.DEFAULT_TIMEOUT
      );

      const executionTime = Date.now() - startTime;

      // Process results
      const processedResult: QueryExecutionResult = {
        success: true,
        data: {
          rows: result.rows,
          fields: result.fields,
          rowCount: result.rowCount,
          executionTime: result.executionTime,
          affectedRows: validation.queryType !== 'SELECT' ? result.rowCount : undefined,
        },
        warnings: validation.warnings.length > 0 ? validation.warnings : undefined,
        metadata: {
          connectionId: options.connectionId,
          executedAt: new Date(),
          executionTime,
          rowsReturned: result.rows.length,
          queryHash,
        },
      };

      // Log execution for analytics
      await this.logQueryExecution(options, processedResult);

      return processedResult;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      const errorResult: QueryExecutionResult = {
        success: false,
        error: errorMessage,
        metadata: {
          connectionId: options.connectionId,
          executedAt: new Date(),
          executionTime,
          rowsReturned: 0,
          queryHash,
        },
      };

      // Log failed execution
      await this.logQueryExecution(options, errorResult);

      return errorResult;
    }
  }

  // Validate query before execution
  static async validateQuery(options: QueryExecutionOptions): Promise<QueryValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Basic validation
    if (!options.sql || options.sql.trim().length === 0) {
      errors.push('Query cannot be empty');
    }

    if (options.sql.length > this.MAX_QUERY_LENGTH) {
      errors.push(`Query too long. Maximum length is ${this.MAX_QUERY_LENGTH} characters`);
    }

    // Detect query type
    const queryType = this.detectQueryType(options.sql);
    const isReadOnly = queryType === 'SELECT';

    // Check for dangerous patterns
    const dangerousPatterns = [
      /drop\s+database/gi,
      /drop\s+schema/gi,
      /truncate\s+table/gi,
      /delete\s+from\s+\w+\s*;?\s*$/gi, // DELETE without WHERE
      /update\s+\w+\s+set\s+.*\s*;?\s*$/gi, // UPDATE without WHERE
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(options.sql)) {
        errors.push('Query contains potentially dangerous operations');
        break;
      }
    }

    // Estimate complexity
    const estimatedComplexity = this.estimateQueryComplexity(options.sql);

    // Add warnings for complex queries
    if (estimatedComplexity === 'HIGH') {
      warnings.push('This query appears to be complex and may take longer to execute');
    }

    // Check for multiple statements
    const statements = options.sql.split(';').filter(s => s.trim().length > 0);
    if (statements.length > 1) {
      errors.push('Multiple statements are not allowed. Please execute one statement at a time');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      queryType,
      isReadOnly,
      estimatedComplexity,
    };
  }

  // Helper methods
  private static async getConnectionConfig(connectionId: string) {
    const connectionString = await ConnectionService.getConnectionString(connectionId);
    if (!connectionString) {
      throw new Error('Connection string not found');
    }

    // Parse connection string (reuse logic from ConnectionService)
    return this.parseConnectionString(connectionString);
  }

  private static parseConnectionString(connectionString: string) {
    // Reuse the parsing logic from ConnectionService
    if (connectionString.startsWith('postgresql://') || connectionString.startsWith('postgres://')) {
      const url = new URL(connectionString);
      return {
        host: url.hostname,
        port: parseInt(url.port) || 5432,
        database: url.pathname.substring(1),
        username: url.username,
        password: url.password,
        ssl: url.searchParams.get('sslmode') === 'require',
      };
    } else if (connectionString.startsWith('mysql://')) {
      const url = new URL(connectionString);
      return {
        host: url.hostname,
        port: parseInt(url.port) || 3306,
        database: url.pathname.substring(1),
        username: url.username,
        password: url.password,
        ssl: url.searchParams.get('ssl') === 'true',
      };
    } else {
      // Parse generic format
      const params = new Map();
      connectionString.split(';').forEach(pair => {
        const [key, value] = pair.split('=');
        if (key && value) {
          params.set(key.trim().toLowerCase(), value.trim());
        }
      });

      return {
        host: params.get('host') || 'localhost',
        port: parseInt(params.get('port') || '5432'),
        database: params.get('database') || '',
        username: params.get('username') || params.get('user') || '',
        password: params.get('password') || '',
        ssl: params.get('ssl') === 'true',
      };
    }
  }

  private static detectQueryType(sql: string): QueryValidationResult['queryType'] {
    const trimmed = sql.trim().toLowerCase();
    
    if (trimmed.startsWith('select')) return 'SELECT';
    if (trimmed.startsWith('insert')) return 'INSERT';
    if (trimmed.startsWith('update')) return 'UPDATE';
    if (trimmed.startsWith('delete')) return 'DELETE';
    if (trimmed.startsWith('create')) return 'CREATE';
    if (trimmed.startsWith('alter')) return 'ALTER';
    if (trimmed.startsWith('drop')) return 'DROP';
    
    return 'UNKNOWN';
  }

  private static estimateQueryComplexity(sql: string): 'LOW' | 'MEDIUM' | 'HIGH' {
    const lowerSql = sql.toLowerCase();
    let complexity = 0;

    // Count complexity indicators
    if (lowerSql.includes('join')) complexity += 1;
    if (lowerSql.includes('subquery') || lowerSql.includes('exists')) complexity += 2;
    if (lowerSql.includes('group by')) complexity += 1;
    if (lowerSql.includes('order by')) complexity += 1;
    if (lowerSql.includes('having')) complexity += 1;
    if (lowerSql.includes('union')) complexity += 2;
    if (lowerSql.includes('window') || lowerSql.includes('over(')) complexity += 2;

    // Count number of tables
    const tableMatches = lowerSql.match(/from\s+\w+|join\s+\w+/g);
    if (tableMatches && tableMatches.length > 2) complexity += 1;

    if (complexity >= 4) return 'HIGH';
    if (complexity >= 2) return 'MEDIUM';
    return 'LOW';
  }

  private static hasLimitClause(sql: string): boolean {
    return /\blimit\s+\d+/gi.test(sql);
  }

  private static addLimitClause(sql: string, maxRows: number, databaseType: string): string {
    const trimmed = sql.trim();
    
    if (databaseType === 'MYSQL' || databaseType === 'POSTGRESQL') {
      return `${trimmed} LIMIT ${maxRows}`;
    }
    
    return trimmed;
  }

  private static generateQueryHash(sql: string): string {
    // Simple hash function for query identification
    let hash = 0;
    for (let i = 0; i < sql.length; i++) {
      const char = sql.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16);
  }

  private static async executeWithTimeout<T>(
    operation: () => Promise<T>,
    timeoutMs: number
  ): Promise<T> {
    const timeout = new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error(`Query execution timeout after ${timeoutMs}ms`)), timeoutMs)
    );

    return Promise.race([operation(), timeout]);
  }

  private static async logQueryExecution(
    options: QueryExecutionOptions,
    result: QueryExecutionResult
  ): Promise<void> {
    try {
      // This would integrate with your analytics/logging system
      console.log('Query execution logged:', {
        connectionId: options.connectionId,
        userId: options.userId,
        success: result.success,
        executionTime: result.metadata.executionTime,
        rowsReturned: result.metadata.rowsReturned,
        queryHash: result.metadata.queryHash,
      });
    } catch (error) {
      console.error('Failed to log query execution:', error);
    }
  }
}
