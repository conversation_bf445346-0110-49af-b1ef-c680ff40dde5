import { prisma, handleDatabaseError } from '../prisma';
import { DatabaseConnection, Prisma } from '@prisma/client';
import { encrypt, decrypt } from '@/lib/utils/encryption';

type DatabaseType = 'MYSQL' | 'POSTGRESQL';

export class ConnectionService {
  // Create a new database connection
  static async createConnection(data: {
    userId: string;
    teamId?: string;
    name: string;
    databaseType: DatabaseType;
    connectionString: string;
    host?: string;
    port?: number;
    database?: string;
    username?: string;
  }): Promise<DatabaseConnection> {
    try {
      const connectionStringHash = encrypt(data.connectionString);
      
      const { connectionString, ...createData } = data;
      return await prisma.databaseConnection.create({
        data: {
          ...createData,
          connectionStringHash,
        },
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Get connection by ID
  static async getConnectionById(id: string): Promise<DatabaseConnection | null> {
    try {
      return await prisma.databaseConnection.findUnique({
        where: { id },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          team: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Get user connections
  static async getUserConnections(userId: string): Promise<DatabaseConnection[]> {
    try {
      return await prisma.databaseConnection.findMany({
        where: {
          userId,
          isActive: true,
        },
        orderBy: {
          lastConnectedAt: 'desc',
        },
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Get team connections
  static async getTeamConnections(teamId: string): Promise<DatabaseConnection[]> {
    try {
      return await prisma.databaseConnection.findMany({
        where: {
          teamId,
          isActive: true,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          lastConnectedAt: 'desc',
        },
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Update connection
  static async updateConnection(
    id: string,
    data: Partial<{
      name: string;
      connectionString: string;
      host: string;
      port: number;
      database: string;
      username: string;
      schemaMetadata: any;
      isActive: boolean;
    }>
  ): Promise<DatabaseConnection> {
    try {
      const updateData: any = { ...data };
      
      // Encrypt connection string if provided
      if (data.connectionString) {
        updateData.connectionStringHash = encrypt(data.connectionString);
        delete updateData.connectionString;
      }

      return await prisma.databaseConnection.update({
        where: { id },
        data: updateData,
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Update last connected timestamp
  static async updateLastConnected(id: string): Promise<void> {
    try {
      await prisma.databaseConnection.update({
        where: { id },
        data: {
          lastConnectedAt: new Date(),
        },
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Delete connection
  static async deleteConnection(id: string): Promise<void> {
    try {
      await prisma.databaseConnection.delete({
        where: { id },
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Get decrypted connection string
  static async getConnectionString(id: string): Promise<string | null> {
    try {
      const connection = await prisma.databaseConnection.findUnique({
        where: { id },
        select: {
          connectionStringHash: true,
        },
      });

      if (!connection) return null;

      return decrypt(connection.connectionStringHash);
    } catch (error) {
      console.error('Failed to decrypt connection string:', error);
      return null;
    }
  }

  // Test connection
  static async testConnection(id: string): Promise<boolean> {
    try {
      const connection = await this.getConnectionById(id);
      if (!connection) return false;

      const connectionString = await this.getConnectionString(id);
      if (!connectionString) return false;

      // Parse connection string to get connection details
      const config = this.parseConnectionString(connectionString);

      // Import connector factory
      const { ConnectorFactory } = await import('../connectors/connector-factory');

      // Create connector and test connection
      const connector = ConnectorFactory.createConnector(
        connection.databaseType as 'MYSQL' | 'POSTGRESQL',
        config
      );

      const isConnected = await connector.testConnection();

      // Clean up connector
      await connector.disconnect();

      if (isConnected) {
        // Update last connected timestamp if successful
        await this.updateLastConnected(id);
      }

      return isConnected;
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  }

  // Get connection statistics
  static async getConnectionStats(id: string) {
    try {
      const [
        totalQueries,
        totalSessions,
        recentActivity,
      ] = await Promise.all([
        prisma.generatedQuery.count({
          where: {
            session: {
              databaseConnectionId: id,
            },
          },
        }),
        prisma.querySession.count({
          where: {
            databaseConnectionId: id,
          },
        }),
        prisma.querySession.count({
          where: {
            databaseConnectionId: id,
            createdAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
            },
          },
        }),
      ]);

      return {
        totalQueries,
        totalSessions,
        recentActivity,
      };
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Parse connection string to extract connection details
  private static parseConnectionString(connectionString: string): {
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
    ssl?: boolean;
  } {
    try {
      // Handle different connection string formats
      if (connectionString.startsWith('postgresql://') || connectionString.startsWith('postgres://')) {
        return this.parsePostgreSQLConnectionString(connectionString);
      } else if (connectionString.startsWith('mysql://')) {
        return this.parseMySQLConnectionString(connectionString);
      } else {
        // Try to parse as a generic format
        return this.parseGenericConnectionString(connectionString);
      }
    } catch (error) {
      throw new Error(`Invalid connection string format: ${error}`);
    }
  }

  private static parsePostgreSQLConnectionString(connectionString: string) {
    const url = new URL(connectionString);
    return {
      host: url.hostname,
      port: parseInt(url.port) || 5432,
      database: url.pathname.substring(1),
      username: url.username,
      password: url.password,
      ssl: url.searchParams.get('sslmode') === 'require',
    };
  }

  private static parseMySQLConnectionString(connectionString: string) {
    const url = new URL(connectionString);
    return {
      host: url.hostname,
      port: parseInt(url.port) || 3306,
      database: url.pathname.substring(1),
      username: url.username,
      password: url.password,
      ssl: url.searchParams.get('ssl') === 'true',
    };
  }

  private static parseGenericConnectionString(connectionString: string) {
    // Parse format like: host=localhost;port=5432;database=mydb;username=user;password=pass
    const params = new Map();
    connectionString.split(';').forEach(pair => {
      const [key, value] = pair.split('=');
      if (key && value) {
        params.set(key.trim().toLowerCase(), value.trim());
      }
    });

    return {
      host: params.get('host') || 'localhost',
      port: parseInt(params.get('port') || '5432'),
      database: params.get('database') || '',
      username: params.get('username') || params.get('user') || '',
      password: params.get('password') || '',
      ssl: params.get('ssl') === 'true',
    };
  }

  // Get schema information for a connection
  static async getConnectionSchema(id: string) {
    try {
      const connection = await this.getConnectionById(id);
      if (!connection) throw new Error('Connection not found');

      const connectionString = await this.getConnectionString(id);
      if (!connectionString) throw new Error('Connection string not found');

      const config = this.parseConnectionString(connectionString);

      const { ConnectorFactory } = await import('../connectors/connector-factory');
      const connector = ConnectorFactory.createConnector(
        connection.databaseType as 'MYSQL' | 'POSTGRESQL',
        config,
        id
      );

      const schema = await connector.getSchema();

      // Update schema metadata in database
      await this.updateConnection(id, {
        schemaMetadata: schema,
      });

      return schema;
    } catch (error) {
      handleDatabaseError(error);
    }
  }
}
