import { prisma, handleDatabaseError } from '../prisma';
import { GeneratedQuery, QuerySession, ChatMessage } from '@prisma/client';

type DatabaseType = 'MYSQL' | 'POSTGRESQL';
type QueryStatus = 'GENERATED' | 'EXECUTED' | 'FAILED' | 'OPTIMIZED';
type MessageRole = 'user' | 'assistant' | 'system';

export class QueryService {
  // Create a new query session
  static async createSession(data: {
    userId: string;
    databaseConnectionId: string;
    name?: string;
    sessionData?: any;
  }): Promise<QuerySession> {
    try {
      return await prisma.querySession.create({
        data: {
          ...data,
          sessionData: data.sessionData || {},
        },
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Get session by ID
  static async getSessionById(id: string): Promise<QuerySession | null> {
    try {
      return await prisma.querySession.findUnique({
        where: { id },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          databaseConnection: {
            select: {
              id: true,
              name: true,
              databaseType: true,
            },
          },
          generatedQueries: {
            orderBy: {
              createdAt: 'desc',
            },
            take: 10,
          },
          chatMessages: {
            orderBy: {
              createdAt: 'asc',
            },
          },
        },
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Get user sessions
  static async getUserSessions(userId: string, limit = 20): Promise<QuerySession[]> {
    try {
      return await prisma.querySession.findMany({
        where: {
          userId,
          isActive: true,
        },
        include: {
          databaseConnection: {
            select: {
              id: true,
              name: true,
              databaseType: true,
            },
          },
          _count: {
            select: {
              generatedQueries: true,
              chatMessages: true,
            },
          },
        },
        orderBy: {
          updatedAt: 'desc',
        },
        take: limit,
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Create a generated query
  static async createGeneratedQuery(data: {
    sessionId: string;
    userId: string;
    userInput: string;
    generatedSQL: string;
    explanation?: string;
    databaseType: DatabaseType;
    executionTime?: number;
    rowsAffected?: number;
    performanceData?: any;
    optimizationTips?: any;
  }): Promise<GeneratedQuery> {
    try {
      return await prisma.generatedQuery.create({
        data,
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Update query execution results
  static async updateQueryExecution(
    id: string,
    data: {
      status: QueryStatus;
      executionTime?: number;
      rowsAffected?: number;
      errorMessage?: string;
      performanceData?: any;
    }
  ): Promise<GeneratedQuery> {
    try {
      return await prisma.generatedQuery.update({
        where: { id },
        data,
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Add user feedback to query
  static async addQueryFeedback(
    id: string,
    feedback: number
  ): Promise<GeneratedQuery> {
    try {
      return await prisma.generatedQuery.update({
        where: { id },
        data: {
          userFeedback: feedback,
        },
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Get user query history
  static async getUserQueryHistory(
    userId: string,
    limit = 50,
    databaseType?: DatabaseType
  ): Promise<GeneratedQuery[]> {
    try {
      return await prisma.generatedQuery.findMany({
        where: {
          userId,
          ...(databaseType && { databaseType }),
        },
        include: {
          session: {
            select: {
              id: true,
              name: true,
              databaseConnection: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Add chat message
  static async addChatMessage(data: {
    sessionId: string;
    role: MessageRole;
    content: string;
    metadata?: any;
  }): Promise<ChatMessage> {
    try {
      return await prisma.chatMessage.create({
        data,
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Get session chat messages
  static async getSessionMessages(sessionId: string): Promise<ChatMessage[]> {
    try {
      return await prisma.chatMessage.findMany({
        where: { sessionId },
        orderBy: {
          createdAt: 'asc',
        },
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Update session
  static async updateSession(
    id: string,
    data: Partial<{
      name: string;
      sessionData: any;
      isActive: boolean;
    }>
  ): Promise<QuerySession> {
    try {
      return await prisma.querySession.update({
        where: { id },
        data,
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Delete session
  static async deleteSession(id: string): Promise<void> {
    try {
      await prisma.querySession.delete({
        where: { id },
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Get query analytics
  static async getQueryAnalytics(userId: string, days = 30) {
    try {
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

      const [
        totalQueries,
        successfulQueries,
        averageExecutionTime,
        topDatabases,
        dailyActivity,
      ] = await Promise.all([
        prisma.generatedQuery.count({
          where: {
            userId,
            createdAt: { gte: startDate },
          },
        }),
        prisma.generatedQuery.count({
          where: {
            userId,
            status: 'EXECUTED',
            createdAt: { gte: startDate },
          },
        }),
        prisma.generatedQuery.aggregate({
          where: {
            userId,
            executionTime: { not: null },
            createdAt: { gte: startDate },
          },
          _avg: {
            executionTime: true,
          },
        }),
        prisma.generatedQuery.groupBy({
          by: ['databaseType'],
          where: {
            userId,
            createdAt: { gte: startDate },
          },
          _count: {
            id: true,
          },
          orderBy: {
            _count: {
              id: 'desc',
            },
          },
        }),
        prisma.$queryRaw`
          SELECT 
            DATE(created_at) as date,
            COUNT(*) as count
          FROM generated_queries 
          WHERE user_id = ${userId} 
            AND created_at >= ${startDate}
          GROUP BY DATE(created_at)
          ORDER BY date DESC
        `,
      ]);

      return {
        totalQueries,
        successfulQueries,
        successRate: totalQueries > 0 ? (successfulQueries / totalQueries) * 100 : 0,
        averageExecutionTime: averageExecutionTime._avg.executionTime || 0,
        topDatabases,
        dailyActivity,
      };
    } catch (error) {
      handleDatabaseError(error);
    }
  }
}
