import { prisma, handleDatabaseError } from '../prisma';
import { User, Prisma } from '@prisma/client';

export class UserService {
  // Create a new user
  static async createUser(data: {
    email: string;
    name: string;
    image?: string;
  }): Promise<User> {
    try {
      return await prisma.user.create({
        data,
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Get user by ID
  static async getUserById(id: string): Promise<User | null> {
    try {
      return await prisma.user.findUnique({
        where: { id },
        include: {
          databaseConnections: true,
          _count: {
            select: {
              generatedQueries: true,
              querySessions: true,
            },
          },
        },
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Get user by email
  static async getUserByEmail(email: string): Promise<User | null> {
    try {
      return await prisma.user.findUnique({
        where: { email },
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Update user
  static async updateUser(
    id: string,
    data: Partial<Pick<User, 'name' | 'image'>>
  ): Promise<User> {
    try {
      return await prisma.user.update({
        where: { id },
        data,
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Delete user
  static async deleteUser(id: string): Promise<void> {
    try {
      await prisma.user.delete({
        where: { id },
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Get user with full profile data
  static async getUserProfile(id: string) {
    try {
      return await prisma.user.findUnique({
        where: { id },
        include: {
          databaseConnections: {
            where: { isActive: true },
            select: {
              id: true,
              name: true,
              databaseType: true,
              lastConnectedAt: true,
            },
          },
          teamMemberships: {
            include: {
              team: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                },
              },
            },
          },
          _count: {
            select: {
              generatedQueries: true,
              querySessions: true,
              databaseConnections: true,
            },
          },
        },
      });
    } catch (error) {
      handleDatabaseError(error);
    }
  }

  // Get user statistics
  static async getUserStats(id: string) {
    try {
      const [
        totalQueries,
        totalSessions,
        recentQueries,
        topDatabases,
      ] = await Promise.all([
        prisma.generatedQuery.count({
          where: { userId: id },
        }),
        prisma.querySession.count({
          where: { userId: id },
        }),
        prisma.generatedQuery.count({
          where: {
            userId: id,
            createdAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
            },
          },
        }),
        prisma.generatedQuery.groupBy({
          by: ['databaseType'],
          where: { userId: id },
          _count: {
            id: true,
          },
          orderBy: {
            _count: {
              id: 'desc',
            },
          },
          take: 3,
        }),
      ]);

      return {
        totalQueries,
        totalSessions,
        recentQueries,
        topDatabases,
      };
    } catch (error) {
      handleDatabaseError(error);
    }
  }
}
