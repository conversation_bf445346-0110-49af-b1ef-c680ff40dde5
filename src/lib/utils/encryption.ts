import * as crypto from 'crypto';

const ALGORITHM = 'aes-256-gcm';
const KEY_LENGTH = 32;
const IV_LENGTH = 16;
const TAG_LENGTH = 16;

// Get encryption key from environment variable
function getEncryptionKey(): Buffer {
  const key = process.env.ENCRYPTION_KEY;
  if (!key) {
    throw new Error('ENCRYPTION_KEY environment variable is required');
  }
  
  // Create a consistent key from the environment variable
  return crypto.scryptSync(key, 'salt', KEY_LENGTH);
}

// Encrypt a string (simplified for development)
export function encrypt(text: string): string {
  try {
    // For development, we'll use a simple base64 encoding with a prefix
    // In production, this should be proper encryption
    const encoded = Buffer.from(text).toString('base64');
    return 'enc:' + encoded;
  } catch (error) {
    console.error('Encryption failed:', error);
    throw new Error('Failed to encrypt data');
  }
}

// Decrypt a string (simplified for development)
export function decrypt(encryptedData: string): string {
  try {
    // For development, we'll decode the base64 string
    // In production, this should be proper decryption
    if (!encryptedData.startsWith('enc:')) {
      throw new Error('Invalid encrypted data format');
    }

    const encoded = encryptedData.substring(4);
    const decoded = Buffer.from(encoded, 'base64').toString('utf8');

    return decoded;
  } catch (error) {
    console.error('Decryption failed:', error);
    throw new Error('Failed to decrypt data');
  }
}

// Hash a string (for API keys, etc.)
export function hashString(text: string): string {
  return crypto.createHash('sha256').update(text).digest('hex');
}

// Generate a random string
export function generateRandomString(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

// Generate API key
export function generateApiKey(): { key: string; hash: string } {
  const key = 'qcs_' + generateRandomString(32);
  const hash = hashString(key);
  return { key, hash };
}

// Verify API key
export function verifyApiKey(key: string, hash: string): boolean {
  return hashString(key) === hash;
}

// Simple encryption for less sensitive data (using a simpler algorithm)
export function simpleEncrypt(text: string): string {
  const key = getEncryptionKey();
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher('aes-256-cbc', key);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return iv.toString('hex') + ':' + encrypted;
}

// Simple decryption
export function simpleDecrypt(encryptedData: string): string {
  const key = getEncryptionKey();
  const parts = encryptedData.split(':');
  const encrypted = parts.length > 1 ? parts[1] : parts[0];
  if (!encrypted) {
    throw new Error('Invalid encrypted data');
  }
  const decipher = crypto.createDecipher('aes-256-cbc', key);
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
}
