import { io, Socket } from 'socket.io-client';
import { CollaborationUser, CursorPosition, ChatMessage } from './server';

export interface CollaborationEvents {
  // Connection events
  authenticated: (data: { success: boolean; user?: CollaborationUser; error?: string }) => void;
  session_joined: (data: { session: any; user: CollaborationUser; participants: CollaborationUser[] }) => void;
  user_joined: (data: { user: CollaborationUser; participants: CollaborationUser[] }) => void;
  user_left: (data: { userId: string; participants: CollaborationUser[] }) => void;
  
  // Content synchronization
  sql_sync: (data: { content: string }) => void;
  sql_change: (data: { content: string; operation: any; userId: string; userName: string }) => void;
  
  // Cursor tracking
  cursor_update: (data: { position: CursorPosition; user: { id: string; name: string; color: string } }) => void;
  
  // Chat
  chat_message: (message: ChatMessage) => void;
  
  // Query execution
  query_execution_start: (data: { userId: string; userName: string; sql: string }) => void;
  query_execution_complete: (data: { userId: string; userName: string; result: any }) => void;
  
  // Errors
  error: (data: { message: string }) => void;
}

export class CollaborationClient {
  private socket: Socket | null = null;
  private eventHandlers: Partial<CollaborationEvents> = {};
  private isConnected = false;
  private currentSessionId: string | null = null;
  private currentUser: CollaborationUser | null = null;

  constructor() {
    this.connect();
  }

  private connect() {
    this.socket = io({
      path: '/api/socket',
      autoConnect: false
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Connected to collaboration server');
      this.isConnected = true;
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from collaboration server');
      this.isConnected = false;
      this.currentSessionId = null;
      this.currentUser = null;
    });

    // Forward all collaboration events to registered handlers
    Object.keys(this.eventHandlers).forEach(event => {
      this.socket!.on(event, (data: any) => {
        const handler = this.eventHandlers[event as keyof CollaborationEvents];
        if (handler) {
          (handler as any)(data);
        }
      });
    });
  }

  // Public API methods
  public async authenticate(token: string): Promise<boolean> {
    return new Promise((resolve) => {
      if (!this.socket) {
        resolve(false);
        return;
      }

      this.socket.connect();

      this.socket.once('authenticated', (data: { success: boolean; user?: CollaborationUser; error?: string }) => {
        if (data.success && data.user) {
          this.currentUser = data.user;
          resolve(true);
        } else {
          console.error('Authentication failed:', data.error);
          resolve(false);
        }
      });

      this.socket.emit('authenticate', token);
    });
  }

  public joinSession(sessionId: string, connectionId: string, sessionName?: string): void {
    if (!this.socket || !this.isConnected) {
      console.error('Not connected to collaboration server');
      return;
    }

    this.currentSessionId = sessionId;
    this.socket.emit('join_session', { sessionId, connectionId, sessionName });
  }

  public leaveSession(): void {
    if (!this.socket || !this.currentSessionId) return;

    this.socket.emit('leave_session', this.currentSessionId);
    this.currentSessionId = null;
  }

  public updateSqlContent(content: string, operation?: any): void {
    if (!this.socket || !this.currentSessionId) return;

    this.socket.emit('sql_change', {
      sessionId: this.currentSessionId,
      content,
      operation
    });
  }

  public updateCursorPosition(position: Omit<CursorPosition, 'userId'>): void {
    if (!this.socket || !this.currentSessionId) return;

    this.socket.emit('cursor_update', {
      sessionId: this.currentSessionId,
      position
    });
  }

  public sendChatMessage(message: string): void {
    if (!this.socket || !this.currentSessionId) return;

    this.socket.emit('chat_message', {
      sessionId: this.currentSessionId,
      message
    });
  }

  public notifyQueryExecutionStart(sql: string): void {
    if (!this.socket || !this.currentSessionId) return;

    this.socket.emit('query_execution_start', {
      sessionId: this.currentSessionId,
      sql
    });
  }

  public notifyQueryExecutionComplete(result: any): void {
    if (!this.socket || !this.currentSessionId) return;

    this.socket.emit('query_execution_complete', {
      sessionId: this.currentSessionId,
      result
    });
  }

  public disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnected = false;
    this.currentSessionId = null;
    this.currentUser = null;
  }

  // Event handler registration
  public on<K extends keyof CollaborationEvents>(event: K, handler: CollaborationEvents[K]): void {
    this.eventHandlers[event] = handler;
    
    // If socket is already connected, register the handler immediately
    if (this.socket) {
      this.socket.on(event, handler as any);
    }
  }

  public off<K extends keyof CollaborationEvents>(event: K): void {
    delete this.eventHandlers[event];
    
    if (this.socket) {
      this.socket.off(event);
    }
  }

  // Getters
  public get connected(): boolean {
    return this.isConnected;
  }

  public get sessionId(): string | null {
    return this.currentSessionId;
  }

  public get user(): CollaborationUser | null {
    return this.currentUser;
  }
}

// Global instance
let collaborationClient: CollaborationClient | null = null;

export function getCollaborationClient(): CollaborationClient {
  if (!collaborationClient) {
    collaborationClient = new CollaborationClient();
  }
  return collaborationClient;
}

export function createCollaborationClient(): CollaborationClient {
  return new CollaborationClient();
}
