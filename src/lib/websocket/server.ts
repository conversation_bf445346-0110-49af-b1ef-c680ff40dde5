import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
import { NextApiRequest } from 'next';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';

export interface CollaborationSession {
  id: string;
  name: string;
  connectionId: string;
  ownerId: string;
  participants: Map<string, CollaborationUser>;
  sqlContent: string;
  cursors: Map<string, CursorPosition>;
  createdAt: Date;
  lastActivity: Date;
}

export interface CollaborationUser {
  id: string;
  name: string;
  email: string;
  color: string;
  socketId: string;
  joinedAt: Date;
  isActive: boolean;
}

export interface CursorPosition {
  userId: string;
  line: number;
  column: number;
  selection?: {
    startLine: number;
    startColumn: number;
    endLine: number;
    endColumn: number;
  };
}

export interface ChatMessage {
  id: string;
  userId: string;
  userName: string;
  message: string;
  timestamp: Date;
  type: 'message' | 'system' | 'query_execution';
}

export class CollaborationServer {
  private io: SocketIOServer;
  private sessions: Map<string, CollaborationSession> = new Map();
  private userColors: string[] = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ];

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.NEXTAUTH_URL || "http://localhost:3000",
        methods: ["GET", "POST"]
      },
      path: '/api/socket'
    });

    this.setupEventHandlers();
    this.startCleanupInterval();
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`Client connected: ${socket.id}`);

      // Authentication
      socket.on('authenticate', async (token: string) => {
        try {
          // In a real implementation, you'd verify the JWT token
          // For now, we'll use a simple approach
          const user = await this.authenticateUser(token);
          if (user) {
            socket.data.user = user;
            socket.emit('authenticated', { success: true, user });
          } else {
            socket.emit('authenticated', { success: false, error: 'Invalid token' });
            socket.disconnect();
          }
        } catch (error) {
          socket.emit('authenticated', { success: false, error: 'Authentication failed' });
          socket.disconnect();
        }
      });

      // Join collaboration session
      socket.on('join_session', (data: { sessionId: string; connectionId: string; sessionName?: string }) => {
        this.handleJoinSession(socket, data);
      });

      // Leave collaboration session
      socket.on('leave_session', (sessionId: string) => {
        this.handleLeaveSession(socket, sessionId);
      });

      // SQL content changes
      socket.on('sql_change', (data: { sessionId: string; content: string; operation: any }) => {
        this.handleSqlChange(socket, data);
      });

      // Cursor position updates
      socket.on('cursor_update', (data: { sessionId: string; position: CursorPosition }) => {
        this.handleCursorUpdate(socket, data);
      });

      // Chat messages
      socket.on('chat_message', (data: { sessionId: string; message: string }) => {
        this.handleChatMessage(socket, data);
      });

      // Query execution events
      socket.on('query_execution_start', (data: { sessionId: string; sql: string }) => {
        this.handleQueryExecutionStart(socket, data);
      });

      socket.on('query_execution_complete', (data: { sessionId: string; result: any }) => {
        this.handleQueryExecutionComplete(socket, data);
      });

      // Disconnect
      socket.on('disconnect', () => {
        this.handleDisconnect(socket);
      });
    });
  }

  private async authenticateUser(token: string): Promise<CollaborationUser | null> {
    try {
      // In a real implementation, decode and verify JWT token
      // For demo purposes, we'll create a mock user
      return {
        id: `user_${Date.now()}`,
        name: 'Demo User',
        email: '<EMAIL>',
        color: this.getRandomColor(),
        socketId: '',
        joinedAt: new Date(),
        isActive: true
      };
    } catch (error) {
      return null;
    }
  }

  private handleJoinSession(socket: any, data: { sessionId: string; connectionId: string; sessionName?: string }) {
    const { sessionId, connectionId, sessionName } = data;
    const user = socket.data.user;

    if (!user) {
      socket.emit('error', { message: 'Not authenticated' });
      return;
    }

    // Get or create session
    let session = this.sessions.get(sessionId);
    if (!session) {
      session = {
        id: sessionId,
        name: sessionName || `Session ${sessionId.slice(0, 8)}`,
        connectionId,
        ownerId: user.id,
        participants: new Map(),
        sqlContent: '',
        cursors: new Map(),
        createdAt: new Date(),
        lastActivity: new Date()
      };
      this.sessions.set(sessionId, session);
    }

    // Add user to session
    user.socketId = socket.id;
    user.color = this.getRandomColor();
    session.participants.set(user.id, user);
    session.lastActivity = new Date();

    // Join socket room
    socket.join(sessionId);
    socket.data.sessionId = sessionId;

    // Notify user of successful join
    socket.emit('session_joined', {
      session: this.serializeSession(session),
      user,
      participants: Array.from(session.participants.values())
    });

    // Notify other participants
    socket.to(sessionId).emit('user_joined', {
      user,
      participants: Array.from(session.participants.values())
    });

    // Send current SQL content
    if (session.sqlContent) {
      socket.emit('sql_sync', { content: session.sqlContent });
    }

    console.log(`User ${user.name} joined session ${sessionId}`);
  }

  private handleLeaveSession(socket: any, sessionId: string) {
    const session = this.sessions.get(sessionId);
    const user = socket.data.user;

    if (!session || !user) return;

    // Remove user from session
    session.participants.delete(user.id);
    session.cursors.delete(user.id);

    // Leave socket room
    socket.leave(sessionId);
    socket.data.sessionId = null;

    // Notify other participants
    socket.to(sessionId).emit('user_left', {
      userId: user.id,
      participants: Array.from(session.participants.values())
    });

    // Clean up empty sessions
    if (session.participants.size === 0) {
      this.sessions.delete(sessionId);
      console.log(`Session ${sessionId} cleaned up (no participants)`);
    }

    console.log(`User ${user.name} left session ${sessionId}`);
  }

  private handleSqlChange(socket: any, data: { sessionId: string; content: string; operation: any }) {
    const session = this.sessions.get(data.sessionId);
    const user = socket.data.user;

    if (!session || !user) return;

    // Update session content
    session.sqlContent = data.content;
    session.lastActivity = new Date();

    // Broadcast to other participants
    socket.to(data.sessionId).emit('sql_change', {
      content: data.content,
      operation: data.operation,
      userId: user.id,
      userName: user.name
    });
  }

  private handleCursorUpdate(socket: any, data: { sessionId: string; position: CursorPosition }) {
    const session = this.sessions.get(data.sessionId);
    const user = socket.data.user;

    if (!session || !user) return;

    // Update cursor position
    data.position.userId = user.id;
    session.cursors.set(user.id, data.position);

    // Broadcast to other participants
    socket.to(data.sessionId).emit('cursor_update', {
      position: data.position,
      user: {
        id: user.id,
        name: user.name,
        color: user.color
      }
    });
  }

  private handleChatMessage(socket: any, data: { sessionId: string; message: string }) {
    const session = this.sessions.get(data.sessionId);
    const user = socket.data.user;

    if (!session || !user) return;

    const chatMessage: ChatMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId: user.id,
      userName: user.name,
      message: data.message,
      timestamp: new Date(),
      type: 'message'
    };

    // Broadcast to all participants including sender
    this.io.to(data.sessionId).emit('chat_message', chatMessage);
  }

  private handleQueryExecutionStart(socket: any, data: { sessionId: string; sql: string }) {
    const user = socket.data.user;
    if (!user) return;

    const systemMessage: ChatMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId: 'system',
      userName: 'System',
      message: `${user.name} started executing a query`,
      timestamp: new Date(),
      type: 'system'
    };

    // Broadcast to all participants
    this.io.to(data.sessionId).emit('query_execution_start', {
      userId: user.id,
      userName: user.name,
      sql: data.sql
    });

    this.io.to(data.sessionId).emit('chat_message', systemMessage);
  }

  private handleQueryExecutionComplete(socket: any, data: { sessionId: string; result: any }) {
    const user = socket.data.user;
    if (!user) return;

    const resultMessage = data.result.success 
      ? `Query completed successfully (${data.result.metadata?.rowsReturned || 0} rows, ${data.result.metadata?.executionTime || 0}ms)`
      : `Query failed: ${data.result.error}`;

    const systemMessage: ChatMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId: 'system',
      userName: 'System',
      message: `${user.name}: ${resultMessage}`,
      timestamp: new Date(),
      type: 'query_execution'
    };

    // Broadcast to all participants
    this.io.to(data.sessionId).emit('query_execution_complete', {
      userId: user.id,
      userName: user.name,
      result: data.result
    });

    this.io.to(data.sessionId).emit('chat_message', systemMessage);
  }

  private handleDisconnect(socket: any) {
    const user = socket.data.user;
    const sessionId = socket.data.sessionId;

    if (sessionId && user) {
      this.handleLeaveSession(socket, sessionId);
    }

    console.log(`Client disconnected: ${socket.id}`);
  }

  private getRandomColor(): string {
    return this.userColors[Math.floor(Math.random() * this.userColors.length)] || '#FF6B6B';
  }

  private serializeSession(session: CollaborationSession) {
    return {
      id: session.id,
      name: session.name,
      connectionId: session.connectionId,
      ownerId: session.ownerId,
      participantCount: session.participants.size,
      createdAt: session.createdAt,
      lastActivity: session.lastActivity
    };
  }

  private startCleanupInterval() {
    // Clean up inactive sessions every 5 minutes
    setInterval(() => {
      const now = new Date();
      const maxInactiveTime = 30 * 60 * 1000; // 30 minutes

      Array.from(this.sessions.entries()).forEach(([sessionId, session]) => {
        if (now.getTime() - session.lastActivity.getTime() > maxInactiveTime) {
          this.sessions.delete(sessionId);
          console.log(`Cleaned up inactive session: ${sessionId}`);
        }
      });
    }, 5 * 60 * 1000);
  }

  // Public methods for external access
  public getActiveSessions(): CollaborationSession[] {
    return Array.from(this.sessions.values());
  }

  public getSessionById(sessionId: string): CollaborationSession | undefined {
    return this.sessions.get(sessionId);
  }

  public getSessionParticipants(sessionId: string): CollaborationUser[] {
    const session = this.sessions.get(sessionId);
    return session ? Array.from(session.participants.values()) : [];
  }
}

// Global instance
let collaborationServer: CollaborationServer | null = null;

export function getCollaborationServer(): CollaborationServer | null {
  return collaborationServer;
}

export function initializeCollaborationServer(server: HTTPServer): CollaborationServer {
  if (!collaborationServer) {
    collaborationServer = new CollaborationServer(server);
  }
  return collaborationServer;
}
