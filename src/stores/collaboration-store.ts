import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { CollaborationUser, CursorPosition, ChatMessage } from '@/lib/websocket/server';

export interface CollaborationState {
  // Connection state
  isConnected: boolean;
  isAuthenticated: boolean;
  currentUser: CollaborationUser | null;
  
  // Session state
  currentSessionId: string | null;
  sessionName: string | null;
  connectionId: string | null;
  isInSession: boolean;
  
  // Participants
  participants: CollaborationUser[];
  
  // Real-time content
  sqlContent: string;
  cursors: Map<string, CursorPosition & { user: { id: string; name: string; color: string } }>;
  
  // Chat
  chatMessages: ChatMessage[];
  unreadMessageCount: number;
  
  // UI state
  isChatExpanded: boolean;
  showCursors: boolean;
  
  // Actions
  setConnectionState: (connected: boolean, authenticated: boolean) => void;
  setCurrentUser: (user: CollaborationUser | null) => void;
  setSessionState: (sessionId: string | null, sessionName: string | null, connectionId: string | null, inSession: boolean) => void;
  setParticipants: (participants: CollaborationUser[]) => void;
  setSqlContent: (content: string) => void;
  updateCursor: (userId: string, cursor: CursorPosition & { user: { id: string; name: string; color: string } }) => void;
  removeCursor: (userId: string) => void;
  addChatMessage: (message: ChatMessage) => void;
  clearChatMessages: () => void;
  setUnreadMessageCount: (count: number) => void;
  setChatExpanded: (expanded: boolean) => void;
  setShowCursors: (show: boolean) => void;
  reset: () => void;
}

const initialState = {
  isConnected: false,
  isAuthenticated: false,
  currentUser: null,
  currentSessionId: null,
  sessionName: null,
  connectionId: null,
  isInSession: false,
  participants: [],
  sqlContent: '',
  cursors: new Map(),
  chatMessages: [],
  unreadMessageCount: 0,
  isChatExpanded: false,
  showCursors: true,
};

export const useCollaborationStore = create<CollaborationState>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,

    setConnectionState: (connected: boolean, authenticated: boolean) => {
      set({ isConnected: connected, isAuthenticated: authenticated });
    },

    setCurrentUser: (user: CollaborationUser | null) => {
      set({ currentUser: user });
    },

    setSessionState: (sessionId: string | null, sessionName: string | null, connectionId: string | null, inSession: boolean) => {
      set({ 
        currentSessionId: sessionId,
        sessionName,
        connectionId,
        isInSession: inSession 
      });
    },

    setParticipants: (participants: CollaborationUser[]) => {
      set({ participants });
    },

    setSqlContent: (content: string) => {
      set({ sqlContent: content });
    },

    updateCursor: (userId: string, cursor: CursorPosition & { user: { id: string; name: string; color: string } }) => {
      const cursors = new Map(get().cursors);
      cursors.set(userId, cursor);
      set({ cursors });
    },

    removeCursor: (userId: string) => {
      const cursors = new Map(get().cursors);
      cursors.delete(userId);
      set({ cursors });
    },

    addChatMessage: (message: ChatMessage) => {
      const currentMessages = get().chatMessages;
      const newMessages = [...currentMessages, message];
      
      // Keep only last 100 messages to prevent memory issues
      const trimmedMessages = newMessages.slice(-100);
      
      set({ 
        chatMessages: trimmedMessages,
        unreadMessageCount: get().isChatExpanded ? 0 : get().unreadMessageCount + 1
      });
    },

    clearChatMessages: () => {
      set({ chatMessages: [], unreadMessageCount: 0 });
    },

    setUnreadMessageCount: (count: number) => {
      set({ unreadMessageCount: count });
    },

    setChatExpanded: (expanded: boolean) => {
      set({ 
        isChatExpanded: expanded,
        unreadMessageCount: expanded ? 0 : get().unreadMessageCount
      });
    },

    setShowCursors: (show: boolean) => {
      set({ showCursors: show });
    },

    reset: () => {
      set(initialState);
    },
  }))
);

// Selectors for computed values
export const useCollaborationSelectors = () => {
  const store = useCollaborationStore();
  
  return {
    ...store,
    
    // Computed values
    canCollaborate: store.isConnected && store.isAuthenticated && store.isInSession,
    participantCount: store.participants.length,
    otherParticipants: store.participants.filter(p => p.id !== store.currentUser?.id),
    hasUnreadMessages: store.unreadMessageCount > 0,
    activeCursors: Array.from(store.cursors.values()).filter(cursor => 
      cursor.user.id !== store.currentUser?.id
    ),
  };
};

// Subscribe to store changes for debugging
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  useCollaborationStore.subscribe(
    (state) => state,
    (state) => {
      console.log('Collaboration store updated:', {
        isConnected: state.isConnected,
        isAuthenticated: state.isAuthenticated,
        isInSession: state.isInSession,
        participantCount: state.participants.length,
        messageCount: state.chatMessages.length,
        cursorCount: state.cursors.size,
      });
    }
  );
}
