// Core application types for QueryCraft Studio

export interface User {
  id: string;
  email: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface DatabaseConnection {
  id: string;
  userId: string;
  name: string;
  databaseType: 'mysql' | 'postgresql';
  connectionString: string; // encrypted
  schemaMetadata?: SchemaMetadata;
  createdAt: Date;
  updatedAt: Date;
}

export interface SchemaMetadata {
  tables: TableMetadata[];
  relationships: Relationship[];
  indexes: IndexMetadata[];
  constraints: ConstraintMetadata[];
  statistics: SchemaStatistics;
}

export interface TableMetadata {
  name: string;
  schema: string;
  columns: ColumnMetadata[];
  rowCount?: number;
  dataSize?: string;
  indexes: string[];
}

export interface ColumnMetadata {
  name: string;
  dataType: string;
  isNullable: boolean;
  isPrimaryKey: boolean;
  isForeignKey: boolean;
  defaultValue?: string;
  maxLength?: number;
  precision?: number;
  scale?: number;
}

export interface Relationship {
  type: 'foreign_key' | 'inferred' | 'semantic';
  fromTable: string;
  fromColumn: string;
  toTable: string;
  toColumn: string;
  confidence: number;
}

export interface IndexMetadata {
  name: string;
  table: string;
  columns: string[];
  isUnique: boolean;
  type: string;
}

export interface ConstraintMetadata {
  name: string;
  table: string;
  type: 'primary_key' | 'foreign_key' | 'unique' | 'check';
  columns: string[];
  referencedTable?: string;
  referencedColumns?: string[];
}

export interface SchemaStatistics {
  totalTables: number;
  totalColumns: number;
  totalIndexes: number;
  totalConstraints: number;
  estimatedSize: string;
}

export interface QuerySession {
  id: string;
  userId: string;
  databaseConnectionId: string;
  sessionData: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface GeneratedQuery {
  id: string;
  sessionId: string;
  userInput: string;
  generatedSQL: string;
  explanation?: string;
  performanceMetrics?: PerformanceMetrics;
  userFeedback?: number; // 1-5 rating
  createdAt: Date;
}

export interface PerformanceMetrics {
  estimatedExecutionTime: number;
  estimatedRowsExamined: number;
  estimatedRowsReturned: number;
  indexUsage: string[];
  optimizationSuggestions: string[];
}

// Agent-related types
export interface AgentContext {
  sessionId: string;
  userId: string;
  databaseType: 'mysql' | 'postgresql';
  schemaContext?: SchemaMetadata;
  conversationHistory?: ConversationMessage[];
}

export interface ConversationMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: any;
}

export interface AgentMessage {
  type: string;
  data: any;
  correlationId?: string;
  priority?: 'low' | 'medium' | 'high' | 'critical';
}

export interface IntentAnalysis {
  primaryIntent: QueryIntent;
  confidence: number;
  entities: ExtractedEntity[];
  requiredClarifications: string[];
  suggestedActions: string[];
}

export type QueryIntent = 
  | 'select_data'
  | 'insert_data'
  | 'update_data'
  | 'delete_data'
  | 'create_table'
  | 'alter_table'
  | 'analytical_query'
  | 'debug_query'
  | 'optimize_query'
  | 'explain_query';

export interface ExtractedEntity {
  type: 'table' | 'column' | 'condition' | 'aggregation' | 'function' | 'value';
  value: string;
  confidence: number;
  context?: string;
}

export interface QuerySpecification {
  intent: IntentAnalysis;
  schemaMetadata: SchemaMetadata;
  databaseType: 'mysql' | 'postgresql';
  optimizationLevel: 'basic' | 'advanced' | 'aggressive';
  userConstraints: QueryConstraints;
}

export interface QueryConstraints {
  maxExecutionTime?: number;
  readOnly?: boolean;
  allowedTables?: string[];
  forbiddenOperations?: string[];
}

export interface GeneratedQueryResult {
  sql: string;
  explanation: string;
  optimizationNotes: string[];
  estimatedPerformance: PerformanceEstimate;
  dialectSpecificFeatures: string[];
  alternatives?: QueryAlternative[];
}

export interface PerformanceEstimate {
  executionTimeMs: number;
  rowsExamined: number;
  rowsReturned: number;
  indexesUsed: string[];
  confidence: number;
}

export interface QueryAlternative {
  sql: string;
  description: string;
  tradeoffs: string[];
  performanceComparison: string;
}

// Error and debugging types
export interface ErrorAnalysis {
  errorType: 'syntax' | 'logical' | 'performance' | 'permission';
  severity: 'low' | 'medium' | 'high' | 'critical';
  location: QueryLocation;
  description: string;
  possibleCauses: string[];
  relatedIssues: string[];
}

export interface QueryLocation {
  line: number;
  column: number;
  clause?: string;
  length?: number;
}

export interface FixSuggestion {
  description: string;
  fixedQuery: string;
  explanation: string;
  confidence: number;
  impactAssessment: string;
}

// Optimization types
export interface OptimizationSuggestion {
  type: 'index' | 'rewrite' | 'hint' | 'schema_change';
  description: string;
  implementation: string;
  expectedImprovement: PerformanceImprovement;
  effort: 'low' | 'medium' | 'high';
  risk: 'low' | 'medium' | 'high';
}

export interface PerformanceImprovement {
  executionTimeReduction: number; // percentage
  resourceUsageReduction: number; // percentage
  scalabilityImprovement: string;
}

// UI-related types
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  type?: 'text' | 'query' | 'error' | 'suggestion';
  metadata?: {
    sql?: string;
    explanation?: string;
    suggestions?: string[];
    userInput?: string;
  };
}

export interface EditorState {
  content: string;
  language: 'mysql' | 'postgresql';
  readOnly: boolean;
  errors: EditorError[];
  suggestions: EditorSuggestion[];
}

export interface EditorError {
  line: number;
  column: number;
  message: string;
  severity: 'error' | 'warning' | 'info';
}

export interface EditorSuggestion {
  line: number;
  column: number;
  text: string;
  description: string;
}
